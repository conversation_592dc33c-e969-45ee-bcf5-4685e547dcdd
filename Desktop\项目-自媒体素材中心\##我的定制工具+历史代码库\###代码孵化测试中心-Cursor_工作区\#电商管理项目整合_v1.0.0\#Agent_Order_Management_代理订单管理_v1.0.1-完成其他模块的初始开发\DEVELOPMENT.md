# 多平台电商管理系统 - 开发文档

## 📋 项目概述

**Ali1688AutoERP** 是一个专业的电商自动化管理工具，基于 Python + PyQt6 + SQLite 构建，专注于1688平台的深度集成，同时保留多平台扩展能力。

### 🎯 核心特性
- 🔐 **OAuth2.0认证**：完整的授权流程和Token管理
- 🛍️ **商品管理**：自动获取、搜索、缓存商品信息
- 📦 **订单自动化**：一键下单、状态跟踪、批量处理
- ⏰ **定时任务**：自动同步、Token刷新、状态监控
- 🎨 **现代化UI**：暗黑主题、响应式设计
- 🔧 **模块化架构**：易于维护和扩展

## 🏗️ 技术架构

### 技术栈选型
```
┌─────────────────┬──────────────────────────────┐
│ 层级            │ 技术选型                      │
├─────────────────┼──────────────────────────────┤
│ UI界面层        │ PyQt6 + QSS暗黑主题          │
│ 业务逻辑层      │ Python 3.8+ + 模块化设计     │
│ API接口层       │ requests + OAuth2.0          │
│ 数据存储层      │ SQLite + JSON配置            │
│ 任务调度层      │ APScheduler                  │
│ 日志系统        │ loguru + Python logging      │
└─────────────────┴──────────────────────────────┘
```

### 项目结构详解
```
#代发订单管理系统/
├── 📱 main.py                      # 应用程序入口
├── 🔧 config/                      # 配置管理
│   ├── settings.py                 # 系统配置
│   └── api_config.py              # API平台配置 ⭐新增
├── 🏛️ core/                        # 核心模块
│   ├── database.py                # 数据库管理
│   └── auth_manager.py            # OAuth认证管理 ⭐新增
├── 🌐 api/                         # API客户端 ⭐新增
│   ├── __init__.py                # 模块初始化
│   ├── base_client.py             # 基础API客户端
│   ├── ali1688_client.py          # 1688专用客户端 [待实现]
│   └── api_utils.py               # API工具函数 [待实现]
├── ⏰ scheduler/                    # 定时任务 ⭐新增
│   ├── __init__.py
│   └── job_scheduler.py           # 任务调度器 [待实现]
├── 🛠️ utils/                       # 工具模块 ⭐新增
│   ├── __init__.py
│   ├── logger.py                  # 日志工具 [待实现]
│   └── helpers.py                 # 辅助函数 [待实现]
├── 🎨 ui/                          # 用户界面
│   ├── main_window.py             # 主窗口
│   ├── styles/dark_theme.qss      # 暗黑主题
│   └── widgets/                   # UI组件
│       ├── dashboard_widget.py         # 仪表板
│       ├── order_management_widget.py  # 订单管理
│       ├── product_management_widget.py # 商品管理
│       ├── inventory_management_widget.py # 库存管理
│       ├── dropship_management_widget.py # 代发管理
│       └── settings_widget.py          # 系统设置
├── 💾 data/                        # 数据存储
│   ├── ecommerce_manager.db       # SQLite数据库
│   └── tokens.json                # OAuth令牌存储 ⭐新增
├── 📋 logs/                        # 日志文件
├── 🗂️ temp/                        # 临时文件
└── 📚 文档文件...
```

## 🔧 开发环境搭建

### 1. 环境要求
```bash
Python >= 3.8
PyQt6 >= 6.4.0
操作系统: Windows 10+, macOS 10.15+, Linux
```

### 2. 依赖安装
```bash
# 安装基础依赖
pip install -r requirements.txt

# 或者手动安装核心依赖
pip install PyQt6>=6.4.0 requests>=2.28.0 APScheduler>=3.10.4 loguru>=0.7.2
```

### 3. 开发工具推荐
- **IDE**: Cursor (AI辅助开发)
- **版本控制**: Git
- **API测试**: Postman/Insomnia
- **数据库查看**: DB Browser for SQLite

## 🚀 快速开始

### 1. 启动应用
```bash
# 方法1: 直接启动
python main.py

# 方法2: Windows批处理
start.bat

# 方法3: 快速测试
python quick_test.py
```

### 2. 配置API密钥
```python
# 编辑 config/api_config.py
ALI1688_CONFIG = {
    "app_key": "你的App Key",
    "app_secret": "你的App Secret",
    "redirect_uri": "http://localhost:8080/callback",
    # ... 其他配置
}
```

### 3. 测试连接
```python
# 测试API配置
from config.api_config import is_platform_configured
print(is_platform_configured("1688"))  # 应该返回True

# 测试认证管理器
from core.auth_manager import AuthManager
auth = AuthManager()
print(auth.get_all_platforms_status())
```

## 📚 核心模块开发指南

### 1. API客户端开发

#### 基础API客户端 (api/base_client.py)
```python
from api.base_client import BaseAPIClient

class CustomPlatformClient(BaseAPIClient):
    def __init__(self):
        super().__init__("platform_name")
    
    def get_platform_config(self):
        # 返回平台配置
        pass
    
    def generate_signature(self, params):
        # 实现签名算法
        pass
    
    # ... 实现其他抽象方法
```

#### 1688客户端开发 (api/ali1688_client.py) [待实现]
```python
# 需要实现的核心功能：
1. OAuth2.0授权流程
2. API签名算法（MD5）
3. 商品搜索和详情获取
4. 订单创建和查询
5. 用户信息获取
6. Token自动刷新
```

### 2. OAuth认证流程

#### 认证管理器使用
```python
from core.auth_manager import AuthManager

# 创建认证管理器
auth = AuthManager()

# 启动授权流程
auth_url = auth.start_auth_flow("1688")
print(f"请访问: {auth_url}")

# 处理回调
auth_code = auth.extract_auth_code(callback_url)
if auth_code:
    # 使用API客户端获取Token
    pass
```

#### Token管理
```python
# 检查Token有效性
if auth.is_token_valid("1688"):
    token = auth.get_access_token("1688")
    # 使用Token调用API
else:
    # 需要重新授权或刷新Token
    pass
```

### 3. 定时任务开发 (scheduler/job_scheduler.py) [待实现]

#### 任务调度器设计
```python
from apscheduler.schedulers.background import BackgroundScheduler

class JobScheduler:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
    
    def add_token_refresh_job(self):
        # 添加Token刷新任务
        pass
    
    def add_order_sync_job(self):
        # 添加订单同步任务
        pass
    
    def start(self):
        self.scheduler.start()
```

### 4. 数据库扩展

#### 新增表结构
```sql
-- OAuth令牌表
CREATE TABLE oauth_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform_name VARCHAR(50) NOT NULL,
    access_token TEXT,
    refresh_token TEXT,
    expires_at INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- API调用日志表
CREATE TABLE api_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform_name VARCHAR(50),
    api_method VARCHAR(100),
    request_data TEXT,
    response_data TEXT,
    status_code INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔌 1688 API集成指南

### API接口列表
| 功能模块 | API方法 | 接口地址 | 状态 |
|---------|---------|----------|------|
| **OAuth认证** |
| 获取授权URL | - | `/oauth/authorize` | ✅ 已配置 |
| 获取Token | POST | `/system.oauth2/getToken` | 🔄 待实现 |
| 刷新Token | POST | `/system.oauth2/refreshToken` | 🔄 待实现 |
| **商品管理** |
| 商品搜索 | POST | `/alibaba.product.search` | 🔄 待实现 |
| 商品详情 | POST | `/alibaba.product.get` | 🔄 待实现 |
| **订单管理** |
| 创建订单 | POST | `/alibaba.trade.create` | 🔄 待实现 |
| 订单详情 | POST | `/alibaba.trade.get` | 🔄 待实现 |
| 订单列表 | POST | `/alibaba.trade.getBuyerOrderList` | 🔄 待实现 |
| **用户信息** |
| 基础信息 | POST | `/alibaba.account.basic` | 🔄 待实现 |

### API签名算法
```python
import hashlib
from urllib.parse import urlencode

def generate_1688_signature(params, app_secret):
    """
    生成1688 API签名
    """
    # 1. 排序参数
    sorted_params = sorted(params.items())
    
    # 2. 构建签名字符串
    sign_string = app_secret
    for key, value in sorted_params:
        sign_string += f"{key}{value}"
    sign_string += app_secret
    
    # 3. MD5加密并转大写
    return hashlib.md5(sign_string.encode('utf-8')).hexdigest().upper()
```

### OAuth授权流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant App as 应用程序
    participant Browser as 浏览器
    participant Auth as 1688授权服务
    participant API as 1688 API
    
    User->>App: 点击授权登录
    App->>Browser: 打开授权URL
    Browser->>Auth: 访问授权页面
    User->>Auth: 输入账号密码
    Auth->>Browser: 返回授权码
    Browser->>App: 回调URL+授权码
    App->>API: 使用授权码获取Token
    API->>App: 返回Access Token
    App->>App: 保存Token到本地
```

## 🧪 测试和调试

### 1. 单元测试
```python
# 创建测试文件 tests/test_api.py
import unittest
from api.ali1688_client import Ali1688Client

class TestAli1688Client(unittest.TestCase):
    def setUp(self):
        self.client = Ali1688Client()
    
    def test_signature_generation(self):
        # 测试签名生成
        pass
    
    def test_token_validation(self):
        # 测试Token验证
        pass

if __name__ == '__main__':
    unittest.main()
```

### 2. API调试
```python
# 创建调试脚本 debug_api.py
from api.ali1688_client import Ali1688Client
from core.auth_manager import AuthManager

def debug_auth_flow():
    """调试OAuth授权流程"""
    auth = AuthManager()
    
    # 1. 获取授权URL
    auth_url = auth.get_authorize_url("1688")
    print(f"授权URL: {auth_url}")
    
    # 2. 手动输入回调URL
    callback_url = input("请输入回调URL: ")
    auth_code = auth.extract_auth_code(callback_url)
    print(f"授权码: {auth_code}")

if __name__ == "__main__":
    debug_auth_flow()
```

### 3. 日志调试
```python
from loguru import logger

# 配置日志
logger.add("logs/api_debug.log", rotation="1 MB", level="DEBUG")

# 在API调用中添加日志
logger.debug(f"API请求: {method} {url}")
logger.info(f"API响应: {response.status_code}")
logger.error(f"API错误: {error}")
```

## 📋 开发任务清单

### 🔥 高优先级（立即开发）
- [ ] **实现ali1688_client.py**
  - [ ] OAuth2.0授权流程
  - [ ] API签名算法
  - [ ] 基础API调用方法
  - [ ] Token自动刷新

- [ ] **完善认证界面**
  - [ ] OAuth登录对话框
  - [ ] 授权状态显示
  - [ ] Token管理界面

- [ ] **API工具函数** (api/api_utils.py)
  - [ ] 通用请求封装
  - [ ] 错误处理
  - [ ] 响应解析

### 🔄 中优先级（后续开发）
- [ ] **定时任务调度器** (scheduler/job_scheduler.py)
  - [ ] Token自动刷新
  - [ ] 订单状态同步
  - [ ] 数据备份任务

- [ ] **日志系统** (utils/logger.py)
  - [ ] 统一日志格式
  - [ ] 日志轮转
  - [ ] 错误报告

- [ ] **业务功能实现**
  - [ ] 商品搜索和管理
  - [ ] 订单创建和跟踪
  - [ ] 库存监控

### 📚 低优先级（未来扩展）
- [ ] **多平台支持**
  - [ ] 淘宝API客户端
  - [ ] 抖音小店API客户端
  - [ ] 小红书API客户端

- [ ] **高级功能**
  - [ ] 数据分析报表
  - [ ] 自动化规则引擎
  - [ ] 插件系统

## 🛠️ 开发规范

### 1. 代码规范
```python
# 文件头部注释
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块描述
功能说明
"""

# 导入顺序
import 标准库
import 第三方库
import 本地模块

# 类和函数注释
class ExampleClass:
    """
    类的功能描述
    
    Attributes:
        attr1 (str): 属性描述
    """
    
    def example_method(self, param1: str) -> bool:
        """
        方法功能描述
        
        Args:
            param1 (str): 参数描述
            
        Returns:
            bool: 返回值描述
        """
        pass
```

### 2. Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动

示例:
feat: 实现1688 OAuth认证流程
fix: 修复Token过期检查逻辑
docs: 更新API集成文档
```

### 3. 错误处理
```python
import logging
from typing import Optional

def safe_api_call(func):
    """API调用装饰器，统一错误处理"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except requests.exceptions.RequestException as e:
            logging.error(f"API请求失败: {e}")
            return None
        except Exception as e:
            logging.error(f"未知错误: {e}")
            return None
    return wrapper
```

## 🔍 故障排除

### 常见问题解决

#### 1. 程序启动失败
```bash
# 问题: ImportError: cannot import name 'QAction'
# 解决: 已修复PyQt6兼容性问题

# 问题: ApplicationAttribute错误
# 解决: 已修复高DPI设置问题
```

#### 2. API调用失败
```python
# 检查配置
from config.api_config import is_platform_configured
print(is_platform_configured("1688"))

# 检查网络连接
import requests
response = requests.get("https://www.1688.com")
print(response.status_code)

# 检查Token有效性
from core.auth_manager import AuthManager
auth = AuthManager()
print(auth.is_token_valid("1688"))
```

#### 3. 数据库问题
```python
# 检查数据库连接
from core.database import DatabaseManager
db = DatabaseManager()
print(db.test_connection())

# 重建数据库
db.initialize_database()
```

## 📞 技术支持

### 开发资源
- **1688开放平台**: https://open.1688.com/
- **PyQt6文档**: https://doc.qt.io/qtforpython/
- **APScheduler文档**: https://apscheduler.readthedocs.io/

### 联系方式
- **项目仓库**: [GitHub地址]
- **技术讨论**: [技术交流群]
- **问题反馈**: [Issues页面]

---

**最后更新**: 2024年12月  
**版本**: v2.0.0 - Ali1688AutoERP专业版  
**开发工具**: Cursor + Python + PyQt6 