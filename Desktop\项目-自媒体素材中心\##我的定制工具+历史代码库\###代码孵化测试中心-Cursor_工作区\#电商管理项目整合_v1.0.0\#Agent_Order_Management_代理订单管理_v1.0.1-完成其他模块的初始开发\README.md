# Ali1688AutoERP - 专业电商自动化管理系统

基于 Python + PyQt6 + SQLite 构建的专业电商自动化管理工具，专注于1688平台的深度集成，同时保留多平台扩展能力。采用现代化暗黑UI主题，支持OAuth认证、自动订单管理、定时同步等功能。

## 🎯 核心特性

### 🔐 专业认证系统
- **OAuth2.0认证**: 完整的授权流程和Token管理
- **自动刷新**: Token自动刷新机制，无需手动重新授权
- **安全存储**: 加密存储认证信息，确保账户安全
- **多平台支持**: 统一的认证管理，支持多个平台同时使用

### 🛍️ 智能商品管理
- **自动获取**: 批量获取商品信息，支持关键词搜索
- **实时同步**: 自动同步商品价格、库存、详情等信息
- **智能缓存**: 本地缓存机制，提升查询速度
- **批量操作**: 支持批量编辑、导入导出等操作

### 📦 自动化订单系统
- **一键下单**: 自动化下单流程，支持批量订单处理
- **状态跟踪**: 实时跟踪订单状态，自动更新物流信息
- **智能提醒**: 订单异常、付款提醒等智能通知
- **历史管理**: 完整的订单历史记录和数据分析

### ⏰ 定时任务调度
- **自动同步**: 定时同步订单状态、商品信息
- **Token管理**: 自动刷新过期Token，保持连接活跃
- **数据备份**: 定期备份重要数据，防止数据丢失
- **监控预警**: 系统状态监控，异常情况及时通知

### 🎨 现代化界面设计
- **暗黑主题**: Material Design风格的暗黑主题
- **响应式布局**: 适配不同屏幕尺寸，支持全屏模式
- **直观仪表板**: 数据可视化展示，关键指标一目了然
- **用户友好**: 简洁直观的操作界面，降低学习成本

## 🏗️ 技术架构

### 核心技术栈
```
┌─────────────────┬──────────────────────────────┐
│ 层级            │ 技术选型                      │
├─────────────────┼──────────────────────────────┤
│ UI界面层        │ PyQt6 + QSS暗黑主题          │
│ 业务逻辑层      │ Python 3.8+ + 模块化设计     │
│ API接口层       │ requests + OAuth2.0          │
│ 数据存储层      │ SQLite + JSON配置            │
│ 任务调度层      │ APScheduler                  │
│ 日志系统        │ loguru + Python logging      │
└─────────────────┴──────────────────────────────┘
```

### 项目结构
```
Ali1688AutoERP/
├── 📱 main.py                      # 应用程序入口
├── 🔧 config/                      # 配置管理
│   ├── settings.py                 # 系统配置
│   └── api_config.py              # API平台配置
├── 🏛️ core/                        # 核心模块
│   ├── database.py                # 数据库管理
│   └── auth_manager.py            # OAuth认证管理
├── 🌐 api/                         # API客户端
│   ├── base_client.py             # 基础API客户端
│   ├── ali1688_client.py          # 1688专用客户端
│   └── api_utils.py               # API工具函数
├── ⏰ scheduler/                    # 定时任务
│   └── job_scheduler.py           # 任务调度器
├── 🛠️ utils/                       # 工具模块
│   ├── logger.py                  # 日志工具
│   └── helpers.py                 # 辅助函数
├── 🎨 ui/                          # 用户界面
│   ├── main_window.py             # 主窗口
│   ├── styles/dark_theme.qss      # 暗黑主题
│   └── widgets/                   # UI组件
│       ├── dashboard_widget.py         # 仪表板
│       ├── order_management_widget.py  # 订单管理
│       ├── product_management_widget.py # 商品管理
│       ├── inventory_management_widget.py # 库存管理
│       ├── dropship_management_widget.py # 代发管理
│       └── settings_widget.py          # 系统设置
├── 💾 data/                        # 数据存储
│   ├── ecommerce_manager.db       # SQLite数据库
│   └── tokens.json                # OAuth令牌存储
└── 📚 文档和配置文件...
```

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10+, macOS 10.15+, Linux
- **Python**: 3.8 或更高版本
- **内存**: 最低 4GB RAM
- **存储**: 最低 1GB 可用空间

### 安装步骤

#### 1. 克隆项目
```bash
git clone [项目地址]
cd Ali1688AutoERP
```

#### 2. 安装依赖
```bash
# 安装所有依赖
pip install -r requirements.txt

# 或者手动安装核心依赖
pip install PyQt6>=6.4.0 requests>=2.28.0 APScheduler>=3.10.4 loguru>=0.7.2
```

#### 3. 配置API密钥
```python
# 编辑 config/api_config.py
ALI1688_CONFIG = {
    "app_key": "你的1688 App Key",
    "app_secret": "你的1688 App Secret",
    "redirect_uri": "http://localhost:8080/callback",
    # ... 其他配置
}
```

#### 4. 启动应用
```bash
# 方法1: 直接启动
python main.py

# 方法2: Windows批处理
start.bat

# 方法3: 快速测试
python quick_test.py
```

## 🔌 1688 API集成

### 支持的API接口
| 功能模块 | API方法 | 状态 | 描述 |
|---------|---------|------|------|
| **OAuth认证** |
| 授权登录 | OAuth2.0 | ✅ | 扫码或网页授权登录 |
| Token刷新 | refresh_token | ✅ | 自动刷新过期Token |
| **商品管理** |
| 商品搜索 | alibaba.product.search | 🔄 | 关键词搜索商品 |
| 商品详情 | alibaba.product.get | 🔄 | 获取详细商品信息 |
| **订单管理** |
| 创建订单 | alibaba.trade.create | 🔄 | 自动下单功能 |
| 订单查询 | alibaba.trade.get | 🔄 | 查询订单详情 |
| 订单列表 | alibaba.trade.getBuyerOrderList | 🔄 | 获取订单列表 |
| **用户信息** |
| 基础信息 | alibaba.account.basic | 🔄 | 获取账户基础信息 |

### OAuth授权流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant App as 应用程序
    participant Browser as 浏览器
    participant Auth as 1688授权服务
    participant API as 1688 API
    
    User->>App: 点击授权登录
    App->>Browser: 打开授权URL
    Browser->>Auth: 访问授权页面
    User->>Auth: 输入账号密码
    Auth->>Browser: 返回授权码
    Browser->>App: 回调URL+授权码
    App->>API: 使用授权码获取Token
    API->>App: 返回Access Token
    App->>App: 保存Token到本地
```

## 📋 使用指南

### 1. 首次设置
1. **启动程序**: 运行 `python main.py`
2. **配置API**: 在设置页面输入1688 App Key和App Secret
3. **OAuth授权**: 点击授权按钮完成1688账户授权
4. **测试连接**: 验证API连接是否正常

### 2. 基本操作
- **商品搜索**: 输入关键词搜索1688商品
- **订单管理**: 查看、创建、跟踪订单状态
- **自动同步**: 设置定时任务自动同步数据
- **数据导出**: 导出订单和商品数据到Excel

### 3. 高级功能
- **批量下单**: 支持批量创建订单
- **利润计算**: 自动计算订单利润和成本
- **库存监控**: 实时监控库存变化
- **数据分析**: 生成销售和利润报表

## 🛠️ 开发状态

### ✅ 已完成功能
- [x] 项目基础架构搭建
- [x] PyQt6现代化界面框架
- [x] SQLite数据库设计
- [x] 暗黑主题UI实现
- [x] OAuth认证管理器
- [x] API客户端基础架构
- [x] 配置管理系统
- [x] 错误处理和日志系统

### 🔄 开发中功能
- [ ] 1688 API客户端实现
- [ ] OAuth认证界面
- [ ] 商品搜索和管理
- [ ] 订单创建和跟踪
- [ ] 定时任务调度器

### 📋 计划功能
- [ ] 批量操作功能
- [ ] 数据分析报表
- [ ] 多平台扩展（淘宝、抖音等）
- [ ] 移动端支持
- [ ] 云端数据同步

## 🔧 开发指南

### 环境搭建
```bash
# 克隆项目
git clone [项目地址]
cd Ali1688AutoERP

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装开发依赖
pip install -r requirements.txt
pip install pytest black flake8  # 开发工具
```

### 代码规范
- 遵循 PEP 8 编码规范
- 使用 Black 进行代码格式化
- 添加类型注解和文档字符串
- 编写单元测试

### 测试运行
```bash
# 运行所有测试
pytest tests/

# 运行快速测试
python quick_test.py

# 检查代码格式
black --check .
flake8 .
```

## 📞 技术支持

### 开发资源
- **1688开放平台**: https://open.1688.com/
- **PyQt6文档**: https://doc.qt.io/qtforpython/
- **APScheduler文档**: https://apscheduler.readthedocs.io/

### 问题反馈
- **GitHub Issues**: [项目Issues页面]
- **技术讨论**: [技术交流群]
- **邮件联系**: [联系邮箱]

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 🎉 更新日志

### v2.0.0 - Ali1688AutoERP专业版 (2024-12)
- 🚀 **重大更新**: 升级为专业化1688自动化管理系统
- 🔐 **新增**: OAuth2.0认证管理器
- 🌐 **新增**: 模块化API客户端架构
- ⏰ **新增**: APScheduler定时任务调度
- 📝 **新增**: loguru增强日志系统
- 🔧 **优化**: 重构项目架构，提升扩展性
- 🎨 **保留**: 现代化暗黑主题UI设计
- 🐛 **修复**: PyQt6兼容性问题

### v1.0.0 - 基础版本 (2024-11)
- 🎯 初始版本发布
- 🏗️ 基础架构完成
- 🎨 暗黑主题UI设计
- 💾 SQLite数据库设计
- 📊 仪表板功能实现

---

**开发工具**: Cursor (AI辅助开发) + Python + PyQt6  
**最后更新**: 2024年12月  
**项目状态**: 🚀 活跃开发中 