#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API管理模块
提供各电商平台的API客户端和工具函数
"""

from .base_client import BaseAPIClient
from .ali1688_client import Ali1688Client
from .api_utils import *

__all__ = [
    "BaseAPIClient",
    "Ali1688Client",
    "create_client",
    "get_supported_platforms",
]


def create_client(platform_name):
    """
    创建指定平台的API客户端

    Args:
        platform_name (str): 平台名称

    Returns:
        BaseAPIClient: API客户端实例
    """
    platform_name = platform_name.lower()

    if platform_name == "1688":
        return Ali1688Client()
    else:
        raise ValueError(f"不支持的平台: {platform_name}")


def get_supported_platforms():
    """
    获取支持的平台列表

    Returns:
        list: 支持的平台名称列表
    """
    return ["1688", "taobao", "douyin", "xiaohongshu"]
