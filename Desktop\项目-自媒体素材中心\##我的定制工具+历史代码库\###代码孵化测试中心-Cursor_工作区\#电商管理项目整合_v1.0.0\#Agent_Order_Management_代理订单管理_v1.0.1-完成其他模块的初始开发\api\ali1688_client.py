#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1688阿里巴巴API客户端
实现1688平台的OAuth认证、商品管理、订单管理等功能
"""

import time
import hashlib
import json
from typing import Dict, Any, Optional, List
from urllib.parse import urlencode

from .base_client import BaseAPIClient
from config.api_config import ALI1688_CONFIG


class Ali1688Client(BaseAPIClient):
    """
    1688阿里巴巴API客户端
    实现1688平台的完整API功能
    """

    def __init__(self):
        super().__init__("1688")
        self.config = ALI1688_CONFIG

    def get_platform_config(self) -> Dict[str, Any]:
        """
        获取1688平台配置

        Returns:
            dict: 平台配置字典
        """
        return self.config

    def generate_signature(self, params: Dict[str, Any]) -> str:
        """
        生成1688 API签名
        使用MD5算法生成签名

        Args:
            params (dict): 请求参数

        Returns:
            str: 签名字符串
        """
        app_secret = self.config.get("app_secret", "")

        # 1. 排序参数（按key排序）
        sorted_params = sorted(params.items())

        # 2. 构建签名字符串：app_secret + key1value1key2value2... + app_secret
        sign_string = app_secret
        for key, value in sorted_params:
            if key != "sign":  # 排除sign参数本身
                sign_string += f"{key}{value}"
        sign_string += app_secret

        # 3. MD5加密并转大写
        return hashlib.md5(sign_string.encode("utf-8")).hexdigest().upper()

    def get_authorize_url(self, state: str = None) -> str:
        """
        获取OAuth授权URL

        Args:
            state (str): 状态参数，用于防止CSRF攻击

        Returns:
            str: 授权URL
        """
        base_url = self.config["api_urls"]["authorize_url"]

        params = {
            "client_id": self.config["app_key"],
            "site": "china",
            "redirect_uri": self.config["redirect_uri"],
            "scope": ",".join(self.config["scopes"]),
        }

        if state:
            params["state"] = state

        query_string = urlencode(params)
        return f"{base_url}?{query_string}"

    def get_access_token(self, auth_code: str) -> Dict[str, Any]:
        """
        通过授权码获取访问令牌

        Args:
            auth_code (str): 授权码

        Returns:
            dict: 包含访问令牌的响应数据
        """
        url = self.config["api_urls"]["token_url"]

        params = {
            "client_id": self.config["app_key"],
            "client_secret": self.config["app_secret"],
            "redirect_uri": self.config["redirect_uri"],
            "grant_type": "authorization_code",
            "need_refresh_token": "true",
            "code": auth_code,
        }

        # 1688的token接口使用特殊的签名方式
        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)

            if response and "access_token" in response:
                # 保存token信息
                self.access_token = response["access_token"]
                self.refresh_token = response.get("refresh_token")

                # 计算过期时间
                expires_in = response.get("expires_in", 3600)
                self.token_expires_at = time.time() + int(expires_in)

                return response
            else:
                raise Exception(f"获取Token失败: {response}")

        except Exception as e:
            raise Exception(f"Token请求异常: {e}")

    def refresh_access_token(self) -> Dict[str, Any]:
        """
        刷新访问令牌

        Returns:
            dict: 包含新访问令牌的响应数据
        """
        if not self.refresh_token:
            raise Exception("没有refresh_token，无法刷新")

        url = self.config["api_urls"]["refresh_token_url"]

        params = {
            "client_id": self.config["app_key"],
            "client_secret": self.config["app_secret"],
            "refresh_token": self.refresh_token,
            "grant_type": "refresh_token",
        }

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)

            if response and "access_token" in response:
                # 更新token信息
                self.access_token = response["access_token"]
                if "refresh_token" in response:
                    self.refresh_token = response["refresh_token"]

                # 更新过期时间
                expires_in = response.get("expires_in", 3600)
                self.token_expires_at = time.time() + int(expires_in)

                return response
            else:
                raise Exception(f"刷新Token失败: {response}")

        except Exception as e:
            raise Exception(f"刷新Token异常: {e}")

    def get_user_info(self) -> Dict[str, Any]:
        """
        获取用户基础信息

        Returns:
            dict: 用户信息
        """
        self.ensure_token_valid()

        url = self.config["api_urls"]["user_info"]

        params = {
            "access_token": self.access_token,
        }

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)
            return response
        except Exception as e:
            raise Exception(f"获取用户信息失败: {e}")

    def search_products(self, keyword: str, **kwargs) -> Dict[str, Any]:
        """
        搜索商品

        Args:
            keyword (str): 搜索关键词
            **kwargs: 其他搜索参数
                - page_size: 每页数量 (默认20)
                - page_no: 页码 (默认1)
                - category_id: 类目ID
                - price_start: 起始价格
                - price_end: 结束价格

        Returns:
            dict: 搜索结果
        """
        self.ensure_token_valid()

        url = self.config["api_urls"]["offer_search"]

        params = {
            "access_token": self.access_token,
            "q": keyword,
            "pageSize": kwargs.get("page_size", 20),
            "page": kwargs.get("page_no", 1),
        }

        # 添加可选参数
        if "category_id" in kwargs:
            params["categoryId"] = kwargs["category_id"]
        if "price_start" in kwargs:
            params["priceStart"] = kwargs["price_start"]
        if "price_end" in kwargs:
            params["priceEnd"] = kwargs["price_end"]

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)
            return response
        except Exception as e:
            raise Exception(f"搜索商品失败: {e}")

    def get_product_detail(self, product_id: str) -> Dict[str, Any]:
        """
        获取商品详情

        Args:
            product_id (str): 商品ID (offerId)

        Returns:
            dict: 商品详情
        """
        self.ensure_token_valid()

        url = self.config["api_urls"]["offer_get"]

        params = {
            "access_token": self.access_token,
            "offerId": product_id,
        }

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)
            return response
        except Exception as e:
            raise Exception(f"获取商品详情失败: {e}")

    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建订单

        Args:
            order_data (dict): 订单数据
                - offer_id: 商品ID
                - quantity: 数量
                - spec_id: 规格ID (可选)
                - address_info: 收货地址信息
                - message: 备注信息 (可选)

        Returns:
            dict: 创建订单结果
        """
        self.ensure_token_valid()

        url = self.config["api_urls"]["trade_create"]

        # 构建订单参数
        params = {
            "access_token": self.access_token,
        }

        # 添加订单商品信息
        if "offer_id" in order_data:
            params["offerId"] = order_data["offer_id"]
        if "quantity" in order_data:
            params["quantity"] = order_data["quantity"]
        if "spec_id" in order_data:
            params["specId"] = order_data["spec_id"]

        # 添加收货地址信息
        if "address_info" in order_data:
            address = order_data["address_info"]
            params.update(
                {
                    "receiverName": address.get("name", ""),
                    "receiverMobile": address.get("mobile", ""),
                    "receiverPhone": address.get("phone", ""),
                    "receiverState": address.get("province", ""),
                    "receiverCity": address.get("city", ""),
                    "receiverDistrict": address.get("district", ""),
                    "receiverAddress": address.get("address", ""),
                    "receiverZip": address.get("zip_code", ""),
                }
            )

        # 添加备注信息
        if "message" in order_data:
            params["message"] = order_data["message"]

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)
            return response
        except Exception as e:
            raise Exception(f"创建订单失败: {e}")

    def get_order_detail(self, order_id: str) -> Dict[str, Any]:
        """
        获取订单详情

        Args:
            order_id (str): 订单ID

        Returns:
            dict: 订单详情
        """
        self.ensure_token_valid()

        url = self.config["api_urls"]["trade_get"]

        params = {
            "access_token": self.access_token,
            "orderId": order_id,
        }

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)
            return response
        except Exception as e:
            raise Exception(f"获取订单详情失败: {e}")

    def get_order_list(self, **kwargs) -> Dict[str, Any]:
        """
        获取订单列表

        Args:
            **kwargs: 查询参数
                - page_size: 每页数量 (默认20)
                - page_no: 页码 (默认1)
                - start_time: 开始时间 (格式: YYYY-MM-DD)
                - end_time: 结束时间 (格式: YYYY-MM-DD)
                - order_status: 订单状态

        Returns:
            dict: 订单列表
        """
        self.ensure_token_valid()

        url = self.config["api_urls"]["trade_list"]

        params = {
            "access_token": self.access_token,
            "pageSize": kwargs.get("page_size", 20),
            "page": kwargs.get("page_no", 1),
        }

        # 添加时间筛选
        if "start_time" in kwargs:
            params["startTime"] = kwargs["start_time"]
        if "end_time" in kwargs:
            params["endTime"] = kwargs["end_time"]

        # 添加状态筛选
        if "order_status" in kwargs:
            params["orderStatus"] = kwargs["order_status"]

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        try:
            response = self.make_request("POST", url, params=params)
            return response
        except Exception as e:
            raise Exception(f"获取订单列表失败: {e}")

    def get_logistics_info(self, order_id: str) -> Dict[str, Any]:
        """
        获取物流信息

        Args:
            order_id (str): 订单ID

        Returns:
            dict: 物流信息
        """
        self.ensure_token_valid()

        # 注意：这里需要根据实际的1688物流API接口进行调整
        # 当前使用订单详情接口获取物流信息
        order_detail = self.get_order_detail(order_id)

        # 提取物流相关信息
        logistics_info = {}
        if "logisticsItems" in order_detail:
            logistics_info = order_detail["logisticsItems"]

        return logistics_info

    def cancel_order(self, order_id: str, reason: str = "") -> Dict[str, Any]:
        """
        取消订单

        Args:
            order_id (str): 订单ID
            reason (str): 取消原因

        Returns:
            dict: 取消结果
        """
        self.ensure_token_valid()

        # 注意：需要根据实际的1688取消订单API接口进行实现
        # 这里提供基础框架
        params = {
            "access_token": self.access_token,
            "orderId": order_id,
            "cancelReason": reason,
        }

        params.update(self.get_common_params())
        signature = self.generate_signature(params)
        params["sign"] = signature

        # 返回模拟结果，实际需要调用真实API
        return {"success": True, "message": "订单取消成功", "order_id": order_id}

    def batch_get_products(self, product_ids: List[str]) -> Dict[str, Any]:
        """
        批量获取商品信息

        Args:
            product_ids (List[str]): 商品ID列表

        Returns:
            dict: 批量商品信息
        """
        results = []

        for product_id in product_ids:
            try:
                product_info = self.get_product_detail(product_id)
                results.append(
                    {"product_id": product_id, "success": True, "data": product_info}
                )
            except Exception as e:
                results.append(
                    {"product_id": product_id, "success": False, "error": str(e)}
                )

        return {
            "total": len(product_ids),
            "success_count": len([r for r in results if r["success"]]),
            "results": results,
        }
