#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API工具函数
提供通用的API请求封装、错误处理、响应解析等功能
"""

import time
import json
import logging
from typing import Dict, Any, Optional, Callable
from functools import wraps

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


def setup_session_with_retry(
    max_retries: int = 3, backoff_factor: float = 0.3
) -> requests.Session:
    """
    创建带重试机制的请求会话

    Args:
        max_retries (int): 最大重试次数
        backoff_factor (float): 重试间隔因子

    Returns:
        requests.Session: 配置好的会话对象
    """
    session = requests.Session()

    # 配置重试策略
    retry_strategy = Retry(
        total=max_retries,
        status_forcelist=[429, 500, 502, 503, 504],
        method_whitelist=["HEAD", "GET", "OPTIONS", "POST"],
        backoff_factor=backoff_factor,
    )

    # 应用重试策略到HTTP和HTTPS
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


def rate_limit(calls_per_second: float = 1.0):
    """
    API调用频率限制装饰器

    Args:
        calls_per_second (float): 每秒调用次数限制
    """
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret

        return wrapper

    return decorator


def log_api_call(func: Callable) -> Callable:
    """
    API调用日志装饰器

    Args:
        func (Callable): 被装饰的函数

    Returns:
        Callable: 装饰后的函数
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()

        # 记录调用开始
        logging.info(f"API调用开始: {func.__name__}")

        try:
            result = func(*args, **kwargs)
            elapsed_time = time.time() - start_time

            # 记录调用成功
            logging.info(f"API调用成功: {func.__name__}, 耗时: {elapsed_time:.2f}s")
            return result

        except Exception as e:
            elapsed_time = time.time() - start_time

            # 记录调用失败
            logging.error(
                f"API调用失败: {func.__name__}, 耗时: {elapsed_time:.2f}s, 错误: {e}"
            )
            raise

    return wrapper


def handle_api_errors(func: Callable) -> Callable:
    """
    API错误处理装饰器

    Args:
        func (Callable): 被装饰的函数

    Returns:
        Callable: 装饰后的函数
    """

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except requests.exceptions.Timeout:
            raise APIException("请求超时，请检查网络连接")
        except requests.exceptions.ConnectionError:
            raise APIException("网络连接错误，请检查网络设置")
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 401:
                raise APIException("认证失败，请检查API密钥或重新授权")
            elif e.response.status_code == 403:
                raise APIException("权限不足，请检查API权限设置")
            elif e.response.status_code == 429:
                raise APIException("请求频率过高，请稍后重试")
            elif e.response.status_code >= 500:
                raise APIException("服务器错误，请稍后重试")
            else:
                raise APIException(f"HTTP错误: {e.response.status_code}")
        except json.JSONDecodeError:
            raise APIException("响应数据格式错误，无法解析JSON")
        except Exception as e:
            raise APIException(f"未知错误: {str(e)}")

    return wrapper


class APIException(Exception):
    """
    API异常类
    用于统一处理API相关的异常
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict] = None,
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class ResponseParser:
    """
    API响应解析器
    提供统一的响应数据解析和验证功能
    """

    @staticmethod
    def parse_1688_response(response: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析1688 API响应

        Args:
            response (dict): 原始响应数据

        Returns:
            dict: 解析后的响应数据

        Raises:
            APIException: 当响应包含错误时
        """
        if not isinstance(response, dict):
            raise APIException("响应数据格式错误")

        # 检查是否有错误信息
        if "error_response" in response:
            error_info = response["error_response"]
            error_code = error_info.get("code", "UNKNOWN")
            error_msg = error_info.get("msg", "未知错误")
            raise APIException(f"API错误: {error_msg}", error_code, error_info)

        # 检查业务错误
        if "success" in response and not response["success"]:
            error_msg = response.get("message", "操作失败")
            raise APIException(error_msg)

        return response

    @staticmethod
    def extract_data_field(response: Dict[str, Any], field_name: str = "data") -> Any:
        """
        提取响应中的数据字段

        Args:
            response (dict): 响应数据
            field_name (str): 数据字段名

        Returns:
            Any: 提取的数据
        """
        if field_name in response:
            return response[field_name]

        # 尝试其他常见的数据字段名
        for alt_field in ["result", "content", "items"]:
            if alt_field in response:
                return response[alt_field]

        return response

    @staticmethod
    def extract_pagination_info(response: Dict[str, Any]) -> Dict[str, int]:
        """
        提取分页信息

        Args:
            response (dict): 响应数据

        Returns:
            dict: 分页信息
        """
        pagination = {}

        # 常见的分页字段名
        page_fields = {
            "current_page": ["page", "currentPage", "pageNo"],
            "page_size": ["pageSize", "size", "limit"],
            "total_count": ["total", "totalCount", "totalSize"],
            "total_pages": ["totalPages", "pageCount"],
        }

        for key, field_names in page_fields.items():
            for field_name in field_names:
                if field_name in response:
                    pagination[key] = response[field_name]
                    break

        return pagination


class RequestBuilder:
    """
    请求构建器
    提供便捷的API请求构建功能
    """

    def __init__(self, base_url: str):
        self.base_url = base_url
        self.params = {}
        self.headers = {}
        self.data = {}

    def add_param(self, key: str, value: Any) -> "RequestBuilder":
        """添加URL参数"""
        if value is not None:
            self.params[key] = value
        return self

    def add_header(self, key: str, value: str) -> "RequestBuilder":
        """添加请求头"""
        self.headers[key] = value
        return self

    def add_data(self, key: str, value: Any) -> "RequestBuilder":
        """添加请求数据"""
        if value is not None:
            self.data[key] = value
        return self

    def set_auth_token(self, token: str) -> "RequestBuilder":
        """设置认证Token"""
        return self.add_param("access_token", token)

    def set_pagination(self, page: int = 1, page_size: int = 20) -> "RequestBuilder":
        """设置分页参数"""
        return self.add_param("page", page).add_param("pageSize", page_size)

    def build_get_request(self) -> Dict[str, Any]:
        """构建GET请求"""
        return {
            "method": "GET",
            "url": self.base_url,
            "params": self.params,
            "headers": self.headers,
        }

    def build_post_request(self) -> Dict[str, Any]:
        """构建POST请求"""
        return {
            "method": "POST",
            "url": self.base_url,
            "params": self.params,
            "headers": self.headers,
            "data": self.data,
        }


def format_datetime_for_api(
    dt: Optional[str], format_type: str = "1688"
) -> Optional[str]:
    """
    格式化日期时间为API所需格式

    Args:
        dt (str): 日期时间字符串
        format_type (str): 格式类型

    Returns:
        str: 格式化后的日期时间字符串
    """
    if not dt:
        return None

    # 这里可以根据不同平台的要求进行格式转换
    if format_type == "1688":
        # 1688通常使用 YYYY-MM-DD HH:mm:ss 格式
        return dt

    return dt


def validate_required_params(params: Dict[str, Any], required_fields: list) -> None:
    """
    验证必需参数

    Args:
        params (dict): 参数字典
        required_fields (list): 必需字段列表

    Raises:
        APIException: 当缺少必需参数时
    """
    missing_fields = []

    for field in required_fields:
        if field not in params or params[field] is None or params[field] == "":
            missing_fields.append(field)

    if missing_fields:
        raise APIException(f"缺少必需参数: {', '.join(missing_fields)}")


def clean_params(params: Dict[str, Any]) -> Dict[str, Any]:
    """
    清理参数，移除空值

    Args:
        params (dict): 原始参数

    Returns:
        dict: 清理后的参数
    """
    cleaned = {}

    for key, value in params.items():
        if value is not None and value != "":
            if isinstance(value, str):
                value = value.strip()
                if value:  # 确保不是空字符串
                    cleaned[key] = value
            else:
                cleaned[key] = value

    return cleaned


def merge_configs(*configs: Dict[str, Any]) -> Dict[str, Any]:
    """
    合并多个配置字典

    Args:
        *configs: 配置字典

    Returns:
        dict: 合并后的配置
    """
    merged = {}

    for config in configs:
        if isinstance(config, dict):
            merged.update(config)

    return merged


# 预定义的常用请求头
COMMON_HEADERS = {
    "User-Agent": "Ali1688AutoERP/2.0.0",
    "Accept": "application/json",
    "Content-Type": "application/json; charset=utf-8",
}

# 预定义的HTTP状态码映射
HTTP_STATUS_MESSAGES = {
    200: "请求成功",
    400: "请求参数错误",
    401: "认证失败",
    403: "权限不足",
    404: "资源不存在",
    429: "请求频率过高",
    500: "服务器内部错误",
    502: "网关错误",
    503: "服务不可用",
    504: "网关超时",
}
