#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础API客户端
定义所有平台API客户端的通用接口和基础功能
"""

import time
import json
import hashlib
import requests
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from urllib.parse import urlencode

from config.api_config import COMMON_CONFIG


class BaseAPIClient(ABC):
    """
    基础API客户端抽象类
    所有平台的API客户端都应该继承此类
    """

    def __init__(self, platform_name: str):
        self.platform_name = platform_name
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": COMMON_CONFIG["user_agent"],
                "Content-Type": "application/json",
            }
        )

        # 配置请求参数
        self.timeout = COMMON_CONFIG["timeout"]
        self.max_retries = COMMON_CONFIG["max_retries"]
        self.retry_delay = COMMON_CONFIG["retry_delay"]

        # 认证信息
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None

    @abstractmethod
    def get_platform_config(self) -> Dict[str, Any]:
        """
        获取平台配置

        Returns:
            dict: 平台配置字典
        """
        pass

    @abstractmethod
    def generate_signature(self, params: Dict[str, Any]) -> str:
        """
        生成API签名

        Args:
            params (dict): 请求参数

        Returns:
            str: 签名字符串
        """
        pass

    @abstractmethod
    def get_authorize_url(self) -> str:
        """
        获取OAuth授权URL

        Returns:
            str: 授权URL
        """
        pass

    @abstractmethod
    def get_access_token(self, auth_code: str) -> Dict[str, Any]:
        """
        通过授权码获取访问令牌

        Args:
            auth_code (str): 授权码

        Returns:
            dict: 包含访问令牌的响应数据
        """
        pass

    @abstractmethod
    def refresh_access_token(self) -> Dict[str, Any]:
        """
        刷新访问令牌

        Returns:
            dict: 包含新访问令牌的响应数据
        """
        pass

    def is_token_valid(self) -> bool:
        """
        检查访问令牌是否有效

        Returns:
            bool: 令牌是否有效
        """
        if not self.access_token:
            return False

        if self.token_expires_at:
            return time.time() < self.token_expires_at

        return True

    def ensure_token_valid(self):
        """
        确保访问令牌有效，如果无效则尝试刷新
        """
        if not self.is_token_valid():
            if self.refresh_token:
                try:
                    self.refresh_access_token()
                except Exception as e:
                    raise Exception(f"刷新令牌失败: {e}")
            else:
                raise Exception("访问令牌无效且无刷新令牌")

    def make_request(
        self,
        method: str,
        url: str,
        params: Dict[str, Any] = None,
        data: Dict[str, Any] = None,
        headers: Dict[str, str] = None,
    ) -> Dict[str, Any]:
        """
        发起API请求

        Args:
            method (str): HTTP方法
            url (str): 请求URL
            params (dict): URL参数
            data (dict): 请求体数据
            headers (dict): 请求头

        Returns:
            dict: 响应数据
        """
        if headers:
            self.session.headers.update(headers)

        for attempt in range(self.max_retries + 1):
            try:
                response = self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data,
                    timeout=self.timeout,
                )

                # 检查HTTP状态码
                response.raise_for_status()

                # 解析JSON响应
                try:
                    result = response.json()
                except json.JSONDecodeError:
                    result = {"raw_response": response.text}

                return result

            except requests.exceptions.RequestException as e:
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay)
                    continue
                else:
                    raise Exception(f"API请求失败: {e}")

    def get_common_params(self) -> Dict[str, Any]:
        """
        获取通用请求参数

        Returns:
            dict: 通用参数字典
        """
        config = self.get_platform_config()

        params = {
            "app_key": config.get("app_key", ""),
            "timestamp": str(int(time.time() * 1000)),
            "format": config.get("format", "json"),
            "version": config.get("api_version", "1"),
            "sign_method": config.get("sign_method", "md5"),
        }

        if self.access_token:
            params["access_token"] = self.access_token

        return params

    def build_request_url(self, base_url: str, params: Dict[str, Any]) -> str:
        """
        构建完整的请求URL

        Args:
            base_url (str): 基础URL
            params (dict): 请求参数

        Returns:
            str: 完整的请求URL
        """
        # 添加通用参数
        all_params = self.get_common_params()
        all_params.update(params)

        # 生成签名
        signature = self.generate_signature(all_params)
        all_params["sign"] = signature

        # 构建URL
        query_string = urlencode(sorted(all_params.items()))
        return f"{base_url}?{query_string}"

    # 抽象方法：具体的API调用方法需要在子类中实现
    @abstractmethod
    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息"""
        pass

    @abstractmethod
    def search_products(self, keyword: str, **kwargs) -> Dict[str, Any]:
        """搜索商品"""
        pass

    @abstractmethod
    def get_product_detail(self, product_id: str) -> Dict[str, Any]:
        """获取商品详情"""
        pass

    @abstractmethod
    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建订单"""
        pass

    @abstractmethod
    def get_order_detail(self, order_id: str) -> Dict[str, Any]:
        """获取订单详情"""
        pass

    @abstractmethod
    def get_order_list(self, **kwargs) -> Dict[str, Any]:
        """获取订单列表"""
        pass
