#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速状态检查脚本
检查程序运行状态和基本信息
"""

import os
import sys
from pathlib import Path


def check_files():
    """检查必要文件是否存在"""
    print("📁 检查项目文件...")

    required_files = [
        "main.py",
        "requirements.txt",
        "README.md",
        "config/settings.py",
        "core/database.py",
        "ui/main_window.py",
        "ui/styles/dark_theme.qss",
    ]

    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✓ {file_path}")
        else:
            print(f"  ✗ {file_path} (缺失)")
            missing_files.append(file_path)

    return len(missing_files) == 0


def check_directories():
    """检查数据目录"""
    print("\n📂 检查数据目录...")

    from config.settings import DATA_DIR, LOGS_DIR, TEMP_DIR

    dirs = [("数据目录", DATA_DIR), ("日志目录", LOGS_DIR), ("临时目录", TEMP_DIR)]

    for name, dir_path in dirs:
        if dir_path.exists():
            print(f"  ✓ {name}: {dir_path}")
        else:
            print(f"  ✗ {name}: {dir_path} (不存在)")


def check_database():
    """检查数据库状态"""
    print("\n🗄️ 检查数据库状态...")

    try:
        from core.database import db_manager
        from config.settings import DATABASE_CONFIG

        db_path = DATABASE_CONFIG["path"]
        if db_path.exists():
            size = db_path.stat().st_size
            print(f"  ✓ 数据库文件: {db_path}")
            print(f"  ✓ 文件大小: {size} 字节")

            # 检查表
            tables = db_manager.get_all_tables()
            print(f"  ✓ 数据表数量: {len(tables)}")
        else:
            print(f"  ✗ 数据库文件不存在: {db_path}")

    except Exception as e:
        print(f"  ✗ 数据库检查失败: {e}")


def show_system_info():
    """显示系统信息"""
    print("\n💻 系统信息...")

    print(f"  Python版本: {sys.version.split()[0]}")
    print(f"  操作系统: {os.name}")
    print(f"  工作目录: {os.getcwd()}")

    try:
        import PyQt6

        print(f"  PyQt6版本: 已安装")
    except ImportError:
        print(f"  PyQt6版本: 未安装")


def main():
    """主函数"""
    print("🔍 多平台电商管理系统 - 状态检查")
    print("=" * 50)

    # 检查文件
    files_ok = check_files()

    # 检查目录
    check_directories()

    # 检查数据库
    check_database()

    # 显示系统信息
    show_system_info()

    print("\n" + "=" * 50)
    if files_ok:
        print("✅ 系统状态正常，可以启动程序！")
        print("\n🚀 启动命令:")
        print("   python main.py")
        print("   或双击 start.bat")
    else:
        print("❌ 发现问题，请检查缺失的文件")

    print("=" * 50)


if __name__ == "__main__":
    main()
