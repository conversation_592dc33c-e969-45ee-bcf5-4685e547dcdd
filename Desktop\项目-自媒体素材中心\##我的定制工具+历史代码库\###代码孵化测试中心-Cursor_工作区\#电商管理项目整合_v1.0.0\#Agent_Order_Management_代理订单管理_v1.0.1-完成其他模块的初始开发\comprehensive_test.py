#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ali1688AutoERP - 综合功能测试脚本
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_imports():
    """测试所有模块导入"""
    print("🔍 测试模块导入...")

    try:
        # 基础模块
        from utils.logger import setup_logger, get_logger
        from utils.file_utils import FileManager
        from utils.encryption import EncryptionManager
        from utils.data_validator import DataValidator

        # 核心模块
        from core.database import DatabaseManager
        from core.config_manager import ConfigManager

        # API模块
        from api.ali1688_client import Ali1688Client
        from api.api_utils import ResponseParser, RequestBuilder

        # 调度器
        from scheduler.job_scheduler import JobScheduler

        print("✅ 所有核心模块导入成功")
        return True

    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        traceback.print_exc()
        return False


def test_database():
    """测试数据库连接"""
    print("\n🔍 测试数据库功能...")

    try:
        from core.database import DatabaseManager

        db_manager = DatabaseManager()

        # 测试表是否存在
        tables = ["platforms", "products", "orders", "inventory"]
        for table in tables:
            try:
                result = db_manager.execute_query(
                    f"SELECT COUNT(*) as count FROM {table}"
                )
                if result:
                    print(f"✅ 表 {table} 存在，记录数: {result[0]['count']}")
                else:
                    print(f"⚠️ 表 {table} 为空")
            except:
                print(f"⚠️ 表 {table} 不存在或有问题")

        print("✅ 数据库连接正常")
        return True

    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False


def test_config():
    """测试配置管理"""
    print("\n🔍 测试配置管理功能...")

    try:
        from core.config_manager import ConfigManager

        config = ConfigManager()

        # 测试获取配置
        app_config = config.get_config("app")
        api_config = config.get_config("api")

        print(f"✅ 应用配置: {bool(app_config)}")
        print(f"✅ API配置: {bool(api_config)}")

        return True

    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False


def test_logger():
    """测试日志系统"""
    print("\n🔍 测试日志系统...")

    try:
        from utils.logger import setup_logger, get_logger

        setup_logger()
        logger = get_logger("test")

        logger.info("测试信息日志")
        logger.warning("测试警告日志")

        # 检查日志文件
        log_files = list(Path("logs").glob("*.log"))
        print(f"✅ 日志文件数量: {len(log_files)}")

        return True

    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False


def test_file_utils():
    """测试文件工具"""
    print("\n🔍 测试文件工具...")

    try:
        from utils.file_utils import FileManager

        file_manager = FileManager()

        # 测试JSON读写
        test_data = {"test": "data", "number": 123}
        test_file = "temp/test_file.json"

        file_manager.write_json(test_file, test_data)
        read_data = file_manager.read_json(test_file)

        if read_data == test_data:
            print("✅ JSON读写测试通过")
        else:
            print("❌ JSON读写测试失败")
            return False

        # 清理
        if os.path.exists(test_file):
            os.remove(test_file)

        return True

    except Exception as e:
        print(f"❌ 文件工具测试失败: {e}")
        return False


def test_ui_imports():
    """测试UI模块导入"""
    print("\n🔍 测试UI模块导入...")

    try:
        from ui.widgets.dashboard_widget import DashboardWidget
        from ui.widgets.order_management_widget import OrderManagementWidget
        from ui.widgets.product_management_widget import ProductManagementWidget
        from ui.widgets.inventory_management_widget import InventoryManagementWidget
        from ui.widgets.dropship_management_widget import DropshipManagementWidget

        print("✅ 所有UI模块导入成功")
        return True

    except Exception as e:
        print(f"❌ UI模块导入失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 Ali1688AutoERP 综合功能测试")
    print("=" * 50)

    # 测试列表
    tests = [
        ("模块导入", test_imports),
        ("数据库功能", test_database),
        ("配置管理", test_config),
        ("日志系统", test_logger),
        ("文件工具", test_file_utils),
        ("UI模块导入", test_ui_imports),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！系统功能正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关模块。")
        return False


if __name__ == "__main__":
    success = main()
    print(f"\n程序退出状态: {'成功' if success else '失败'}")
