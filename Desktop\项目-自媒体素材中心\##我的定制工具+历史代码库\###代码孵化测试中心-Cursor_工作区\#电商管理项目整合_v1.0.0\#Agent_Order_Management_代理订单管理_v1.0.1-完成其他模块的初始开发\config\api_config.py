#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置管理
专门管理各电商平台的API配置信息
"""

# 1688 阿里巴巴开放平台配置
ALI1688_CONFIG = {
    "app_key": "",  # 应用的App Key
    "app_secret": "",  # 应用的App Secret
    "redirect_uri": "http://localhost:8080/callback",  # OAuth回调地址
    "sandbox": True,  # 是否使用沙箱环境
    # API接口地址
    "api_urls": {
        # OAuth相关
        "authorize_url": "https://auth.1688.com/oauth/authorize",
        "token_url": "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken",
        "refresh_token_url": "https://gw.open.1688.com/openapi/http/1/system.oauth2/refreshToken",
        # 商品相关
        "offer_get": "https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.get",
        "offer_search": "https://gw.open.1688.com/openapi/param2/1/com.alibaba.product/alibaba.product.search",
        # 订单相关
        "trade_create": "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.create",
        "trade_get": "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.get",
        "trade_list": "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.getBuyerOrderList",
        # 用户信息
        "user_info": "https://gw.open.1688.com/openapi/param2/1/com.alibaba.account/alibaba.account.basic",
    },
    # 权限范围
    "scopes": [
        "china1688",  # 基础权限
        "offer",  # 商品权限
        "trade",  # 交易权限
        "account",  # 账户权限
    ],
    # API版本
    "api_version": "1",
    # 签名方式
    "sign_method": "md5",
    # 请求格式
    "format": "json",
}

# 淘宝开放平台配置
TAOBAO_CONFIG = {
    "app_key": "",
    "app_secret": "",
    "redirect_uri": "http://localhost:8080/callback",
    "sandbox": True,
    "api_urls": {
        "authorize_url": "https://oauth.taobao.com/authorize",
        "token_url": "https://eco.taobao.com/router/rest",
    },
    "scopes": [
        "item",
        "trade",
        "user",
    ],
}

# 抖音小店配置
DOUYIN_CONFIG = {
    "app_key": "",
    "app_secret": "",
    "redirect_uri": "http://localhost:8080/callback",
    "sandbox": True,
    "api_urls": {
        "base_url": "https://openapi-fxg.jinritemai.com",
        "authorize_url": "https://fxg.jinritemai.com/login/common",
    },
    "scopes": [
        "product.list",
        "order.list",
        "shop.basic",
    ],
}

# 小红书配置
XIAOHONGSHU_CONFIG = {
    "app_key": "",
    "app_secret": "",
    "redirect_uri": "http://localhost:8080/callback",
    "sandbox": True,
    "api_urls": {
        "base_url": "https://ark.xiaohongshu.com",
    },
    "scopes": [
        "basic",
        "content",
        "commerce",
    ],
}

# 平台配置映射
PLATFORM_CONFIGS = {
    "1688": ALI1688_CONFIG,
    "taobao": TAOBAO_CONFIG,
    "douyin": DOUYIN_CONFIG,
    "xiaohongshu": XIAOHONGSHU_CONFIG,
}

# 通用API配置
COMMON_CONFIG = {
    # 请求超时时间（秒）
    "timeout": 30,
    # 重试次数
    "max_retries": 3,
    # 重试间隔（秒）
    "retry_delay": 1,
    # 用户代理
    "user_agent": "EcommerceManager/1.0.0",
    # 日志级别
    "log_level": "INFO",
    # Token存储路径
    "token_storage_path": "data/tokens.json",
    # 缓存配置
    "cache": {
        "enabled": True,
        "ttl": 3600,  # 缓存有效期（秒）
        "max_size": 1000,  # 最大缓存条目数
    },
}


def get_platform_config(platform_name):
    """
    获取指定平台的配置

    Args:
        platform_name (str): 平台名称

    Returns:
        dict: 平台配置字典
    """
    return PLATFORM_CONFIGS.get(platform_name.lower(), {})


def is_platform_configured(platform_name):
    """
    检查平台是否已配置

    Args:
        platform_name (str): 平台名称

    Returns:
        bool: 是否已配置
    """
    config = get_platform_config(platform_name)
    return bool(config.get("app_key") and config.get("app_secret"))


def get_all_configured_platforms():
    """
    获取所有已配置的平台列表

    Returns:
        list: 已配置的平台名称列表
    """
    configured = []
    for platform_name in PLATFORM_CONFIGS.keys():
        if is_platform_configured(platform_name):
            configured.append(platform_name)
    return configured
