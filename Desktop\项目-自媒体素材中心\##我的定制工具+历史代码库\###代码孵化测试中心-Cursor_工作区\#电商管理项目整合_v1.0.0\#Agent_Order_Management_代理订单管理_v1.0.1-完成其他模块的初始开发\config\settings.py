# -*- coding: utf-8 -*-
"""
配置文件
包含应用程序的基本设置和各平台API配置
"""

import os
from pathlib import Path

# 应用程序基本配置
APP_CONFIG = {
    "app_name": "多平台电商管理系统",
    "version": "1.0.0",
    "organization": "ECommerce Manager",
    "description": "支持淘宝、小红书、抖音小店、1688等多平台的订单和商品管理系统",
}

# 项目路径配置
BASE_DIR = Path(__file__).resolve().parent.parent
DATA_DIR = BASE_DIR / "data"
LOGS_DIR = BASE_DIR / "logs"
TEMP_DIR = BASE_DIR / "temp"

# 确保目录存在
for dir_path in [DATA_DIR, LOGS_DIR, TEMP_DIR]:
    dir_path.mkdir(exist_ok=True)

# 数据库配置
DATABASE_CONFIG = {
    "type": "sqlite",
    "path": DATA_DIR / "ecommerce_manager.db",
    "backup_path": DATA_DIR / "backup",
}

# API配置模板（用户需要在设置中填入真实的API密钥）
API_CONFIGS = {
    "taobao": {
        "name": "淘宝开放平台",
        "enabled": False,
        "app_key": "",
        "app_secret": "",
        "session_key": "",
        "sandbox_mode": True,
        "api_base_url": "https://eco.taobao.com/router/rest",
        "sandbox_url": "https://gw.api.tbsandbox.com/router/rest",
    },
    "xiaohongshu": {
        "name": "小红书开放平台",
        "enabled": False,
        "app_id": "",
        "app_secret": "",
        "access_token": "",
        "api_base_url": "https://ark.xiaohongshu.com/api",
    },
    "douyin": {
        "name": "抖音小店开放平台",
        "enabled": False,
        "app_key": "",
        "app_secret": "",
        "access_token": "",
        "shop_id": "",
        "api_base_url": "https://openapi-fxgw.jinritemai.com",
    },
    "alibaba_1688": {
        "name": "1688开放平台",
        "enabled": False,
        "app_key": "",
        "app_secret": "",
        "access_token": "",
        "api_base_url": "https://gw.open.1688.com/openapi",
    },
}

# UI配置
UI_CONFIG = {
    "window_size": {
        "width": 1300,
        "height": 1200,
        "min_width": 1000,
        "min_height": 600,
    },
    "theme": "dark",
    "language": "zh_CN",
    "auto_save_interval": 300,  # 自动保存间隔（秒）
    "refresh_interval": 60,  # 数据刷新间隔（秒）
}

# 功能模块配置
FEATURES_CONFIG = {
    "order_management": {
        "enabled": True,
        "auto_sync": True,
        "sync_interval": 300,  # 订单同步间隔（秒）
        "max_orders_per_sync": 100,
    },
    "product_management": {
        "enabled": True,
        "auto_sync": True,
        "sync_interval": 600,  # 商品同步间隔（秒）
        "image_cache_enabled": True,
    },
    "inventory_management": {
        "enabled": True,
        "low_stock_warning": 10,
        "auto_update": True,
    },
    "dropshipping": {
        "enabled": True,
        "auto_order": False,  # 是否自动下单
        "profit_margin": 0.15,  # 默认利润率
    },
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file_path": LOGS_DIR / "app.log",
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "backup_count": 5,
}

# 网络请求配置
NETWORK_CONFIG = {
    "timeout": 30,
    "retry_times": 3,
    "retry_delay": 1,
    "user_agent": f'{APP_CONFIG["app_name"]} v{APP_CONFIG["version"]}',
}

# 数据导入导出配置
IMPORT_EXPORT_CONFIG = {
    "supported_formats": ["xlsx", "csv", "json"],
    "max_file_size": 50 * 1024 * 1024,  # 50MB
    "batch_size": 1000,
}
