#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OAuth认证管理器
处理各电商平台的OAuth2.0认证流程
"""

import os
import json
import time
import webbrowser
from typing import Dict, Any, Optional
from urllib.parse import urlencode, parse_qs, urlparse

from config.api_config import get_platform_config, COMMON_CONFIG


class AuthManager:
    """
    OAuth认证管理器
    统一管理各平台的认证流程和令牌存储
    """

    def __init__(self):
        self.token_storage_path = COMMON_CONFIG["token_storage_path"]
        self.tokens = self._load_tokens()

    def _load_tokens(self) -> Dict[str, Any]:
        """
        从本地文件加载已保存的令牌

        Returns:
            dict: 令牌数据
        """
        if os.path.exists(self.token_storage_path):
            try:
                with open(self.token_storage_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载令牌文件失败: {e}")
                return {}
        return {}

    def _save_tokens(self):
        """
        保存令牌到本地文件
        """
        # 确保目录存在
        os.makedirs(os.path.dirname(self.token_storage_path), exist_ok=True)

        try:
            with open(self.token_storage_path, "w", encoding="utf-8") as f:
                json.dump(self.tokens, f, ensure_ascii=False, indent=2)
        except IOError as e:
            print(f"保存令牌文件失败: {e}")

    def get_platform_tokens(self, platform_name: str) -> Dict[str, Any]:
        """
        获取指定平台的令牌信息

        Args:
            platform_name (str): 平台名称

        Returns:
            dict: 令牌信息
        """
        return self.tokens.get(platform_name.lower(), {})

    def save_platform_tokens(self, platform_name: str, token_data: Dict[str, Any]):
        """
        保存平台令牌信息

        Args:
            platform_name (str): 平台名称
            token_data (dict): 令牌数据
        """
        platform_name = platform_name.lower()

        # 添加保存时间戳
        token_data["saved_at"] = int(time.time())

        # 计算过期时间戳
        if "expires_in" in token_data:
            token_data["expires_at"] = int(time.time()) + int(token_data["expires_in"])

        self.tokens[platform_name] = token_data
        self._save_tokens()

    def is_token_valid(self, platform_name: str) -> bool:
        """
        检查平台令牌是否有效

        Args:
            platform_name (str): 平台名称

        Returns:
            bool: 令牌是否有效
        """
        token_data = self.get_platform_tokens(platform_name)

        if not token_data or "access_token" not in token_data:
            return False

        # 检查是否已过期
        if "expires_at" in token_data:
            # 提前5分钟认为过期，避免边界情况
            return time.time() < (token_data["expires_at"] - 300)

        return True

    def get_authorize_url(self, platform_name: str, state: str = None) -> str:
        """
        获取OAuth授权URL

        Args:
            platform_name (str): 平台名称
            state (str): 状态参数，用于防止CSRF攻击

        Returns:
            str: 授权URL
        """
        config = get_platform_config(platform_name)
        if not config:
            raise ValueError(f"不支持的平台: {platform_name}")

        api_urls = config.get("api_urls", {})
        authorize_url = api_urls.get("authorize_url")

        if not authorize_url:
            raise ValueError(f"平台 {platform_name} 未配置授权URL")

        # 构建授权参数
        params = {
            "client_id": config["app_key"],
            "redirect_uri": config["redirect_uri"],
            "response_type": "code",
        }

        # 添加权限范围
        if config.get("scopes"):
            if platform_name.lower() == "1688":
                params["scope"] = ",".join(config["scopes"])
            else:
                params["scope"] = " ".join(config["scopes"])

        # 添加状态参数
        if state:
            params["state"] = state

        # 构建完整URL
        query_string = urlencode(params)
        return f"{authorize_url}?{query_string}"

    def start_auth_flow(self, platform_name: str) -> str:
        """
        启动OAuth认证流程

        Args:
            platform_name (str): 平台名称

        Returns:
            str: 授权URL
        """
        # 生成状态参数
        state = f"{platform_name}_{int(time.time())}"

        # 获取授权URL
        auth_url = self.get_authorize_url(platform_name, state)

        # 打开浏览器进行授权
        try:
            webbrowser.open(auth_url)
            print(f"已打开浏览器进行 {platform_name} 授权")
            print(f"授权URL: {auth_url}")
        except Exception as e:
            print(f"打开浏览器失败: {e}")
            print(f"请手动访问: {auth_url}")

        return auth_url

    def extract_auth_code(self, callback_url: str) -> Optional[str]:
        """
        从回调URL中提取授权码

        Args:
            callback_url (str): 回调URL

        Returns:
            str: 授权码，如果提取失败返回None
        """
        try:
            parsed_url = urlparse(callback_url)
            query_params = parse_qs(parsed_url.query)

            # 检查是否有错误
            if "error" in query_params:
                error = query_params["error"][0]
                error_description = query_params.get("error_description", [""])[0]
                raise Exception(f"授权失败: {error} - {error_description}")

            # 提取授权码
            if "code" in query_params:
                return query_params["code"][0]

            return None

        except Exception as e:
            print(f"提取授权码失败: {e}")
            return None

    def clear_platform_tokens(self, platform_name: str):
        """
        清除指定平台的令牌

        Args:
            platform_name (str): 平台名称
        """
        platform_name = platform_name.lower()
        if platform_name in self.tokens:
            del self.tokens[platform_name]
            self._save_tokens()

    def clear_all_tokens(self):
        """
        清除所有令牌
        """
        self.tokens.clear()
        self._save_tokens()

    def get_all_platforms_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有平台的认证状态

        Returns:
            dict: 各平台的状态信息
        """
        status = {}

        for platform_name in self.tokens.keys():
            token_data = self.get_platform_tokens(platform_name)
            status[platform_name] = {
                "authenticated": self.is_token_valid(platform_name),
                "access_token": bool(token_data.get("access_token")),
                "refresh_token": bool(token_data.get("refresh_token")),
                "expires_at": token_data.get("expires_at"),
                "saved_at": token_data.get("saved_at"),
            }

        return status

    def get_access_token(self, platform_name: str) -> Optional[str]:
        """
        获取有效的访问令牌

        Args:
            platform_name (str): 平台名称

        Returns:
            str: 访问令牌，如果无效返回None
        """
        if self.is_token_valid(platform_name):
            token_data = self.get_platform_tokens(platform_name)
            return token_data.get("access_token")
        return None

    def get_refresh_token(self, platform_name: str) -> Optional[str]:
        """
        获取刷新令牌

        Args:
            platform_name (str): 平台名称

        Returns:
            str: 刷新令牌，如果不存在返回None
        """
        token_data = self.get_platform_tokens(platform_name)
        return token_data.get("refresh_token")
