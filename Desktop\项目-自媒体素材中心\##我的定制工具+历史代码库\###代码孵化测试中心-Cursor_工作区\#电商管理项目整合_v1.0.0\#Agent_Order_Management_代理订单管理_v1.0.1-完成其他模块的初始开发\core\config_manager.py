# -*- coding: utf-8 -*-
"""
配置管理器
统一管理应用程序配置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from config.settings import (
    APP_CONFIG,
    DATABASE_CONFIG,
    API_CONFIGS,
    UI_CONFIG,
    FEATURES_CONFIG,
    LOGGING_CONFIG,
    NETWORK_CONFIG,
    IMPORT_EXPORT_CONFIG,
)


class ConfigManager:
    """配置管理器"""

    def __init__(self):
        """初始化配置管理器"""
        self.base_dir = Path(__file__).resolve().parent.parent
        self.config_dir = self.base_dir / "config"
        self.user_config_file = self.config_dir / "user_config.json"

        # 默认配置
        self.default_configs = {
            "app": APP_CONFIG,
            "database": DATABASE_CONFIG,
            "api": API_CONFIGS,
            "ui": UI_CONFIG,
            "features": FEATURES_CONFIG,
            "logging": LOGGING_CONFIG,
            "network": NETWORK_CONFIG,
            "import_export": IMPORT_EXPORT_CONFIG,
        }

        # 用户配置
        self.user_configs = self.load_user_config()

    def load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        if self.user_config_file.exists():
            try:
                with open(self.user_config_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载用户配置失败: {e}")
                return {}
        return {}

    def save_user_config(self) -> bool:
        """保存用户配置"""
        try:
            self.config_dir.mkdir(exist_ok=True)
            with open(self.user_config_file, "w", encoding="utf-8") as f:
                json.dump(self.user_configs, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"保存用户配置失败: {e}")
            return False

    def get_config(self, config_name: str) -> Dict[str, Any]:
        """获取配置"""
        # 先从用户配置获取，如果没有则使用默认配置
        user_config = self.user_configs.get(config_name, {})
        default_config = self.default_configs.get(config_name, {})

        # 合并配置（用户配置覆盖默认配置）
        merged_config = default_config.copy()
        if isinstance(user_config, dict):
            merged_config.update(user_config)

        return merged_config

    def set_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """设置配置"""
        try:
            if config_name not in self.default_configs:
                print(f"未知的配置类型: {config_name}")
                return False

            self.user_configs[config_name] = config_data
            return self.save_user_config()
        except Exception as e:
            print(f"设置配置失败: {e}")
            return False

    def update_config(self, config_name: str, updates: Dict[str, Any]) -> bool:
        """更新配置"""
        try:
            current_config = self.user_configs.get(config_name, {})
            current_config.update(updates)
            self.user_configs[config_name] = current_config
            return self.save_user_config()
        except Exception as e:
            print(f"更新配置失败: {e}")
            return False

    def get_api_config(self, platform: str) -> Dict[str, Any]:
        """获取特定平台的API配置"""
        api_configs = self.get_config("api")
        return api_configs.get(platform, {})

    def set_api_config(self, platform: str, api_config: Dict[str, Any]) -> bool:
        """设置特定平台的API配置"""
        try:
            current_api_configs = self.user_configs.get("api", {})
            current_api_configs[platform] = api_config
            self.user_configs["api"] = current_api_configs
            return self.save_user_config()
        except Exception as e:
            print(f"设置API配置失败: {e}")
            return False

    def is_api_enabled(self, platform: str) -> bool:
        """检查API是否启用"""
        api_config = self.get_api_config(platform)
        return api_config.get("enabled", False)

    def get_database_path(self) -> Path:
        """获取数据库路径"""
        db_config = self.get_config("database")
        db_path = db_config.get("path")
        if isinstance(db_path, str):
            return Path(db_path)
        return db_path

    def get_logs_dir(self) -> Path:
        """获取日志目录"""
        return self.base_dir / "logs"

    def get_temp_dir(self) -> Path:
        """获取临时目录"""
        return self.base_dir / "temp"

    def get_data_dir(self) -> Path:
        """获取数据目录"""
        return self.base_dir / "data"

    def reset_config(self, config_name: str) -> bool:
        """重置配置为默认值"""
        try:
            if config_name in self.user_configs:
                del self.user_configs[config_name]
                return self.save_user_config()
            return True
        except Exception as e:
            print(f"重置配置失败: {e}")
            return False

    def export_config(self, file_path: str) -> bool:
        """导出配置"""
        try:
            export_data = {
                "default_configs": self.default_configs,
                "user_configs": self.user_configs,
            }
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(export_data, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"导出配置失败: {e}")
            return False

    def import_config(self, file_path: str) -> bool:
        """导入配置"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                import_data = json.load(f)

            if "user_configs" in import_data:
                self.user_configs = import_data["user_configs"]
                return self.save_user_config()
            return False
        except Exception as e:
            print(f"导入配置失败: {e}")
            return False
