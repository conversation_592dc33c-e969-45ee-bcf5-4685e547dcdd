# -*- coding: utf-8 -*-
"""
数据库管理模块
处理SQLite数据库的连接、表创建和基本操作
"""

import sqlite3
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from config.settings import DATABASE_CONFIG


class DatabaseManager:
    """数据库管理器"""

    def __init__(self):
        self.db_path = DATABASE_CONFIG["path"]
        self.connection = None
        self.cursor = None
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        try:
            # 确保数据库目录存在
            self.db_path.parent.mkdir(parents=True, exist_ok=True)

            # 连接数据库
            self.connect()

            # 创建表
            self.create_tables()

            logging.info("数据库初始化完成")

        except Exception as e:
            logging.error(f"数据库初始化失败: {e}")
            raise

    def connect(self):
        """连接数据库"""
        try:
            self.connection = sqlite3.connect(
                self.db_path, check_same_thread=False, timeout=30
            )
            self.connection.row_factory = sqlite3.Row  # 使结果可以通过列名访问
            self.cursor = self.connection.cursor()

            # 启用外键约束
            self.cursor.execute("PRAGMA foreign_keys = ON")

        except Exception as e:
            logging.error(f"数据库连接失败: {e}")
            raise

    def disconnect(self):
        """断开数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()

    def create_tables(self):
        """创建数据库表"""
        tables = [
            self._create_platforms_table(),
            self._create_stores_table(),
            self._create_products_table(),
            self._create_orders_table(),
            self._create_order_items_table(),
            self._create_inventory_table(),
            self._create_suppliers_table(),
            self._create_dropship_orders_table(),
            self._create_sync_logs_table(),
            self._create_settings_table(),
        ]

        for table_sql in tables:
            try:
                self.cursor.execute(table_sql)
                self.connection.commit()
            except Exception as e:
                logging.error(f"创建表失败: {e}")
                raise

    def _create_platforms_table(self):
        """创建平台表"""
        return """
        CREATE TABLE IF NOT EXISTS platforms (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            code TEXT NOT NULL UNIQUE,
            api_config TEXT,
            enabled BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

    def _create_stores_table(self):
        """创建店铺表"""
        return """
        CREATE TABLE IF NOT EXISTS stores (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            store_id TEXT NOT NULL,
            access_token TEXT,
            refresh_token TEXT,
            expires_at TIMESTAMP,
            enabled BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (platform_id) REFERENCES platforms (id),
            UNIQUE(platform_id, store_id)
        )
        """

    def _create_products_table(self):
        """创建商品表"""
        return """
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            store_id INTEGER NOT NULL,
            platform_product_id TEXT NOT NULL,
            title TEXT NOT NULL,
            description TEXT,
            price DECIMAL(10,2),
            original_price DECIMAL(10,2),
            currency TEXT DEFAULT 'CNY',
            main_image TEXT,
            images TEXT,
            category TEXT,
            brand TEXT,
            sku_count INTEGER DEFAULT 0,
            status TEXT DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            synced_at TIMESTAMP,
            FOREIGN KEY (store_id) REFERENCES stores (id),
            UNIQUE(store_id, platform_product_id)
        )
        """

    def _create_orders_table(self):
        """创建订单表"""
        return """
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            store_id INTEGER NOT NULL,
            platform_order_id TEXT NOT NULL,
            order_status TEXT NOT NULL,
            payment_status TEXT,
            shipping_status TEXT,
            total_amount DECIMAL(10,2),
            shipping_fee DECIMAL(10,2),
            discount_amount DECIMAL(10,2),
            currency TEXT DEFAULT 'CNY',
            buyer_name TEXT,
            buyer_phone TEXT,
            shipping_address TEXT,
            order_time TIMESTAMP,
            payment_time TIMESTAMP,
            shipping_time TIMESTAMP,
            delivery_time TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            synced_at TIMESTAMP,
            FOREIGN KEY (store_id) REFERENCES stores (id),
            UNIQUE(store_id, platform_order_id)
        )
        """

    def _create_order_items_table(self):
        """创建订单商品表"""
        return """
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER,
            platform_product_id TEXT,
            sku_id TEXT,
            title TEXT NOT NULL,
            price DECIMAL(10,2),
            quantity INTEGER NOT NULL,
            total_amount DECIMAL(10,2),
            specifications TEXT,
            image TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """

    def _create_inventory_table(self):
        """创建库存表"""
        return """
        CREATE TABLE IF NOT EXISTS inventory (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            sku_id TEXT,
            stock_quantity INTEGER DEFAULT 0,
            reserved_quantity INTEGER DEFAULT 0,
            available_quantity INTEGER GENERATED ALWAYS AS (stock_quantity - reserved_quantity) VIRTUAL,
            low_stock_threshold INTEGER DEFAULT 10,
            cost_price DECIMAL(10,2),
            supplier_id INTEGER,
            warehouse_location TEXT,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
            UNIQUE(product_id, sku_id)
        )
        """

    def _create_suppliers_table(self):
        """创建供应商表"""
        return """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            contact_person TEXT,
            phone TEXT,
            email TEXT,
            address TEXT,
            platform TEXT,
            platform_id TEXT,
            api_config TEXT,
            enabled BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

    def _create_dropship_orders_table(self):
        """创建代发订单表"""
        return """
        CREATE TABLE IF NOT EXISTS dropship_orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            supplier_id INTEGER NOT NULL,
            supplier_order_id TEXT,
            status TEXT DEFAULT 'pending',
            total_cost DECIMAL(10,2),
            profit DECIMAL(10,2),
            tracking_number TEXT,
            shipping_company TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
        """

    def _create_sync_logs_table(self):
        """创建同步日志表"""
        return """
        CREATE TABLE IF NOT EXISTS sync_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT NOT NULL,
            sync_type TEXT NOT NULL,
            status TEXT NOT NULL,
            message TEXT,
            records_count INTEGER DEFAULT 0,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP,
            error_details TEXT
        )
        """

    def _create_settings_table(self):
        """创建设置表"""
        return """
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            key TEXT NOT NULL,
            value TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(category, key)
        )
        """

    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询语句"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)

            results = self.cursor.fetchall()
            return [dict(row) for row in results]

        except Exception as e:
            logging.error(f"查询执行失败: {e}")
            raise

    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新语句"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)

            self.connection.commit()
            return self.cursor.rowcount

        except Exception as e:
            logging.error(f"更新执行失败: {e}")
            self.connection.rollback()
            raise

    def execute_many(self, query: str, params_list: List[tuple]) -> int:
        """批量执行语句"""
        try:
            self.cursor.executemany(query, params_list)
            self.connection.commit()
            return self.cursor.rowcount

        except Exception as e:
            logging.error(f"批量执行失败: {e}")
            self.connection.rollback()
            raise

    def get_last_insert_id(self) -> int:
        """获取最后插入的ID"""
        return self.cursor.lastrowid

    def backup_database(self, backup_path: Optional[Path] = None):
        """备份数据库"""
        if backup_path is None:
            backup_dir = DATABASE_CONFIG["backup_path"]
            backup_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"backup_{timestamp}.db"

        try:
            # 创建备份连接
            backup_conn = sqlite3.connect(backup_path)

            # 执行备份
            self.connection.backup(backup_conn)
            backup_conn.close()

            logging.info(f"数据库备份完成: {backup_path}")
            return backup_path

        except Exception as e:
            logging.error(f"数据库备份失败: {e}")
            raise

    def restore_database(self, backup_path: Path):
        """恢复数据库"""
        try:
            if not backup_path.exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")

            # 断开当前连接
            self.disconnect()

            # 复制备份文件
            import shutil

            shutil.copy2(backup_path, self.db_path)

            # 重新连接
            self.connect()

            logging.info(f"数据库恢复完成: {backup_path}")

        except Exception as e:
            logging.error(f"数据库恢复失败: {e}")
            raise

    def get_table_info(self, table_name: str) -> List[Dict]:
        """获取表结构信息"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)

    def get_all_tables(self) -> List[str]:
        """获取所有表名"""
        query = "SELECT name FROM sqlite_master WHERE type='table'"
        results = self.execute_query(query)
        return [row["name"] for row in results]

    def vacuum_database(self):
        """压缩数据库"""
        try:
            self.cursor.execute("VACUUM")
            self.connection.commit()
            logging.info("数据库压缩完成")

        except Exception as e:
            logging.error(f"数据库压缩失败: {e}")
            raise


# 全局数据库管理器实例
db_manager = DatabaseManager()
