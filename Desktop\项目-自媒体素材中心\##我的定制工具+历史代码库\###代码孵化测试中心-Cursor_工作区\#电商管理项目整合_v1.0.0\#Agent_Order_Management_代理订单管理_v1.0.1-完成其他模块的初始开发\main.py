#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多平台电商管理系统
主程序入口
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QDir
from PyQt6.QtGui import QIcon, QFont

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow
from config.settings import APP_CONFIG


def setup_application():
    """设置应用程序基本配置"""
    app = QApplication(sys.argv)

    # 设置应用程序基本信息
    app.setApplicationName(APP_CONFIG["app_name"])
    app.setApplicationVersion(APP_CONFIG["version"])
    app.setOrganizationName(APP_CONFIG["organization"])

    # 设置高DPI支持（PyQt6中自动启用，无需手动设置）
    # 在PyQt6中，高DPI支持是默认启用的
    try:
        # 尝试设置高DPI属性（如果可用）
        if hasattr(Qt.ApplicationAttribute, "AA_UseHighDpiPixmaps"):
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    except AttributeError:
        # PyQt6中可能不需要这些属性
        pass

    # 设置默认字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)

    # 加载暗黑主题样式
    load_dark_theme(app)

    return app


def load_dark_theme(app):
    """加载暗黑主题样式"""
    style_file = os.path.join(
        os.path.dirname(__file__), "ui", "styles", "dark_theme.qss"
    )
    if os.path.exists(style_file):
        with open(style_file, "r", encoding="utf-8") as f:
            app.setStyleSheet(f.read())


def main():
    """主函数"""
    try:
        app = setup_application()

        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        # 启动应用程序事件循环
        sys.exit(app.exec())

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
