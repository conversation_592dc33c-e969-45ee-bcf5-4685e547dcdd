#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证程序启动
"""


def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    try:
        import sys
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt

        print("✅ PyQt6 基本模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 基本导入失败: {e}")
        return False


def test_app_creation():
    """测试应用程序创建"""
    print("🔍 测试应用程序创建...")
    try:
        import sys
        from PyQt6.QtWidgets import QApplication

        # 检查是否已有应用实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            print("✅ 新应用程序创建成功")
        else:
            print("✅ 使用现有应用程序实例")

        app.quit()
        return True
    except Exception as e:
        print(f"❌ 应用程序创建失败: {e}")
        return False


def test_main_window_import():
    """测试主窗口导入"""
    print("🔍 测试主窗口导入...")
    try:
        from ui.main_window import MainWindow

        print("✅ 主窗口模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 主窗口导入失败: {e}")
        return False


def test_config_import():
    """测试配置导入"""
    print("🔍 测试配置导入...")
    try:
        from config.settings import APP_CONFIG

        print(f"✅ 配置导入成功 - 应用名: {APP_CONFIG['app_name']}")
        return True
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 多平台电商管理系统 - 快速测试")
    print("=" * 50)

    tests = [
        test_basic_imports,
        test_app_creation,
        test_config_import,
        test_main_window_import,
    ]

    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}\n")

    print("=" * 50)
    print(f"📊 测试结果: {passed}/{len(tests)} 通过")

    if passed == len(tests):
        print("🎉 所有测试通过！程序应该可以正常启动")
        print("💡 启动命令: python main.py")
    else:
        print("⚠️ 部分测试失败，可能存在问题")

    return passed == len(tests)


if __name__ == "__main__":
    main()
