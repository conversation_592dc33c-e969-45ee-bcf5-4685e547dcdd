# Ali1688AutoERP 专业版依赖包
# 基于 PyQt6 的现代化电商自动化管理系统

# GUI框架
PyQt6>=6.4.0

# 网络请求和HTTP客户端
requests>=2.28.0
urllib3>=1.26.0
httpx>=0.24.0
aiohttp>=3.8.0

# 数据处理和分析
pandas>=1.5.0
openpyxl>=3.0.0
numpy>=1.24.0

# 加密和安全
cryptography>=41.0.0
pycryptodome>=3.18.0

# 数据验证和序列化
pydantic>=2.0.0
marshmallow>=3.19.0

# 日志和配置管理
python-dotenv>=1.0.0
loguru>=0.7.2
pyyaml>=6.0

# 时间和日期处理
python-dateutil>=2.8.0
pytz>=2023.3

# 任务调度
APScheduler>=3.10.4

# JSON处理（高性能）
orjson>=3.9.0

# 数据库相关
sqlalchemy>=2.0.0  # 可选，用于高级数据库操作

# 图像处理（商品图片处理）
Pillow>=10.0.0

# 系统信息和监控
psutil>=5.9.0

# 正则表达式和文本处理
regex>=2023.6.0

# 开发和测试工具
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# 性能分析和调试
memory-profiler>=0.61.0
line-profiler>=4.1.0

# 文档生成（可选）
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# 打包和分发（可选）
pyinstaller>=5.13.0
cx-freeze>=6.15.0 