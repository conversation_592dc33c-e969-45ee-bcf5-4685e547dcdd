#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务调度器
实现Token自动刷新、订单状态同步、数据备份等自动化任务
"""

import logging
from typing import Dict, Any, Optional, Callable
from datetime import datetime, timedelta

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR

from core.auth_manager import AuthManager
from api import create_client
from core.database import DatabaseManager


class JobScheduler:
    """
    定时任务调度器
    管理所有自动化任务的执行
    """

    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.auth_manager = AuthManager()
        self.db_manager = DatabaseManager()
        self.jobs = {}

        # 配置调度器事件监听
        self.scheduler.add_listener(
            self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR
        )

        # 默认任务配置
        self.default_configs = {
            "token_refresh": {
                "interval_minutes": 30,  # 每30分钟检查一次Token
                "enabled": True,
            },
            "order_sync": {
                "interval_minutes": 15,  # 每15分钟同步一次订单
                "enabled": True,
            },
            "data_backup": {"hour": 2, "minute": 0, "enabled": True},  # 每天凌晨2点备份
            "system_monitor": {
                "interval_minutes": 5,  # 每5分钟监控一次系统状态
                "enabled": True,
            },
        }

    def start(self):
        """启动调度器"""
        if not self.scheduler.running:
            self.scheduler.start()
            logging.info("定时任务调度器已启动")

            # 添加默认任务
            self._add_default_jobs()

    def stop(self):
        """停止调度器"""
        if self.scheduler.running:
            self.scheduler.shutdown(wait=False)
            logging.info("定时任务调度器已停止")

    def _job_listener(self, event):
        """任务执行事件监听器"""
        job_id = event.job_id

        if event.exception:
            logging.error(f"任务执行失败: {job_id}, 错误: {event.exception}")
        else:
            logging.info(f"任务执行成功: {job_id}")

    def _add_default_jobs(self):
        """添加默认任务"""
        # Token刷新任务
        if self.default_configs["token_refresh"]["enabled"]:
            self.add_token_refresh_job()

        # 订单同步任务
        if self.default_configs["order_sync"]["enabled"]:
            self.add_order_sync_job()

        # 数据备份任务
        if self.default_configs["data_backup"]["enabled"]:
            self.add_data_backup_job()

        # 系统监控任务
        if self.default_configs["system_monitor"]["enabled"]:
            self.add_system_monitor_job()

    def add_token_refresh_job(self, interval_minutes: Optional[int] = None):
        """
        添加Token刷新任务

        Args:
            interval_minutes (int): 检查间隔（分钟）
        """
        interval = (
            interval_minutes
            or self.default_configs["token_refresh"]["interval_minutes"]
        )

        job_id = "token_refresh"

        # 移除已存在的任务
        if job_id in self.jobs:
            self.remove_job(job_id)

        # 添加新任务
        job = self.scheduler.add_job(
            func=self._refresh_tokens_task,
            trigger=IntervalTrigger(minutes=interval),
            id=job_id,
            name="Token自动刷新",
            max_instances=1,
            replace_existing=True,
        )

        self.jobs[job_id] = job
        logging.info(f"已添加Token刷新任务，间隔: {interval}分钟")

    def add_order_sync_job(self, interval_minutes: Optional[int] = None):
        """
        添加订单同步任务

        Args:
            interval_minutes (int): 同步间隔（分钟）
        """
        interval = (
            interval_minutes or self.default_configs["order_sync"]["interval_minutes"]
        )

        job_id = "order_sync"

        # 移除已存在的任务
        if job_id in self.jobs:
            self.remove_job(job_id)

        # 添加新任务
        job = self.scheduler.add_job(
            func=self._sync_orders_task,
            trigger=IntervalTrigger(minutes=interval),
            id=job_id,
            name="订单状态同步",
            max_instances=1,
            replace_existing=True,
        )

        self.jobs[job_id] = job
        logging.info(f"已添加订单同步任务，间隔: {interval}分钟")

    def add_data_backup_job(
        self, hour: Optional[int] = None, minute: Optional[int] = None
    ):
        """
        添加数据备份任务

        Args:
            hour (int): 备份时间（小时）
            minute (int): 备份时间（分钟）
        """
        backup_hour = hour or self.default_configs["data_backup"]["hour"]
        backup_minute = minute or self.default_configs["data_backup"]["minute"]

        job_id = "data_backup"

        # 移除已存在的任务
        if job_id in self.jobs:
            self.remove_job(job_id)

        # 添加新任务
        job = self.scheduler.add_job(
            func=self._backup_data_task,
            trigger=CronTrigger(hour=backup_hour, minute=backup_minute),
            id=job_id,
            name="数据备份",
            max_instances=1,
            replace_existing=True,
        )

        self.jobs[job_id] = job
        logging.info(f"已添加数据备份任务，时间: {backup_hour:02d}:{backup_minute:02d}")

    def add_system_monitor_job(self, interval_minutes: Optional[int] = None):
        """
        添加系统监控任务

        Args:
            interval_minutes (int): 监控间隔（分钟）
        """
        interval = (
            interval_minutes
            or self.default_configs["system_monitor"]["interval_minutes"]
        )

        job_id = "system_monitor"

        # 移除已存在的任务
        if job_id in self.jobs:
            self.remove_job(job_id)

        # 添加新任务
        job = self.scheduler.add_job(
            func=self._monitor_system_task,
            trigger=IntervalTrigger(minutes=interval),
            id=job_id,
            name="系统状态监控",
            max_instances=1,
            replace_existing=True,
        )

        self.jobs[job_id] = job
        logging.info(f"已添加系统监控任务，间隔: {interval}分钟")

    def add_custom_job(
        self, job_id: str, func: Callable, trigger_config: Dict[str, Any], **kwargs
    ):
        """
        添加自定义任务

        Args:
            job_id (str): 任务ID
            func (Callable): 任务函数
            trigger_config (dict): 触发器配置
            **kwargs: 其他任务参数
        """
        # 解析触发器配置
        trigger_type = trigger_config.get("type", "interval")

        if trigger_type == "interval":
            trigger = IntervalTrigger(
                **{k: v for k, v in trigger_config.items() if k != "type"}
            )
        elif trigger_type == "cron":
            trigger = CronTrigger(
                **{k: v for k, v in trigger_config.items() if k != "type"}
            )
        else:
            raise ValueError(f"不支持的触发器类型: {trigger_type}")

        # 移除已存在的任务
        if job_id in self.jobs:
            self.remove_job(job_id)

        # 添加新任务
        job = self.scheduler.add_job(
            func=func,
            trigger=trigger,
            id=job_id,
            max_instances=1,
            replace_existing=True,
            **kwargs,
        )

        self.jobs[job_id] = job
        logging.info(f"已添加自定义任务: {job_id}")

    def remove_job(self, job_id: str):
        """
        移除任务

        Args:
            job_id (str): 任务ID
        """
        if job_id in self.jobs:
            self.scheduler.remove_job(job_id)
            del self.jobs[job_id]
            logging.info(f"已移除任务: {job_id}")

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态

        Args:
            job_id (str): 任务ID

        Returns:
            dict: 任务状态信息
        """
        if job_id not in self.jobs:
            return None

        job = self.jobs[job_id]
        return {
            "id": job.id,
            "name": job.name,
            "next_run_time": job.next_run_time,
            "trigger": str(job.trigger),
            "max_instances": job.max_instances,
        }

    def get_all_jobs_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有任务状态

        Returns:
            dict: 所有任务的状态信息
        """
        status = {}
        for job_id in self.jobs:
            status[job_id] = self.get_job_status(job_id)
        return status

    def _refresh_tokens_task(self):
        """Token刷新任务"""
        try:
            logging.info("开始执行Token刷新任务")

            # 获取所有平台的Token状态
            platforms_status = self.auth_manager.get_all_platforms_status()

            for platform_name, status in platforms_status.items():
                try:
                    # 检查Token是否即将过期（提前5分钟刷新）
                    if not self.auth_manager.is_token_valid(platform_name):
                        logging.info(f"Token即将过期，开始刷新: {platform_name}")

                        # 创建API客户端
                        client = create_client(platform_name)

                        # 设置现有的Token信息
                        token_data = self.auth_manager.get_platform_tokens(
                            platform_name
                        )
                        client.access_token = token_data.get("access_token")
                        client.refresh_token = token_data.get("refresh_token")

                        # 刷新Token
                        new_token_data = client.refresh_access_token()

                        # 保存新Token
                        self.auth_manager.save_platform_tokens(
                            platform_name, new_token_data
                        )

                        logging.info(f"Token刷新成功: {platform_name}")

                except Exception as e:
                    logging.error(f"Token刷新失败: {platform_name}, 错误: {e}")

            logging.info("Token刷新任务完成")

        except Exception as e:
            logging.error(f"Token刷新任务异常: {e}")

    def _sync_orders_task(self):
        """订单同步任务"""
        try:
            logging.info("开始执行订单同步任务")

            # 获取有效的平台Token
            platforms_status = self.auth_manager.get_all_platforms_status()

            for platform_name, status in platforms_status.items():
                if not status["authenticated"]:
                    continue

                try:
                    logging.info(f"同步订单数据: {platform_name}")

                    # 创建API客户端
                    client = create_client(platform_name)

                    # 设置Token
                    client.access_token = self.auth_manager.get_access_token(
                        platform_name
                    )
                    client.refresh_token = self.auth_manager.get_refresh_token(
                        platform_name
                    )

                    # 获取最近的订单（最近24小时）
                    end_time = datetime.now()
                    start_time = end_time - timedelta(hours=24)

                    orders_response = client.get_order_list(
                        start_time=start_time.strftime("%Y-%m-%d"),
                        end_time=end_time.strftime("%Y-%m-%d"),
                        page_size=50,
                    )

                    # 处理订单数据（这里需要根据实际需求实现）
                    # self._process_orders_data(platform_name, orders_response)

                    logging.info(f"订单同步完成: {platform_name}")

                except Exception as e:
                    logging.error(f"订单同步失败: {platform_name}, 错误: {e}")

            logging.info("订单同步任务完成")

        except Exception as e:
            logging.error(f"订单同步任务异常: {e}")

    def _backup_data_task(self):
        """数据备份任务"""
        try:
            logging.info("开始执行数据备份任务")

            # 执行数据库备份
            backup_result = self.db_manager.backup_database()

            if backup_result:
                logging.info("数据备份成功")
            else:
                logging.error("数据备份失败")

            # 清理旧备份文件（保留最近7天的备份）
            self.db_manager.cleanup_old_backups(days=7)

            logging.info("数据备份任务完成")

        except Exception as e:
            logging.error(f"数据备份任务异常: {e}")

    def _monitor_system_task(self):
        """系统监控任务"""
        try:
            logging.debug("开始执行系统监控任务")

            # 检查数据库连接
            db_status = self.db_manager.test_connection()
            if not db_status:
                logging.warning("数据库连接异常")

            # 检查API连接状态
            platforms_status = self.auth_manager.get_all_platforms_status()

            for platform_name, status in platforms_status.items():
                if status["authenticated"]:
                    try:
                        # 测试API连接
                        client = create_client(platform_name)
                        client.access_token = self.auth_manager.get_access_token(
                            platform_name
                        )

                        # 调用简单的API测试连接
                        user_info = client.get_user_info()
                        logging.debug(f"API连接正常: {platform_name}")

                    except Exception as e:
                        logging.warning(f"API连接异常: {platform_name}, 错误: {e}")

            logging.debug("系统监控任务完成")

        except Exception as e:
            logging.error(f"系统监控任务异常: {e}")

    def _process_orders_data(self, platform_name: str, orders_data: Dict[str, Any]):
        """
        处理订单数据

        Args:
            platform_name (str): 平台名称
            orders_data (dict): 订单数据
        """
        # 这里实现订单数据的处理逻辑
        # 例如：更新数据库中的订单状态、发送通知等
        pass

    def pause_job(self, job_id: str):
        """暂停任务"""
        if job_id in self.jobs:
            self.scheduler.pause_job(job_id)
            logging.info(f"已暂停任务: {job_id}")

    def resume_job(self, job_id: str):
        """恢复任务"""
        if job_id in self.jobs:
            self.scheduler.resume_job(job_id)
            logging.info(f"已恢复任务: {job_id}")

    def modify_job(self, job_id: str, **changes):
        """修改任务"""
        if job_id in self.jobs:
            self.scheduler.modify_job(job_id, **changes)
            logging.info(f"已修改任务: {job_id}")

    def run_job_now(self, job_id: str):
        """立即执行任务"""
        if job_id in self.jobs:
            job = self.jobs[job_id]
            job.func()
            logging.info(f"已手动执行任务: {job_id}")


# 全局调度器实例
_scheduler_instance = None


def get_scheduler() -> JobScheduler:
    """获取全局调度器实例"""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = JobScheduler()
    return _scheduler_instance
