@echo off
chcp 65001 >nul
title 多平台电商管理系统

echo.
echo ====================================
echo    多平台电商管理系统
echo    基于 PyQt6 的现代化暗黑主题UI
echo ====================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo 正在检查依赖包...
python -c "import PyQt6" >nul 2>&1
if errorlevel 1 (
    echo 警告: PyQt6未安装，正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 正在启动程序...
echo.
python main.py

if errorlevel 1 (
    echo.
    echo 程序运行出现错误，请检查日志文件
    pause
) 