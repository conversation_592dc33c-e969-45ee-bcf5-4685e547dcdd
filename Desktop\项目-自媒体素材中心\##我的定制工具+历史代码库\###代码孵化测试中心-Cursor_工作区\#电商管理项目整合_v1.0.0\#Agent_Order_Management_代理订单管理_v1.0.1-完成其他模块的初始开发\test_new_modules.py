#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新模块功能测试
测试Ali1688AutoERP新完成的核心模块
"""

import sys
import os
from pathlib import Path


def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")

    try:
        # API模块
        from api.ali1688_client import Ali1688Client
        from api.api_utils import APIException, ResponseParser

        print("   ✅ API客户端模块导入成功")

        # 定时任务模块
        from scheduler.job_scheduler import JobScheduler, get_scheduler

        print("   ✅ 定时任务调度器模块导入成功")

        # 工具模块
        from utils.logger import setup_logger, get_logger
        from utils.file_utils import FileManager
        from utils.data_validator import DataValidator
        from utils.encryption import EncryptionManager, get_encryption_manager

        print("   ✅ 工具模块导入成功")

        return True
    except Exception as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False


def test_file_manager():
    """测试文件管理器"""
    print("🔍 测试文件管理器...")

    try:
        from utils.file_utils import FileManager

        fm = FileManager()

        # 测试JSON读写
        test_data = {
            "name": "Ali1688AutoERP",
            "version": "2.0.0",
            "modules": ["api", "scheduler", "utils"],
            "config": {"debug": True, "max_retries": 3},
        }

        # 写入测试
        if fm.write_json("temp/test_fm.json", test_data):
            print("   ✅ JSON文件写入成功")
        else:
            print("   ❌ JSON文件写入失败")
            return False

        # 读取测试
        read_data = fm.read_json("temp/test_fm.json")
        if read_data == test_data:
            print("   ✅ JSON文件读取成功")
        else:
            print("   ❌ JSON文件读取数据不匹配")
            return False

        # 测试文件备份
        backup_path = fm.backup_file("temp/test_fm.json")
        if backup_path and backup_path.exists():
            print("   ✅ 文件备份成功")
        else:
            print("   ❌ 文件备份失败")
            return False

        return True
    except Exception as e:
        print(f"   ❌ 文件管理器测试失败: {e}")
        return False


def test_encryption():
    """测试加密管理器"""
    print("🔍 测试加密管理器...")

    try:
        from utils.encryption import EncryptionManager, get_encryption_manager

        # 测试基本加解密
        em = get_encryption_manager()

        test_texts = [
            "Hello, Ali1688AutoERP!",
            "这是中文测试内容",
            "API密钥: sk-1234567890abcdef",
            '{"access_token": "test_token", "refresh_token": "refresh_test"}',
        ]

        for text in test_texts:
            encrypted = em.encrypt(text)
            decrypted = em.decrypt(encrypted)

            if decrypted == text:
                print(f"   ✅ 加解密测试通过: {text[:20]}...")
            else:
                print(f"   ❌ 加解密测试失败: {text[:20]}...")
                return False

        # 测试字典加密
        sensitive_data = {
            "app_key": "test_app_key_12345",
            "app_secret": "test_secret_67890",
            "access_token": "at_test_token_abcdef",
            "refresh_token": "rt_test_token_fedcba",
        }

        encrypted_dict = em.encrypt_dict(
            sensitive_data, ["app_secret", "access_token", "refresh_token"]
        )
        decrypted_dict = em.decrypt_dict(
            encrypted_dict, ["app_secret", "access_token", "refresh_token"]
        )

        if decrypted_dict == sensitive_data:
            print("   ✅ 字典加解密测试通过")
        else:
            print("   ❌ 字典加解密测试失败")
            return False

        # 测试密码哈希
        password = "test_password_123"
        hashed, salt = em.hash_password(password)

        if em.verify_password(password, hashed, salt):
            print("   ✅ 密码哈希验证通过")
        else:
            print("   ❌ 密码哈希验证失败")
            return False

        return True
    except Exception as e:
        print(f"   ❌ 加密管理器测试失败: {e}")
        return False


def test_data_validator():
    """测试数据验证器"""
    print("🔍 测试数据验证器...")

    try:
        from utils.data_validator import DataValidator, ValidationError

        validator = DataValidator()

        # 测试邮箱验证
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]
        invalid_emails = ["invalid-email", "@domain.com", "user@", "user@domain"]

        for email in valid_emails:
            try:
                result = validator.validate_email(email)
                print(f"   ✅ 邮箱验证通过: {email}")
            except ValidationError:
                print(f"   ❌ 邮箱验证失败: {email}")
                return False

        for email in invalid_emails:
            try:
                validator.validate_email(email)
                print(f"   ❌ 邮箱验证应该失败但通过了: {email}")
                return False
            except ValidationError:
                print(f"   ✅ 邮箱验证正确拒绝: {email}")

        # 测试整数验证
        try:
            result = validator.validate_integer("123", min_value=1, max_value=1000)
            if result == 123:
                print("   ✅ 整数验证通过")
            else:
                print("   ❌ 整数验证结果错误")
                return False
        except ValidationError as e:
            print(f"   ❌ 整数验证失败: {e}")
            return False

        # 测试字符串清理
        dirty_string = "  Hello    World  \n\t  "
        clean_string = validator.clean_string(dirty_string)
        expected = "Hello World"

        if clean_string == expected:
            print("   ✅ 字符串清理通过")
        else:
            print(f"   ❌ 字符串清理失败: '{clean_string}' != '{expected}'")
            return False

        return True
    except Exception as e:
        print(f"   ❌ 数据验证器测试失败: {e}")
        return False


def test_logger():
    """测试日志系统"""
    print("🔍 测试日志系统...")

    try:
        from utils.logger import setup_logger, get_logger

        # 设置日志系统
        setup_logger(
            log_level="INFO", enable_file_logging=True, enable_console_logging=False
        )

        # 获取日志器
        logger = get_logger("test_module")

        # 记录不同级别的日志
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告日志")
        logger.error("这是一条错误日志")

        # 检查日志文件是否创建
        log_dir = Path("logs")
        if log_dir.exists():
            log_files = list(log_dir.glob("*.log"))
            if log_files:
                print(f"   ✅ 日志文件创建成功: {len(log_files)} 个文件")
            else:
                print("   ❌ 日志文件未创建")
                return False
        else:
            print("   ❌ 日志目录未创建")
            return False

        return True
    except Exception as e:
        print(f"   ❌ 日志系统测试失败: {e}")
        return False


def test_api_client():
    """测试API客户端"""
    print("🔍 测试API客户端...")

    try:
        from api.ali1688_client import Ali1688Client
        from api.api_utils import APIException, ResponseParser

        # 创建客户端实例
        client = Ali1688Client()

        # 测试配置获取
        config = client.get_platform_config()
        if config and "app_key" in config:
            print("   ✅ API客户端配置获取成功")
        else:
            print("   ❌ API客户端配置获取失败")
            return False

        # 测试签名生成
        test_params = {
            "method": "alibaba.product.get",
            "app_key": "test_key",
            "timestamp": "2024-01-01 12:00:00",
            "v": "1.0",
        }

        signature = client.generate_signature(test_params)
        if signature and len(signature) == 32:  # MD5哈希长度
            print("   ✅ API签名生成成功")
        else:
            print("   ❌ API签名生成失败")
            return False

        # 测试响应解析器
        test_response = {
            "success": True,
            "data": {"product_id": "123", "name": "Test Product"},
            "message": "Success",
        }

        parsed = ResponseParser.parse_1688_response(test_response)
        if parsed == test_response:
            print("   ✅ 响应解析测试通过")
        else:
            print("   ❌ 响应解析测试失败")
            return False

        return True
    except Exception as e:
        print(f"   ❌ API客户端测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 Ali1688AutoERP 新模块功能测试")
    print("=" * 50)

    tests = [
        ("模块导入", test_imports),
        ("文件管理器", test_file_manager),
        ("加密管理器", test_encryption),
        ("数据验证器", test_data_validator),
        ("日志系统", test_logger),
        ("API客户端", test_api_client),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 测试 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有新模块测试通过！")
        print("✨ Ali1688AutoERP 核心功能模块已成功实现")
        print("\n🔧 已完成的核心模块:")
        print("   • API客户端 (1688专用)")
        print("   • 定时任务调度器")
        print("   • 文件管理器")
        print("   • 加密管理器")
        print("   • 数据验证器")
        print("   • 增强日志系统")
        print("\n💡 现在可以继续完善UI界面和业务逻辑了！")
        return True
    else:
        print(f"⚠️  还有 {total - passed} 个模块需要修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
