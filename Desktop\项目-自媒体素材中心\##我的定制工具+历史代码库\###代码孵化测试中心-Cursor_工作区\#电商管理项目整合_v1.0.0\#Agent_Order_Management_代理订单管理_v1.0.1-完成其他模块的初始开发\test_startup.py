#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动测试脚本
验证程序的基本功能是否正常
"""

import sys
import os


def test_imports():
    """测试所有必要的导入"""
    print("正在测试导入...")

    try:
        # 测试 PyQt6
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QAction

        print("✓ PyQt6 导入成功")

        # 测试项目模块
        from config.settings import APP_CONFIG, API_CONFIGS

        print("✓ 配置模块导入成功")

        from core.database import DatabaseManager

        print("✓ 数据库模块导入成功")

        from ui.main_window import MainWindow

        print("✓ 主窗口模块导入成功")

        from ui.widgets.dashboard_widget import DashboardWidget

        print("✓ 仪表板组件导入成功")

        return True

    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_database():
    """测试数据库功能"""
    print("\n正在测试数据库...")

    try:
        from core.database import db_manager

        # 测试数据库连接
        tables = db_manager.get_all_tables()
        print(f"✓ 数据库连接成功，共有 {len(tables)} 个表")

        # 显示表名
        for table in tables:
            print(f"  - {table}")

        return True

    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")
        return False


def test_config():
    """测试配置"""
    print("\n正在测试配置...")

    try:
        from config.settings import APP_CONFIG, API_CONFIGS, UI_CONFIG

        print(f"✓ 应用名称: {APP_CONFIG['app_name']}")
        print(f"✓ 版本: {APP_CONFIG['version']}")
        print(f"✓ 支持的平台: {list(API_CONFIGS.keys())}")
        print(f"✓ UI主题: {UI_CONFIG['theme']}")

        return True

    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 50)
    print("多平台电商管理系统 - 启动测试")
    print("=" * 50)

    tests = [
        ("导入测试", test_imports),
        ("数据库测试", test_database),
        ("配置测试", test_config),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！程序可以正常启动。")
        print("\n启动命令:")
        print("  python main.py")
        print("  或双击 start.bat")
    else:
        print("❌ 部分测试失败，请检查错误信息。")

    print("=" * 50)


if __name__ == "__main__":
    main()
