# -*- coding: utf-8 -*-
"""
主窗口模块
包含应用程序的主界面和核心功能区域
"""

from PyQt6.QtWidgets import (
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTabWidget,
    QMenuBar,
    QToolBar,
    QStatusBar,
    QLabel,
    QSplitter,
    QFrame,
    QMessageBox,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap, QAction

from ui.widgets.dashboard_widget import DashboardWidget
from ui.widgets.order_management_widget import OrderManagementWidget
from ui.widgets.product_management_widget import ProductManagementWidget
from ui.widgets.inventory_management_widget import InventoryManagementWidget
from ui.widgets.dropship_management_widget import DropshipManagementWidget
from ui.widgets.settings_widget import SettingsWidget
from config.settings import APP_CONFIG, UI_CONFIG


class MainWindow(QMainWindow):
    """主窗口类"""

    # 信号定义
    status_message = pyqtSignal(str)

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_connections()
        self.init_timers()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"{APP_CONFIG['app_name']} v{APP_CONFIG['version']}")
        self.setMinimumSize(
            UI_CONFIG["window_size"]["min_width"],
            UI_CONFIG["window_size"]["min_height"],
        )
        self.resize(
            UI_CONFIG["window_size"]["width"], UI_CONFIG["window_size"]["height"]
        )

        # 设置窗口居中
        self.center_window()

        # 创建菜单栏
        self.create_menu_bar()

        # 创建工具栏
        self.create_tool_bar()

        # 创建状态栏
        self.create_status_bar()

        # 创建中央窗口
        self.create_central_widget()

    def center_window(self):
        """将窗口居中显示"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        center_point = screen.center()
        window.moveCenter(center_point)
        self.move(window.topLeft())

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        # 导入数据
        import_action = QAction("导入数据", self)
        import_action.setShortcut("Ctrl+I")
        import_action.setStatusTip("导入订单或商品数据")
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)

        # 导出数据
        export_action = QAction("导出数据", self)
        export_action.setShortcut("Ctrl+E")
        export_action.setStatusTip("导出订单或商品数据")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑")

        # 设置
        settings_action = QAction("设置", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.setStatusTip("打开设置界面")
        settings_action.triggered.connect(self.open_settings)
        edit_menu.addAction(settings_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图")

        # 刷新
        refresh_action = QAction("刷新", self)
        refresh_action.setShortcut("F5")
        refresh_action.setStatusTip("刷新当前数据")
        refresh_action.triggered.connect(self.refresh_data)
        view_menu.addAction(refresh_action)

        # 全屏
        fullscreen_action = QAction("全屏", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.setStatusTip("切换全屏模式")
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具")

        # API测试
        api_test_action = QAction("API连接测试", self)
        api_test_action.setStatusTip("测试各平台API连接状态")
        api_test_action.triggered.connect(self.test_api_connections)
        tools_menu.addAction(api_test_action)

        # 数据同步
        sync_action = QAction("数据同步", self)
        sync_action.setShortcut("Ctrl+S")
        sync_action.setStatusTip("手动同步所有平台数据")
        sync_action.triggered.connect(self.sync_all_data)
        tools_menu.addAction(sync_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")

        # 关于
        about_action = QAction("关于", self)
        about_action.setStatusTip("关于本应用程序")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)

        # 刷新按钮
        refresh_action = QAction("刷新", self)
        refresh_action.setStatusTip("刷新当前数据")
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # 同步按钮
        sync_action = QAction("同步", self)
        sync_action.setStatusTip("同步所有平台数据")
        sync_action.triggered.connect(self.sync_all_data)
        toolbar.addAction(sync_action)

        toolbar.addSeparator()

        # 设置按钮
        settings_action = QAction("设置", self)
        settings_action.setStatusTip("打开设置界面")
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 连接状态标签
        self.connection_label = QLabel("API连接: 未连接")
        self.status_bar.addPermanentWidget(self.connection_label)

        # 时间标签
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)

        # 连接状态消息信号
        self.status_message.connect(self.update_status_message)

    def create_central_widget(self):
        """创建中央窗口"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 创建各个功能页面
        self.create_tabs()

    def create_tabs(self):
        """创建选项卡页面"""
        # 仪表板
        self.dashboard_widget = DashboardWidget()
        self.tab_widget.addTab(self.dashboard_widget, "仪表板")

        # 订单管理
        self.order_widget = OrderManagementWidget()
        self.tab_widget.addTab(self.order_widget, "订单管理")

        # 商品管理
        self.product_widget = ProductManagementWidget()
        self.tab_widget.addTab(self.product_widget, "商品管理")

        # 库存管理
        self.inventory_widget = InventoryManagementWidget()
        self.tab_widget.addTab(self.inventory_widget, "库存管理")

        # 代发管理
        self.dropship_widget = DropshipManagementWidget()
        self.tab_widget.addTab(self.dropship_widget, "代发管理")

        # 设置
        self.settings_widget = SettingsWidget()
        self.tab_widget.addTab(self.settings_widget, "设置")

    def init_connections(self):
        """初始化信号连接"""
        # 选项卡切换
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def init_timers(self):
        """初始化定时器"""
        # 状态栏时间更新定时器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # 每秒更新一次

        # 数据刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh_data)
        self.refresh_timer.start(UI_CONFIG["refresh_interval"] * 1000)

    def update_time(self):
        """更新状态栏时间"""
        from datetime import datetime

        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def update_status_message(self, message):
        """更新状态栏消息"""
        self.status_label.setText(message)

    def on_tab_changed(self, index):
        """选项卡切换事件"""
        tab_names = ["仪表板", "订单管理", "商品管理", "库存管理", "代发管理", "设置"]
        if 0 <= index < len(tab_names):
            self.status_message.emit(f"切换到: {tab_names[index]}")

    def import_data(self):
        """导入数据"""
        self.status_message.emit("导入数据功能开发中...")

    def export_data(self):
        """导出数据"""
        self.status_message.emit("导出数据功能开发中...")

    def open_settings(self):
        """打开设置界面"""
        self.tab_widget.setCurrentWidget(self.settings_widget)

    def refresh_data(self):
        """刷新数据"""
        self.status_message.emit("正在刷新数据...")
        # 刷新当前选项卡的数据
        current_widget = self.tab_widget.currentWidget()
        if hasattr(current_widget, "refresh_data"):
            current_widget.refresh_data()
        self.status_message.emit("数据刷新完成")

    def auto_refresh_data(self):
        """自动刷新数据"""
        # 在后台自动刷新数据，不显示状态消息
        current_widget = self.tab_widget.currentWidget()
        if hasattr(current_widget, "auto_refresh_data"):
            current_widget.auto_refresh_data()

    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def test_api_connections(self):
        """测试API连接"""
        self.status_message.emit("正在测试API连接...")
        # 这里将实现API连接测试逻辑
        self.status_message.emit("API连接测试完成")

    def sync_all_data(self):
        """同步所有数据"""
        self.status_message.emit("正在同步所有平台数据...")
        # 这里将实现数据同步逻辑
        self.status_message.emit("数据同步完成")

    def show_about(self):
        """显示关于对话框"""
        about_text = f"""
        <h2>{APP_CONFIG['app_name']}</h2>
        <p>版本: {APP_CONFIG['version']}</p>
        <p>{APP_CONFIG['description']}</p>
        <p>支持平台: 淘宝、小红书、抖音小店、1688等</p>
        <p>开发者: {APP_CONFIG['organization']}</p>
        """

        QMessageBox.about(self, "关于", about_text)

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出应用程序吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 保存设置和数据
            self.save_settings()
            event.accept()
        else:
            event.ignore()

    def save_settings(self):
        """保存设置"""
        # 这里将实现设置保存逻辑
        pass
