/* 
多平台电商管理系统 - 暗黑主题样式
现代化暗黑UI设计
*/

/* 全局样式 */
* {
    color: #E0E0E0;
    font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
}

/* 主窗口 */
QMainWindow {
    background-color: #1E1E1E;
    color: #E0E0E0;
}

/* 中央窗口 */
QWidget {
    background-color: #1E1E1E;
    color: #E0E0E0;
    border: none;
}

/* 菜单栏 */
QMenuBar {
    background-color: #2D2D2D;
    color: #E0E0E0;
    border-bottom: 1px solid #404040;
    padding: 4px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 6px 12px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #404040;
}

QMenuBar::item:pressed {
    background-color: #505050;
}

/* 菜单 */
QMenu {
    background-color: #2D2D2D;
    color: #E0E0E0;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 4px;
}

QMenu::item {
    padding: 8px 16px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #404040;
}

QMenu::separator {
    height: 1px;
    background-color: #404040;
    margin: 4px 0px;
}

/* 工具栏 */
QToolBar {
    background-color: #2D2D2D;
    border: none;
    spacing: 4px;
    padding: 4px;
}

QToolBar::separator {
    background-color: #404040;
    width: 1px;
    margin: 4px 2px;
}

/* 状态栏 */
QStatusBar {
    background-color: #2D2D2D;
    color: #E0E0E0;
    border-top: 1px solid #404040;
    padding: 4px;
}

/* 按钮 */
QPushButton {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #505050;
    border-color: #606060;
}

QPushButton:pressed {
    background-color: #606060;
    border-color: #707070;
}

QPushButton:disabled {
    background-color: #2D2D2D;
    color: #808080;
    border-color: #404040;
}

/* 主要按钮样式 */
QPushButton[class="primary"] {
    background-color: #0078D4;
    border-color: #106EBE;
}

QPushButton[class="primary"]:hover {
    background-color: #106EBE;
    border-color: #005A9E;
}

QPushButton[class="primary"]:pressed {
    background-color: #005A9E;
}

/* 成功按钮样式 */
QPushButton[class="success"] {
    background-color: #107C10;
    border-color: #0E6E0E;
}

QPushButton[class="success"]:hover {
    background-color: #0E6E0E;
    border-color: #0C5E0C;
}

/* 危险按钮样式 */
QPushButton[class="danger"] {
    background-color: #D13438;
    border-color: #B92B2F;
}

QPushButton[class="danger"]:hover {
    background-color: #B92B2F;
    border-color: #A12226;
}

/* 标签 */
QLabel {
    color: #E0E0E0;
    background-color: transparent;
}

/* 输入框 */
QLineEdit {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
}

QLineEdit:focus {
    border-color: #0078D4;
    background-color: #505050;
}

QLineEdit:disabled {
    background-color: #2D2D2D;
    color: #808080;
    border-color: #404040;
}

/* 文本编辑器 */
QTextEdit, QPlainTextEdit {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #0078D4;
}

/* 组合框 */
QComboBox {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 4px;
    padding: 8px 12px;
    min-width: 120px;
}

QComboBox:hover {
    border-color: #606060;
}

QComboBox:focus {
    border-color: #0078D4;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI0UwRTBFMCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #2D2D2D;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 4px;
    selection-background-color: #404040;
}

/* 复选框 */
QCheckBox {
    color: #E0E0E0;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #505050;
    border-radius: 3px;
    background-color: #404040;
}

QCheckBox::indicator:hover {
    border-color: #606060;
}

QCheckBox::indicator:checked {
    background-color: #0078D4;
    border-color: #0078D4;
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=);
}

/* 单选按钮 */
QRadioButton {
    color: #E0E0E0;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border: 2px solid #505050;
    border-radius: 9px;
    background-color: #404040;
}

QRadioButton::indicator:hover {
    border-color: #606060;
}

QRadioButton::indicator:checked {
    background-color: #0078D4;
    border-color: #0078D4;
}

QRadioButton::indicator:checked::after {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    background-color: white;
    top: 3px;
    left: 3px;
}

/* 滑块 */
QSlider::groove:horizontal {
    border: none;
    height: 4px;
    background-color: #505050;
    border-radius: 2px;
}

QSlider::handle:horizontal {
    background-color: #0078D4;
    border: none;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -7px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #106EBE;
}

/* 进度条 */
QProgressBar {
    background-color: #505050;
    border: none;
    border-radius: 4px;
    text-align: center;
    color: #E0E0E0;
    font-weight: 500;
}

QProgressBar::chunk {
    background-color: #0078D4;
    border-radius: 4px;
}

/* 表格 */
QTableWidget, QTableView {
    background-color: #2D2D2D;
    alternate-background-color: #353535;
    color: #E0E0E0;
    gridline-color: #505050;
    selection-background-color: #404040;
    border: 1px solid #505050;
    border-radius: 4px;
}

QTableWidget::item, QTableView::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected, QTableView::item:selected {
    background-color: #404040;
}

QHeaderView::section {
    background-color: #404040;
    color: #E0E0E0;
    padding: 10px;
    border: none;
    border-right: 1px solid #505050;
    border-bottom: 1px solid #505050;
    font-weight: 600;
}

QHeaderView::section:hover {
    background-color: #505050;
}

/* 列表 */
QListWidget, QListView {
    background-color: #2D2D2D;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 4px;
    selection-background-color: #404040;
}

QListWidget::item, QListView::item {
    padding: 8px;
    border: none;
    border-bottom: 1px solid #353535;
}

QListWidget::item:selected, QListView::item:selected {
    background-color: #404040;
}

QListWidget::item:hover, QListView::item:hover {
    background-color: #353535;
}

/* 树形控件 */
QTreeWidget, QTreeView {
    background-color: #2D2D2D;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 4px;
    selection-background-color: #404040;
}

QTreeWidget::item, QTreeView::item {
    padding: 6px;
    border: none;
}

QTreeWidget::item:selected, QTreeView::item:selected {
    background-color: #404040;
}

QTreeWidget::item:hover, QTreeView::item:hover {
    background-color: #353535;
}

QTreeWidget::branch:closed:has-children {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgOCA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMiAxTDYgNEwyIDciIHN0cm9rZT0iI0UwRTBFMCIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=);
}

QTreeWidget::branch:open:has-children {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgOCA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMSAyTDQgNkw3IDIiIHN0cm9rZT0iI0UwRTBFMCIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=);
}

/* 选项卡 */
QTabWidget::pane {
    background-color: #2D2D2D;
    border: 1px solid #505050;
    border-radius: 4px;
    margin-top: -1px;
}

QTabBar::tab {
    background-color: #404040;
    color: #E0E0E0;
    padding: 10px 16px;
    margin-right: 2px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border: 1px solid #505050;
    border-bottom: none;
}

QTabBar::tab:selected {
    background-color: #2D2D2D;
    border-color: #505050;
    border-bottom: 1px solid #2D2D2D;
}

QTabBar::tab:hover:!selected {
    background-color: #505050;
}

/* 分组框 */
QGroupBox {
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 6px;
    margin-top: 12px;
    padding-top: 12px;
    font-weight: 600;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px;
    background-color: #1E1E1E;
}

/* 滚动条 */
QScrollBar:vertical {
    background-color: #2D2D2D;
    width: 12px;
    border-radius: 6px;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #505050;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #606060;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #2D2D2D;
    height: 12px;
    border-radius: 6px;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #505050;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #606060;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

/* 分割器 */
QSplitter::handle {
    background-color: #505050;
}

QSplitter::handle:horizontal {
    width: 2px;
}

QSplitter::handle:vertical {
    height: 2px;
}

QSplitter::handle:hover {
    background-color: #606060;
}

/* 对话框 */
QDialog {
    background-color: #1E1E1E;
    color: #E0E0E0;
}

/* 消息框 */
QMessageBox {
    background-color: #1E1E1E;
    color: #E0E0E0;
}

QMessageBox QPushButton {
    min-width: 80px;
    padding: 6px 16px;
}

/* 工具提示 */
QToolTip {
    background-color: #404040;
    color: #E0E0E0;
    border: 1px solid #505050;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 12px;
} 