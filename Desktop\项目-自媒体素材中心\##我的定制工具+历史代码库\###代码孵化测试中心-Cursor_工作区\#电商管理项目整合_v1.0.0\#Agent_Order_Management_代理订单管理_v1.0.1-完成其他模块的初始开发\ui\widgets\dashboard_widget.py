# -*- coding: utf-8 -*-
"""
仪表板组件
显示系统概览信息和关键指标
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QFrame,
    QPushButton,
    QProgressBar,
    QScrollArea,
    QGroupBox,
    QSizePolicy,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPalette


class StatCard(QFrame):
    """统计卡片组件"""

    def __init__(
        self, title: str, value: str, subtitle: str = "", color: str = "#0078D4"
    ):
        super().__init__()
        self.init_ui(title, value, subtitle, color)

    def init_ui(self, title: str, value: str, subtitle: str, color: str):
        """初始化UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(
            f"""
            QFrame {{
                border: 1px solid #505050;
                border-radius: 8px;
                background-color: #2D2D2D;
                padding: 16px;
            }}
            QFrame:hover {{
                border-color: {color};
                background-color: #353535;
            }}
        """
        )

        layout = QVBoxLayout(self)
        layout.setSpacing(8)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #B0B0B0; font-size: 12px;")
        layout.addWidget(title_label)

        # 数值
        value_label = QLabel(value)
        value_font = QFont()
        value_font.setPointSize(24)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)

        # 副标题
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("color: #808080; font-size: 11px;")
            layout.addWidget(subtitle_label)

        self.value_label = value_label

    def update_value(self, new_value: str):
        """更新数值"""
        self.value_label.setText(new_value)


class PlatformStatusCard(QFrame):
    """平台状态卡片"""

    def __init__(self, platform_name: str, status: str = "未连接"):
        super().__init__()
        self.platform_name = platform_name
        self.init_ui(status)

    def init_ui(self, status: str):
        """初始化UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(
            """
            QFrame {
                border: 1px solid #505050;
                border-radius: 6px;
                background-color: #2D2D2D;
                padding: 12px;
            }
        """
        )

        layout = QHBoxLayout(self)
        layout.setSpacing(12)

        # 平台名称
        name_label = QLabel(self.platform_name)
        name_label.setStyleSheet("color: #E0E0E0; font-weight: 500;")
        layout.addWidget(name_label)

        layout.addStretch()

        # 状态指示器
        self.status_indicator = QLabel("●")
        self.status_label = QLabel(status)

        self.update_status(status)

        layout.addWidget(self.status_indicator)
        layout.addWidget(self.status_label)

    def update_status(self, status: str):
        """更新状态"""
        if status == "已连接":
            color = "#107C10"
        elif status == "连接中":
            color = "#FF8C00"
        else:
            color = "#D13438"

        self.status_indicator.setStyleSheet(f"color: {color}; font-size: 16px;")
        self.status_label.setText(status)
        self.status_label.setStyleSheet(f"color: {color}; font-size: 12px;")


class DashboardWidget(QWidget):
    """仪表板主组件"""

    # 信号定义
    refresh_requested = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_data()
        self.init_timers()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 滚动内容
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(20)

        # 欢迎区域
        self.create_welcome_section(scroll_layout)

        # 统计卡片区域
        self.create_stats_section(scroll_layout)

        # 平台状态区域
        self.create_platform_status_section(scroll_layout)

        # 快速操作区域
        self.create_quick_actions_section(scroll_layout)

        # 最近活动区域
        self.create_recent_activity_section(scroll_layout)

        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

    def create_welcome_section(self, parent_layout):
        """创建欢迎区域"""
        welcome_frame = QFrame()
        welcome_frame.setStyleSheet(
            """
            QFrame {
                background-color: #2D2D2D;
                border-radius: 8px;
                padding: 20px;
            }
        """
        )

        layout = QVBoxLayout(welcome_frame)

        # 欢迎标题
        welcome_title = QLabel("欢迎使用多平台电商管理系统")
        welcome_font = QFont()
        welcome_font.setPointSize(18)
        welcome_font.setBold(True)
        welcome_title.setFont(welcome_font)
        welcome_title.setStyleSheet("color: #E0E0E0;")
        layout.addWidget(welcome_title)

        # 欢迎描述
        welcome_desc = QLabel("统一管理您的淘宝、小红书、抖音小店等多平台订单和商品")
        welcome_desc.setStyleSheet("color: #B0B0B0; font-size: 14px; margin-top: 8px;")
        layout.addWidget(welcome_desc)

        parent_layout.addWidget(welcome_frame)

    def create_stats_section(self, parent_layout):
        """创建统计区域"""
        stats_group = QGroupBox("数据概览")
        stats_group.setStyleSheet(
            """
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #E0E0E0;
                border: 1px solid #505050;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                background-color: #1E1E1E;
            }
        """
        )

        stats_layout = QGridLayout(stats_group)
        stats_layout.setSpacing(16)

        # 创建统计卡片
        self.total_orders_card = StatCard("总订单数", "0", "今日新增: 0", "#0078D4")
        self.total_products_card = StatCard("商品总数", "0", "在售商品: 0", "#107C10")
        self.total_revenue_card = StatCard("总收入", "¥0", "今日收入: ¥0", "#FF8C00")
        self.pending_orders_card = StatCard("待处理订单", "0", "需要关注", "#D13438")

        stats_layout.addWidget(self.total_orders_card, 0, 0)
        stats_layout.addWidget(self.total_products_card, 0, 1)
        stats_layout.addWidget(self.total_revenue_card, 1, 0)
        stats_layout.addWidget(self.pending_orders_card, 1, 1)

        parent_layout.addWidget(stats_group)

    def create_platform_status_section(self, parent_layout):
        """创建平台状态区域"""
        platform_group = QGroupBox("平台连接状态")
        platform_group.setStyleSheet(
            """
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #E0E0E0;
                border: 1px solid #505050;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                background-color: #1E1E1E;
            }
        """
        )

        platform_layout = QGridLayout(platform_group)
        platform_layout.setSpacing(12)

        # 平台状态卡片
        self.taobao_status = PlatformStatusCard("淘宝", "未连接")
        self.xiaohongshu_status = PlatformStatusCard("小红书", "未连接")
        self.douyin_status = PlatformStatusCard("抖音小店", "未连接")
        self.alibaba_status = PlatformStatusCard("1688", "未连接")

        platform_layout.addWidget(self.taobao_status, 0, 0)
        platform_layout.addWidget(self.xiaohongshu_status, 0, 1)
        platform_layout.addWidget(self.douyin_status, 1, 0)
        platform_layout.addWidget(self.alibaba_status, 1, 1)

        parent_layout.addWidget(platform_group)

    def create_quick_actions_section(self, parent_layout):
        """创建快速操作区域"""
        actions_group = QGroupBox("快速操作")
        actions_group.setStyleSheet(
            """
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #E0E0E0;
                border: 1px solid #505050;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                background-color: #1E1E1E;
            }
        """
        )

        actions_layout = QHBoxLayout(actions_group)
        actions_layout.setSpacing(12)

        # 快速操作按钮
        sync_btn = QPushButton("同步数据")
        sync_btn.setProperty("class", "primary")
        sync_btn.clicked.connect(self.sync_data)

        import_btn = QPushButton("导入商品")
        import_btn.clicked.connect(self.import_products)

        export_btn = QPushButton("导出订单")
        export_btn.clicked.connect(self.export_orders)

        settings_btn = QPushButton("系统设置")
        settings_btn.clicked.connect(self.open_settings)

        actions_layout.addWidget(sync_btn)
        actions_layout.addWidget(import_btn)
        actions_layout.addWidget(export_btn)
        actions_layout.addWidget(settings_btn)
        actions_layout.addStretch()

        parent_layout.addWidget(actions_group)

    def create_recent_activity_section(self, parent_layout):
        """创建最近活动区域"""
        activity_group = QGroupBox("最近活动")
        activity_group.setStyleSheet(
            """
            QGroupBox {
                font-weight: 600;
                font-size: 14px;
                color: #E0E0E0;
                border: 1px solid #505050;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                background-color: #1E1E1E;
            }
        """
        )

        activity_layout = QVBoxLayout(activity_group)

        # 活动列表
        self.activity_label = QLabel("暂无最近活动")
        self.activity_label.setStyleSheet(
            "color: #808080; padding: 20px; text-align: center;"
        )
        self.activity_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        activity_layout.addWidget(self.activity_label)

        parent_layout.addWidget(activity_group)

    def init_data(self):
        """初始化数据"""
        # 这里可以从数据库加载实际数据
        self.update_stats()
        self.update_platform_status()

    def init_timers(self):
        """初始化定时器"""
        # 定期更新数据
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.auto_refresh_data)
        self.update_timer.start(60000)  # 每分钟更新一次

    def update_stats(self):
        """更新统计数据"""
        # 这里应该从数据库获取真实数据
        # 目前使用模拟数据
        self.total_orders_card.update_value("156")
        self.total_products_card.update_value("89")
        self.total_revenue_card.update_value("¥12,345")
        self.pending_orders_card.update_value("3")

    def update_platform_status(self):
        """更新平台状态"""
        # 这里应该检查实际的API连接状态
        # 目前使用模拟状态
        platforms = [
            (self.taobao_status, "未连接"),
            (self.xiaohongshu_status, "未连接"),
            (self.douyin_status, "未连接"),
            (self.alibaba_status, "未连接"),
        ]

        for status_card, status in platforms:
            status_card.update_status(status)

    def refresh_data(self):
        """刷新数据"""
        self.update_stats()
        self.update_platform_status()

    def auto_refresh_data(self):
        """自动刷新数据"""
        self.refresh_data()

    def sync_data(self):
        """同步数据"""
        # 发射信号通知主窗口执行同步
        self.refresh_requested.emit()

    def import_products(self):
        """导入商品"""
        # 这里实现商品导入逻辑
        pass

    def export_orders(self):
        """导出订单"""
        # 这里实现订单导出逻辑
        pass

    def open_settings(self):
        """打开设置"""
        # 这里实现打开设置的逻辑
        pass
