# -*- coding: utf-8 -*-
"""
代发管理组件
用于管理代发订单和供应商
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QPushButton,
    QLineEdit,
    QComboBox,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QFrame,
    QGroupBox,
    QDoubleSpinBox,
    QSpinBox,
    QTextEdit,
    QDialog,
    QDialogButtonBox,
    QMessageBox,
    QProgressBar,
    QTabWidget,
    QScrollArea,
    QCheckBox,
    QDateEdit,
    QSplitter,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from core.database import DatabaseManager
from api.ali1688_client import Ali1688Client
from utils.logger import get_logger

logger = get_logger(__name__)


class DropshipOrderDialog(QDialog):
    """代发订单对话框"""

    def __init__(self, order_data: Dict[str, Any] = None, parent=None):
        super().__init__(parent)
        self.order_data = order_data or {}
        self.is_edit_mode = bool(order_data)
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        title = "编辑代发订单" if self.is_edit_mode else "创建代发订单"
        self.setWindowTitle(title)
        self.setMinimumSize(600, 500)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # 创建标签页
        tab_widget = QTabWidget()

        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        tab_widget.addTab(basic_tab, "基本信息")

        # 商品信息标签页
        product_tab = self.create_product_info_tab()
        tab_widget.addTab(product_tab, "商品信息")

        # 价格信息标签页
        price_tab = self.create_price_info_tab()
        tab_widget.addTab(price_tab, "价格信息")

        layout.addWidget(tab_widget)

        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        form_layout = QGridLayout()

        # 客户订单号
        form_layout.addWidget(QLabel("客户订单号:"), 0, 0)
        self.customer_order_id_edit = QLineEdit()
        self.customer_order_id_edit.setText(
            self.order_data.get("customer_order_id", "")
        )
        self.customer_order_id_edit.setPlaceholderText("输入客户订单号")
        form_layout.addWidget(self.customer_order_id_edit, 0, 1)

        # 供应商
        form_layout.addWidget(QLabel("供应商:"), 0, 2)
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItems(["阿里巴巴", "1688", "拼多多", "淘宝", "其他"])
        if self.order_data.get("supplier_name"):
            index = self.supplier_combo.findText(self.order_data["supplier_name"])
            if index >= 0:
                self.supplier_combo.setCurrentIndex(index)
        form_layout.addWidget(self.supplier_combo, 0, 3)

        # 供应商订单号
        form_layout.addWidget(QLabel("供应商订单号:"), 1, 0)
        self.supplier_order_id_edit = QLineEdit()
        self.supplier_order_id_edit.setText(
            self.order_data.get("supplier_order_id", "")
        )
        self.supplier_order_id_edit.setPlaceholderText("输入供应商订单号")
        form_layout.addWidget(self.supplier_order_id_edit, 1, 1)

        # 订单状态
        form_layout.addWidget(QLabel("订单状态:"), 1, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["待下单", "已下单", "已发货", "已完成", "已取消"])
        if self.order_data.get("status"):
            index = self.status_combo.findText(self.order_data["status"])
            if index >= 0:
                self.status_combo.setCurrentIndex(index)
        form_layout.addWidget(self.status_combo, 1, 3)

        # 下单时间
        form_layout.addWidget(QLabel("下单时间:"), 2, 0)
        self.order_date = QDateEdit()
        if self.order_data.get("order_date"):
            self.order_date.setDate(
                QDate.fromString(self.order_data["order_date"], "yyyy-MM-dd")
            )
        else:
            self.order_date.setDate(QDate.currentDate())
        self.order_date.setCalendarPopup(True)
        form_layout.addWidget(self.order_date, 2, 1)

        layout.addLayout(form_layout)

        # 收货地址
        layout.addWidget(QLabel("收货地址:"))
        self.shipping_address_edit = QTextEdit()
        self.shipping_address_edit.setPlainText(
            self.order_data.get("shipping_address", "")
        )
        self.shipping_address_edit.setMaximumHeight(80)
        layout.addWidget(self.shipping_address_edit)

        layout.addStretch()
        return widget

    def create_product_info_tab(self) -> QWidget:
        """创建商品信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        form_layout = QGridLayout()

        # 商品名称
        form_layout.addWidget(QLabel("商品名称:"), 0, 0)
        self.product_name_edit = QLineEdit()
        self.product_name_edit.setText(self.order_data.get("product_name", ""))
        self.product_name_edit.setPlaceholderText("输入商品名称")
        form_layout.addWidget(self.product_name_edit, 0, 1, 1, 2)

        # 商品SKU
        form_layout.addWidget(QLabel("商品SKU:"), 1, 0)
        self.product_sku_edit = QLineEdit()
        self.product_sku_edit.setText(self.order_data.get("product_sku", ""))
        self.product_sku_edit.setPlaceholderText("输入商品SKU")
        form_layout.addWidget(self.product_sku_edit, 1, 1)

        # 数量
        form_layout.addWidget(QLabel("数量:"), 1, 2)
        self.quantity_spinbox = QSpinBox()
        self.quantity_spinbox.setRange(1, 9999)
        self.quantity_spinbox.setValue(int(self.order_data.get("quantity", 1)))
        form_layout.addWidget(self.quantity_spinbox, 1, 3)

        layout.addLayout(form_layout)

        # 商品规格
        layout.addWidget(QLabel("商品规格:"))
        self.product_specs_edit = QTextEdit()
        self.product_specs_edit.setPlainText(self.order_data.get("product_specs", ""))
        self.product_specs_edit.setPlaceholderText("输入商品规格信息...")
        self.product_specs_edit.setMaximumHeight(100)
        layout.addWidget(self.product_specs_edit)

        layout.addStretch()
        return widget

    def create_price_info_tab(self) -> QWidget:
        """创建价格信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        form_layout = QGridLayout()

        # 客户价格
        form_layout.addWidget(QLabel("客户价格:"), 0, 0)
        self.customer_price_spinbox = QDoubleSpinBox()
        self.customer_price_spinbox.setRange(0.01, 999999.99)
        self.customer_price_spinbox.setDecimals(2)
        self.customer_price_spinbox.setSuffix(" 元")
        self.customer_price_spinbox.setValue(
            float(self.order_data.get("customer_price", 0))
        )
        form_layout.addWidget(self.customer_price_spinbox, 0, 1)

        # 供应商价格
        form_layout.addWidget(QLabel("供应商价格:"), 0, 2)
        self.supplier_price_spinbox = QDoubleSpinBox()
        self.supplier_price_spinbox.setRange(0.01, 999999.99)
        self.supplier_price_spinbox.setDecimals(2)
        self.supplier_price_spinbox.setSuffix(" 元")
        self.supplier_price_spinbox.setValue(
            float(self.order_data.get("supplier_price", 0))
        )
        form_layout.addWidget(self.supplier_price_spinbox, 0, 3)

        # 运费
        form_layout.addWidget(QLabel("运费:"), 1, 0)
        self.shipping_fee_spinbox = QDoubleSpinBox()
        self.shipping_fee_spinbox.setRange(0.00, 999999.99)
        self.shipping_fee_spinbox.setDecimals(2)
        self.shipping_fee_spinbox.setSuffix(" 元")
        self.shipping_fee_spinbox.setValue(
            float(self.order_data.get("shipping_fee", 0))
        )
        form_layout.addWidget(self.shipping_fee_spinbox, 1, 1)

        # 其他费用
        form_layout.addWidget(QLabel("其他费用:"), 1, 2)
        self.other_fee_spinbox = QDoubleSpinBox()
        self.other_fee_spinbox.setRange(0.00, 999999.99)
        self.other_fee_spinbox.setDecimals(2)
        self.other_fee_spinbox.setSuffix(" 元")
        self.other_fee_spinbox.setValue(float(self.order_data.get("other_fee", 0)))
        form_layout.addWidget(self.other_fee_spinbox, 1, 3)

        layout.addLayout(form_layout)

        # 利润计算
        profit_group = QGroupBox("利润计算")
        profit_layout = QGridLayout(profit_group)

        self.total_cost_label = QLabel("总成本: ¥0.00")
        self.profit_label = QLabel("利润: ¥0.00")
        self.profit_margin_label = QLabel("利润率: 0.00%")

        for label in [
            self.total_cost_label,
            self.profit_label,
            self.profit_margin_label,
        ]:
            label.setStyleSheet("font-weight: bold; color: #0078D4;")

        profit_layout.addWidget(self.total_cost_label, 0, 0)
        profit_layout.addWidget(self.profit_label, 0, 1)
        profit_layout.addWidget(self.profit_margin_label, 0, 2)

        layout.addWidget(profit_group)

        # 连接信号以实时计算利润
        self.customer_price_spinbox.valueChanged.connect(self.calculate_profit)
        self.supplier_price_spinbox.valueChanged.connect(self.calculate_profit)
        self.shipping_fee_spinbox.valueChanged.connect(self.calculate_profit)
        self.other_fee_spinbox.valueChanged.connect(self.calculate_profit)
        self.quantity_spinbox.valueChanged.connect(self.calculate_profit)

        # 初始计算
        self.calculate_profit()

        layout.addStretch()
        return widget

    def calculate_profit(self):
        """计算利润"""
        quantity = self.quantity_spinbox.value()
        customer_price = self.customer_price_spinbox.value()
        supplier_price = self.supplier_price_spinbox.value()
        shipping_fee = self.shipping_fee_spinbox.value()
        other_fee = self.other_fee_spinbox.value()

        total_cost = (supplier_price * quantity) + shipping_fee + other_fee
        total_revenue = customer_price * quantity
        profit = total_revenue - total_cost
        profit_margin = (profit / total_revenue * 100) if total_revenue > 0 else 0

        self.total_cost_label.setText(f"总成本: ¥{total_cost:.2f}")
        self.profit_label.setText(f"利润: ¥{profit:.2f}")
        self.profit_margin_label.setText(f"利润率: {profit_margin:.2f}%")

        # 根据利润率设置颜色
        if profit_margin < 10:
            color = "#dc3545"  # 红色
        elif profit_margin < 20:
            color = "#ffc107"  # 黄色
        else:
            color = "#28a745"  # 绿色

        self.profit_label.setStyleSheet(f"font-weight: bold; color: {color};")
        self.profit_margin_label.setStyleSheet(f"font-weight: bold; color: {color};")

    def get_order_data(self) -> Dict[str, Any]:
        """获取订单数据"""
        return {
            "customer_order_id": self.customer_order_id_edit.text().strip(),
            "supplier_name": self.supplier_combo.currentText(),
            "supplier_order_id": self.supplier_order_id_edit.text().strip(),
            "status": self.status_combo.currentText(),
            "order_date": self.order_date.date().toString("yyyy-MM-dd"),
            "shipping_address": self.shipping_address_edit.toPlainText().strip(),
            "product_name": self.product_name_edit.text().strip(),
            "product_sku": self.product_sku_edit.text().strip(),
            "quantity": self.quantity_spinbox.value(),
            "product_specs": self.product_specs_edit.toPlainText().strip(),
            "customer_price": self.customer_price_spinbox.value(),
            "supplier_price": self.supplier_price_spinbox.value(),
            "shipping_fee": self.shipping_fee_spinbox.value(),
            "other_fee": self.other_fee_spinbox.value(),
        }


class DropshipSearchWidget(QFrame):
    """代发搜索组件"""

    search_requested = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(
            """
            QFrame {
                border: 1px solid #505050;
                border-radius: 8px;
                background-color: #2D2D2D;
                padding: 16px;
            }
        """
        )

        layout = QGridLayout(self)
        layout.setSpacing(12)

        # 搜索条件
        layout.addWidget(QLabel("客户订单号:"), 0, 0)
        self.customer_order_edit = QLineEdit()
        self.customer_order_edit.setPlaceholderText("输入客户订单号")
        layout.addWidget(self.customer_order_edit, 0, 1)

        layout.addWidget(QLabel("供应商:"), 0, 2)
        self.supplier_combo = QComboBox()
        self.supplier_combo.addItems(
            ["全部", "阿里巴巴", "1688", "拼多多", "淘宝", "其他"]
        )
        layout.addWidget(self.supplier_combo, 0, 3)

        layout.addWidget(QLabel("订单状态:"), 1, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(
            ["全部", "待下单", "已下单", "已发货", "已完成", "已取消"]
        )
        layout.addWidget(self.status_combo, 1, 1)

        layout.addWidget(QLabel("商品SKU:"), 1, 2)
        self.sku_edit = QLineEdit()
        self.sku_edit.setPlaceholderText("输入商品SKU")
        layout.addWidget(self.sku_edit, 1, 3)

        # 按钮
        button_layout = QHBoxLayout()

        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #0078D4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """
        )
        search_btn.clicked.connect(self.perform_search)
        button_layout.addWidget(search_btn)

        reset_btn = QPushButton("重置")
        reset_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """
        )
        reset_btn.clicked.connect(self.reset_search)
        button_layout.addWidget(reset_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout, 2, 0, 1, 4)

    def perform_search(self):
        """执行搜索"""
        search_params = {
            "customer_order_id": self.customer_order_edit.text().strip(),
            "supplier": (
                self.supplier_combo.currentText()
                if self.supplier_combo.currentText() != "全部"
                else ""
            ),
            "status": (
                self.status_combo.currentText()
                if self.status_combo.currentText() != "全部"
                else ""
            ),
            "sku": self.sku_edit.text().strip(),
        }
        self.search_requested.emit(search_params)

    def reset_search(self):
        """重置搜索条件"""
        self.customer_order_edit.clear()
        self.supplier_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.sku_edit.clear()


class DropshipManagementWidget(QWidget):
    """代发管理组件"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.ali_client = Ali1688Client()
        self.dropship_data = []
        self.init_ui()
        self.load_dropship_orders()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题区域
        title_layout = QHBoxLayout()
        title_label = QLabel("代发管理")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #E0E0E0;")
        title_layout.addWidget(title_label)

        # 操作按钮
        add_btn = QPushButton("创建代发订单")
        add_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )
        add_btn.clicked.connect(self.add_dropship_order)
        title_layout.addWidget(add_btn)

        refresh_btn = QPushButton("刷新")
        refresh_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """
        )
        refresh_btn.clicked.connect(self.refresh_data)
        title_layout.addWidget(refresh_btn)

        profit_report_btn = QPushButton("利润报表")
        profit_report_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """
        )
        profit_report_btn.clicked.connect(self.generate_profit_report)
        title_layout.addWidget(profit_report_btn)

        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # 搜索区域
        self.search_widget = DropshipSearchWidget()
        self.search_widget.search_requested.connect(self.search_dropship_orders)
        main_layout.addWidget(self.search_widget)

        # 统计信息
        stats_layout = QHBoxLayout()
        self.total_label = QLabel("总订单: 0")
        self.pending_label = QLabel("待下单: 0")
        self.processing_label = QLabel("处理中: 0")
        self.completed_label = QLabel("已完成: 0")
        self.profit_label = QLabel("总利润: ¥0.00")

        for label in [
            self.total_label,
            self.pending_label,
            self.processing_label,
            self.completed_label,
            self.profit_label,
        ]:
            label.setStyleSheet(
                """
                QLabel {
                    background-color: #2D2D2D;
                    border: 1px solid #505050;
                    border-radius: 4px;
                    padding: 8px 12px;
                    color: #E0E0E0;
                    font-weight: bold;
                }
            """
            )
            stats_layout.addWidget(label)

        stats_layout.addStretch()
        main_layout.addLayout(stats_layout)

        # 代发订单列表表格
        self.dropship_table = QTableWidget()
        self.dropship_table.setColumnCount(13)
        self.dropship_table.setHorizontalHeaderLabels(
            [
                "客户订单号",
                "供应商",
                "供应商订单号",
                "商品名称",
                "SKU",
                "数量",
                "客户价格",
                "供应商价格",
                "利润",
                "利润率",
                "状态",
                "操作",
                "ID",
            ]
        )

        # 设置表格样式
        self.dropship_table.setAlternatingRowColors(True)
        self.dropship_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.dropship_table.horizontalHeader().setStretchLastSection(True)
        self.dropship_table.setStyleSheet(
            """
            QTableWidget {
                background-color: #2D2D2D;
                alternate-background-color: #353535;
                gridline-color: #505050;
                color: #E0E0E0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #505050;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #E0E0E0;
                padding: 8px;
                border: 1px solid #505050;
                font-weight: bold;
            }
        """
        )

        # 隐藏ID列
        self.dropship_table.setColumnHidden(12, True)

        # 双击编辑订单
        self.dropship_table.doubleClicked.connect(self.edit_selected_order)

        main_layout.addWidget(self.dropship_table)

        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

    def load_dropship_orders(self):
        """加载代发订单数据"""
        try:
            # 从数据库加载代发订单
            query = """
                SELECT * FROM dropship_orders
                ORDER BY created_at DESC
                LIMIT 1000
            """
            self.dropship_data = self.db_manager.execute_query(query)
            self.update_dropship_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"加载代发订单数据失败: {e}")
            QMessageBox.warning(self, "错误", f"加载代发订单数据失败: {e}")

    def update_dropship_table(self):
        """更新代发订单表格"""
        self.dropship_table.setRowCount(len(self.dropship_data))

        for row, order in enumerate(self.dropship_data):
            # 客户订单号
            self.dropship_table.setItem(
                row, 0, QTableWidgetItem(str(order.get("customer_order_id", "")))
            )

            # 供应商
            self.dropship_table.setItem(
                row, 1, QTableWidgetItem(str(order.get("supplier_name", "")))
            )

            # 供应商订单号
            self.dropship_table.setItem(
                row, 2, QTableWidgetItem(str(order.get("supplier_order_id", "")))
            )

            # 商品名称
            product_name = str(order.get("product_name", ""))
            if len(product_name) > 20:
                product_name = product_name[:20] + "..."
            self.dropship_table.setItem(row, 3, QTableWidgetItem(product_name))

            # SKU
            self.dropship_table.setItem(
                row, 4, QTableWidgetItem(str(order.get("product_sku", "")))
            )

            # 数量
            self.dropship_table.setItem(
                row, 5, QTableWidgetItem(str(order.get("quantity", 0)))
            )

            # 客户价格
            customer_price = f"¥{float(order.get('customer_price', 0)):.2f}"
            self.dropship_table.setItem(row, 6, QTableWidgetItem(customer_price))

            # 供应商价格
            supplier_price = f"¥{float(order.get('supplier_price', 0)):.2f}"
            self.dropship_table.setItem(row, 7, QTableWidgetItem(supplier_price))

            # 计算利润
            quantity = int(order.get("quantity", 0))
            customer_total = float(order.get("customer_price", 0)) * quantity
            supplier_total = float(order.get("supplier_price", 0)) * quantity
            shipping_fee = float(order.get("shipping_fee", 0))
            other_fee = float(order.get("other_fee", 0))
            profit = customer_total - supplier_total - shipping_fee - other_fee

            # 利润
            profit_item = QTableWidgetItem(f"¥{profit:.2f}")
            if profit < 0:
                profit_item.setForeground(QColor("#dc3545"))  # 红色
            elif profit < customer_total * 0.1:
                profit_item.setForeground(QColor("#ffc107"))  # 黄色
            else:
                profit_item.setForeground(QColor("#28a745"))  # 绿色
            self.dropship_table.setItem(row, 8, profit_item)

            # 利润率
            profit_margin = (profit / customer_total * 100) if customer_total > 0 else 0
            margin_item = QTableWidgetItem(f"{profit_margin:.2f}%")
            if profit_margin < 0:
                margin_item.setForeground(QColor("#dc3545"))  # 红色
            elif profit_margin < 10:
                margin_item.setForeground(QColor("#ffc107"))  # 黄色
            else:
                margin_item.setForeground(QColor("#28a745"))  # 绿色
            self.dropship_table.setItem(row, 9, margin_item)

            # 状态
            status_item = QTableWidgetItem(str(order.get("status", "")))
            status_color = self.get_status_color(order.get("status", ""))
            status_item.setForeground(QColor(status_color))
            self.dropship_table.setItem(row, 10, status_item)

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(2, 2, 2, 2)
            action_layout.setSpacing(4)

            edit_btn = QPushButton("编辑")
            edit_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #0078D4;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """
            )
            edit_btn.clicked.connect(lambda checked, r=row: self.edit_order_by_row(r))
            action_layout.addWidget(edit_btn)

            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """
            )
            delete_btn.clicked.connect(
                lambda checked, r=row: self.delete_order_by_row(r)
            )
            action_layout.addWidget(delete_btn)

            self.dropship_table.setCellWidget(row, 11, action_widget)

            # ID (隐藏)
            self.dropship_table.setItem(
                row, 12, QTableWidgetItem(str(order.get("id", "")))
            )

    def get_status_color(self, status: str) -> str:
        """获取状态颜色"""
        status_colors = {
            "待下单": "#ffc107",
            "已下单": "#17a2b8",
            "已发货": "#0078D4",
            "已完成": "#28a745",
            "已取消": "#dc3545",
        }
        return status_colors.get(status, "#E0E0E0")

    def update_statistics(self):
        """更新统计信息"""
        total = len(self.dropship_data)
        pending = len([o for o in self.dropship_data if o.get("status") == "待下单"])
        processing = len(
            [o for o in self.dropship_data if o.get("status") in ["已下单", "已发货"]]
        )
        completed = len([o for o in self.dropship_data if o.get("status") == "已完成"])

        # 计算总利润
        total_profit = 0
        for order in self.dropship_data:
            if order.get("status") == "已完成":
                quantity = int(order.get("quantity", 0))
                customer_total = float(order.get("customer_price", 0)) * quantity
                supplier_total = float(order.get("supplier_price", 0)) * quantity
                shipping_fee = float(order.get("shipping_fee", 0))
                other_fee = float(order.get("other_fee", 0))
                profit = customer_total - supplier_total - shipping_fee - other_fee
                total_profit += profit

        self.total_label.setText(f"总订单: {total}")
        self.pending_label.setText(f"待下单: {pending}")
        self.processing_label.setText(f"处理中: {processing}")
        self.completed_label.setText(f"已完成: {completed}")
        self.profit_label.setText(f"总利润: ¥{total_profit:.2f}")

    def search_dropship_orders(self, search_params: Dict[str, Any]):
        """搜索代发订单"""
        try:
            conditions = []
            params = []

            if search_params.get("customer_order_id"):
                conditions.append("customer_order_id LIKE ?")
                params.append(f"%{search_params['customer_order_id']}%")

            if search_params.get("supplier"):
                conditions.append("supplier_name = ?")
                params.append(search_params["supplier"])

            if search_params.get("status"):
                conditions.append("status = ?")
                params.append(search_params["status"])

            if search_params.get("sku"):
                conditions.append("product_sku LIKE ?")
                params.append(f"%{search_params['sku']}%")

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            query = f"""
                SELECT * FROM dropship_orders
                WHERE {where_clause}
                ORDER BY created_at DESC
                LIMIT 1000
            """

            self.dropship_data = self.db_manager.execute_query(query, tuple(params))
            self.update_dropship_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"搜索代发订单失败: {e}")
            QMessageBox.warning(self, "错误", f"搜索代发订单失败: {e}")

    def add_dropship_order(self):
        """添加代发订单"""
        dialog = DropshipOrderDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                order_data = dialog.get_order_data()

                # 添加时间戳
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                order_data.update({"created_at": now, "updated_at": now})

                # 插入数据库
                columns = ", ".join(order_data.keys())
                placeholders = ", ".join(["?" for _ in order_data])
                query = (
                    f"INSERT INTO dropship_orders ({columns}) VALUES ({placeholders})"
                )

                self.db_manager.execute_update(query, tuple(order_data.values()))

                QMessageBox.information(self, "成功", "代发订单创建成功！")
                self.refresh_data()

            except Exception as e:
                logger.error(f"创建代发订单失败: {e}")
                QMessageBox.warning(self, "错误", f"创建代发订单失败: {e}")

    def edit_selected_order(self):
        """编辑选中的订单"""
        current_row = self.dropship_table.currentRow()
        if current_row >= 0:
            self.edit_order_by_row(current_row)

    def edit_order_by_row(self, row: int):
        """通过行号编辑订单"""
        if 0 <= row < len(self.dropship_data):
            order_data = self.dropship_data[row]
            dialog = DropshipOrderDialog(order_data, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                try:
                    updated_data = dialog.get_order_data()
                    updated_data["updated_at"] = datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )

                    # 更新数据库
                    set_clause = ", ".join(
                        [f"{key} = ?" for key in updated_data.keys()]
                    )
                    query = f"UPDATE dropship_orders SET {set_clause} WHERE id = ?"
                    params = list(updated_data.values()) + [order_data["id"]]

                    self.db_manager.execute_update(query, tuple(params))

                    QMessageBox.information(self, "成功", "代发订单更新成功！")
                    self.refresh_data()

                except Exception as e:
                    logger.error(f"更新代发订单失败: {e}")
                    QMessageBox.warning(self, "错误", f"更新代发订单失败: {e}")

    def delete_order_by_row(self, row: int):
        """通过行号删除订单"""
        if 0 <= row < len(self.dropship_data):
            order_data = self.dropship_data[row]
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除订单 '{order_data.get('customer_order_id', '')}' 吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    query = "DELETE FROM dropship_orders WHERE id = ?"
                    self.db_manager.execute_update(query, (order_data["id"],))

                    QMessageBox.information(self, "成功", "代发订单删除成功！")
                    self.refresh_data()

                except Exception as e:
                    logger.error(f"删除代发订单失败: {e}")
                    QMessageBox.warning(self, "错误", f"删除代发订单失败: {e}")

    def generate_profit_report(self):
        """生成利润报表"""
        try:
            from utils.file_utils import FileManager

            # 准备导出数据
            export_data = []
            total_revenue = 0
            total_cost = 0
            total_profit = 0

            for order in self.dropship_data:
                quantity = int(order.get("quantity", 0))
                customer_price = float(order.get("customer_price", 0))
                supplier_price = float(order.get("supplier_price", 0))
                shipping_fee = float(order.get("shipping_fee", 0))
                other_fee = float(order.get("other_fee", 0))

                revenue = customer_price * quantity
                cost = supplier_price * quantity + shipping_fee + other_fee
                profit = revenue - cost
                profit_margin = (profit / revenue * 100) if revenue > 0 else 0

                export_data.append(
                    {
                        "客户订单号": order.get("customer_order_id", ""),
                        "供应商": order.get("supplier_name", ""),
                        "商品名称": order.get("product_name", ""),
                        "SKU": order.get("product_sku", ""),
                        "数量": quantity,
                        "客户价格": customer_price,
                        "供应商价格": supplier_price,
                        "运费": shipping_fee,
                        "其他费用": other_fee,
                        "总收入": revenue,
                        "总成本": cost,
                        "利润": profit,
                        "利润率(%)": profit_margin,
                        "状态": order.get("status", ""),
                        "创建时间": order.get("created_at", ""),
                    }
                )

                if order.get("status") == "已完成":
                    total_revenue += revenue
                    total_cost += cost
                    total_profit += profit

            # 添加汇总行
            export_data.append(
                {
                    "客户订单号": "=== 汇总 ===",
                    "供应商": "",
                    "商品名称": "",
                    "SKU": "",
                    "数量": "",
                    "客户价格": "",
                    "供应商价格": "",
                    "运费": "",
                    "其他费用": "",
                    "总收入": total_revenue,
                    "总成本": total_cost,
                    "利润": total_profit,
                    "利润率(%)": (
                        (total_profit / total_revenue * 100) if total_revenue > 0 else 0
                    ),
                    "状态": "",
                    "创建时间": "",
                }
            )

            # 导出CSV文件
            file_manager = FileManager()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"dropship_profit_report_{timestamp}.csv"

            file_manager.write_csv(filename, export_data)

            QMessageBox.information(self, "成功", f"利润报表已导出到: {filename}")

        except Exception as e:
            logger.error(f"生成利润报表失败: {e}")
            QMessageBox.warning(self, "错误", f"生成利润报表失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_dropship_orders()

    def auto_refresh_data(self):
        """自动刷新数据"""
        self.refresh_data()
