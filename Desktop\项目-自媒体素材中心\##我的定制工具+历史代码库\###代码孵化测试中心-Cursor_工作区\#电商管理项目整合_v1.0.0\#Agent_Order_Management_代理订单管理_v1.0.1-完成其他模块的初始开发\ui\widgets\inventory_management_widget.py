# -*- coding: utf-8 -*-
"""
库存管理组件
用于管理商品库存
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QPushButton,
    QLineEdit,
    QComboBox,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QFrame,
    QGroupBox,
    QSpinBox,
    QDoubleSpinBox,
    QDialog,
    QDialogButtonBox,
    QMessageBox,
    QProgressBar,
    QTabWidget,
    QTextEdit,
    QCheckBox,
    QDateEdit,
    QSplitter,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QDate
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from core.database import DatabaseManager
from api.ali1688_client import Ali1688Client
from utils.logger import get_logger

logger = get_logger(__name__)


class InventoryEditDialog(QDialog):
    """库存编辑对话框"""

    def __init__(self, inventory_data: Dict[str, Any] = None, parent=None):
        super().__init__(parent)
        self.inventory_data = inventory_data or {}
        self.is_edit_mode = bool(inventory_data)
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        title = "编辑库存" if self.is_edit_mode else "添加库存"
        self.setWindowTitle(title)
        self.setMinimumSize(500, 400)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # 基本信息
        form_layout = QGridLayout()

        # 商品ID
        form_layout.addWidget(QLabel("商品ID:"), 0, 0)
        self.product_id_edit = QLineEdit()
        self.product_id_edit.setText(str(self.inventory_data.get("product_id", "")))
        self.product_id_edit.setPlaceholderText("输入商品ID")
        form_layout.addWidget(self.product_id_edit, 0, 1)

        # SKU
        form_layout.addWidget(QLabel("SKU:"), 1, 0)
        self.sku_edit = QLineEdit()
        self.sku_edit.setText(self.inventory_data.get("sku", ""))
        self.sku_edit.setPlaceholderText("输入SKU")
        form_layout.addWidget(self.sku_edit, 1, 1)

        # 库存数量
        form_layout.addWidget(QLabel("库存数量:"), 2, 0)
        self.quantity_spinbox = QSpinBox()
        self.quantity_spinbox.setRange(0, 999999)
        self.quantity_spinbox.setValue(int(self.inventory_data.get("quantity", 0)))
        form_layout.addWidget(self.quantity_spinbox, 2, 1)

        # 预留库存
        form_layout.addWidget(QLabel("预留库存:"), 3, 0)
        self.reserved_spinbox = QSpinBox()
        self.reserved_spinbox.setRange(0, 999999)
        self.reserved_spinbox.setValue(
            int(self.inventory_data.get("reserved_quantity", 0))
        )
        form_layout.addWidget(self.reserved_spinbox, 3, 1)

        # 安全库存
        form_layout.addWidget(QLabel("安全库存:"), 4, 0)
        self.safety_stock_spinbox = QSpinBox()
        self.safety_stock_spinbox.setRange(0, 999999)
        self.safety_stock_spinbox.setValue(
            int(self.inventory_data.get("safety_stock", 0))
        )
        form_layout.addWidget(self.safety_stock_spinbox, 4, 1)

        # 成本价
        form_layout.addWidget(QLabel("成本价:"), 5, 0)
        self.cost_price_spinbox = QDoubleSpinBox()
        self.cost_price_spinbox.setRange(0.01, 999999.99)
        self.cost_price_spinbox.setDecimals(2)
        self.cost_price_spinbox.setSuffix(" 元")
        self.cost_price_spinbox.setValue(
            float(self.inventory_data.get("cost_price", 0))
        )
        form_layout.addWidget(self.cost_price_spinbox, 5, 1)

        # 仓库位置
        form_layout.addWidget(QLabel("仓库位置:"), 6, 0)
        self.location_edit = QLineEdit()
        self.location_edit.setText(self.inventory_data.get("location", ""))
        self.location_edit.setPlaceholderText("输入仓库位置")
        form_layout.addWidget(self.location_edit, 6, 1)

        layout.addLayout(form_layout)

        # 备注
        layout.addWidget(QLabel("备注:"))
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlainText(self.inventory_data.get("notes", ""))
        self.notes_edit.setMaximumHeight(80)
        layout.addWidget(self.notes_edit)

        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def get_inventory_data(self) -> Dict[str, Any]:
        """获取库存数据"""
        return {
            "product_id": (
                int(self.product_id_edit.text())
                if self.product_id_edit.text().isdigit()
                else 0
            ),
            "sku": self.sku_edit.text().strip(),
            "quantity": self.quantity_spinbox.value(),
            "reserved_quantity": self.reserved_spinbox.value(),
            "safety_stock": self.safety_stock_spinbox.value(),
            "cost_price": self.cost_price_spinbox.value(),
            "location": self.location_edit.text().strip(),
            "notes": self.notes_edit.toPlainText().strip(),
        }


class InventorySearchWidget(QFrame):
    """库存搜索组件"""

    search_requested = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(
            """
            QFrame {
                border: 1px solid #505050;
                border-radius: 8px;
                background-color: #2D2D2D;
                padding: 16px;
            }
        """
        )

        layout = QGridLayout(self)
        layout.setSpacing(12)

        # 搜索条件
        layout.addWidget(QLabel("SKU:"), 0, 0)
        self.sku_edit = QLineEdit()
        self.sku_edit.setPlaceholderText("输入SKU")
        layout.addWidget(self.sku_edit, 0, 1)

        layout.addWidget(QLabel("库存状态:"), 0, 2)
        self.stock_status_combo = QComboBox()
        self.stock_status_combo.addItems(["全部", "正常", "库存不足", "缺货", "超库存"])
        layout.addWidget(self.stock_status_combo, 0, 3)

        layout.addWidget(QLabel("仓库位置:"), 1, 0)
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("输入仓库位置")
        layout.addWidget(self.location_edit, 1, 1)

        # 只显示低库存
        self.low_stock_checkbox = QCheckBox("只显示低库存商品")
        layout.addWidget(self.low_stock_checkbox, 1, 2)

        # 按钮
        button_layout = QHBoxLayout()

        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #0078D4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """
        )
        search_btn.clicked.connect(self.perform_search)
        button_layout.addWidget(search_btn)

        reset_btn = QPushButton("重置")
        reset_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """
        )
        reset_btn.clicked.connect(self.reset_search)
        button_layout.addWidget(reset_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout, 2, 0, 1, 4)

    def perform_search(self):
        """执行搜索"""
        search_params = {
            "sku": self.sku_edit.text().strip(),
            "stock_status": (
                self.stock_status_combo.currentText()
                if self.stock_status_combo.currentText() != "全部"
                else ""
            ),
            "location": self.location_edit.text().strip(),
            "low_stock_only": self.low_stock_checkbox.isChecked(),
        }
        self.search_requested.emit(search_params)

    def reset_search(self):
        """重置搜索条件"""
        self.sku_edit.clear()
        self.stock_status_combo.setCurrentIndex(0)
        self.location_edit.clear()
        self.low_stock_checkbox.setChecked(False)


class InventoryManagementWidget(QWidget):
    """库存管理组件"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.ali_client = Ali1688Client()
        self.inventory_data = []
        self.init_ui()
        self.load_inventory()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题区域
        title_layout = QHBoxLayout()
        title_label = QLabel("库存管理")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #E0E0E0;")
        title_layout.addWidget(title_label)

        # 操作按钮
        add_btn = QPushButton("添加库存")
        add_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )
        add_btn.clicked.connect(self.add_inventory)
        title_layout.addWidget(add_btn)

        refresh_btn = QPushButton("刷新")
        refresh_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """
        )
        refresh_btn.clicked.connect(self.refresh_data)
        title_layout.addWidget(refresh_btn)

        sync_btn = QPushButton("同步库存")
        sync_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """
        )
        sync_btn.clicked.connect(self.sync_inventory)
        title_layout.addWidget(sync_btn)

        export_btn = QPushButton("导出报表")
        export_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """
        )
        export_btn.clicked.connect(self.export_inventory_report)
        title_layout.addWidget(export_btn)

        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # 搜索区域
        self.search_widget = InventorySearchWidget()
        self.search_widget.search_requested.connect(self.search_inventory)
        main_layout.addWidget(self.search_widget)

        # 统计信息
        stats_layout = QHBoxLayout()
        self.total_label = QLabel("总SKU: 0")
        self.normal_label = QLabel("正常: 0")
        self.low_stock_label = QLabel("库存不足: 0")
        self.out_of_stock_label = QLabel("缺货: 0")

        for label in [
            self.total_label,
            self.normal_label,
            self.low_stock_label,
            self.out_of_stock_label,
        ]:
            label.setStyleSheet(
                """
                QLabel {
                    background-color: #2D2D2D;
                    border: 1px solid #505050;
                    border-radius: 4px;
                    padding: 8px 12px;
                    color: #E0E0E0;
                    font-weight: bold;
                }
            """
            )
            stats_layout.addWidget(label)

        stats_layout.addStretch()
        main_layout.addLayout(stats_layout)

        # 库存列表表格
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(12)
        self.inventory_table.setHorizontalHeaderLabels(
            [
                "SKU",
                "商品名称",
                "当前库存",
                "预留库存",
                "可用库存",
                "安全库存",
                "成本价",
                "库存价值",
                "仓库位置",
                "状态",
                "操作",
                "ID",
            ]
        )

        # 设置表格样式
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.inventory_table.horizontalHeader().setStretchLastSection(True)
        self.inventory_table.setStyleSheet(
            """
            QTableWidget {
                background-color: #2D2D2D;
                alternate-background-color: #353535;
                gridline-color: #505050;
                color: #E0E0E0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #505050;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #E0E0E0;
                padding: 8px;
                border: 1px solid #505050;
                font-weight: bold;
            }
        """
        )

        # 隐藏ID列
        self.inventory_table.setColumnHidden(11, True)

        # 双击编辑库存
        self.inventory_table.doubleClicked.connect(self.edit_selected_inventory)

        main_layout.addWidget(self.inventory_table)

        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

    def load_inventory(self):
        """加载库存数据"""
        try:
            # 从数据库加载库存
            query = """
                SELECT i.*, p.title as product_title, p.price as product_price
                FROM inventory i
                LEFT JOIN products p ON i.product_id = p.id
                ORDER BY i.last_updated DESC
                LIMIT 1000
            """
            self.inventory_data = self.db_manager.execute_query(query)
            self.update_inventory_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"加载库存数据失败: {e}")
            QMessageBox.warning(self, "错误", f"加载库存数据失败: {e}")

    def update_inventory_table(self):
        """更新库存表格"""
        self.inventory_table.setRowCount(len(self.inventory_data))

        for row, inventory in enumerate(self.inventory_data):
            # SKU
            self.inventory_table.setItem(
                row, 0, QTableWidgetItem(str(inventory.get("sku", "")))
            )

            # 商品名称
            title = str(inventory.get("product_title", ""))
            if len(title) > 20:
                title = title[:20] + "..."
            self.inventory_table.setItem(row, 1, QTableWidgetItem(title))

            # 当前库存
            quantity = int(inventory.get("stock_quantity", 0))
            quantity_item = QTableWidgetItem(str(quantity))
            if quantity <= 0:
                quantity_item.setForeground(QColor("#dc3545"))  # 红色
            elif quantity <= int(inventory.get("low_stock_threshold", 0)):
                quantity_item.setForeground(QColor("#ffc107"))  # 黄色
            else:
                quantity_item.setForeground(QColor("#28a745"))  # 绿色
            self.inventory_table.setItem(row, 2, quantity_item)

            # 预留库存
            self.inventory_table.setItem(
                row, 3, QTableWidgetItem(str(inventory.get("reserved_quantity", 0)))
            )

            # 可用库存
            available = quantity - int(inventory.get("reserved_quantity", 0))
            self.inventory_table.setItem(row, 4, QTableWidgetItem(str(available)))

            # 安全库存
            self.inventory_table.setItem(
                row, 5, QTableWidgetItem(str(inventory.get("low_stock_threshold", 0)))
            )

            # 成本价
            cost_price = f"¥{float(inventory.get('cost_price', 0)):.2f}"
            self.inventory_table.setItem(row, 6, QTableWidgetItem(cost_price))

            # 库存价值
            inventory_value = quantity * float(inventory.get("cost_price", 0))
            value_text = f"¥{inventory_value:.2f}"
            self.inventory_table.setItem(row, 7, QTableWidgetItem(value_text))

            # 仓库位置
            self.inventory_table.setItem(
                row, 8, QTableWidgetItem(str(inventory.get("warehouse_location", "")))
            )

            # 状态
            status = self.get_stock_status(inventory)
            status_item = QTableWidgetItem(status)
            status_color = self.get_status_color(status)
            status_item.setForeground(QColor(status_color))
            self.inventory_table.setItem(row, 9, status_item)

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(2, 2, 2, 2)
            action_layout.setSpacing(4)

            edit_btn = QPushButton("编辑")
            edit_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #0078D4;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """
            )
            edit_btn.clicked.connect(
                lambda checked, r=row: self.edit_inventory_by_row(r)
            )
            action_layout.addWidget(edit_btn)

            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """
            )
            delete_btn.clicked.connect(
                lambda checked, r=row: self.delete_inventory_by_row(r)
            )
            action_layout.addWidget(delete_btn)

            self.inventory_table.setCellWidget(row, 10, action_widget)

            # ID (隐藏)
            self.inventory_table.setItem(
                row, 11, QTableWidgetItem(str(inventory.get("id", "")))
            )

    def get_stock_status(self, inventory: Dict[str, Any]) -> str:
        """获取库存状态"""
        quantity = int(inventory.get("stock_quantity", 0))
        threshold = int(inventory.get("low_stock_threshold", 0))

        if quantity <= 0:
            return "缺货"
        elif quantity <= threshold:
            return "库存不足"
        else:
            return "正常"

    def get_status_color(self, status: str) -> str:
        """获取状态颜色"""
        status_colors = {
            "正常": "#28a745",
            "库存不足": "#ffc107",
            "缺货": "#dc3545",
        }
        return status_colors.get(status, "#E0E0E0")

    def update_statistics(self):
        """更新统计信息"""
        total = len(self.inventory_data)
        normal = len(
            [i for i in self.inventory_data if self.get_stock_status(i) == "正常"]
        )
        low_stock = len(
            [i for i in self.inventory_data if self.get_stock_status(i) == "库存不足"]
        )
        out_of_stock = len(
            [i for i in self.inventory_data if self.get_stock_status(i) == "缺货"]
        )

        self.total_label.setText(f"总SKU: {total}")
        self.normal_label.setText(f"正常: {normal}")
        self.low_stock_label.setText(f"库存不足: {low_stock}")
        self.out_of_stock_label.setText(f"缺货: {out_of_stock}")

    def search_inventory(self, search_params: Dict[str, Any]):
        """搜索库存"""
        try:
            conditions = []
            params = []

            if search_params.get("sku"):
                conditions.append("i.sku LIKE ?")
                params.append(f"%{search_params['sku']}%")

            if search_params.get("location"):
                conditions.append("i.location LIKE ?")
                params.append(f"%{search_params['location']}%")

            if search_params.get("low_stock_only"):
                conditions.append("i.quantity <= i.safety_stock")

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            query = f"""
                SELECT i.*, p.title as product_title, p.price as product_price
                FROM inventory i
                LEFT JOIN products p ON i.product_id = p.id
                WHERE {where_clause}
                ORDER BY i.updated_at DESC
                LIMIT 1000
            """

            self.inventory_data = self.db_manager.execute_query(query, tuple(params))
            self.update_inventory_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"搜索库存失败: {e}")
            QMessageBox.warning(self, "错误", f"搜索库存失败: {e}")

    def add_inventory(self):
        """添加库存"""
        dialog = InventoryEditDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                inventory_data = dialog.get_inventory_data()

                # 添加时间戳
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                inventory_data.update({"created_at": now, "updated_at": now})

                # 插入数据库
                columns = ", ".join(inventory_data.keys())
                placeholders = ", ".join(["?" for _ in inventory_data])
                query = f"INSERT INTO inventory ({columns}) VALUES ({placeholders})"

                self.db_manager.execute_update(query, tuple(inventory_data.values()))

                QMessageBox.information(self, "成功", "库存添加成功！")
                self.refresh_data()

            except Exception as e:
                logger.error(f"添加库存失败: {e}")
                QMessageBox.warning(self, "错误", f"添加库存失败: {e}")

    def edit_selected_inventory(self):
        """编辑选中的库存"""
        current_row = self.inventory_table.currentRow()
        if current_row >= 0:
            self.edit_inventory_by_row(current_row)

    def edit_inventory_by_row(self, row: int):
        """通过行号编辑库存"""
        if 0 <= row < len(self.inventory_data):
            inventory_data = self.inventory_data[row]
            dialog = InventoryEditDialog(inventory_data, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                try:
                    updated_data = dialog.get_inventory_data()
                    updated_data["updated_at"] = datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )

                    # 更新数据库
                    set_clause = ", ".join(
                        [f"{key} = ?" for key in updated_data.keys()]
                    )
                    query = f"UPDATE inventory SET {set_clause} WHERE id = ?"
                    params = list(updated_data.values()) + [inventory_data["id"]]

                    self.db_manager.execute_update(query, tuple(params))

                    QMessageBox.information(self, "成功", "库存更新成功！")
                    self.refresh_data()

                except Exception as e:
                    logger.error(f"更新库存失败: {e}")
                    QMessageBox.warning(self, "错误", f"更新库存失败: {e}")

    def delete_inventory_by_row(self, row: int):
        """通过行号删除库存"""
        if 0 <= row < len(self.inventory_data):
            inventory_data = self.inventory_data[row]
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除SKU '{inventory_data.get('sku', '')}' 的库存记录吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    query = "DELETE FROM inventory WHERE id = ?"
                    self.db_manager.execute_update(query, (inventory_data["id"],))

                    QMessageBox.information(self, "成功", "库存删除成功！")
                    self.refresh_data()

                except Exception as e:
                    logger.error(f"删除库存失败: {e}")
                    QMessageBox.warning(self, "错误", f"删除库存失败: {e}")

    def sync_inventory(self):
        """同步库存数据"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条

            # 这里应该调用API同步库存
            # 暂时使用模拟数据
            QTimer.singleShot(2000, self.sync_completed)

        except Exception as e:
            logger.error(f"同步库存失败: {e}")
            QMessageBox.warning(self, "错误", f"同步库存失败: {e}")
            self.progress_bar.setVisible(False)

    def sync_completed(self):
        """同步完成"""
        self.progress_bar.setVisible(False)
        QMessageBox.information(self, "成功", "库存同步完成！")
        self.refresh_data()

    def export_inventory_report(self):
        """导出库存报表"""
        try:
            from utils.file_utils import FileManager

            # 准备导出数据
            export_data = []
            for inventory in self.inventory_data:
                export_data.append(
                    {
                        "SKU": inventory.get("sku", ""),
                        "商品名称": inventory.get("product_title", ""),
                        "当前库存": inventory.get("quantity", 0),
                        "预留库存": inventory.get("reserved_quantity", 0),
                        "可用库存": int(inventory.get("quantity", 0))
                        - int(inventory.get("reserved_quantity", 0)),
                        "安全库存": inventory.get("safety_stock", 0),
                        "成本价": inventory.get("cost_price", 0),
                        "库存价值": int(inventory.get("quantity", 0))
                        * float(inventory.get("cost_price", 0)),
                        "仓库位置": inventory.get("location", ""),
                        "状态": self.get_stock_status(inventory),
                        "更新时间": inventory.get("updated_at", ""),
                    }
                )

            # 导出CSV文件
            file_manager = FileManager()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"inventory_report_{timestamp}.csv"

            file_manager.write_csv(filename, export_data)

            QMessageBox.information(self, "成功", f"库存报表已导出到: {filename}")

        except Exception as e:
            logger.error(f"导出库存报表失败: {e}")
            QMessageBox.warning(self, "错误", f"导出库存报表失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_inventory()

    def auto_refresh_data(self):
        """自动刷新数据"""
        self.refresh_data()
