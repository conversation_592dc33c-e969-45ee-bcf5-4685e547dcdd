# -*- coding: utf-8 -*-
"""
订单管理组件
用于管理多平台订单
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QPushButton,
    QLineEdit,
    QComboBox,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QFrame,
    QGroupBox,
    QDateEdit,
    QSpinBox,
    QDoubleSpinBox,
    QTextEdit,
    QDialog,
    QDialogButtonBox,
    QMessageBox,
    QProgressBar,
    QTabWidget,
    QSplitter,
    QScrollArea,
    QCheckBox,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QDate, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor
from datetime import datetime, timedelta
import json
from typing import List, Dict, Any, Optional

from core.database import DatabaseManager
from api.ali1688_client import Ali1688Client
from utils.logger import get_logger

logger = get_logger(__name__)


class OrderDetailDialog(QDialog):
    """订单详情对话框"""

    def __init__(self, order_data: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.order_data = order_data
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle(
            f"订单详情 - {self.order_data.get('platform_order_id', 'N/A')}"
        )
        self.setMinimumSize(800, 600)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # 创建标签页
        tab_widget = QTabWidget()

        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        tab_widget.addTab(basic_tab, "基本信息")

        # 商品信息标签页
        items_tab = self.create_items_tab()
        tab_widget.addTab(items_tab, "商品信息")

        # 物流信息标签页
        shipping_tab = self.create_shipping_tab()
        tab_widget.addTab(shipping_tab, "物流信息")

        layout.addWidget(tab_widget)

        # 按钮区域
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)

    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 订单基本信息
        basic_group = QGroupBox("订单基本信息")
        basic_layout = QGridLayout(basic_group)

        fields = [
            ("订单号:", self.order_data.get("platform_order_id", "N/A")),
            ("订单状态:", self.order_data.get("order_status", "N/A")),
            ("支付状态:", self.order_data.get("payment_status", "N/A")),
            ("物流状态:", self.order_data.get("shipping_status", "N/A")),
            ("订单金额:", f"¥{self.order_data.get('total_amount', 0):.2f}"),
            ("运费:", f"¥{self.order_data.get('shipping_fee', 0):.2f}"),
            ("优惠金额:", f"¥{self.order_data.get('discount_amount', 0):.2f}"),
            ("下单时间:", self.order_data.get("order_time", "N/A")),
            ("支付时间:", self.order_data.get("payment_time", "N/A")),
        ]

        for i, (label, value) in enumerate(fields):
            basic_layout.addWidget(QLabel(label), i, 0)
            value_label = QLabel(str(value))
            value_label.setStyleSheet("font-weight: bold; color: #0078D4;")
            basic_layout.addWidget(value_label, i, 1)

        layout.addWidget(basic_group)

        # 买家信息
        buyer_group = QGroupBox("买家信息")
        buyer_layout = QGridLayout(buyer_group)

        buyer_fields = [
            ("买家姓名:", self.order_data.get("buyer_name", "N/A")),
            ("联系电话:", self.order_data.get("buyer_phone", "N/A")),
            ("收货地址:", self.order_data.get("shipping_address", "N/A")),
        ]

        for i, (label, value) in enumerate(buyer_fields):
            buyer_layout.addWidget(QLabel(label), i, 0)
            value_label = QLabel(str(value))
            value_label.setWordWrap(True)
            buyer_layout.addWidget(value_label, i, 1)

        layout.addWidget(buyer_group)
        layout.addStretch()

        return widget

    def create_items_tab(self) -> QWidget:
        """创建商品信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 商品列表表格
        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels(
            ["商品名称", "规格", "单价", "数量", "小计", "商品ID"]
        )

        # 这里应该从order_items表获取数据，暂时使用模拟数据
        table.setRowCount(1)
        table.setItem(0, 0, QTableWidgetItem("示例商品"))
        table.setItem(0, 1, QTableWidgetItem("颜色:红色, 尺寸:L"))
        table.setItem(0, 2, QTableWidgetItem("¥99.00"))
        table.setItem(0, 3, QTableWidgetItem("2"))
        table.setItem(0, 4, QTableWidgetItem("¥198.00"))
        table.setItem(0, 5, QTableWidgetItem("PROD123456"))

        # 设置表格样式
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        layout.addWidget(table)

        return widget

    def create_shipping_tab(self) -> QWidget:
        """创建物流信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        shipping_group = QGroupBox("物流信息")
        shipping_layout = QGridLayout(shipping_group)

        shipping_fields = [
            ("物流公司:", "待发货"),
            ("运单号:", "待发货"),
            ("发货时间:", self.order_data.get("shipping_time", "N/A")),
            ("预计送达:", "待发货"),
            ("签收时间:", self.order_data.get("delivery_time", "N/A")),
        ]

        for i, (label, value) in enumerate(shipping_fields):
            shipping_layout.addWidget(QLabel(label), i, 0)
            value_label = QLabel(str(value))
            shipping_layout.addWidget(value_label, i, 1)

        layout.addWidget(shipping_group)
        layout.addStretch()

        return widget


class OrderSearchWidget(QFrame):
    """订单搜索组件"""

    search_requested = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(
            """
            QFrame {
                border: 1px solid #505050;
                border-radius: 8px;
                background-color: #2D2D2D;
                padding: 16px;
            }
        """
        )

        layout = QGridLayout(self)
        layout.setSpacing(12)

        # 搜索条件
        layout.addWidget(QLabel("订单号:"), 0, 0)
        self.order_id_edit = QLineEdit()
        self.order_id_edit.setPlaceholderText("输入订单号")
        layout.addWidget(self.order_id_edit, 0, 1)

        layout.addWidget(QLabel("订单状态:"), 0, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems(
            ["全部", "待付款", "待发货", "已发货", "已完成", "已取消"]
        )
        layout.addWidget(self.status_combo, 0, 3)

        layout.addWidget(QLabel("开始日期:"), 1, 0)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        layout.addWidget(self.start_date, 1, 1)

        layout.addWidget(QLabel("结束日期:"), 1, 2)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        layout.addWidget(self.end_date, 1, 3)

        # 按钮
        button_layout = QHBoxLayout()

        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #0078D4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """
        )
        search_btn.clicked.connect(self.perform_search)
        button_layout.addWidget(search_btn)

        reset_btn = QPushButton("重置")
        reset_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """
        )
        reset_btn.clicked.connect(self.reset_search)
        button_layout.addWidget(reset_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout, 2, 0, 1, 4)

    def perform_search(self):
        """执行搜索"""
        search_params = {
            "order_id": self.order_id_edit.text().strip(),
            "status": (
                self.status_combo.currentText()
                if self.status_combo.currentText() != "全部"
                else ""
            ),
            "start_date": self.start_date.date().toString("yyyy-MM-dd"),
            "end_date": self.end_date.date().toString("yyyy-MM-dd"),
        }
        self.search_requested.emit(search_params)

    def reset_search(self):
        """重置搜索条件"""
        self.order_id_edit.clear()
        self.status_combo.setCurrentIndex(0)
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.end_date.setDate(QDate.currentDate())


class OrderManagementWidget(QWidget):
    """订单管理组件"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.ali_client = Ali1688Client()
        self.orders_data = []
        self.init_ui()
        self.load_orders()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题区域
        title_layout = QHBoxLayout()
        title_label = QLabel("订单管理")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #E0E0E0;")
        title_layout.addWidget(title_label)

        # 操作按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )
        refresh_btn.clicked.connect(self.refresh_data)
        title_layout.addWidget(refresh_btn)

        sync_btn = QPushButton("同步订单")
        sync_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """
        )
        sync_btn.clicked.connect(self.sync_orders)
        title_layout.addWidget(sync_btn)

        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # 搜索区域
        self.search_widget = OrderSearchWidget()
        self.search_widget.search_requested.connect(self.search_orders)
        main_layout.addWidget(self.search_widget)

        # 统计信息
        stats_layout = QHBoxLayout()
        self.total_label = QLabel("总订单: 0")
        self.pending_label = QLabel("待处理: 0")
        self.shipped_label = QLabel("已发货: 0")
        self.completed_label = QLabel("已完成: 0")

        for label in [
            self.total_label,
            self.pending_label,
            self.shipped_label,
            self.completed_label,
        ]:
            label.setStyleSheet(
                """
                QLabel {
                    background-color: #2D2D2D;
                    border: 1px solid #505050;
                    border-radius: 4px;
                    padding: 8px 12px;
                    color: #E0E0E0;
                    font-weight: bold;
                }
            """
            )
            stats_layout.addWidget(label)

        stats_layout.addStretch()
        main_layout.addLayout(stats_layout)

        # 订单列表表格
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(10)
        self.orders_table.setHorizontalHeaderLabels(
            [
                "订单号",
                "状态",
                "买家",
                "金额",
                "下单时间",
                "支付时间",
                "发货时间",
                "平台",
                "操作",
                "ID",
            ]
        )

        # 设置表格样式
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.orders_table.horizontalHeader().setStretchLastSection(True)
        self.orders_table.setStyleSheet(
            """
            QTableWidget {
                background-color: #2D2D2D;
                alternate-background-color: #353535;
                gridline-color: #505050;
                color: #E0E0E0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #505050;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #E0E0E0;
                padding: 8px;
                border: 1px solid #505050;
                font-weight: bold;
            }
        """
        )

        # 隐藏ID列
        self.orders_table.setColumnHidden(9, True)

        # 双击查看详情
        self.orders_table.doubleClicked.connect(self.show_order_detail)

        main_layout.addWidget(self.orders_table)

        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

    def load_orders(self):
        """加载订单数据"""
        try:
            # 从数据库加载订单
            query = """
                SELECT o.*, s.name as store_name, p.name as platform_name
                FROM orders o
                LEFT JOIN stores s ON o.store_id = s.id
                LEFT JOIN platforms p ON s.platform_id = p.id
                ORDER BY o.order_time DESC
                LIMIT 1000
            """
            self.orders_data = self.db_manager.execute_query(query)
            self.update_orders_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"加载订单数据失败: {e}")
            QMessageBox.warning(self, "错误", f"加载订单数据失败: {e}")

    def update_orders_table(self):
        """更新订单表格"""
        self.orders_table.setRowCount(len(self.orders_data))

        for row, order in enumerate(self.orders_data):
            # 订单号
            self.orders_table.setItem(
                row, 0, QTableWidgetItem(str(order.get("platform_order_id", "")))
            )

            # 状态
            status_item = QTableWidgetItem(str(order.get("order_status", "")))
            status_color = self.get_status_color(order.get("order_status", ""))
            status_item.setForeground(QColor(status_color))
            self.orders_table.setItem(row, 1, status_item)

            # 买家
            self.orders_table.setItem(
                row, 2, QTableWidgetItem(str(order.get("buyer_name", "")))
            )

            # 金额
            amount = f"¥{float(order.get('total_amount', 0)):.2f}"
            self.orders_table.setItem(row, 3, QTableWidgetItem(amount))

            # 时间字段
            self.orders_table.setItem(
                row, 4, QTableWidgetItem(str(order.get("order_time", "")))
            )
            self.orders_table.setItem(
                row, 5, QTableWidgetItem(str(order.get("payment_time", "")))
            )
            self.orders_table.setItem(
                row, 6, QTableWidgetItem(str(order.get("shipping_time", "")))
            )

            # 平台
            self.orders_table.setItem(
                row, 7, QTableWidgetItem(str(order.get("platform_name", "")))
            )

            # 操作按钮
            action_btn = QPushButton("查看详情")
            action_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #0078D4;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """
            )
            action_btn.clicked.connect(
                lambda checked, r=row: self.show_order_detail_by_row(r)
            )
            self.orders_table.setCellWidget(row, 8, action_btn)

            # ID (隐藏)
            self.orders_table.setItem(
                row, 9, QTableWidgetItem(str(order.get("id", "")))
            )

    def get_status_color(self, status: str) -> str:
        """获取状态颜色"""
        status_colors = {
            "待付款": "#FF8C00",
            "待发货": "#FFA500",
            "已发货": "#17a2b8",
            "已完成": "#28a745",
            "已取消": "#dc3545",
        }
        return status_colors.get(status, "#E0E0E0")

    def update_statistics(self):
        """更新统计信息"""
        total = len(self.orders_data)
        pending = len(
            [
                o
                for o in self.orders_data
                if o.get("order_status") in ["待付款", "待发货"]
            ]
        )
        shipped = len(
            [o for o in self.orders_data if o.get("order_status") == "已发货"]
        )
        completed = len(
            [o for o in self.orders_data if o.get("order_status") == "已完成"]
        )

        self.total_label.setText(f"总订单: {total}")
        self.pending_label.setText(f"待处理: {pending}")
        self.shipped_label.setText(f"已发货: {shipped}")
        self.completed_label.setText(f"已完成: {completed}")

    def search_orders(self, search_params: Dict[str, Any]):
        """搜索订单"""
        try:
            conditions = []
            params = []

            if search_params.get("order_id"):
                conditions.append("o.platform_order_id LIKE ?")
                params.append(f"%{search_params['order_id']}%")

            if search_params.get("status"):
                conditions.append("o.order_status = ?")
                params.append(search_params["status"])

            if search_params.get("start_date"):
                conditions.append("DATE(o.order_time) >= ?")
                params.append(search_params["start_date"])

            if search_params.get("end_date"):
                conditions.append("DATE(o.order_time) <= ?")
                params.append(search_params["end_date"])

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            query = f"""
                SELECT o.*, s.name as store_name, p.name as platform_name
                FROM orders o
                LEFT JOIN stores s ON o.store_id = s.id
                LEFT JOIN platforms p ON s.platform_id = p.id
                WHERE {where_clause}
                ORDER BY o.order_time DESC
                LIMIT 1000
            """

            self.orders_data = self.db_manager.execute_query(query, tuple(params))
            self.update_orders_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"搜索订单失败: {e}")
            QMessageBox.warning(self, "错误", f"搜索订单失败: {e}")

    def show_order_detail(self):
        """显示订单详情"""
        current_row = self.orders_table.currentRow()
        if current_row >= 0:
            self.show_order_detail_by_row(current_row)

    def show_order_detail_by_row(self, row: int):
        """通过行号显示订单详情"""
        if 0 <= row < len(self.orders_data):
            order_data = self.orders_data[row]
            dialog = OrderDetailDialog(order_data, self)
            dialog.exec()

    def sync_orders(self):
        """同步订单数据"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条

            # 这里应该调用API同步订单
            # 暂时使用模拟数据
            QTimer.singleShot(2000, self.sync_completed)

        except Exception as e:
            logger.error(f"同步订单失败: {e}")
            QMessageBox.warning(self, "错误", f"同步订单失败: {e}")
            self.progress_bar.setVisible(False)

    def sync_completed(self):
        """同步完成"""
        self.progress_bar.setVisible(False)
        QMessageBox.information(self, "成功", "订单同步完成！")
        self.refresh_data()

    def refresh_data(self):
        """刷新数据"""
        self.load_orders()

    def auto_refresh_data(self):
        """自动刷新数据"""
        self.refresh_data()
