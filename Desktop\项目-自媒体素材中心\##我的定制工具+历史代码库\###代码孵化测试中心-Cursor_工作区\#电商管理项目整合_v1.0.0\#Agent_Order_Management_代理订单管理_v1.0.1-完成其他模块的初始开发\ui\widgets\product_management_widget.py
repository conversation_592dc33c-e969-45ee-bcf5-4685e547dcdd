# -*- coding: utf-8 -*-
"""
商品管理组件
用于管理多平台商品
"""

from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QPushButton,
    QLineEdit,
    QComboBox,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QFrame,
    QGroupBox,
    QDoubleSpinBox,
    QSpinBox,
    QTextEdit,
    QDialog,
    QDialogButtonBox,
    QMessageBox,
    QProgressBar,
    QTabWidget,
    QScrollArea,
    QCheckBox,
    QFileDialog,
    QSplitter,
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

from core.database import DatabaseManager
from api.ali1688_client import Ali1688Client
from utils.logger import get_logger

logger = get_logger(__name__)


class ProductEditDialog(QDialog):
    """商品编辑对话框"""

    def __init__(self, product_data: Dict[str, Any] = None, parent=None):
        super().__init__(parent)
        self.product_data = product_data or {}
        self.is_edit_mode = bool(product_data)
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        title = "编辑商品" if self.is_edit_mode else "添加商品"
        self.setWindowTitle(title)
        self.setMinimumSize(600, 500)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # 创建标签页
        tab_widget = QTabWidget()

        # 基本信息标签页
        basic_tab = self.create_basic_info_tab()
        tab_widget.addTab(basic_tab, "基本信息")

        # 价格库存标签页
        price_tab = self.create_price_inventory_tab()
        tab_widget.addTab(price_tab, "价格库存")

        # 详细描述标签页
        desc_tab = self.create_description_tab()
        tab_widget.addTab(desc_tab, "详细描述")

        layout.addWidget(tab_widget)

        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本信息表单
        form_layout = QGridLayout()

        # 商品标题
        form_layout.addWidget(QLabel("商品标题:"), 0, 0)
        self.title_edit = QLineEdit()
        self.title_edit.setText(self.product_data.get("title", ""))
        self.title_edit.setPlaceholderText("输入商品标题")
        form_layout.addWidget(self.title_edit, 0, 1, 1, 2)

        # 商品分类
        form_layout.addWidget(QLabel("商品分类:"), 1, 0)
        self.category_combo = QComboBox()
        self.category_combo.addItems(
            [
                "服装鞋帽",
                "数码电器",
                "家居用品",
                "美妆护肤",
                "食品饮料",
                "运动户外",
                "母婴用品",
                "其他",
            ]
        )
        if self.product_data.get("category"):
            index = self.category_combo.findText(self.product_data["category"])
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
        form_layout.addWidget(self.category_combo, 1, 1)

        # 品牌
        form_layout.addWidget(QLabel("品牌:"), 1, 2)
        self.brand_edit = QLineEdit()
        self.brand_edit.setText(self.product_data.get("brand", ""))
        self.brand_edit.setPlaceholderText("输入品牌名称")
        form_layout.addWidget(self.brand_edit, 1, 3)

        # 状态
        form_layout.addWidget(QLabel("商品状态:"), 2, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["上架", "下架", "草稿"])
        if self.product_data.get("status"):
            status_map = {"active": "上架", "inactive": "下架", "draft": "草稿"}
            status_text = status_map.get(self.product_data["status"], "上架")
            index = self.status_combo.findText(status_text)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)
        form_layout.addWidget(self.status_combo, 2, 1)

        # 主图URL
        form_layout.addWidget(QLabel("主图URL:"), 3, 0)
        self.main_image_edit = QLineEdit()
        self.main_image_edit.setText(self.product_data.get("main_image", ""))
        self.main_image_edit.setPlaceholderText("输入主图URL")
        form_layout.addWidget(self.main_image_edit, 3, 1, 1, 2)

        # 选择图片按钮
        select_image_btn = QPushButton("选择图片")
        select_image_btn.clicked.connect(self.select_image)
        form_layout.addWidget(select_image_btn, 3, 3)

        layout.addLayout(form_layout)
        layout.addStretch()

        return widget

    def create_price_inventory_tab(self) -> QWidget:
        """创建价格库存标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 价格信息
        price_group = QGroupBox("价格信息")
        price_layout = QGridLayout(price_group)

        # 销售价格
        price_layout.addWidget(QLabel("销售价格:"), 0, 0)
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0.01, 999999.99)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" 元")
        self.price_spinbox.setValue(float(self.product_data.get("price", 0)))
        price_layout.addWidget(self.price_spinbox, 0, 1)

        # 原价
        price_layout.addWidget(QLabel("原价:"), 0, 2)
        self.original_price_spinbox = QDoubleSpinBox()
        self.original_price_spinbox.setRange(0.01, 999999.99)
        self.original_price_spinbox.setDecimals(2)
        self.original_price_spinbox.setSuffix(" 元")
        self.original_price_spinbox.setValue(
            float(self.product_data.get("original_price", 0))
        )
        price_layout.addWidget(self.original_price_spinbox, 0, 3)

        # 货币类型
        price_layout.addWidget(QLabel("货币类型:"), 1, 0)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["CNY", "USD", "EUR", "GBP", "JPY"])
        currency = self.product_data.get("currency", "CNY")
        index = self.currency_combo.findText(currency)
        if index >= 0:
            self.currency_combo.setCurrentIndex(index)
        price_layout.addWidget(self.currency_combo, 1, 1)

        layout.addWidget(price_group)

        # 库存信息
        inventory_group = QGroupBox("库存信息")
        inventory_layout = QGridLayout(inventory_group)

        # SKU数量
        inventory_layout.addWidget(QLabel("SKU数量:"), 0, 0)
        self.sku_count_spinbox = QSpinBox()
        self.sku_count_spinbox.setRange(0, 9999)
        self.sku_count_spinbox.setValue(int(self.product_data.get("sku_count", 1)))
        inventory_layout.addWidget(self.sku_count_spinbox, 0, 1)

        layout.addWidget(inventory_group)
        layout.addStretch()

        return widget

    def create_description_tab(self) -> QWidget:
        """创建详细描述标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 商品描述
        layout.addWidget(QLabel("商品描述:"))
        self.description_edit = QTextEdit()
        self.description_edit.setPlainText(self.product_data.get("description", ""))
        self.description_edit.setPlaceholderText("输入商品的详细描述...")
        layout.addWidget(self.description_edit)

        # 商品图片URLs
        layout.addWidget(QLabel("商品图片URLs (每行一个):"))
        self.images_edit = QTextEdit()
        images = self.product_data.get("images", "")
        if images:
            try:
                images_list = json.loads(images) if isinstance(images, str) else images
                self.images_edit.setPlainText("\n".join(images_list))
            except:
                self.images_edit.setPlainText(str(images))
        self.images_edit.setPlaceholderText("输入商品图片URLs，每行一个...")
        layout.addWidget(self.images_edit)

        return widget

    def select_image(self):
        """选择图片文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片", "", "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp)"
        )
        if file_path:
            self.main_image_edit.setText(file_path)

    def get_product_data(self) -> Dict[str, Any]:
        """获取商品数据"""
        # 状态映射
        status_map = {"上架": "active", "下架": "inactive", "草稿": "draft"}

        # 处理图片URLs
        images_text = self.images_edit.toPlainText().strip()
        images_list = [url.strip() for url in images_text.split("\n") if url.strip()]

        return {
            "title": self.title_edit.text().strip(),
            "description": self.description_edit.toPlainText().strip(),
            "price": self.price_spinbox.value(),
            "original_price": self.original_price_spinbox.value(),
            "currency": self.currency_combo.currentText(),
            "main_image": self.main_image_edit.text().strip(),
            "images": json.dumps(images_list),
            "category": self.category_combo.currentText(),
            "brand": self.brand_edit.text().strip(),
            "sku_count": self.sku_count_spinbox.value(),
            "status": status_map.get(self.status_combo.currentText(), "active"),
        }


class ProductSearchWidget(QFrame):
    """商品搜索组件"""

    search_requested = pyqtSignal(dict)

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(
            """
            QFrame {
                border: 1px solid #505050;
                border-radius: 8px;
                background-color: #2D2D2D;
                padding: 16px;
            }
        """
        )

        layout = QGridLayout(self)
        layout.setSpacing(12)

        # 搜索条件
        layout.addWidget(QLabel("商品名称:"), 0, 0)
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("输入商品名称")
        layout.addWidget(self.title_edit, 0, 1)

        layout.addWidget(QLabel("分类:"), 0, 2)
        self.category_combo = QComboBox()
        self.category_combo.addItems(
            [
                "全部",
                "服装鞋帽",
                "数码电器",
                "家居用品",
                "美妆护肤",
                "食品饮料",
                "运动户外",
                "母婴用品",
                "其他",
            ]
        )
        layout.addWidget(self.category_combo, 0, 3)

        layout.addWidget(QLabel("状态:"), 1, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部", "上架", "下架", "草稿"])
        layout.addWidget(self.status_combo, 1, 1)

        layout.addWidget(QLabel("品牌:"), 1, 2)
        self.brand_edit = QLineEdit()
        self.brand_edit.setPlaceholderText("输入品牌名称")
        layout.addWidget(self.brand_edit, 1, 3)

        # 按钮
        button_layout = QHBoxLayout()

        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #0078D4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """
        )
        search_btn.clicked.connect(self.perform_search)
        button_layout.addWidget(search_btn)

        reset_btn = QPushButton("重置")
        reset_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """
        )
        reset_btn.clicked.connect(self.reset_search)
        button_layout.addWidget(reset_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout, 2, 0, 1, 4)

    def perform_search(self):
        """执行搜索"""
        search_params = {
            "title": self.title_edit.text().strip(),
            "category": (
                self.category_combo.currentText()
                if self.category_combo.currentText() != "全部"
                else ""
            ),
            "status": (
                self.status_combo.currentText()
                if self.status_combo.currentText() != "全部"
                else ""
            ),
            "brand": self.brand_edit.text().strip(),
        }
        self.search_requested.emit(search_params)

    def reset_search(self):
        """重置搜索条件"""
        self.title_edit.clear()
        self.category_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.brand_edit.clear()


class ProductManagementWidget(QWidget):
    """商品管理组件"""

    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.ali_client = Ali1688Client()
        self.products_data = []
        self.init_ui()
        self.load_products()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(16)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # 标题区域
        title_layout = QHBoxLayout()
        title_label = QLabel("商品管理")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #E0E0E0;")
        title_layout.addWidget(title_label)

        # 操作按钮
        add_btn = QPushButton("添加商品")
        add_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )
        add_btn.clicked.connect(self.add_product)
        title_layout.addWidget(add_btn)

        refresh_btn = QPushButton("刷新")
        refresh_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """
        )
        refresh_btn.clicked.connect(self.refresh_data)
        title_layout.addWidget(refresh_btn)

        sync_btn = QPushButton("同步商品")
        sync_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """
        )
        sync_btn.clicked.connect(self.sync_products)
        title_layout.addWidget(sync_btn)

        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # 搜索区域
        self.search_widget = ProductSearchWidget()
        self.search_widget.search_requested.connect(self.search_products)
        main_layout.addWidget(self.search_widget)

        # 统计信息
        stats_layout = QHBoxLayout()
        self.total_label = QLabel("总商品: 0")
        self.active_label = QLabel("上架中: 0")
        self.inactive_label = QLabel("已下架: 0")
        self.draft_label = QLabel("草稿: 0")

        for label in [
            self.total_label,
            self.active_label,
            self.inactive_label,
            self.draft_label,
        ]:
            label.setStyleSheet(
                """
                QLabel {
                    background-color: #2D2D2D;
                    border: 1px solid #505050;
                    border-radius: 4px;
                    padding: 8px 12px;
                    color: #E0E0E0;
                    font-weight: bold;
                }
            """
            )
            stats_layout.addWidget(label)

        stats_layout.addStretch()
        main_layout.addLayout(stats_layout)

        # 商品列表表格
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(10)
        self.products_table.setHorizontalHeaderLabels(
            [
                "商品标题",
                "分类",
                "品牌",
                "价格",
                "原价",
                "状态",
                "SKU数",
                "更新时间",
                "操作",
                "ID",
            ]
        )

        # 设置表格样式
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        self.products_table.horizontalHeader().setStretchLastSection(True)
        self.products_table.setStyleSheet(
            """
            QTableWidget {
                background-color: #2D2D2D;
                alternate-background-color: #353535;
                gridline-color: #505050;
                color: #E0E0E0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #505050;
            }
            QTableWidget::item:selected {
                background-color: #0078D4;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #E0E0E0;
                padding: 8px;
                border: 1px solid #505050;
                font-weight: bold;
            }
        """
        )

        # 隐藏ID列
        self.products_table.setColumnHidden(9, True)

        # 双击编辑商品
        self.products_table.doubleClicked.connect(self.edit_selected_product)

        main_layout.addWidget(self.products_table)

        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)

    def load_products(self):
        """加载商品数据"""
        try:
            # 从数据库加载商品
            query = """
                SELECT p.*, s.name as store_name, pl.name as platform_name
                FROM products p
                LEFT JOIN stores s ON p.store_id = s.id
                LEFT JOIN platforms pl ON s.platform_id = pl.id
                ORDER BY p.updated_at DESC
                LIMIT 1000
            """
            self.products_data = self.db_manager.execute_query(query)
            self.update_products_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"加载商品数据失败: {e}")
            QMessageBox.warning(self, "错误", f"加载商品数据失败: {e}")

    def update_products_table(self):
        """更新商品表格"""
        self.products_table.setRowCount(len(self.products_data))

        for row, product in enumerate(self.products_data):
            # 商品标题
            title = str(product.get("title", ""))
            if len(title) > 30:
                title = title[:30] + "..."
            self.products_table.setItem(row, 0, QTableWidgetItem(title))

            # 分类
            self.products_table.setItem(
                row, 1, QTableWidgetItem(str(product.get("category", "")))
            )

            # 品牌
            self.products_table.setItem(
                row, 2, QTableWidgetItem(str(product.get("brand", "")))
            )

            # 价格
            price = f"¥{float(product.get('price', 0)):.2f}"
            self.products_table.setItem(row, 3, QTableWidgetItem(price))

            # 原价
            original_price = f"¥{float(product.get('original_price', 0)):.2f}"
            self.products_table.setItem(row, 4, QTableWidgetItem(original_price))

            # 状态
            status_item = QTableWidgetItem(str(product.get("status", "")))
            status_color = self.get_status_color(product.get("status", ""))
            status_item.setForeground(QColor(status_color))
            self.products_table.setItem(row, 5, status_item)

            # SKU数量
            self.products_table.setItem(
                row, 6, QTableWidgetItem(str(product.get("sku_count", 0)))
            )

            # 更新时间
            self.products_table.setItem(
                row, 7, QTableWidgetItem(str(product.get("updated_at", "")))
            )

            # 操作按钮
            action_widget = QWidget()
            action_layout = QHBoxLayout(action_widget)
            action_layout.setContentsMargins(2, 2, 2, 2)
            action_layout.setSpacing(4)

            edit_btn = QPushButton("编辑")
            edit_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #0078D4;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """
            )
            edit_btn.clicked.connect(lambda checked, r=row: self.edit_product_by_row(r))
            action_layout.addWidget(edit_btn)

            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 2px 6px;
                    border-radius: 3px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
            """
            )
            delete_btn.clicked.connect(
                lambda checked, r=row: self.delete_product_by_row(r)
            )
            action_layout.addWidget(delete_btn)

            self.products_table.setCellWidget(row, 8, action_widget)

            # ID (隐藏)
            self.products_table.setItem(
                row, 9, QTableWidgetItem(str(product.get("id", "")))
            )

    def get_status_color(self, status: str) -> str:
        """获取状态颜色"""
        status_colors = {
            "active": "#28a745",
            "inactive": "#dc3545",
            "draft": "#ffc107",
        }
        return status_colors.get(status, "#E0E0E0")

    def update_statistics(self):
        """更新统计信息"""
        total = len(self.products_data)
        active = len([p for p in self.products_data if p.get("status") == "active"])
        inactive = len([p for p in self.products_data if p.get("status") == "inactive"])
        draft = len([p for p in self.products_data if p.get("status") == "draft"])

        self.total_label.setText(f"总商品: {total}")
        self.active_label.setText(f"上架中: {active}")
        self.inactive_label.setText(f"已下架: {inactive}")
        self.draft_label.setText(f"草稿: {draft}")

    def search_products(self, search_params: Dict[str, Any]):
        """搜索商品"""
        try:
            conditions = []
            params = []

            if search_params.get("title"):
                conditions.append("p.title LIKE ?")
                params.append(f"%{search_params['title']}%")

            if search_params.get("category"):
                conditions.append("p.category = ?")
                params.append(search_params["category"])

            if search_params.get("status"):
                status_map = {"上架": "active", "下架": "inactive", "草稿": "draft"}
                status = status_map.get(
                    search_params["status"], search_params["status"]
                )
                conditions.append("p.status = ?")
                params.append(status)

            if search_params.get("brand"):
                conditions.append("p.brand LIKE ?")
                params.append(f"%{search_params['brand']}%")

            where_clause = " AND ".join(conditions) if conditions else "1=1"

            query = f"""
                SELECT p.*, s.name as store_name, pl.name as platform_name
                FROM products p
                LEFT JOIN stores s ON p.store_id = s.id
                LEFT JOIN platforms pl ON s.platform_id = pl.id
                WHERE {where_clause}
                ORDER BY p.updated_at DESC
                LIMIT 1000
            """

            self.products_data = self.db_manager.execute_query(query, tuple(params))
            self.update_products_table()
            self.update_statistics()

        except Exception as e:
            logger.error(f"搜索商品失败: {e}")
            QMessageBox.warning(self, "错误", f"搜索商品失败: {e}")

    def add_product(self):
        """添加商品"""
        dialog = ProductEditDialog(parent=self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                product_data = dialog.get_product_data()

                # 添加时间戳
                now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                product_data.update(
                    {
                        "store_id": 1,  # 默认店铺ID，实际应该从用户选择获取
                        "platform_product_id": f"PROD_{int(datetime.now().timestamp())}",
                        "created_at": now,
                        "updated_at": now,
                    }
                )

                # 插入数据库
                columns = ", ".join(product_data.keys())
                placeholders = ", ".join(["?" for _ in product_data])
                query = f"INSERT INTO products ({columns}) VALUES ({placeholders})"

                self.db_manager.execute_update(query, tuple(product_data.values()))

                QMessageBox.information(self, "成功", "商品添加成功！")
                self.refresh_data()

            except Exception as e:
                logger.error(f"添加商品失败: {e}")
                QMessageBox.warning(self, "错误", f"添加商品失败: {e}")

    def edit_selected_product(self):
        """编辑选中的商品"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            self.edit_product_by_row(current_row)

    def edit_product_by_row(self, row: int):
        """通过行号编辑商品"""
        if 0 <= row < len(self.products_data):
            product_data = self.products_data[row]
            dialog = ProductEditDialog(product_data, self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                try:
                    updated_data = dialog.get_product_data()
                    updated_data["updated_at"] = datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )

                    # 更新数据库
                    set_clause = ", ".join(
                        [f"{key} = ?" for key in updated_data.keys()]
                    )
                    query = f"UPDATE products SET {set_clause} WHERE id = ?"
                    params = list(updated_data.values()) + [product_data["id"]]

                    self.db_manager.execute_update(query, tuple(params))

                    QMessageBox.information(self, "成功", "商品更新成功！")
                    self.refresh_data()

                except Exception as e:
                    logger.error(f"更新商品失败: {e}")
                    QMessageBox.warning(self, "错误", f"更新商品失败: {e}")

    def delete_product_by_row(self, row: int):
        """通过行号删除商品"""
        if 0 <= row < len(self.products_data):
            product_data = self.products_data[row]
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除商品 '{product_data.get('title', '')}' 吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                try:
                    query = "DELETE FROM products WHERE id = ?"
                    self.db_manager.execute_update(query, (product_data["id"],))

                    QMessageBox.information(self, "成功", "商品删除成功！")
                    self.refresh_data()

                except Exception as e:
                    logger.error(f"删除商品失败: {e}")
                    QMessageBox.warning(self, "错误", f"删除商品失败: {e}")

    def sync_products(self):
        """同步商品数据"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 无限进度条

            # 这里应该调用API同步商品
            # 暂时使用模拟数据
            QTimer.singleShot(2000, self.sync_completed)

        except Exception as e:
            logger.error(f"同步商品失败: {e}")
            QMessageBox.warning(self, "错误", f"同步商品失败: {e}")
            self.progress_bar.setVisible(False)

    def sync_completed(self):
        """同步完成"""
        self.progress_bar.setVisible(False)
        QMessageBox.information(self, "成功", "商品同步完成！")
        self.refresh_data()

    def refresh_data(self):
        """刷新数据"""
        self.load_products()

    def auto_refresh_data(self):
        """自动刷新数据"""
        self.refresh_data()
