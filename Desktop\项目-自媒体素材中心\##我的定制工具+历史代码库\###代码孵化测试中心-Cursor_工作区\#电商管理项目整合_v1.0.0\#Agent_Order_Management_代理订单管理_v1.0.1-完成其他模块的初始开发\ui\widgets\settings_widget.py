# -*- coding: utf-8 -*-
"""
设置组件
用于配置系统设置和API参数
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QLabel, QLineEdit, QSpinBox, QCheckBox, QComboBox, QPushButton,
    QGroupBox, QMessageBox, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from core.config_manager import ConfigManager
from utils.logger import get_logger

logger = get_logger(__name__)


class SettingsWidget(QWidget):
    """设置组件"""
    
    settings_changed = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.config_manager = ConfigManager()
        self.init_ui()
        self.load_settings()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)

        # 标题
        title_label = QLabel("系统设置")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2196F3; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #3C3C3C;
                background-color: #2B2B2B;
            }
            QTabBar::tab {
                background-color: #3C3C3C;
                color: #E0E0E0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #2196F3;
                color: white;
            }
        """)

        # 创建设置选项卡
        self.create_api_tab()
        self.create_ui_tab()

        layout.addWidget(self.tab_widget)

        # 底部按钮
        self.create_bottom_buttons(layout)

    def create_api_tab(self):
        """创建API设置选项卡"""
        api_widget = QWidget()
        scroll = QScrollArea()
        scroll.setWidget(api_widget)
        scroll.setWidgetResizable(True)
        
        layout = QVBoxLayout(api_widget)
        layout.setSpacing(15)

        # 1688 API设置
        api1688_group = QGroupBox("阿里巴巴1688 API设置")
        api1688_layout = QFormLayout(api1688_group)

        self.api1688_enabled = QCheckBox("启用1688 API")
        api1688_layout.addRow(self.api1688_enabled)

        self.api1688_app_key = QLineEdit()
        self.api1688_app_key.setPlaceholderText("输入App Key")
        api1688_layout.addRow("App Key:", self.api1688_app_key)

        self.api1688_app_secret = QLineEdit()
        self.api1688_app_secret.setEchoMode(QLineEdit.EchoMode.Password)
        self.api1688_app_secret.setPlaceholderText("输入App Secret")
        api1688_layout.addRow("App Secret:", self.api1688_app_secret)

        self.api1688_access_token = QLineEdit()
        self.api1688_access_token.setPlaceholderText("输入Access Token")
        api1688_layout.addRow("Access Token:", self.api1688_access_token)

        layout.addWidget(api1688_group)

        # API请求设置
        request_group = QGroupBox("API请求设置")
        request_layout = QFormLayout(request_group)

        self.request_timeout = QSpinBox()
        self.request_timeout.setRange(5, 120)
        self.request_timeout.setSuffix(" 秒")
        request_layout.addRow("请求超时:", self.request_timeout)

        self.retry_times = QSpinBox()
        self.retry_times.setRange(1, 10)
        request_layout.addRow("重试次数:", self.retry_times)

        layout.addWidget(request_group)

        layout.addStretch()
        self.tab_widget.addTab(scroll, "API设置")

    def create_ui_tab(self):
        """创建界面设置选项卡"""
        ui_widget = QWidget()
        scroll = QScrollArea()
        scroll.setWidget(ui_widget)
        scroll.setWidgetResizable(True)
        
        layout = QVBoxLayout(ui_widget)
        layout.setSpacing(15)

        # 外观设置
        appearance_group = QGroupBox("外观设置")
        appearance_layout = QFormLayout(appearance_group)

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["深色主题", "浅色主题"])
        appearance_layout.addRow("主题:", self.theme_combo)

        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        appearance_layout.addRow("语言:", self.language_combo)

        layout.addWidget(appearance_group)

        # 数据设置
        data_group = QGroupBox("数据设置")
        data_layout = QFormLayout(data_group)

        self.auto_save_interval = QSpinBox()
        self.auto_save_interval.setRange(60, 3600)
        self.auto_save_interval.setSuffix(" 秒")
        data_layout.addRow("自动保存间隔:", self.auto_save_interval)

        self.sync_interval = QSpinBox()
        self.sync_interval.setRange(60, 3600)
        self.sync_interval.setSuffix(" 秒")
        data_layout.addRow("同步间隔:", self.sync_interval)

        layout.addWidget(data_group)

        layout.addStretch()
        self.tab_widget.addTab(scroll, "界面设置")

    def create_bottom_buttons(self, layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        # 保存按钮
        save_btn = QPushButton("保存设置")
        save_btn.clicked.connect(self.save_settings)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(save_btn)

        layout.addLayout(button_layout)

    def load_settings(self):
        """加载设置"""
        try:
            # 加载API设置
            api_config = self.config_manager.get_config('api')
            alibaba_config = api_config.get('alibaba_1688', {})
            
            self.api1688_enabled.setChecked(alibaba_config.get('enabled', False))
            self.api1688_app_key.setText(alibaba_config.get('app_key', ''))
            self.api1688_app_secret.setText(alibaba_config.get('app_secret', ''))
            self.api1688_access_token.setText(alibaba_config.get('access_token', ''))

            # 加载网络设置
            network_config = self.config_manager.get_config('network')
            self.request_timeout.setValue(network_config.get('timeout', 30))
            self.retry_times.setValue(network_config.get('retry_times', 3))

            # 加载界面设置
            ui_config = self.config_manager.get_config('ui')
            theme_map = {"dark": 0, "light": 1}
            self.theme_combo.setCurrentIndex(theme_map.get(ui_config.get('theme', 'dark'), 0))
            
            self.auto_save_interval.setValue(ui_config.get('auto_save_interval', 300))

            logger.info("设置加载完成")

        except Exception as e:
            logger.error(f"加载设置失败: {e}")

    def save_settings(self):
        """保存设置"""
        try:
            # 保存API设置
            api_config = {
                'alibaba_1688': {
                    'enabled': self.api1688_enabled.isChecked(),
                    'app_key': self.api1688_app_key.text().strip(),
                    'app_secret': self.api1688_app_secret.text().strip(),
                    'access_token': self.api1688_access_token.text().strip(),
                }
            }
            self.config_manager.set_config('api', api_config)

            # 保存界面设置
            theme_map = {0: "dark", 1: "light"}
            ui_config = {
                'theme': theme_map.get(self.theme_combo.currentIndex(), 'dark'),
                'auto_save_interval': self.auto_save_interval.value(),
            }
            self.config_manager.update_config('ui', ui_config)

            # 保存网络设置
            network_config = {
                'timeout': self.request_timeout.value(),
                'retry_times': self.retry_times.value(),
            }
            self.config_manager.update_config('network', network_config)

            QMessageBox.information(self, "成功", "设置已保存！")
            self.settings_changed.emit()
            logger.info("设置保存完成")

        except Exception as e:
            logger.error(f"保存设置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        self.load_settings()

    def auto_refresh_data(self):
        """自动刷新数据"""
        pass
