#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证器
提供数据验证、清理、格式化等功能
"""

import re
import json
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime
from decimal import Decimal, InvalidOperation


class ValidationError(Exception):
    """数据验证异常"""

    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message)
        self.message = message
        self.field = field
        self.value = value

    def __str__(self):
        if self.field:
            return f"字段 '{self.field}' 验证失败: {self.message}"
        return self.message


class DataValidator:
    """
    数据验证器
    提供各种数据验证和清理功能
    """

    # 常用正则表达式
    PATTERNS = {
        "email": re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"),
        "phone": re.compile(r"^1[3-9]\d{9}$"),  # 中国手机号
        "id_card": re.compile(r"^\d{17}[\dXx]$"),  # 身份证号
        "url": re.compile(
            r"^https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?$"
        ),
        "ip": re.compile(
            r"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
        ),
        "chinese": re.compile(r"^[\u4e00-\u9fa5]+$"),  # 中文字符
        "alphanumeric": re.compile(r"^[a-zA-Z0-9]+$"),  # 字母数字
        "numeric": re.compile(r"^\d+$"),  # 纯数字
        "decimal": re.compile(r"^\d+(\.\d+)?$"),  # 小数
    }

    @staticmethod
    def is_required(value: Any, field_name: str = None) -> Any:
        """
        验证必填字段

        Args:
            value: 待验证值
            field_name: 字段名

        Returns:
            Any: 验证通过的值

        Raises:
            ValidationError: 验证失败
        """
        if value is None or (isinstance(value, str) and value.strip() == ""):
            raise ValidationError("字段不能为空", field_name, value)
        return value

    @staticmethod
    def validate_string(
        value: Any,
        min_length: int = 0,
        max_length: int = None,
        pattern: str = None,
        field_name: str = None,
    ) -> str:
        """
        验证字符串

        Args:
            value: 待验证值
            min_length: 最小长度
            max_length: 最大长度
            pattern: 正则表达式模式
            field_name: 字段名

        Returns:
            str: 验证通过的字符串

        Raises:
            ValidationError: 验证失败
        """
        if not isinstance(value, str):
            if value is None:
                raise ValidationError("字符串不能为空", field_name, value)
            value = str(value)

        # 长度验证
        if len(value) < min_length:
            raise ValidationError(
                f"字符串长度不能少于{min_length}个字符", field_name, value
            )

        if max_length is not None and len(value) > max_length:
            raise ValidationError(
                f"字符串长度不能超过{max_length}个字符", field_name, value
            )

        # 模式验证
        if pattern:
            if isinstance(pattern, str):
                pattern = re.compile(pattern)
            if not pattern.match(value):
                raise ValidationError("字符串格式不正确", field_name, value)

        return value

    @staticmethod
    def validate_integer(
        value: Any, min_value: int = None, max_value: int = None, field_name: str = None
    ) -> int:
        """
        验证整数

        Args:
            value: 待验证值
            min_value: 最小值
            max_value: 最大值
            field_name: 字段名

        Returns:
            int: 验证通过的整数

        Raises:
            ValidationError: 验证失败
        """
        if isinstance(value, str):
            if not value.strip():
                raise ValidationError("整数不能为空", field_name, value)
            try:
                value = int(value.strip())
            except ValueError:
                raise ValidationError("不是有效的整数", field_name, value)
        elif not isinstance(value, int):
            try:
                value = int(value)
            except (ValueError, TypeError):
                raise ValidationError("不是有效的整数", field_name, value)

        # 范围验证
        if min_value is not None and value < min_value:
            raise ValidationError(f"整数不能小于{min_value}", field_name, value)

        if max_value is not None and value > max_value:
            raise ValidationError(f"整数不能大于{max_value}", field_name, value)

        return value

    @staticmethod
    def validate_float(
        value: Any,
        min_value: float = None,
        max_value: float = None,
        precision: int = None,
        field_name: str = None,
    ) -> float:
        """
        验证浮点数

        Args:
            value: 待验证值
            min_value: 最小值
            max_value: 最大值
            precision: 小数位数
            field_name: 字段名

        Returns:
            float: 验证通过的浮点数

        Raises:
            ValidationError: 验证失败
        """
        if isinstance(value, str):
            if not value.strip():
                raise ValidationError("数字不能为空", field_name, value)
            try:
                value = float(value.strip())
            except ValueError:
                raise ValidationError("不是有效的数字", field_name, value)
        elif not isinstance(value, (int, float)):
            try:
                value = float(value)
            except (ValueError, TypeError):
                raise ValidationError("不是有效的数字", field_name, value)

        # 范围验证
        if min_value is not None and value < min_value:
            raise ValidationError(f"数字不能小于{min_value}", field_name, value)

        if max_value is not None and value > max_value:
            raise ValidationError(f"数字不能大于{max_value}", field_name, value)

        # 精度验证
        if precision is not None:
            decimal_places = len(str(value).split(".")[1]) if "." in str(value) else 0
            if decimal_places > precision:
                raise ValidationError(
                    f"小数位数不能超过{precision}位", field_name, value
                )

        return float(value)

    @staticmethod
    def validate_decimal(
        value: Any,
        min_value: Decimal = None,
        max_value: Decimal = None,
        precision: int = None,
        field_name: str = None,
    ) -> Decimal:
        """
        验证高精度小数

        Args:
            value: 待验证值
            min_value: 最小值
            max_value: 最大值
            precision: 小数位数
            field_name: 字段名

        Returns:
            Decimal: 验证通过的小数

        Raises:
            ValidationError: 验证失败
        """
        try:
            if isinstance(value, str):
                value = value.strip()
            decimal_value = Decimal(str(value))
        except (InvalidOperation, ValueError):
            raise ValidationError("不是有效的数字", field_name, value)

        # 范围验证
        if min_value is not None and decimal_value < min_value:
            raise ValidationError(f"数字不能小于{min_value}", field_name, value)

        if max_value is not None and decimal_value > max_value:
            raise ValidationError(f"数字不能大于{max_value}", field_name, value)

        # 精度验证
        if precision is not None:
            _, digits, exponent = decimal_value.as_tuple()
            if exponent < -precision:
                raise ValidationError(
                    f"小数位数不能超过{precision}位", field_name, value
                )

        return decimal_value

    @staticmethod
    def validate_email(value: Any, field_name: str = None) -> str:
        """
        验证邮箱地址

        Args:
            value: 待验证值
            field_name: 字段名

        Returns:
            str: 验证通过的邮箱地址

        Raises:
            ValidationError: 验证失败
        """
        if not isinstance(value, str):
            raise ValidationError("邮箱地址必须是字符串", field_name, value)

        value = value.strip().lower()

        if not DataValidator.PATTERNS["email"].match(value):
            raise ValidationError("邮箱地址格式不正确", field_name, value)

        return value

    @staticmethod
    def validate_phone(value: Any, field_name: str = None) -> str:
        """
        验证手机号码

        Args:
            value: 待验证值
            field_name: 字段名

        Returns:
            str: 验证通过的手机号码

        Raises:
            ValidationError: 验证失败
        """
        if not isinstance(value, str):
            value = str(value)

        value = value.strip()

        if not DataValidator.PATTERNS["phone"].match(value):
            raise ValidationError("手机号码格式不正确", field_name, value)

        return value

    @staticmethod
    def validate_url(value: Any, field_name: str = None) -> str:
        """
        验证URL

        Args:
            value: 待验证值
            field_name: 字段名

        Returns:
            str: 验证通过的URL

        Raises:
            ValidationError: 验证失败
        """
        if not isinstance(value, str):
            raise ValidationError("URL必须是字符串", field_name, value)

        value = value.strip()

        if not DataValidator.PATTERNS["url"].match(value):
            raise ValidationError("URL格式不正确", field_name, value)

        return value

    @staticmethod
    def validate_datetime(
        value: Any, format_string: str = "%Y-%m-%d %H:%M:%S", field_name: str = None
    ) -> datetime:
        """
        验证日期时间

        Args:
            value: 待验证值
            format_string: 日期时间格式
            field_name: 字段名

        Returns:
            datetime: 验证通过的日期时间

        Raises:
            ValidationError: 验证失败
        """
        if isinstance(value, datetime):
            return value

        if not isinstance(value, str):
            raise ValidationError(
                "日期时间必须是字符串或datetime对象", field_name, value
            )

        value = value.strip()

        try:
            return datetime.strptime(value, format_string)
        except ValueError:
            raise ValidationError(
                f"日期时间格式不正确，期望格式: {format_string}", field_name, value
            )

    @staticmethod
    def validate_choice(value: Any, choices: List[Any], field_name: str = None) -> Any:
        """
        验证选择项

        Args:
            value: 待验证值
            choices: 可选值列表
            field_name: 字段名

        Returns:
            Any: 验证通过的值

        Raises:
            ValidationError: 验证失败
        """
        if value not in choices:
            raise ValidationError(f"值必须是以下选项之一: {choices}", field_name, value)

        return value

    @staticmethod
    def validate_list(
        value: Any,
        item_validator: Callable = None,
        min_length: int = 0,
        max_length: int = None,
        field_name: str = None,
    ) -> List[Any]:
        """
        验证列表

        Args:
            value: 待验证值
            item_validator: 列表项验证函数
            min_length: 最小长度
            max_length: 最大长度
            field_name: 字段名

        Returns:
            List[Any]: 验证通过的列表

        Raises:
            ValidationError: 验证失败
        """
        if not isinstance(value, list):
            raise ValidationError("值必须是列表", field_name, value)

        # 长度验证
        if len(value) < min_length:
            raise ValidationError(
                f"列表长度不能少于{min_length}个元素", field_name, value
            )

        if max_length is not None and len(value) > max_length:
            raise ValidationError(
                f"列表长度不能超过{max_length}个元素", field_name, value
            )

        # 列表项验证
        if item_validator:
            validated_items = []
            for i, item in enumerate(value):
                try:
                    validated_item = item_validator(item)
                    validated_items.append(validated_item)
                except ValidationError as e:
                    raise ValidationError(
                        f"列表第{i+1}项验证失败: {e.message}", field_name, item
                    )
            return validated_items

        return value

    @staticmethod
    def validate_dict(
        value: Any,
        schema: Dict[str, Callable] = None,
        required_keys: List[str] = None,
        field_name: str = None,
    ) -> Dict[str, Any]:
        """
        验证字典

        Args:
            value: 待验证值
            schema: 字段验证规则
            required_keys: 必需字段列表
            field_name: 字段名

        Returns:
            Dict[str, Any]: 验证通过的字典

        Raises:
            ValidationError: 验证失败
        """
        if not isinstance(value, dict):
            raise ValidationError("值必须是字典", field_name, value)

        validated_dict = {}

        # 检查必需字段
        if required_keys:
            for key in required_keys:
                if key not in value:
                    raise ValidationError(f"缺少必需字段: {key}", field_name, value)

        # 验证字段
        if schema:
            for key, validator in schema.items():
                if key in value:
                    try:
                        validated_dict[key] = validator(value[key])
                    except ValidationError as e:
                        raise ValidationError(
                            f"字段'{key}'验证失败: {e.message}", field_name, value[key]
                        )

            # 保留未在schema中定义的字段
            for key, val in value.items():
                if key not in schema:
                    validated_dict[key] = val
        else:
            validated_dict = value.copy()

        return validated_dict

    @staticmethod
    def clean_string(
        value: str,
        strip: bool = True,
        remove_extra_spaces: bool = True,
        to_lower: bool = False,
        to_upper: bool = False,
    ) -> str:
        """
        清理字符串

        Args:
            value: 待清理字符串
            strip: 是否去除首尾空格
            remove_extra_spaces: 是否移除多余空格
            to_lower: 是否转为小写
            to_upper: 是否转为大写

        Returns:
            str: 清理后的字符串
        """
        if not isinstance(value, str):
            value = str(value)

        if strip:
            value = value.strip()

        if remove_extra_spaces:
            value = re.sub(r"\s+", " ", value)

        if to_lower:
            value = value.lower()
        elif to_upper:
            value = value.upper()

        return value

    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """
        清理文件名，移除非法字符

        Args:
            filename: 原始文件名

        Returns:
            str: 清理后的文件名
        """
        # 移除或替换非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        filename = re.sub(illegal_chars, "_", filename)

        # 移除首尾空格和点
        filename = filename.strip(". ")

        # 限制长度
        if len(filename) > 255:
            name, ext = filename.rsplit(".", 1) if "." in filename else (filename, "")
            max_name_length = 255 - len(ext) - 1 if ext else 255
            filename = name[:max_name_length] + ("." + ext if ext else "")

        return filename or "untitled"

    @staticmethod
    def validate_json(value: Any, field_name: str = None) -> Dict[str, Any]:
        """
        验证JSON数据

        Args:
            value: 待验证值
            field_name: 字段名

        Returns:
            Dict[str, Any]: 解析后的JSON数据

        Raises:
            ValidationError: 验证失败
        """
        if isinstance(value, dict):
            return value

        if not isinstance(value, str):
            raise ValidationError("JSON数据必须是字符串或字典", field_name, value)

        try:
            return json.loads(value)
        except json.JSONDecodeError as e:
            raise ValidationError(f"JSON格式不正确: {e}", field_name, value)

    @classmethod
    def validate_schema(
        cls, data: Dict[str, Any], schema: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        根据schema验证数据

        Args:
            data: 待验证数据
            schema: 验证规则

        Returns:
            Dict[str, Any]: 验证通过的数据

        Raises:
            ValidationError: 验证失败

        Schema格式示例:
        {
            'field_name': {
                'type': 'string',  # 数据类型
                'required': True,  # 是否必需
                'min_length': 1,   # 最小长度
                'max_length': 100, # 最大长度
                'pattern': r'^[a-zA-Z]+$',  # 正则模式
                'choices': ['option1', 'option2'],  # 选择项
                'validator': custom_validator_function  # 自定义验证函数
            }
        }
        """
        validated_data = {}

        # 检查必需字段
        for field_name, rules in schema.items():
            if rules.get("required", False) and field_name not in data:
                raise ValidationError(f"缺少必需字段: {field_name}")

        # 验证每个字段
        for field_name, value in data.items():
            if field_name not in schema:
                # 如果字段不在schema中，直接保留
                validated_data[field_name] = value
                continue

            rules = schema[field_name]

            try:
                # 跳过空值（除非是必需字段）
                if value is None or (isinstance(value, str) and value.strip() == ""):
                    if rules.get("required", False):
                        raise ValidationError("字段不能为空", field_name, value)
                    validated_data[field_name] = value
                    continue

                # 根据类型验证
                field_type = rules.get("type", "string")

                if field_type == "string":
                    validated_value = cls.validate_string(
                        value,
                        min_length=rules.get("min_length", 0),
                        max_length=rules.get("max_length"),
                        pattern=rules.get("pattern"),
                        field_name=field_name,
                    )
                elif field_type == "integer":
                    validated_value = cls.validate_integer(
                        value,
                        min_value=rules.get("min_value"),
                        max_value=rules.get("max_value"),
                        field_name=field_name,
                    )
                elif field_type == "float":
                    validated_value = cls.validate_float(
                        value,
                        min_value=rules.get("min_value"),
                        max_value=rules.get("max_value"),
                        precision=rules.get("precision"),
                        field_name=field_name,
                    )
                elif field_type == "email":
                    validated_value = cls.validate_email(value, field_name)
                elif field_type == "phone":
                    validated_value = cls.validate_phone(value, field_name)
                elif field_type == "url":
                    validated_value = cls.validate_url(value, field_name)
                elif field_type == "datetime":
                    validated_value = cls.validate_datetime(
                        value,
                        format_string=rules.get("format", "%Y-%m-%d %H:%M:%S"),
                        field_name=field_name,
                    )
                elif field_type == "list":
                    validated_value = cls.validate_list(
                        value,
                        item_validator=rules.get("item_validator"),
                        min_length=rules.get("min_length", 0),
                        max_length=rules.get("max_length"),
                        field_name=field_name,
                    )
                elif field_type == "dict":
                    validated_value = cls.validate_dict(
                        value,
                        schema=rules.get("schema"),
                        required_keys=rules.get("required_keys"),
                        field_name=field_name,
                    )
                else:
                    validated_value = value

                # 选择项验证
                if "choices" in rules:
                    validated_value = cls.validate_choice(
                        validated_value, rules["choices"], field_name
                    )

                # 自定义验证器
                if "validator" in rules:
                    validated_value = rules["validator"](validated_value, field_name)

                validated_data[field_name] = validated_value

            except ValidationError:
                raise
            except Exception as e:
                raise ValidationError(f"字段验证异常: {e}", field_name, value)

        return validated_data
