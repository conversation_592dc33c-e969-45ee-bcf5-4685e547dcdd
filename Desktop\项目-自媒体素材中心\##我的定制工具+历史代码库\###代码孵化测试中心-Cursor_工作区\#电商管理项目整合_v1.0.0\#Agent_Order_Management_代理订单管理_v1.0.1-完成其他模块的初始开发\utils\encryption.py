#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密管理
提供敏感数据加密存储功能，如API密钥、Token等
"""

import os
import base64
import hashlib
import secrets
from typing import Optional, Union
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class EncryptionManager:
    """
    加密管理器
    提供数据加密、解密、哈希等功能
    """

    def __init__(self, key: Optional[Union[str, bytes]] = None):
        """
        初始化加密管理器

        Args:
            key: 加密密钥，如果为None则自动生成
        """
        if key is None:
            self.key = self.generate_key()
        elif isinstance(key, str):
            self.key = key.encode("utf-8")
        else:
            self.key = key

        self.fernet = Fernet(self._derive_fernet_key(self.key))

    @staticmethod
    def generate_key() -> bytes:
        """
        生成随机密钥

        Returns:
            bytes: 32字节的随机密钥
        """
        return secrets.token_bytes(32)

    @staticmethod
    def _derive_fernet_key(key: bytes) -> bytes:
        """
        从原始密钥派生Fernet密钥

        Args:
            key: 原始密钥

        Returns:
            bytes: Fernet格式的密钥
        """
        # 使用固定的盐值（在生产环境中应该使用随机盐值并存储）
        salt = b"ali1688_erp_salt"
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(key))
        return key

    def encrypt(self, data: Union[str, bytes]) -> str:
        """
        加密数据

        Args:
            data: 待加密数据

        Returns:
            str: Base64编码的加密数据
        """
        if isinstance(data, str):
            data = data.encode("utf-8")

        encrypted_data = self.fernet.encrypt(data)
        return base64.urlsafe_b64encode(encrypted_data).decode("utf-8")

    def decrypt(self, encrypted_data: str) -> str:
        """
        解密数据

        Args:
            encrypted_data: Base64编码的加密数据

        Returns:
            str: 解密后的数据

        Raises:
            Exception: 解密失败
        """
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode("utf-8"))
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode("utf-8")
        except Exception as e:
            raise Exception(f"解密失败: {e}")

    def encrypt_dict(self, data: dict, fields_to_encrypt: list = None) -> dict:
        """
        加密字典中的指定字段

        Args:
            data: 待加密的字典
            fields_to_encrypt: 需要加密的字段列表，如果为None则加密所有字段

        Returns:
            dict: 加密后的字典
        """
        encrypted_data = data.copy()

        if fields_to_encrypt is None:
            fields_to_encrypt = list(data.keys())

        for field in fields_to_encrypt:
            if field in encrypted_data and encrypted_data[field] is not None:
                encrypted_data[field] = self.encrypt(str(encrypted_data[field]))

        return encrypted_data

    def decrypt_dict(
        self, encrypted_data: dict, fields_to_decrypt: list = None
    ) -> dict:
        """
        解密字典中的指定字段

        Args:
            encrypted_data: 加密的字典
            fields_to_decrypt: 需要解密的字段列表，如果为None则解密所有字段

        Returns:
            dict: 解密后的字典
        """
        decrypted_data = encrypted_data.copy()

        if fields_to_decrypt is None:
            fields_to_decrypt = list(encrypted_data.keys())

        for field in fields_to_decrypt:
            if field in decrypted_data and decrypted_data[field] is not None:
                try:
                    decrypted_data[field] = self.decrypt(decrypted_data[field])
                except Exception:
                    # 如果解密失败，保留原值（可能已经是明文）
                    pass

        return decrypted_data

    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> tuple:
        """
        哈希密码

        Args:
            password: 明文密码
            salt: 盐值，如果为None则自动生成

        Returns:
            tuple: (哈希值, 盐值)
        """
        if salt is None:
            salt = secrets.token_hex(16)
        elif isinstance(salt, str):
            salt = salt

        # 使用PBKDF2算法
        pwdhash = hashlib.pbkdf2_hmac(
            "sha256", password.encode("utf-8"), salt.encode("utf-8"), 100000
        )  # 迭代次数

        return base64.b64encode(pwdhash).decode("utf-8"), salt

    @staticmethod
    def verify_password(password: str, hashed_password: str, salt: str) -> bool:
        """
        验证密码

        Args:
            password: 明文密码
            hashed_password: 哈希密码
            salt: 盐值

        Returns:
            bool: 验证结果
        """
        try:
            new_hash, _ = EncryptionManager.hash_password(password, salt)
            return new_hash == hashed_password
        except Exception:
            return False

    @staticmethod
    def generate_token(length: int = 32) -> str:
        """
        生成随机Token

        Args:
            length: Token长度

        Returns:
            str: 随机Token
        """
        return secrets.token_urlsafe(length)

    @staticmethod
    def hash_data(data: Union[str, bytes], algorithm: str = "sha256") -> str:
        """
        计算数据哈希值

        Args:
            data: 待哈希数据
            algorithm: 哈希算法

        Returns:
            str: 十六进制哈希值
        """
        if isinstance(data, str):
            data = data.encode("utf-8")

        hash_obj = hashlib.new(algorithm)
        hash_obj.update(data)
        return hash_obj.hexdigest()

    def save_key_to_file(self, file_path: str) -> bool:
        """
        将密钥保存到文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否成功
        """
        try:
            with open(file_path, "wb") as f:
                f.write(self.key)
            return True
        except Exception as e:
            print(f"保存密钥失败: {e}")
            return False

    @classmethod
    def load_key_from_file(cls, file_path: str) -> Optional["EncryptionManager"]:
        """
        从文件加载密钥创建加密管理器

        Args:
            file_path: 文件路径

        Returns:
            EncryptionManager: 加密管理器实例，失败时返回None
        """
        try:
            with open(file_path, "rb") as f:
                key = f.read()
            return cls(key)
        except Exception as e:
            print(f"加载密钥失败: {e}")
            return None

    def get_key_fingerprint(self) -> str:
        """
        获取密钥指纹（用于验证密钥）

        Returns:
            str: 密钥指纹
        """
        return self.hash_data(self.key)[:16]


class SecureStorage:
    """
    安全存储
    提供加密的键值存储功能
    """

    def __init__(self, encryption_manager: EncryptionManager):
        """
        初始化安全存储

        Args:
            encryption_manager: 加密管理器
        """
        self.encryption_manager = encryption_manager
        self.storage = {}

    def set(self, key: str, value: Union[str, dict]) -> None:
        """
        存储加密数据

        Args:
            key: 键
            value: 值
        """
        if isinstance(value, dict):
            import json

            value = json.dumps(value, ensure_ascii=False)

        encrypted_value = self.encryption_manager.encrypt(str(value))
        self.storage[key] = encrypted_value

    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """
        获取解密数据

        Args:
            key: 键
            default: 默认值

        Returns:
            str: 解密后的值
        """
        if key not in self.storage:
            return default

        try:
            return self.encryption_manager.decrypt(self.storage[key])
        except Exception:
            return default

    def get_dict(self, key: str, default: Optional[dict] = None) -> Optional[dict]:
        """
        获取字典类型的解密数据

        Args:
            key: 键
            default: 默认值

        Returns:
            dict: 解密后的字典
        """
        value = self.get(key)
        if value is None:
            return default

        try:
            import json

            return json.loads(value)
        except Exception:
            return default

    def delete(self, key: str) -> bool:
        """
        删除数据

        Args:
            key: 键

        Returns:
            bool: 是否成功
        """
        if key in self.storage:
            del self.storage[key]
            return True
        return False

    def exists(self, key: str) -> bool:
        """
        检查键是否存在

        Args:
            key: 键

        Returns:
            bool: 是否存在
        """
        return key in self.storage

    def clear(self) -> None:
        """清空所有数据"""
        self.storage.clear()

    def keys(self) -> list:
        """获取所有键"""
        return list(self.storage.keys())

    def save_to_file(self, file_path: str) -> bool:
        """
        保存到文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否成功
        """
        try:
            import json

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(self.storage, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False

    def load_from_file(self, file_path: str) -> bool:
        """
        从文件加载

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否成功
        """
        try:
            import json

            with open(file_path, "r", encoding="utf-8") as f:
                self.storage = json.load(f)
            return True
        except Exception as e:
            print(f"加载文件失败: {e}")
            return False


# 全局加密管理器实例
_encryption_manager = None
_secure_storage = None


def get_encryption_manager(
    key: Optional[Union[str, bytes]] = None,
) -> EncryptionManager:
    """
    获取全局加密管理器实例

    Args:
        key: 加密密钥

    Returns:
        EncryptionManager: 加密管理器实例
    """
    global _encryption_manager
    if _encryption_manager is None or key is not None:
        _encryption_manager = EncryptionManager(key)
    return _encryption_manager


def get_secure_storage() -> SecureStorage:
    """
    获取全局安全存储实例

    Returns:
        SecureStorage: 安全存储实例
    """
    global _secure_storage
    if _secure_storage is None:
        _secure_storage = SecureStorage(get_encryption_manager())
    return _secure_storage


def init_encryption_from_env() -> EncryptionManager:
    """
    从环境变量初始化加密管理器

    Returns:
        EncryptionManager: 加密管理器实例
    """
    # 尝试从环境变量获取密钥
    env_key = os.getenv("ALI1688_ENCRYPTION_KEY")

    if env_key:
        # 如果环境变量中有密钥，使用它
        key = base64.urlsafe_b64decode(env_key.encode("utf-8"))
    else:
        # 否则生成新密钥并保存到环境变量提示
        key = EncryptionManager.generate_key()
        encoded_key = base64.urlsafe_b64encode(key).decode("utf-8")
        print(f"请将以下密钥添加到环境变量 ALI1688_ENCRYPTION_KEY: {encoded_key}")

    return get_encryption_manager(key)
