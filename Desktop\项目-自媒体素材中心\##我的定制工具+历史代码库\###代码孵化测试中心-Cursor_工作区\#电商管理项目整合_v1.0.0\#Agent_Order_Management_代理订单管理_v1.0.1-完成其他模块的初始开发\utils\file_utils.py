#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理工具
提供文件操作、备份、压缩、清理等功能
"""

import os
import shutil
import zipfile
import json
import csv
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import hashlib


class FileManager:
    """
    文件管理器
    提供统一的文件操作接口
    """

    def __init__(self, base_dir: Optional[Union[str, Path]] = None):
        """
        初始化文件管理器

        Args:
            base_dir: 基础目录
        """
        self.base_dir = Path(base_dir) if base_dir else Path.cwd()

    def ensure_dir(self, dir_path: Union[str, Path]) -> Path:
        """
        确保目录存在，不存在则创建

        Args:
            dir_path: 目录路径

        Returns:
            Path: 目录路径对象
        """
        path = Path(dir_path)
        if not path.is_absolute():
            path = self.base_dir / path

        path.mkdir(parents=True, exist_ok=True)
        return path

    def safe_write_file(
        self, file_path: Union[str, Path], content: str, encoding: str = "utf-8"
    ) -> bool:
        """
        安全写入文件（先写入临时文件，再重命名）

        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 编码格式

        Returns:
            bool: 是否成功
        """
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.base_dir / path

            # 确保目录存在
            self.ensure_dir(path.parent)

            # 写入临时文件
            temp_path = path.with_suffix(path.suffix + ".tmp")

            with open(temp_path, "w", encoding=encoding) as f:
                f.write(content)

            # 原子性重命名
            temp_path.replace(path)
            return True

        except Exception as e:
            print(f"写入文件失败: {e}")
            return False

    def safe_read_file(
        self, file_path: Union[str, Path], encoding: str = "utf-8"
    ) -> Optional[str]:
        """
        安全读取文件

        Args:
            file_path: 文件路径
            encoding: 编码格式

        Returns:
            str: 文件内容，失败时返回None
        """
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.base_dir / path

            if not path.exists():
                return None

            with open(path, "r", encoding=encoding) as f:
                return f.read()

        except Exception as e:
            print(f"读取文件失败: {e}")
            return None

    def write_json(
        self, file_path: Union[str, Path], data: Any, indent: int = 2
    ) -> bool:
        """
        写入JSON文件

        Args:
            file_path: 文件路径
            data: 数据
            indent: 缩进空格数

        Returns:
            bool: 是否成功
        """
        try:
            json_content = json.dumps(data, ensure_ascii=False, indent=indent)
            return self.safe_write_file(file_path, json_content)
        except Exception as e:
            print(f"写入JSON文件失败: {e}")
            return False

    def read_json(self, file_path: Union[str, Path]) -> Optional[Any]:
        """
        读取JSON文件

        Args:
            file_path: 文件路径

        Returns:
            Any: 解析的数据，失败时返回None
        """
        try:
            content = self.safe_read_file(file_path)
            if content is None:
                return None

            return json.loads(content)
        except Exception as e:
            print(f"读取JSON文件失败: {e}")
            return None

    def write_csv(
        self,
        file_path: Union[str, Path],
        data: List[Dict[str, Any]],
        fieldnames: Optional[List[str]] = None,
    ) -> bool:
        """
        写入CSV文件

        Args:
            file_path: 文件路径
            data: 数据列表
            fieldnames: 字段名列表

        Returns:
            bool: 是否成功
        """
        try:
            if not data:
                return True

            path = Path(file_path)
            if not path.is_absolute():
                path = self.base_dir / path

            # 确保目录存在
            self.ensure_dir(path.parent)

            # 如果没有指定字段名，使用第一行数据的键
            if fieldnames is None:
                fieldnames = list(data[0].keys())

            with open(path, "w", newline="", encoding="utf-8") as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)

            return True

        except Exception as e:
            print(f"写入CSV文件失败: {e}")
            return False

    def read_csv(self, file_path: Union[str, Path]) -> Optional[List[Dict[str, Any]]]:
        """
        读取CSV文件

        Args:
            file_path: 文件路径

        Returns:
            List[Dict]: 数据列表，失败时返回None
        """
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.base_dir / path

            if not path.exists():
                return None

            data = []
            with open(path, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data.append(row)

            return data

        except Exception as e:
            print(f"读取CSV文件失败: {e}")
            return None

    def backup_file(
        self, file_path: Union[str, Path], backup_dir: Optional[Union[str, Path]] = None
    ) -> Optional[Path]:
        """
        备份文件

        Args:
            file_path: 源文件路径
            backup_dir: 备份目录

        Returns:
            Path: 备份文件路径，失败时返回None
        """
        try:
            source_path = Path(file_path)
            if not source_path.is_absolute():
                source_path = self.base_dir / source_path

            if not source_path.exists():
                return None

            # 默认备份目录
            if backup_dir is None:
                backup_dir = self.base_dir / "backups"
            else:
                backup_dir = Path(backup_dir)
                if not backup_dir.is_absolute():
                    backup_dir = self.base_dir / backup_dir

            # 确保备份目录存在
            self.ensure_dir(backup_dir)

            # 生成备份文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{source_path.stem}_{timestamp}{source_path.suffix}"
            backup_path = backup_dir / backup_name

            # 复制文件
            shutil.copy2(source_path, backup_path)
            return backup_path

        except Exception as e:
            print(f"备份文件失败: {e}")
            return None

    def create_zip_archive(
        self,
        archive_path: Union[str, Path],
        files: List[Union[str, Path]],
        base_dir: Optional[Union[str, Path]] = None,
    ) -> bool:
        """
        创建ZIP压缩包

        Args:
            archive_path: 压缩包路径
            files: 要压缩的文件列表
            base_dir: 基础目录（用于计算相对路径）

        Returns:
            bool: 是否成功
        """
        try:
            archive_path = Path(archive_path)
            if not archive_path.is_absolute():
                archive_path = self.base_dir / archive_path

            # 确保目录存在
            self.ensure_dir(archive_path.parent)

            base_path = Path(base_dir) if base_dir else self.base_dir

            with zipfile.ZipFile(archive_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                for file_path in files:
                    file_path = Path(file_path)
                    if not file_path.is_absolute():
                        file_path = self.base_dir / file_path

                    if file_path.exists():
                        # 计算在压缩包中的相对路径
                        try:
                            arc_name = file_path.relative_to(base_path)
                        except ValueError:
                            arc_name = file_path.name

                        zipf.write(file_path, arc_name)

            return True

        except Exception as e:
            print(f"创建压缩包失败: {e}")
            return False

    def extract_zip_archive(
        self, archive_path: Union[str, Path], extract_dir: Union[str, Path]
    ) -> bool:
        """
        解压ZIP压缩包

        Args:
            archive_path: 压缩包路径
            extract_dir: 解压目录

        Returns:
            bool: 是否成功
        """
        try:
            archive_path = Path(archive_path)
            if not archive_path.is_absolute():
                archive_path = self.base_dir / archive_path

            extract_path = Path(extract_dir)
            if not extract_path.is_absolute():
                extract_path = self.base_dir / extract_path

            # 确保解压目录存在
            self.ensure_dir(extract_path)

            with zipfile.ZipFile(archive_path, "r") as zipf:
                zipf.extractall(extract_path)

            return True

        except Exception as e:
            print(f"解压文件失败: {e}")
            return False

    def get_file_hash(
        self, file_path: Union[str, Path], algorithm: str = "md5"
    ) -> Optional[str]:
        """
        计算文件哈希值

        Args:
            file_path: 文件路径
            algorithm: 哈希算法

        Returns:
            str: 哈希值，失败时返回None
        """
        try:
            path = Path(file_path)
            if not path.is_absolute():
                path = self.base_dir / path

            if not path.exists():
                return None

            hash_obj = hashlib.new(algorithm)

            with open(path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)

            return hash_obj.hexdigest()

        except Exception as e:
            print(f"计算文件哈希失败: {e}")
            return None

    def cleanup_old_files(
        self, directory: Union[str, Path], days: int = 30, pattern: str = "*"
    ) -> int:
        """
        清理旧文件

        Args:
            directory: 目录路径
            days: 保留天数
            pattern: 文件匹配模式

        Returns:
            int: 删除的文件数量
        """
        try:
            dir_path = Path(directory)
            if not dir_path.is_absolute():
                dir_path = self.base_dir / dir_path

            if not dir_path.exists():
                return 0

            import time

            cutoff_time = time.time() - (days * 24 * 60 * 60)
            deleted_count = 0

            for file_path in dir_path.glob(pattern):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    try:
                        file_path.unlink()
                        deleted_count += 1
                    except Exception as e:
                        print(f"删除文件失败: {file_path}, 错误: {e}")

            return deleted_count

        except Exception as e:
            print(f"清理文件失败: {e}")
            return 0

    def get_directory_size(self, directory: Union[str, Path]) -> int:
        """
        获取目录大小（字节）

        Args:
            directory: 目录路径

        Returns:
            int: 目录大小
        """
        try:
            dir_path = Path(directory)
            if not dir_path.is_absolute():
                dir_path = self.base_dir / dir_path

            if not dir_path.exists():
                return 0

            total_size = 0
            for file_path in dir_path.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size

            return total_size

        except Exception as e:
            print(f"计算目录大小失败: {e}")
            return 0

    def format_file_size(self, size_bytes: int) -> str:
        """
        格式化文件大小

        Args:
            size_bytes: 字节数

        Returns:
            str: 格式化的大小字符串
        """
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math

        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        return f"{s} {size_names[i]}"

    def find_files(
        self, directory: Union[str, Path], pattern: str = "*", recursive: bool = True
    ) -> List[Path]:
        """
        查找文件

        Args:
            directory: 搜索目录
            pattern: 文件匹配模式
            recursive: 是否递归搜索

        Returns:
            List[Path]: 找到的文件列表
        """
        try:
            dir_path = Path(directory)
            if not dir_path.is_absolute():
                dir_path = self.base_dir / dir_path

            if not dir_path.exists():
                return []

            if recursive:
                return [p for p in dir_path.rglob(pattern) if p.is_file()]
            else:
                return [p for p in dir_path.glob(pattern) if p.is_file()]

        except Exception as e:
            print(f"查找文件失败: {e}")
            return []
