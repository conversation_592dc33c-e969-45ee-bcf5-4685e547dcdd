#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强日志管理
基于loguru提供更强大的日志功能
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any

from loguru import logger


class LoggerManager:
    """
    日志管理器
    统一管理应用程序的日志配置
    """

    def __init__(self):
        self.is_configured = False
        self.log_dir = Path("logs")
        self.log_format = (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )

        # 确保日志目录存在
        self.log_dir.mkdir(exist_ok=True)

    def setup_logger(
        self,
        log_level: str = "INFO",
        enable_file_logging: bool = True,
        enable_console_logging: bool = True,
        max_file_size: str = "10 MB",
        retention_days: int = 30,
        compression: str = "zip",
    ) -> None:
        """
        配置日志系统

        Args:
            log_level (str): 日志级别
            enable_file_logging (bool): 是否启用文件日志
            enable_console_logging (bool): 是否启用控制台日志
            max_file_size (str): 日志文件最大大小
            retention_days (int): 日志保留天数
            compression (str): 压缩格式
        """
        if self.is_configured:
            return

        # 移除默认处理器
        logger.remove()

        # 控制台日志
        if enable_console_logging:
            logger.add(
                sys.stdout,
                format=self.log_format,
                level=log_level,
                colorize=True,
                backtrace=True,
                diagnose=True,
            )

        # 文件日志
        if enable_file_logging:
            # 应用程序主日志
            logger.add(
                self.log_dir / "app_{time:YYYY-MM-DD}.log",
                format=self.log_format,
                level=log_level,
                rotation="00:00",  # 每天轮转
                retention=f"{retention_days} days",
                compression=compression,
                encoding="utf-8",
                backtrace=True,
                diagnose=True,
            )

            # 错误日志（单独记录）
            logger.add(
                self.log_dir / "error_{time:YYYY-MM-DD}.log",
                format=self.log_format,
                level="ERROR",
                rotation="00:00",
                retention=f"{retention_days} days",
                compression=compression,
                encoding="utf-8",
                backtrace=True,
                diagnose=True,
            )

        self.is_configured = True
        logger.info("日志系统初始化完成")

    def get_logger(self, name: Optional[str] = None):
        """
        获取日志器实例

        Args:
            name (str): 日志器名称

        Returns:
            logger: 日志器实例
        """
        if not self.is_configured:
            self.setup_logger()

        if name:
            return logger.bind(name=name)
        return logger


# 全局日志管理器实例
_logger_manager = LoggerManager()


def setup_logger(log_level: str = "INFO", **kwargs) -> None:
    """
    设置日志系统（便捷函数）

    Args:
        log_level (str): 日志级别
        **kwargs: 其他配置参数
    """
    _logger_manager.setup_logger(log_level=log_level, **kwargs)


def get_logger(name: Optional[str] = None):
    """
    获取日志器实例（便捷函数）

    Args:
        name (str): 日志器名称

    Returns:
        logger: 日志器实例
    """
    return _logger_manager.get_logger(name)
