项目文档：基于图像识别的1688商品信息采集系统

📌 项目概述

本项目旨在通过模拟用户打开1688商品链接、自动截图页面，并使用本地 AI 模型（如 QwenVL）识别图像内容，从而提取商品标题、价格、库存等信息并同步到本地 ERP 系统。

✅ 本方案不依赖 API，不解析 HTML，不属于传统爬虫行为，具有更好的稳定性与合规性。

🏗️ 项目结构设计

project_root/
├── browser/                # 浏览器自动化模块
│   └── capture.py          # 启动浏览器并截图
├── recognizer/             # 图像识别模块
│   └── qwenvl_reader.py    # 使用 QwenVL 模型识别截图内容
├── templates/              # 区域坐标模板，用于裁剪标题、价格等区域
│   └── default.json
├── data/                   # 存储截图与识别结果
│   ├── screenshots/
│   └── output.json
├── erp/                    # 数据写入本地 ERP 或数据库的模块
│   └── sync.py
├── app.py                  # 主运行入口
└── requirements.txt

🔧 环境依赖

pip install selenium playwright qwen openai pillow opencv-python
playwright install

✅ 建议使用 Python 3.9+，并确保本地支持 CUDA（如果使用 GPU 加速识别模型）。

🔁 操作流程

1. 自动打开并截图商品页面

# capture.py
from playwright.sync_api import sync_playwright

def capture_screenshot(url: str, output_path: str):
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        page.goto(url)
        page.wait_for_timeout(3000)  # 等待页面加载
        page.screenshot(path=output_path, full_page=True)
        browser.close()

2. 使用 QwenVL 模型识别截图内容

# qwenvl_reader.py
from PIL import Image
from qwen import QWenVL

def recognize(image_path):
    img = Image.open(image_path)
    model = QWenVL()
    question = "这张图中显示的商品名称、价格和库存分别是多少？"
    result = model.ask(img, question)
    return result

3. 区域裁剪 + OCR（可选增强）

可使用 OpenCV 提前裁剪价格/标题区域

配合 PaddleOCR 提升数字识别精度

4. 数据同步到 ERP 系统

# sync.py
import json
import sqlite3

def write_to_db(data):
    conn = sqlite3.connect("erp.db")
    c = conn.cursor()
    c.execute("""
        CREATE TABLE IF NOT EXISTS product (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            price TEXT,
            stock TEXT
        )
    """)
    c.execute("INSERT INTO product (title, price, stock) VALUES (?, ?, ?)",
              (data['title'], data['price'], data['stock']))
    conn.commit()
    conn.close()

🚀 主程序调用流程

# app.py
from browser.capture import capture_screenshot
from recognizer.qwenvl_reader import recognize
from erp.sync import write_to_db

URL = "https://detail.1688.com/offer/641897294646.html"
IMAGE_PATH = "data/screenshots/sample.png"

capture_screenshot(URL, IMAGE_PATH)
data = recognize(IMAGE_PATH)
write_to_db(data)

📎 模型替代方案（如无法使用 QwenVL）

模型名

类型

说明

Qwen-VL

多模态大模型

支持复杂图文问答

Donut

OCR + VQA

适合文档类结构化提取

PaddleOCR

OCR

数字/中文识别精度优秀

LayoutLMv3

文档理解

用于识别复杂布局内容

⚠️ 风控与合规建议

建议加上操作频率控制（限速访问，不要批量同时抓多个链接）

添加模拟鼠标滚动/点击行为，提高仿真度

禁止用于商业分发或大规模爬取他人商品数据

合理缓存商品截图，避免重复访问

✅ 适合扩展方向

接入用户登录系统（支持多用户使用）

加入页面商品参数识别（如颜色、材质）

接入 ChatGPT 或模型问答，做智能选品建议

接入 FastAPI 提供 Web 管理界面

📞 联系方式 / 开发交接说明

本项目支持通过 Cursor 编辑继续开发

所有模型推理可离线运行（如 QwenVL 下载本地）

若对接口/模型有疑问，请附带测试截图反馈