#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def check_database():
    """检查数据库中的重复数据"""
    db_path = "compare_tool/compare_tool.db"
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查对比组数据
        print("=== 对比组数据 ===")
        cursor.execute("SELECT id, name, description, created_at FROM compare_group ORDER BY id")
        groups = cursor.fetchall()
        
        if not groups:
            print("没有找到对比组数据")
        else:
            for group in groups:
                print(f"ID: {group[0]}, 名称: {group[1]}, 描述: {group[2]}, 创建时间: {group[3]}")
        
        # 检查是否有重复的名称
        print("\n=== 检查重复名称 ===")
        cursor.execute("""
            SELECT name, COUNT(*) as count 
            FROM compare_group 
            GROUP BY name 
            HAVING COUNT(*) > 1
        """)
        duplicates = cursor.fetchall()
        
        if duplicates:
            print("发现重复的对比组名称:")
            for dup in duplicates:
                print(f"名称: {dup[0]}, 重复次数: {dup[1]}")
        else:
            print("没有发现重复的对比组名称")
        
        # 检查商品数据
        print("\n=== 商品数据统计 ===")
        cursor.execute("""
            SELECT cg.name, COUNT(p.id) as product_count
            FROM compare_group cg
            LEFT JOIN product p ON cg.id = p.group_id
            GROUP BY cg.id, cg.name
            ORDER BY cg.id
        """)
        product_counts = cursor.fetchall()
        
        for count_data in product_counts:
            print(f"对比组: {count_data[0]}, 商品数量: {count_data[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database()