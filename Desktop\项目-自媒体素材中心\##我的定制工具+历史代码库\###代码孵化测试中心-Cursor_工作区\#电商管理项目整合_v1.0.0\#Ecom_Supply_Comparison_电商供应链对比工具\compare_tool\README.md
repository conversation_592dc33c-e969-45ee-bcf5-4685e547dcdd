# 商品对比工具

基于 PyQt6 的可视化商品对比工具，支持多平台商品信息对比分析。

## 功能特性

- 🛍️ **多平台支持**: 支持淘宝、拼多多、1688等多个电商平台
- 📊 **可视化对比**: 直观的表格和图表对比展示
- 🌙 **暗黑主题**: 现代化的暗黑主题界面
- 💾 **数据持久化**: 本地SQLite数据库存储
- 🖼️ **图片管理**: 商品图片上传、预览和管理
- 📈 **价格分析**: 价格对比和趋势分析
- 📤 **数据导出**: 支持Excel格式数据导出

## 安装说明

### 环境要求

- Python 3.10+
- PyQt6
- SQLite3

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd compare_tool
```

2. 创建虚拟环境
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 运行程序
```bash
python main.py
```

## 项目结构

```
compare_tool/
├── main.py                 # 主程序入口
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── db/                    # 数据库模块
│   ├── __init__.py
│   └── db_manager.py      # 数据库管理类
├── ui/                    # 用户界面模块
│   ├── __init__.py
│   ├── main_window.py     # 主窗口
│   └── add_product_dialog.py  # 添加商品对话框
├── models/                # 数据模型
│   ├── __init__.py
│   └── product.py         # 商品数据模型
├── utils/                 # 工具模块
│   ├── __init__.py
│   └── image_utils.py     # 图片处理工具
├── style/                 # 样式文件
│   └── dark.qss          # 暗黑主题样式
└── assets/               # 资源文件
    └── images/           # 图片资源
```

## 使用说明

1. **创建对比组**: 点击"添加对比组"按钮创建新的商品对比组
2. **添加商品**: 在对比组中添加需要对比的商品信息
3. **多来源对比**: 为每个商品添加多个来源（不同平台的价格信息）
4. **查看对比**: 在主界面查看商品的详细对比信息
5. **导出数据**: 将对比结果导出为Excel文件

## 开发说明

### 数据库设计

- `compare_group`: 对比组表
- `product`: 商品表
- `product_source`: 商品来源表

### 技术栈

- **GUI框架**: PyQt6
- **数据库**: SQLite3
- **图片处理**: Pillow
- **数据处理**: pandas
- **样式**: QDarkStyle + 自定义QSS

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License

## 更新日志

### v1.0.0
- 初始版本发布
- 基础的商品对比功能
- 暗黑主题支持
- 数据库持久化
