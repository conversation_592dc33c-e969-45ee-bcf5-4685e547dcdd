#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
负责SQLite数据库的创建、连接和数据操作
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DBManager:
    """数据库管理类"""

    def __init__(self, db_path: str = "compare_tool.db"):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()

    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn

    def init_database(self):
        """初始化数据库，创建表结构"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 创建对比组表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS compare_group (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # 创建商品表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS product (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        group_id INTEGER NOT NULL,
                        name TEXT NOT NULL,
                        description TEXT,
                        image_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY(group_id) REFERENCES compare_group(id) ON DELETE CASCADE
                    )
                """
                )

                # 创建商品来源表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS product_source (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        product_id INTEGER NOT NULL,
                        source_name TEXT NOT NULL,
                        price REAL,
                        shipping REAL DEFAULT 0,
                        stock INTEGER,
                        url TEXT,
                        note TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY(product_id) REFERENCES product(id) ON DELETE CASCADE
                    )
                """
                )

                # 创建索引以提高查询性能
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_product_group_id ON product(group_id)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_source_product_id ON product_source(product_id)"
                )

                conn.commit()
                logger.info("数据库初始化完成")

        except sqlite3.Error as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    # 对比组相关操作
    def add_group(self, name: str, description: str = "") -> int:
        """
        添加对比组

        Args:
            name: 组名
            description: 描述

        Returns:
            新创建的组ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO compare_group (name, description) VALUES (?, ?)",
                    (name, description),
                )
                conn.commit()
                group_id = cursor.lastrowid
                logger.info(f"添加对比组成功: {name} (ID: {group_id})")
                return group_id
        except sqlite3.IntegrityError:
            logger.error(f"对比组名称已存在: {name}")
            raise ValueError(f"对比组名称 '{name}' 已存在")
        except sqlite3.Error as e:
            logger.error(f"添加对比组失败: {e}")
            raise

    def get_all_groups(self) -> List[Dict]:
        """获取所有对比组"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM compare_group ORDER BY created_at DESC")
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except sqlite3.Error as e:
            logger.error(f"获取对比组失败: {e}")
            return []

    def update_group(
        self, group_id: int, name: str = None, description: str = None
    ) -> bool:
        """
        更新对比组信息

        Args:
            group_id: 组ID
            name: 新名称
            description: 新描述

        Returns:
            是否更新成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                updates = []
                params = []

                if name is not None:
                    updates.append("name = ?")
                    params.append(name)

                if description is not None:
                    updates.append("description = ?")
                    params.append(description)

                if not updates:
                    return True

                updates.append("updated_at = CURRENT_TIMESTAMP")
                params.append(group_id)

                sql = f"UPDATE compare_group SET {', '.join(updates)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()

                success = cursor.rowcount > 0
                if success:
                    logger.info(f"更新对比组成功: ID {group_id}")
                else:
                    logger.warning(f"对比组不存在: ID {group_id}")

                return success
        except sqlite3.Error as e:
            logger.error(f"更新对比组失败: {e}")
            return False

    def delete_group(self, group_id: int) -> bool:
        """
        删除对比组（级联删除相关商品和来源）

        Args:
            group_id: 组ID

        Returns:
            是否删除成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM compare_group WHERE id = ?", (group_id,))
                conn.commit()

                success = cursor.rowcount > 0
                if success:
                    logger.info(f"删除对比组成功: ID {group_id}")
                else:
                    logger.warning(f"对比组不存在: ID {group_id}")

                return success
        except sqlite3.Error as e:
            logger.error(f"删除对比组失败: {e}")
            return False

    # 商品相关操作
    def add_product(
        self, group_id: int, name: str, description: str = "", image_path: str = ""
    ) -> int:
        """
        添加商品

        Args:
            group_id: 所属对比组ID
            name: 商品名称
            description: 商品描述
            image_path: 图片路径

        Returns:
            新创建的商品ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO product (group_id, name, description, image_path) VALUES (?, ?, ?, ?)",
                    (group_id, name, description, image_path),
                )
                conn.commit()
                product_id = cursor.lastrowid
                logger.info(f"添加商品成功: {name} (ID: {product_id})")
                return product_id
        except sqlite3.Error as e:
            logger.error(f"添加商品失败: {e}")
            raise

    def get_products_by_group(self, group_id: int) -> List[Dict]:
        """获取指定对比组的所有商品"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM product WHERE group_id = ? ORDER BY created_at DESC",
                    (group_id,),
                )
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except sqlite3.Error as e:
            logger.error(f"获取商品失败: {e}")
            return []

    def get_product_by_id(self, product_id: int) -> Optional[Dict]:
        """根据ID获取商品信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM product WHERE id = ?", (product_id,))
                row = cursor.fetchone()
                return dict(row) if row else None
        except sqlite3.Error as e:
            logger.error(f"获取商品失败: {e}")
            return None

    def update_product(
        self,
        product_id: int,
        name: str = None,
        description: str = None,
        image_path: str = None,
    ) -> bool:
        """
        更新商品信息

        Args:
            product_id: 商品ID
            name: 新名称
            description: 新描述
            image_path: 新图片路径

        Returns:
            是否更新成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                updates = []
                params = []

                if name is not None:
                    updates.append("name = ?")
                    params.append(name)

                if description is not None:
                    updates.append("description = ?")
                    params.append(description)

                if image_path is not None:
                    updates.append("image_path = ?")
                    params.append(image_path)

                if not updates:
                    return True

                updates.append("updated_at = CURRENT_TIMESTAMP")
                params.append(product_id)

                sql = f"UPDATE product SET {', '.join(updates)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()

                success = cursor.rowcount > 0
                if success:
                    logger.info(f"更新商品成功: ID {product_id}")
                else:
                    logger.warning(f"商品不存在: ID {product_id}")

                return success
        except sqlite3.Error as e:
            logger.error(f"更新商品失败: {e}")
            return False

    def delete_product(self, product_id: int) -> bool:
        """
        删除商品（级联删除相关来源）

        Args:
            product_id: 商品ID

        Returns:
            是否删除成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM product WHERE id = ?", (product_id,))
                conn.commit()

                success = cursor.rowcount > 0
                if success:
                    logger.info(f"删除商品成功: ID {product_id}")
                else:
                    logger.warning(f"商品不存在: ID {product_id}")

                return success
        except sqlite3.Error as e:
            logger.error(f"删除商品失败: {e}")
            return False

    # 商品来源相关操作
    def add_source(
        self,
        product_id: int,
        source_name: str,
        price: float = None,
        shipping: float = 0,
        stock: int = None,
        url: str = "",
        note: str = "",
    ) -> int:
        """
        添加商品来源

        Args:
            product_id: 商品ID
            source_name: 来源名称（如：淘宝、拼多多等）
            price: 价格
            shipping: 运费
            stock: 库存
            url: 链接
            note: 备注

        Returns:
            新创建的来源ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """INSERT INTO product_source
                       (product_id, source_name, price, shipping, stock, url, note)
                       VALUES (?, ?, ?, ?, ?, ?, ?)""",
                    (product_id, source_name, price, shipping, stock, url, note),
                )
                conn.commit()
                source_id = cursor.lastrowid
                logger.info(f"添加商品来源成功: {source_name} (ID: {source_id})")
                return source_id
        except sqlite3.Error as e:
            logger.error(f"添加商品来源失败: {e}")
            raise

    def get_sources_by_product(self, product_id: int) -> List[Dict]:
        """获取指定商品的所有来源"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM product_source WHERE product_id = ? ORDER BY created_at DESC",
                    (product_id,),
                )
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except sqlite3.Error as e:
            logger.error(f"获取商品来源失败: {e}")
            return []

    def get_source_by_id(self, source_id: int) -> Optional[Dict]:
        """根据ID获取来源信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM product_source WHERE id = ?", (source_id,)
                )
                row = cursor.fetchone()
                return dict(row) if row else None
        except sqlite3.Error as e:
            logger.error(f"获取来源失败: {e}")
            return None

    def update_source(
        self,
        source_id: int,
        source_name: str = None,
        price: float = None,
        shipping: float = None,
        stock: int = None,
        url: str = None,
        note: str = None,
    ) -> bool:
        """
        更新商品来源信息

        Args:
            source_id: 来源ID
            source_name: 来源名称
            price: 价格
            shipping: 运费
            stock: 库存
            url: 链接
            note: 备注

        Returns:
            是否更新成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                updates = []
                params = []

                if source_name is not None:
                    updates.append("source_name = ?")
                    params.append(source_name)

                if price is not None:
                    updates.append("price = ?")
                    params.append(price)

                if shipping is not None:
                    updates.append("shipping = ?")
                    params.append(shipping)

                if stock is not None:
                    updates.append("stock = ?")
                    params.append(stock)

                if url is not None:
                    updates.append("url = ?")
                    params.append(url)

                if note is not None:
                    updates.append("note = ?")
                    params.append(note)

                if not updates:
                    return True

                updates.append("updated_at = CURRENT_TIMESTAMP")
                params.append(source_id)

                sql = f"UPDATE product_source SET {', '.join(updates)} WHERE id = ?"
                cursor.execute(sql, params)
                conn.commit()

                success = cursor.rowcount > 0
                if success:
                    logger.info(f"更新商品来源成功: ID {source_id}")
                else:
                    logger.warning(f"商品来源不存在: ID {source_id}")

                return success
        except sqlite3.Error as e:
            logger.error(f"更新商品来源失败: {e}")
            return False

    def delete_source(self, source_id: int) -> bool:
        """
        删除商品来源

        Args:
            source_id: 来源ID

        Returns:
            是否删除成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM product_source WHERE id = ?", (source_id,))
                conn.commit()

                success = cursor.rowcount > 0
                if success:
                    logger.info(f"删除商品来源成功: ID {source_id}")
                else:
                    logger.warning(f"商品来源不存在: ID {source_id}")

                return success
        except sqlite3.Error as e:
            logger.error(f"删除商品来源失败: {e}")
            return False

    # 复合查询方法
    def get_group_with_products_and_sources(self, group_id: int) -> Optional[Dict]:
        """
        获取对比组及其所有商品和来源信息

        Args:
            group_id: 对比组ID

        Returns:
            包含完整信息的字典
        """
        try:
            # 获取对比组信息
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM compare_group WHERE id = ?", (group_id,))
                group_row = cursor.fetchone()

                if not group_row:
                    return None

                group_data = dict(group_row)
                group_data["products"] = []

                # 获取该组的所有商品
                products = self.get_products_by_group(group_id)

                for product in products:
                    product_data = dict(product)
                    # 获取每个商品的所有来源
                    product_data["sources"] = self.get_sources_by_product(product["id"])
                    group_data["products"].append(product_data)

                return group_data

        except sqlite3.Error as e:
            logger.error(f"获取对比组完整信息失败: {e}")
            return None

    def get_all_groups_with_counts(self) -> List[Dict]:
        """获取所有对比组及其商品数量"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT g.*, COUNT(p.id) as product_count
                    FROM compare_group g
                    LEFT JOIN product p ON g.id = p.group_id
                    GROUP BY g.id
                    ORDER BY g.created_at DESC
                """
                )
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except sqlite3.Error as e:
            logger.error(f"获取对比组统计失败: {e}")
            return []

    def search_products(self, keyword: str, group_id: int = None) -> List[Dict]:
        """
        搜索商品

        Args:
            keyword: 搜索关键词
            group_id: 限制在指定对比组内搜索（可选）

        Returns:
            匹配的商品列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                if group_id:
                    cursor.execute(
                        """
                        SELECT * FROM product
                        WHERE group_id = ? AND (name LIKE ? OR description LIKE ?)
                        ORDER BY created_at DESC
                    """,
                        (group_id, f"%{keyword}%", f"%{keyword}%"),
                    )
                else:
                    cursor.execute(
                        """
                        SELECT * FROM product
                        WHERE name LIKE ? OR description LIKE ?
                        ORDER BY created_at DESC
                    """,
                        (f"%{keyword}%", f"%{keyword}%"),
                    )

                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except sqlite3.Error as e:
            logger.error(f"搜索商品失败: {e}")
            return []

    def get_price_statistics(self, product_id: int) -> Dict:
        """
        获取商品价格统计信息

        Args:
            product_id: 商品ID

        Returns:
            价格统计字典（最低价、最高价、平均价等）
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT
                        MIN(price) as min_price,
                        MAX(price) as max_price,
                        AVG(price) as avg_price,
                        COUNT(*) as source_count,
                        MIN(price + shipping) as min_total_price,
                        MAX(price + shipping) as max_total_price,
                        AVG(price + shipping) as avg_total_price
                    FROM product_source
                    WHERE product_id = ? AND price IS NOT NULL
                """,
                    (product_id,),
                )

                row = cursor.fetchone()
                if row:
                    stats = dict(row)
                    # 处理可能的None值
                    for key, value in stats.items():
                        if value is None:
                            stats[key] = 0
                    return stats
                else:
                    return {
                        "min_price": 0,
                        "max_price": 0,
                        "avg_price": 0,
                        "source_count": 0,
                        "min_total_price": 0,
                        "max_total_price": 0,
                        "avg_total_price": 0,
                    }
        except sqlite3.Error as e:
            logger.error(f"获取价格统计失败: {e}")
            return {}

    def close(self):
        """关闭数据库连接（如果需要的话）"""
        # 由于我们使用的是上下文管理器，连接会自动关闭
        # 这个方法主要是为了接口完整性
        pass
