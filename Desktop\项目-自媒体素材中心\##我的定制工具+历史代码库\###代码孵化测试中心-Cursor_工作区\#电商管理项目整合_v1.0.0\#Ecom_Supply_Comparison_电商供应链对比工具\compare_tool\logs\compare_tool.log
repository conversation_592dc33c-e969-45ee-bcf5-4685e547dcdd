2025-06-28 19:18:58 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:18:58 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:18:58 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:18:58 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:18:58 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:19:48 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:19:48 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:19:48 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:19:48 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:19:48 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:19:48 | INFO     | logger.info:97 | 这是一条信息日志
2025-06-28 19:19:48 | DEBUG    | logger.debug:93 | 这是一条调试日志
2025-06-28 19:19:48 | WARNING  | logger.warning:101 | 这是一条警告日志
2025-06-28 19:19:48 | ERROR    | logger.error:105 | 这是一条错误日志
2025-06-28 19:19:48 | ERROR    | logger.exception:113 | 捕获到异常: 这是一个测试异常
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\test_professional_ui.py", line 28, in test_logger
    raise ValueError("这是一个测试异常")
ValueError: 这是一个测试异常
2025-06-28 19:19:49 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-28 19:19:50 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-28 19:19:50 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-28 19:19:50 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:19:50 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:19:50 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-28 19:19:50 | INFO     | logger.info:97 | 专业样式表已成功加载
2025-06-28 19:19:50 | INFO     | logger.info:97 | 窗口尺寸正确: 1300x1200
2025-06-28 19:19:50 | INFO     | logger.info:97 | 快速添加按钮存在且有效
2025-06-28 19:19:50 | INFO     | logger.info:97 | 智能分组按钮存在且有效
2025-06-28 19:19:50 | INFO     | logger.info:97 | 统计标签存在且有效
2025-06-28 19:19:50 | INFO     | logger.info:97 | 对比组列表存在且有效
2025-06-28 19:19:50 | INFO     | logger.info:97 | 商品滚动区域存在且有效
2025-06-28 19:19:50 | INFO     | logger.info:97 | 商品对比视图存在且有效
2025-06-28 19:19:50 | INFO     | logger.info:97 | 主窗口已显示，请手动检查UI样式
2025-06-28 19:23:35 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:23:35 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:23:35 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:23:35 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:23:35 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:23:36 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-28 19:23:37 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-28 19:23:37 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-28 19:23:37 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:23:37 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:23:37 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-28 19:23:38 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:23:38 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:23:38 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:23:38 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:23:38 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:23:54 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:23:54 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:23:54 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:23:54 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:23:54 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:23:55 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:23:55 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:23:55 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:23:55 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:23:55 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:24:00 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:24:01 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:24:21 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:24:21 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:24:21 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:00 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:29:00 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:29:00 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:29:00 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:29:00 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:29:01 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-28 19:29:02 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-28 19:29:02 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-28 19:29:02 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:02 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:29:02 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:04 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:05 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:29:19 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:19 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:19 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:19 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:19 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:29:20 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:20 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:20 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:20 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:20 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:3
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:24 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:25 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:25 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:25 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:25 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:25 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:26 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:27 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:27 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:27 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:27 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:27 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:28 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:28 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:28 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:28 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:28 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:29 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:29 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:29 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:29 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:29 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:31 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:32 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:32 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:32 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:32 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:32 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 获取到 1 个商品
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 1 个商品
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:29:33 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:4
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 获取到 2 个商品
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 2 个商品
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:4
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 获取到 2 个商品
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 2 个商品
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:30:01 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:4
2025-06-28 19:41:05 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:41:05 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:41:05 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:41:05 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:41:05 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:41:07 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-28 19:41:08 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-28 19:41:08 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-28 19:41:08 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:08 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:4
2025-06-28 19:41:08 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-28 19:41:13 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:13 | DEBUG    | logger.debug:93 | 获取到 2 个商品
2025-06-28 19:41:13 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 2 个商品
2025-06-28 19:41:13 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:13 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:4
2025-06-28 19:41:15 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:15 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:15 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:15 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:15 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:4
2025-06-28 19:41:17 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:17 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:17 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:17 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:17 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:3
2025-06-28 19:41:17 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:17 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:3
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:3
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:18 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:3
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:3
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:19 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:3
2025-06-28 19:41:20 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:20 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:20 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:20 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:20 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:3
2025-06-28 19:41:22 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:22 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:22 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:22 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:22 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 19:41:22 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:22 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 19:41:23 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:23 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:41:23 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:41:23 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:23 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 19:41:42 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:41:42 | DEBUG    | logger.debug:93 | 获取到 2 个商品
2025-06-28 19:41:42 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 2 个商品
2025-06-28 19:41:42 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:41:42 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 19:50:59 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:50:59 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:50:59 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:50:59 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:50:59 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:51:11 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:51:11 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:51:11 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:51:11 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:51:11 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:54:56 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 19:54:56 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 19:54:56 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 19:54:56 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 19:54:56 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 19:54:57 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-28 19:54:58 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-28 19:54:58 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-28 19:54:58 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:54:58 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 19:54:58 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 获取到 0 个商品
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 0 个商品
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 获取到 2 个商品
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 2 个商品
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 19:55:06 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 20:01:35 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 20:01:35 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 20:01:35 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 20:01:35 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 20:01:35 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 20:01:36 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-28 20:01:40 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-28 20:01:40 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-28 20:01:40 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 20:01:40 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 20:01:40 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-28 20:01:42 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 20:01:42 | DEBUG    | logger.debug:93 | 获取到 2 个商品
2025-06-28 20:01:42 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 2 个商品
2025-06-28 20:01:42 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 20:01:42 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 获取到 3 个商品
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 3 个商品
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:3, 组:2
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 获取到 3 个商品
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 3 个商品
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 20:01:49 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:3, 组:2
2025-06-28 20:01:51 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 20:01:51 | DEBUG    | logger.debug:93 | 获取到 4 个商品
2025-06-28 20:01:51 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 4 个商品
2025-06-28 20:01:51 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 20:01:51 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:4, 组:2
2025-06-28 20:01:54 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 20:01:54 | DEBUG    | logger.debug:93 | 获取到 5 个商品
2025-06-28 20:01:54 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 5 个商品
2025-06-28 20:01:54 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 20:01:54 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:5, 组:2
2025-06-28 20:02:49 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 20:02:49 | DEBUG    | logger.debug:93 | 获取到 5 个商品
2025-06-28 20:02:49 | DEBUG    | logger.debug:93 | 商品卡片刷新完成，显示 5 个商品
2025-06-28 22:47:43 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 22:47:43 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 22:47:43 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 22:47:43 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-28 22:47:43 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:19:07 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:19:07 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:19:07 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:19:07 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-29 00:19:07 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 01:54:40 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 01:54:40 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 01:54:40 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 01:54:40 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-29 01:54:40 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:53:43 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:53:43 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:53:43 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:53:43 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-29 02:53:43 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:54:24 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:54:24 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:54:24 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:54:24 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool
2025-06-29 02:54:24 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:54:26 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:54:27 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
2025-06-29 03:12:13 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 03:12:13 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 03:12:13 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 03:12:13 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:5, 组:2
2025-06-29 03:12:13 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
2025-06-29 03:12:14 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 03:12:16 | CRITICAL | logger.critical:109 | 未捕获的异常
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 2028, in on_group_selection_changed
    self.compare_title_label.setText(
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'compare_title_label'. Did you mean: 'compare_info_label'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 2035, in on_group_selection_changed
    self.compare_title_label.setText("商品对比 - 获取信息失败")
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'compare_title_label'. Did you mean: 'compare_info_label'?
