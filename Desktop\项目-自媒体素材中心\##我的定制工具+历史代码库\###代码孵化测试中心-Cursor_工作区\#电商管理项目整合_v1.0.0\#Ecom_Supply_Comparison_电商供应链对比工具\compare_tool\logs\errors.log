2025-06-28 19:19:48 | ERROR    | logger.error:105 | 这是一条错误日志
2025-06-28 19:19:48 | ERROR    | logger.exception:113 | 捕获到异常: 这是一个测试异常
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\test_professional_ui.py", line 28, in test_logger
    raise ValueError("这是一个测试异常")
ValueError: 这是一个测试异常
2025-06-29 02:54:27 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
2025-06-29 03:12:13 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
2025-06-29 03:12:16 | CRITICAL | logger.critical:109 | 未捕获的异常
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 2028, in on_group_selection_changed
    self.compare_title_label.setText(
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'compare_title_label'. Did you mean: 'compare_info_label'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 2035, in on_group_selection_changed
    self.compare_title_label.setText("商品对比 - 获取信息失败")
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'compare_title_label'. Did you mean: 'compare_info_label'?
