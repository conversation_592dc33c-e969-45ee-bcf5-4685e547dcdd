#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品对比工具 - 主程序入口
基于 PyQt6 的可视化商品对比工具
"""

import sys
import os
import traceback
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# 添加项目根目录到路径
# 确保在导入其他模块之前执行
if __name__ == "__main__":
    # 当直接运行此文件时，将 compare_tool 的父目录添加到 sys.path
    # 这样可以确保根目录的 main.py 启动器能够正确找到模块
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)


from ui.main_window import MainWindow
from utils.theme_manager import theme_manager
from utils.logger import logger

# 在顶部设置日志，以便尽早捕获信息
# logger 已经是全局实例，不需要调用 setup_logger()


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理器"""
    # 记录日志
    logger.critical("未捕获的异常", exc_info=(exc_type, exc_value, exc_traceback))

    # 显示一个错误消息框
    error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))

    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Icon.Critical)
    msg_box.setWindowTitle("程序遇到致命错误")
    msg_box.setText("抱歉，程序遇到无法恢复的错误，即将退出。")
    msg_box.setInformativeText("详细错误信息已记录到日志文件 `logs/errors.log` 中。")
    msg_box.setDetailedText(error_msg)
    msg_box.exec()

    # 退出程序
    sys.exit(1)


def main():
    """主程序入口"""
    # 安装全局异常处理器
    sys.excepthook = handle_exception

    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("商品对比工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("商品对比工具开发团队")

    # 设置高DPI支持 (PyQt6中这些属性已经默认启用，不需要手动设置)
    # app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)

    # 应用主题
    theme_manager.apply_theme(app)

    # 创建主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    # 切换工作目录到脚本所在目录，确保相对路径正确
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    main()
