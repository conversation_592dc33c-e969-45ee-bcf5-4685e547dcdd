#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型定义
定义商品对比工具的核心数据模型
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional
import json


@dataclass
class CompareGroup:
    """对比组数据模型"""

    id: Optional[int] = None
    name: str = ""
    description: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    products: List["Product"] = field(default_factory=list)

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "products": [product.to_dict() for product in self.products],
        }

    @classmethod
    def from_dict(cls, data: dict) -> "CompareGroup":
        """从字典创建对象"""
        group = cls(
            id=data.get("id"),
            name=data.get("name", ""),
            description=data.get("description", ""),
            created_at=(
                datetime.fromisoformat(data["created_at"])
                if data.get("created_at")
                else None
            ),
            updated_at=(
                datetime.fromisoformat(data["updated_at"])
                if data.get("updated_at")
                else None
            ),
        )

        # 加载商品列表
        if "products" in data:
            group.products = [Product.from_dict(p) for p in data["products"]]

        return group

    def add_product(self, product: "Product"):
        """添加商品到对比组"""
        if product not in self.products:
            self.products.append(product)
            product.group_id = self.id

    def remove_product(self, product: "Product"):
        """从对比组移除商品"""
        if product in self.products:
            self.products.remove(product)
            product.group_id = None

    def get_product_count(self) -> int:
        """获取商品数量"""
        return len(self.products)


@dataclass
class ProductSource:
    """商品来源数据模型"""

    id: Optional[int] = None
    product_id: Optional[int] = None
    source_name: str = ""  # 淘宝、拼多多、1688等
    price: Optional[float] = None
    shipping: float = 0.0
    stock: Optional[int] = None
    url: str = ""
    note: str = ""
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "product_id": self.product_id,
            "source_name": self.source_name,
            "price": self.price,
            "shipping": self.shipping,
            "stock": self.stock,
            "url": self.url,
            "note": self.note,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    @classmethod
    def from_dict(cls, data: dict) -> "ProductSource":
        """从字典创建对象"""
        return cls(
            id=data.get("id"),
            product_id=data.get("product_id"),
            source_name=data.get("source_name", ""),
            price=data.get("price"),
            shipping=data.get("shipping", 0.0),
            stock=data.get("stock"),
            url=data.get("url", ""),
            note=data.get("note", ""),
            created_at=(
                datetime.fromisoformat(data["created_at"])
                if data.get("created_at")
                else None
            ),
            updated_at=(
                datetime.fromisoformat(data["updated_at"])
                if data.get("updated_at")
                else None
            ),
        )

    def get_total_price(self) -> Optional[float]:
        """获取总价（商品价格+运费）"""
        if self.price is not None:
            return self.price + self.shipping
        return None

    def is_in_stock(self) -> bool:
        """检查是否有库存"""
        return self.stock is None or self.stock > 0

    def get_display_name(self) -> str:
        """获取显示名称"""
        if self.note:
            return f"{self.source_name} - {self.note}"
        return self.source_name


@dataclass
class Product:
    """商品数据模型 - 增强版"""

    id: Optional[int] = None
    group_id: Optional[int] = None
    name: str = ""
    description: str = ""
    image_path: str = ""

    # 新增商品属性
    brand: str = ""  # 品牌
    category: str = ""  # 分类
    color: str = ""  # 颜色
    size: str = ""  # 尺寸/规格
    style: str = ""  # 款式
    material: str = ""  # 材质
    weight: Optional[float] = None  # 重量(kg)
    rating: Optional[float] = None  # 评分(1-5)
    sales_volume: Optional[int] = None  # 销量
    tags: str = ""  # 标签(逗号分隔)

    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    sources: List[ProductSource] = field(default_factory=list)

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "group_id": self.group_id,
            "name": self.name,
            "description": self.description,
            "image_path": self.image_path,
            # 新增字段
            "brand": self.brand,
            "category": self.category,
            "color": self.color,
            "size": self.size,
            "style": self.style,
            "material": self.material,
            "weight": self.weight,
            "rating": self.rating,
            "sales_volume": self.sales_volume,
            "tags": self.tags,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "sources": [source.to_dict() for source in self.sources],
        }

    @classmethod
    def from_dict(cls, data: dict) -> "Product":
        """从字典创建对象"""
        product = cls(
            id=data.get("id"),
            group_id=data.get("group_id"),
            name=data.get("name", ""),
            description=data.get("description", ""),
            image_path=data.get("image_path", ""),
            # 新增字段
            brand=data.get("brand", ""),
            category=data.get("category", ""),
            color=data.get("color", ""),
            size=data.get("size", ""),
            style=data.get("style", ""),
            material=data.get("material", ""),
            weight=data.get("weight"),
            rating=data.get("rating"),
            sales_volume=data.get("sales_volume"),
            tags=data.get("tags", ""),
            created_at=(
                datetime.fromisoformat(data["created_at"])
                if data.get("created_at")
                else None
            ),
            updated_at=(
                datetime.fromisoformat(data["updated_at"])
                if data.get("updated_at")
                else None
            ),
        )

        # 加载来源列表
        if "sources" in data:
            product.sources = [ProductSource.from_dict(s) for s in data["sources"]]

        return product

    def add_source(self, source: ProductSource):
        """添加商品来源"""
        if source not in self.sources:
            self.sources.append(source)
            source.product_id = self.id

    def remove_source(self, source: ProductSource):
        """移除商品来源"""
        if source in self.sources:
            self.sources.remove(source)
            source.product_id = None

    def get_source_count(self) -> int:
        """获取来源数量"""
        return len(self.sources)

    def get_min_price(self) -> Optional[float]:
        """获取最低价格"""
        prices = [s.price for s in self.sources if s.price is not None and s.price >= 0]
        return min(prices) if prices else None

    def get_max_price(self) -> Optional[float]:
        """获取最高价格"""
        prices = [s.price for s in self.sources if s.price is not None and s.price >= 0]
        return max(prices) if prices else None

    def get_avg_price(self) -> Optional[float]:
        """获取平均价格"""
        prices = [s.price for s in self.sources if s.price is not None and s.price >= 0]
        return sum(prices) / len(prices) if prices else None

    def get_min_total_price(self) -> Optional[float]:
        """获取最低总价（含运费）"""
        total_prices = [
            s.get_total_price() for s in self.sources if s.get_total_price() is not None
        ]
        return min(total_prices) if total_prices else None

    def get_max_total_price(self) -> Optional[float]:
        """获取最高总价（含运费）"""
        total_prices = [
            s.get_total_price() for s in self.sources if s.get_total_price() is not None
        ]
        return max(total_prices) if total_prices else None

    def get_best_source(self) -> Optional[ProductSource]:
        """获取最优来源（价格最低且有库存）"""
        available_sources = [
            s
            for s in self.sources
            if s.is_in_stock() and s.price is not None and s.price >= 0
        ]
        if not available_sources:
            return None

        return min(available_sources, key=lambda s: s.get_total_price() or float("inf"))

    def has_image(self) -> bool:
        """检查是否有图片"""
        return bool(self.image_path and self.image_path.strip())

    def get_price_range_text(self) -> str:
        """获取价格范围文本"""
        min_price = self.get_min_price()
        max_price = self.get_max_price()

        if min_price is None:
            return "暂无价格"

        if min_price == max_price:
            return f"¥{min_price:.2f}"
        else:
            return f"¥{min_price:.2f} - ¥{max_price:.2f}"

    def get_platform_names(self) -> list[str]:
        """获取来源平台名称列表"""
        return [s.source_name for s in self.sources if s.source_name]

    def get_platforms_text(self) -> str:
        """获取展示用的平台文本，如 "淘宝、1688"""
        names = self.get_platform_names()
        # 去重并保持原有顺序
        unique_names = []
        for name in names:
            if name not in unique_names:
                unique_names.append(name)
        return "、".join(unique_names) if unique_names else "无平台"


# 工具函数
def create_sample_data() -> CompareGroup:
    """创建示例数据用于测试"""
    group = CompareGroup(id=1, name="手机对比", description="智能手机价格对比")

    # 创建示例商品
    product = Product(
        id=1, group_id=1, name="iPhone 15 Pro", description="苹果最新旗舰手机"
    )

    # 添加来源
    sources = [
        ProductSource(
            id=1,
            product_id=1,
            source_name="淘宝",
            price=8999.0,
            shipping=0.0,
            stock=100,
            url="https://taobao.com/item/123",
            note="官方旗舰店",
        ),
        ProductSource(
            id=2,
            product_id=1,
            source_name="京东",
            price=9199.0,
            shipping=0.0,
            stock=50,
            url="https://jd.com/item/456",
            note="自营",
        ),
    ]

    for source in sources:
        product.add_source(source)

    group.add_product(product)

    return group
