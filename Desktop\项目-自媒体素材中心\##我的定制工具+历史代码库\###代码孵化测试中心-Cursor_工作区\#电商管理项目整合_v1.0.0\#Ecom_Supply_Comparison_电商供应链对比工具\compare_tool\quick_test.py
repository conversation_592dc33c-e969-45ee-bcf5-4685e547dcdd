#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速功能测试脚本
验证商品对比工具的核心功能
"""

import os
import sys
import tempfile

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.db_manager import DBManager
from models.product import Product, ProductSource
from utils.import_export import ImportExportManager


def test_basic_functionality():
    """测试基本功能"""
    print("开始基本功能测试...")
    
    # 创建临时数据库
    test_db_path = tempfile.mktemp(suffix='.db')
    
    try:
        # 初始化数据库
        db_manager = DBManager(test_db_path)
        print("✓ 数据库初始化成功")
        
        # 测试对比组操作
        group_id = db_manager.add_group("测试对比组", "这是一个测试对比组")
        print(f"✓ 创建对比组成功，ID: {group_id}")
        
        groups = db_manager.get_all_groups()
        assert len(groups) == 1
        assert groups[0]['name'] == "测试对比组"
        print("✓ 对比组查询成功")
        
        # 测试商品操作
        product_id = db_manager.add_product(group_id, "测试商品", "这是一个测试商品")
        print(f"✓ 创建商品成功，ID: {product_id}")
        
        products = db_manager.get_products_by_group(group_id)
        assert len(products) == 1
        assert products[0]['name'] == "测试商品"
        print("✓ 商品查询成功")
        
        # 测试来源操作
        source_id1 = db_manager.add_source(product_id, "淘宝", 99.99, 10.0, 100, "https://taobao.com", "官方店")
        source_id2 = db_manager.add_source(product_id, "京东", 109.99, 0, 50, "https://jd.com", "自营")
        print(f"✓ 创建来源成功，ID: {source_id1}, {source_id2}")
        
        sources = db_manager.get_sources_by_product(product_id)
        assert len(sources) == 2
        print("✓ 来源查询成功")
        
        # 测试复合查询
        group_data = db_manager.get_group_with_products_and_sources(group_id)
        assert group_data is not None
        assert len(group_data['products']) == 1
        assert len(group_data['products'][0]['sources']) == 2
        print("✓ 复合查询成功")
        
        # 测试数据模型
        product = Product.from_dict(group_data['products'][0])
        assert product.name == "测试商品"
        assert len(product.sources) == 2
        
        min_price = product.get_min_price()
        max_price = product.get_max_price()
        assert min_price == 99.99
        assert max_price == 109.99
        print("✓ 数据模型测试成功")
        
        # 测试导入导出
        import_export_manager = ImportExportManager(db_manager)
        
        # 生成模板
        template_path = tempfile.mktemp(suffix='.xlsx')
        success = import_export_manager.get_excel_template(template_path)
        assert success
        assert os.path.exists(template_path)
        print("✓ Excel模板生成成功")
        
        # 导出数据
        export_path = tempfile.mktemp(suffix='.xlsx')
        success = import_export_manager.export_group_to_excel(group_id, export_path)
        assert success
        assert os.path.exists(export_path)
        print("✓ 数据导出成功")
        
        # 清理临时文件
        if os.path.exists(template_path):
            os.remove(template_path)
        if os.path.exists(export_path):
            os.remove(export_path)
        
        print("✓ 所有基本功能测试通过!")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    
    finally:
        # 清理测试数据库
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
    
    return True


def test_performance():
    """测试性能"""
    print("\n开始性能测试...")
    
    test_db_path = tempfile.mktemp(suffix='.db')
    
    try:
        import time
        
        db_manager = DBManager(test_db_path)
        
        # 测试批量插入性能
        start_time = time.time()
        
        # 创建10个对比组
        group_ids = []
        for i in range(10):
            group_id = db_manager.add_group(f"对比组{i}", f"描述{i}")
            group_ids.append(group_id)
        
        # 每个对比组创建10个商品
        product_ids = []
        for group_id in group_ids:
            for j in range(10):
                product_id = db_manager.add_product(group_id, f"商品{j}", f"描述{j}")
                product_ids.append(product_id)
        
        # 每个商品创建3个来源
        for product_id in product_ids:
            for k in range(3):
                db_manager.add_source(
                    product_id, f"来源{k}", 99.99 + k, 10.0, 100, 
                    f"https://test{k}.com", f"备注{k}"
                )
        
        insert_time = time.time() - start_time
        
        print(f"✓ 批量插入测试完成:")
        print(f"  - 10个对比组, 100个商品, 300个来源")
        print(f"  - 耗时: {insert_time:.2f}秒")
        
        # 测试查询性能
        start_time = time.time()
        
        groups = db_manager.get_all_groups_with_counts()
        for group in groups[:3]:  # 只测试前3个
            group_data = db_manager.get_group_with_products_and_sources(group['id'])
        
        query_time = time.time() - start_time
        
        print(f"✓ 查询性能测试完成:")
        print(f"  - 查询了{len(groups)}个对比组")
        print(f"  - 详细查询了3个对比组")
        print(f"  - 耗时: {query_time:.2f}秒")
        
        if insert_time < 5.0 and query_time < 2.0:
            print("✓ 性能测试通过!")
            return True
        else:
            print("⚠ 性能可能需要优化")
            return True
        
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")
        return False
    
    finally:
        if os.path.exists(test_db_path):
            os.remove(test_db_path)


def main():
    """主函数"""
    print("=" * 50)
    print("商品对比工具 - 快速功能测试")
    print("=" * 50)
    
    # 基本功能测试
    basic_success = test_basic_functionality()
    
    # 性能测试
    performance_success = test_performance()
    
    print("\n" + "=" * 50)
    if basic_success and performance_success:
        print("🎉 所有测试通过! 应用程序运行正常。")
    else:
        print("❌ 部分测试失败，请检查问题。")
    print("=" * 50)


if __name__ == "__main__":
    main()
