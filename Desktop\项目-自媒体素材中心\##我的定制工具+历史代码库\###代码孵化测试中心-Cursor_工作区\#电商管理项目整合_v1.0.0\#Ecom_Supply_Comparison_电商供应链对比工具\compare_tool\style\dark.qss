/* 自定义暗黑主题样式 */

/* 主窗口样式 */
QMainWindow {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* 工具栏样式 */
QToolBar {
    background-color: #3c3c3c;
    border: none;
    spacing: 3px;
    padding: 5px;
}

QToolBar::separator {
    background-color: #555555;
    width: 1px;
    margin: 5px;
}

/* 按钮样式 */
QPushButton {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 8px 16px;
    color: #ffffff;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #505050;
    border-color: #777777;
}

QPushButton:pressed {
    background-color: #353535;
}

QPushButton:disabled {
    background-color: #2b2b2b;
    color: #666666;
    border-color: #444444;
}

/* 列表控件样式 */
QListWidget {
    background-color: #353535;
    border: 1px solid #555555;
    border-radius: 4px;
    color: #ffffff;
    selection-background-color: #0078d4;
}

QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #444444;
}

QListWidget::item:hover {
    background-color: #404040;
}

QListWidget::item:selected {
    background-color: #0078d4;
}

/* 表格样式 */
QTableWidget {
    background-color: #353535;
    border: 1px solid #555555;
    border-radius: 4px;
    color: #ffffff;
    gridline-color: #555555;
    selection-background-color: #0078d4;
}

QTableWidget::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:hover {
    background-color: #404040;
}

QTableWidget::item:selected {
    background-color: #0078d4;
}

QHeaderView::section {
    background-color: #404040;
    color: #ffffff;
    padding: 8px;
    border: 1px solid #555555;
    font-weight: bold;
}

/* 输入框样式 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 8px;
    color: #ffffff;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #0078d4;
}

/* 下拉框样式 */
QComboBox {
    background-color: #404040;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 8px;
    color: #ffffff;
}

QComboBox:hover {
    border-color: #777777;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #404040;
    border: 1px solid #555555;
    color: #ffffff;
    selection-background-color: #0078d4;
}

/* 标签样式 */
QLabel {
    color: #ffffff;
    background-color: transparent;
}

/* 分组框样式 */
QGroupBox {
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 10px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px 0 5px;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #2b2b2b;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #555555;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #666666;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QScrollBar:horizontal {
    background-color: #2b2b2b;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #555555;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #666666;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-top: 1px solid #555555;
}

/* 对话框样式 */
QDialog {
    background-color: #2b2b2b;
    color: #ffffff;
}

/* 菜单样式 */
QMenuBar {
    background-color: #3c3c3c;
    color: #ffffff;
    border-bottom: 1px solid #555555;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
}

QMenuBar::item:selected {
    background-color: #0078d4;
}

QMenu {
    background-color: #404040;
    color: #ffffff;
    border: 1px solid #555555;
}

QMenu::item {
    padding: 8px 20px;
}

QMenu::item:selected {
    background-color: #0078d4;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 5px 0;
}
