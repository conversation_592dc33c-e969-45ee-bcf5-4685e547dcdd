/* 专业级商品对比工具样式表 */

/* Cursor风格主窗口样式 */
QMainWindow {
    background-color: #1e1e1e;
    color: #cccccc;
    font-family: "Segoe UI", "Microsoft YaHei UI", sans-serif;
}

/* 工具栏样式 */
QToolBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2d2d2d, stop:1 #262626);
    border: none;
    spacing: 6px;
    padding: 8px;
    border-bottom: 1px solid #404040;
}

QToolBar::separator {
    background-color: #404040;
    width: 1px;
    margin: 4px 8px;
}

QToolBar QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 6px 12px;
    color: #e0e0e0;
    font-weight: 500;
}

QToolBar QToolButton:hover {
    background-color: #3d3d3d;
    border-color: #555555;
}

QToolBar QToolButton:pressed {
    background-color: #2a2a2a;
}

/* Cursor风格按钮样式 - 方正专业 */
QPushButton {
    background-color: #2d2d2d;
    border: 1px solid #464647;
    border-radius: 2px;  /* 最小圆角，更专业 */
    padding: 10px 20px;
    color: #cccccc;
    font-weight: 500;
    font-size: 13px;
    font-family: "Segoe UI", "Microsoft YaHei UI", sans-serif;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #37373d;
    border-color: #007acc;
    color: #ffffff;
}

QPushButton:pressed {
    background-color: #1e1e1e;
    border-color: #007acc;
    color: #ffffff;
}

QPushButton:disabled {
    background-color: #2a2a2a;
    color: #666666;
    border-color: #444444;
}

/* Cursor风格主要操作按钮 */
QPushButton[class="primary"] {
    background-color: #007acc;
    border: 1px solid #007acc;
    color: #ffffff;
    font-weight: 600;
}

QPushButton[class="primary"]:hover {
    background-color: #1177bb;
    border-color: #1177bb;
}

QPushButton[class="primary"]:pressed {
    background-color: #005a9e;
}

/* Cursor风格成功按钮 */
QPushButton[class="success"] {
    background-color: #4ec9b0;
    border: 1px solid #4ec9b0;
    color: #1e1e1e;
    font-weight: 600;
}

QPushButton[class="success"]:hover {
    background-color: #5dd4c1;
    border-color: #5dd4c1;
}

QPushButton[class="success"]:pressed {
    background-color: #3eb89f;
}

/* Cursor风格警告按钮 */
QPushButton[class="warning"] {
    background-color: #dcdcaa;
    border: 1px solid #dcdcaa;
    color: #1e1e1e;
    font-weight: 600;
}

QPushButton[class="warning"]:hover {
    background-color: #e6e6bb;
    border-color: #e6e6bb;
}

QPushButton[class="warning"]:pressed {
    background-color: #d1d199;
}

/* Cursor风格危险按钮 */
QPushButton[class="danger"] {
    background-color: #f14c4c;
    border: 1px solid #f14c4c;
    color: #ffffff;
    font-weight: 600;
}

QPushButton[class="danger"]:hover {
    background-color: #ff5c5c;
    border-color: #ff5c5c;
}

QPushButton[class="danger"]:pressed {
    background-color: #e13c3c;
}

/* 列表控件样式 - Cursor风格，修复间隔色问题 */
QListWidget {
    background-color: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 0px;  /* 去掉圆角，更专业 */
    color: #ffffff;  /* 强制白色文字 */
    outline: none;
    selection-background-color: transparent;
    font-family: "Segoe UI", "Microsoft YaHei UI", sans-serif;
    font-size: 13px;
    alternate-background-color: transparent;  /* 禁用间隔色 */
}

QListWidget::item {
    padding: 12px 16px;
    border: none;
    border-bottom: 1px solid #2d2d2d;
    color: #ffffff !important;  /* 强制白色文字 */
    background-color: transparent !important;  /* 强制透明背景 */
    min-height: 20px;
}

QListWidget::item:alternate {
    background-color: transparent !important;  /* 禁用间隔色 */
    color: #ffffff !important;  /* 强制白色文字 */
}

QListWidget::item:hover {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
    border-left: 3px solid #007acc;
}

QListWidget::item:selected {
    background-color: #0e639c !important;  /* 更深的蓝色确保对比度 */
    color: #ffffff !important;  /* 强制白色文字 */
    font-weight: 600;
    border-left: 3px solid #007acc;
}

QListWidget::item:selected:hover {
    background-color: #1177bb !important;
    color: #ffffff !important;  /* 保持白色文字 */
}

/* Cursor风格表格样式 */
QTableWidget {
    background-color: #1e1e1e;
    border: 1px solid #333333;
    border-radius: 0px;  /* 去掉圆角 */
    color: #cccccc;
    gridline-color: #2d2d2d;
    selection-background-color: #264f78;
    alternate-background-color: #262626;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
}

QTableWidget::item {
    padding: 10px 14px;
    border: none;
    color: #cccccc;
    min-height: 20px;
}

QTableWidget::item:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

QTableWidget::item:selected {
    background-color: #264f78;
    color: #ffffff;
}

QHeaderView::section {
    background-color: #252526;
    color: #cccccc;
    padding: 12px 14px;
    border: none;
    border-right: 1px solid #2d2d2d;
    border-bottom: 1px solid #2d2d2d;
    font-weight: 600;
    font-size: 13px;
    font-family: "Segoe UI", "Microsoft YaHei UI", sans-serif;
}

QHeaderView::section:hover {
    background-color: #2d2d2d;
    color: #ffffff;
}

/* Cursor风格输入框样式 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #1e1e1e;
    border: 1px solid #464647;
    border-radius: 2px;  /* 最小圆角 */
    padding: 10px 14px;
    color: #cccccc;
    font-size: 13px;
    font-family: "Consolas", "Monaco", "Courier New", monospace;
    selection-background-color: #264f78;
    selection-color: #ffffff;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #007acc;
    background-color: #1e1e1e;
    outline: none;
}

/* 下拉框样式 */
QComboBox {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 8px 12px;
    color: #ffffff;
    font-size: 13px;
    min-width: 100px;
}

QComboBox:hover {
    border-color: #666666;
    background-color: #2d2d2d;
}

QComboBox:focus {
    border-color: #0078d4;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
    background-color: transparent;
}

QComboBox::down-arrow {
    width: 12px;
    height: 12px;
    background-color: #888888;
}

QComboBox QAbstractItemView {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    color: #ffffff;
    selection-background-color: #0078d4;
    outline: none;
}

/* 标签样式 */
QLabel {
    color: #e0e0e0;
    background-color: transparent;
    font-size: 13px;
}

QLabel[class="title"] {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
}

QLabel[class="subtitle"] {
    font-size: 14px;
    font-weight: 500;
    color: #cccccc;
}

QLabel[class="caption"] {
    font-size: 11px;
    color: #888888;
}

/* 分组框样式 */
QGroupBox {
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 6px;
    margin-top: 12px;
    padding-top: 8px;
    font-weight: 500;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 12px;
    padding: 0 8px;
    background-color: #1e1e1e;
}

/* Cursor风格滚动条样式 */
QScrollBar:vertical {
    background-color: #1e1e1e;
    width: 14px;
    border-radius: 0px;  /* 方形滚动条 */
    margin: 0;
    border: none;
}

QScrollBar::handle:vertical {
    background-color: #464647;
    border-radius: 0px;  /* 方形滑块 */
    min-height: 30px;
    margin: 0;
}

QScrollBar::handle:vertical:hover {
    background-color: #555555;
}

QScrollBar::handle:vertical:pressed {
    background-color: #666666;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0;
}

QScrollBar:horizontal {
    background-color: #1e1e1e;
    height: 14px;
    border-radius: 0px;  /* 方形滚动条 */
    margin: 0;
    border: none;
}

QScrollBar::handle:horizontal {
    background-color: #464647;
    border-radius: 0px;  /* 方形滑块 */
    min-width: 30px;
    margin: 0;
}

QScrollBar::handle:horizontal:hover {
    background-color: #555555;
}

QScrollBar::handle:horizontal:pressed {
    background-color: #666666;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
    width: 0;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #404040;
}

QSplitter::handle:horizontal {
    width: 4px;
}

QSplitter::handle:vertical {
    height: 4px;
}

QSplitter::handle:hover {
    background-color: #0078d4;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #2d2d2d;
    border-top: 1px solid #404040;
    color: #e0e0e0;
    font-size: 12px;
}

QStatusBar QLabel {
    color: #cccccc;
    font-size: 12px;
    padding: 4px 8px;
}

/* 菜单样式 */
QMenuBar {
    background-color: #2d2d2d;
    color: #e0e0e0;
    border-bottom: 1px solid #404040;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 12px;
}

QMenuBar::item:hover {
    background-color: #3d3d3d;
}

QMenuBar::item:pressed {
    background-color: #0078d4;
}

QMenu {
    background-color: #2a2a2a;
    border: 1px solid #555555;
    color: #e0e0e0;
    padding: 4px;
}

QMenu::item {
    padding: 8px 24px;
    border-radius: 4px;
    margin: 1px;
}

QMenu::item:hover {
    background-color: #0078d4;
    color: #ffffff;
}

QMenu::separator {
    height: 1px;
    background-color: #555555;
    margin: 4px 8px;
}

/* 对话框样式 */
QDialog {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

/* 消息框样式 */
QMessageBox {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

QMessageBox QPushButton {
    min-width: 80px;
    margin: 4px;
}

/* Cursor风格特殊控件样式 */
QFrame[class="control-panel"] {
    background-color: #252526;
    border: 1px solid #333333;
    border-radius: 0px;  /* 去掉圆角 */
}

QFrame[class="product-card"] {
    background-color: #252526;
    border: 1px solid #333333;
    border-radius: 2px;  /* 最小圆角 */
}

QFrame[class="product-card"]:hover {
    border-color: #007acc;
    background-color: #2d2d2d;
}

/* 工具提示样式 */
QToolTip {
    background-color: #333333;
    color: #ffffff;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 6px;
    font-size: 12px;
} 