#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能测试脚本
测试商品对比工具的各项功能
"""

import os
import sys
import unittest
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.db_manager import DBManager
from models.product import Product, ProductSource, CompareGroup
from utils.image_utils import ImageUtils
from utils.import_export import ImportExportManager


class TestDatabaseFunctionality(unittest.TestCase):
    """测试数据库功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db_manager = DBManager(self.test_db_path)
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
    
    def test_group_operations(self):
        """测试对比组操作"""
        # 添加对比组
        group_id = self.db_manager.add_group("测试对比组", "这是一个测试对比组")
        self.assertIsInstance(group_id, int)
        self.assertGreater(group_id, 0)
        
        # 获取对比组
        groups = self.db_manager.get_all_groups()
        self.assertEqual(len(groups), 1)
        self.assertEqual(groups[0]['name'], "测试对比组")
        
        # 更新对比组
        success = self.db_manager.update_group(group_id, "更新的对比组", "更新的描述")
        self.assertTrue(success)
        
        # 验证更新
        groups = self.db_manager.get_all_groups()
        self.assertEqual(groups[0]['name'], "更新的对比组")
        
        # 删除对比组
        success = self.db_manager.delete_group(group_id)
        self.assertTrue(success)
        
        # 验证删除
        groups = self.db_manager.get_all_groups()
        self.assertEqual(len(groups), 0)
    
    def test_product_operations(self):
        """测试商品操作"""
        # 先创建对比组
        group_id = self.db_manager.add_group("测试对比组", "测试")
        
        # 添加商品
        product_id = self.db_manager.add_product(group_id, "测试商品", "测试商品描述")
        self.assertIsInstance(product_id, int)
        self.assertGreater(product_id, 0)
        
        # 获取商品
        products = self.db_manager.get_products_by_group(group_id)
        self.assertEqual(len(products), 1)
        self.assertEqual(products[0]['name'], "测试商品")
        
        # 更新商品
        success = self.db_manager.update_product(product_id, "更新的商品", "更新的描述")
        self.assertTrue(success)
        
        # 删除商品
        success = self.db_manager.delete_product(product_id)
        self.assertTrue(success)
    
    def test_source_operations(self):
        """测试来源操作"""
        # 创建对比组和商品
        group_id = self.db_manager.add_group("测试对比组", "测试")
        product_id = self.db_manager.add_product(group_id, "测试商品", "测试")
        
        # 添加来源
        source_id = self.db_manager.add_source(
            product_id, "淘宝", 99.99, 10.0, 100, "https://test.com", "测试备注"
        )
        self.assertIsInstance(source_id, int)
        self.assertGreater(source_id, 0)
        
        # 获取来源
        sources = self.db_manager.get_sources_by_product(product_id)
        self.assertEqual(len(sources), 1)
        self.assertEqual(sources[0]['source_name'], "淘宝")
        self.assertEqual(sources[0]['price'], 99.99)
        
        # 更新来源
        success = self.db_manager.update_source(source_id, price=89.99)
        self.assertTrue(success)
        
        # 验证更新
        sources = self.db_manager.get_sources_by_product(product_id)
        self.assertEqual(sources[0]['price'], 89.99)
        
        # 删除来源
        success = self.db_manager.delete_source(source_id)
        self.assertTrue(success)


class TestDataModels(unittest.TestCase):
    """测试数据模型"""
    
    def test_product_source_model(self):
        """测试商品来源模型"""
        source = ProductSource(
            source_name="淘宝",
            price=99.99,
            shipping=10.0,
            stock=100,
            url="https://test.com",
            note="测试"
        )
        
        # 测试总价计算
        self.assertEqual(source.get_total_price(), 109.99)
        
        # 测试库存检查
        self.assertTrue(source.is_in_stock())
        
        source.stock = 0
        self.assertFalse(source.is_in_stock())
        
        # 测试显示名称
        self.assertEqual(source.get_display_name(), "淘宝 - 测试")
    
    def test_product_model(self):
        """测试商品模型"""
        product = Product(name="测试商品", description="测试描述")
        
        # 添加来源
        source1 = ProductSource(source_name="淘宝", price=99.99)
        source2 = ProductSource(source_name="京东", price=89.99)
        source3 = ProductSource(source_name="拼多多", price=79.99, stock=0)
        
        product.add_source(source1)
        product.add_source(source2)
        product.add_source(source3)
        
        # 测试价格统计
        self.assertEqual(product.get_min_price(), 79.99)
        self.assertEqual(product.get_max_price(), 99.99)
        self.assertAlmostEqual(product.get_avg_price(), 89.99, places=2)
        
        # 测试最优来源（有库存且价格最低）
        best_source = product.get_best_source()
        self.assertEqual(best_source.source_name, "京东")  # 拼多多没库存
        
        # 测试价格范围文本
        price_range = product.get_price_range_text()
        self.assertIn("79.99", price_range)
        self.assertIn("99.99", price_range)


class TestImageUtils(unittest.TestCase):
    """测试图片工具"""
    
    def setUp(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.image_utils = ImageUtils(self.temp_dir)
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_format_support(self):
        """测试格式支持"""
        self.assertTrue(self.image_utils.is_supported_format("test.jpg"))
        self.assertTrue(self.image_utils.is_supported_format("test.png"))
        self.assertFalse(self.image_utils.is_supported_format("test.txt"))
    
    def test_directory_creation(self):
        """测试目录创建"""
        self.assertTrue(os.path.exists(self.temp_dir))


class TestImportExport(unittest.TestCase):
    """测试导入导出功能"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_db_path = tempfile.mktemp(suffix='.db')
        self.db_manager = DBManager(self.test_db_path)
        self.import_export_manager = ImportExportManager(self.db_manager)
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_template_generation(self):
        """测试模板生成"""
        template_path = os.path.join(self.temp_dir, "template.xlsx")
        success = self.import_export_manager.get_excel_template(template_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(template_path))
    
    def test_export_functionality(self):
        """测试导出功能"""
        # 创建测试数据
        group_id = self.db_manager.add_group("测试对比组", "测试")
        product_id = self.db_manager.add_product(group_id, "测试商品", "测试")
        self.db_manager.add_source(product_id, "淘宝", 99.99, 10.0, 100, "https://test.com", "测试")
        
        # 测试导出
        export_path = os.path.join(self.temp_dir, "export.xlsx")
        success = self.import_export_manager.export_group_to_excel(group_id, export_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(export_path))


def run_performance_test():
    """运行性能测试"""
    print("开始性能测试...")
    
    # 创建临时数据库
    test_db_path = tempfile.mktemp(suffix='.db')
    db_manager = DBManager(test_db_path)
    
    try:
        # 测试大量数据插入性能
        start_time = datetime.now()
        
        # 创建100个对比组
        group_ids = []
        for i in range(100):
            group_id = db_manager.add_group(f"对比组{i}", f"描述{i}")
            group_ids.append(group_id)
        
        # 每个对比组创建10个商品
        product_ids = []
        for group_id in group_ids:
            for j in range(10):
                product_id = db_manager.add_product(group_id, f"商品{j}", f"描述{j}")
                product_ids.append(product_id)
        
        # 每个商品创建5个来源
        for product_id in product_ids:
            for k in range(5):
                db_manager.add_source(
                    product_id, f"来源{k}", 99.99 + k, 10.0, 100, 
                    f"https://test{k}.com", f"备注{k}"
                )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"插入性能测试完成:")
        print(f"- 创建了100个对比组")
        print(f"- 创建了1000个商品")
        print(f"- 创建了5000个来源")
        print(f"- 总耗时: {duration:.2f}秒")
        
        # 测试查询性能
        start_time = datetime.now()
        
        # 查询所有对比组
        groups = db_manager.get_all_groups_with_counts()
        
        # 查询第一个对比组的完整数据
        if groups:
            group_data = db_manager.get_group_with_products_and_sources(groups[0]['id'])
        
        end_time = datetime.now()
        query_duration = (end_time - start_time).total_seconds()
        
        print(f"查询性能测试完成:")
        print(f"- 查询所有对比组: {len(groups)}个")
        print(f"- 查询完整对比组数据")
        print(f"- 查询耗时: {query_duration:.2f}秒")
        
    finally:
        # 清理
        if os.path.exists(test_db_path):
            os.remove(test_db_path)
    
    print("性能测试完成!")


def main():
    """主函数"""
    print("=" * 50)
    print("商品对比工具 - 功能测试")
    print("=" * 50)
    
    # 运行单元测试
    print("\n1. 运行单元测试...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行性能测试
    print("\n2. 运行性能测试...")
    run_performance_test()
    
    print("\n" + "=" * 50)
    print("所有测试完成!")
    print("=" * 50)


if __name__ == "__main__":
    main()
