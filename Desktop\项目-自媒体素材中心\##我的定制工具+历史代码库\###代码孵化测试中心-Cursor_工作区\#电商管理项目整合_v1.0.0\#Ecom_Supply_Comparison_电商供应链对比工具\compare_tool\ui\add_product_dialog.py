#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加商品对话框模块
实现添加/编辑商品的对话框界面
"""

import os
import sys
from typing import List, Optional, Dict
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QGridLayout,
    QLineEdit,
    QTextEdit,
    QComboBox,
    QPushButton,
    QLabel,
    QGroupBox,
    QScrollArea,
    QWidget,
    QFileDialog,
    QMessageBox,
    QSpinBox,
    QDoubleSpinBox,
    QFrame,
    QSizePolicy,
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QPixmap, QIcon

# 导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.product import Product, ProductSource
from utils.image_utils import image_utils
from ui.image_preview_dialog import show_image_preview


class SourceWidget(QWidget):
    """单个来源信息输入控件"""

    # 删除信号
    delete_requested = pyqtSignal(object)  # 传递自身引用

    def __init__(self, source_data: ProductSource = None, parent=None):
        super().__init__(parent)
        self.source_data = source_data or ProductSource()
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建框架
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box)
        frame.setLineWidth(1)
        frame.setStyleSheet("""
            QFrame {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 2px;
            }
            QLabel {
                color: #ffffff;
                font-weight: bold;
                padding: 2px;
            }
            QComboBox, QDoubleSpinBox, QSpinBox, QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 6px;
                font-size: 12px;
            }
            QComboBox:hover, QDoubleSpinBox:hover, QSpinBox:hover, QLineEdit:hover {
                border-color: #0078d4;
            }
            QComboBox:focus, QDoubleSpinBox:focus, QSpinBox:focus, QLineEdit:focus {
                border-color: #0078d4;
                background-color: #454545;
            }
            QComboBox::drop-down {
                border: none;
                background-color: #555555;
                border-radius: 2px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #ffffff;
                border-top: none;
                border-left: 2px solid transparent;
                border-right: 2px solid transparent;
            }
        """)
        layout.addWidget(frame)

        # 框架内布局
        frame_layout = QFormLayout(frame)

        # 来源名称
        self.source_name_combo = QComboBox()
        self.source_name_combo.setEditable(True)
        self.source_name_combo.addItems(
            ["淘宝", "天猫", "京东", "拼多多", "1688", "苏宁", "国美", "亚马逊", "其他"]
        )
        frame_layout.addRow("来源平台:", self.source_name_combo)

        # 价格和运费
        price_layout = QHBoxLayout()
        self.price_spinbox = QDoubleSpinBox()
        self.price_spinbox.setRange(0, 999999.99)
        self.price_spinbox.setDecimals(2)
        self.price_spinbox.setSuffix(" 元")
        price_layout.addWidget(self.price_spinbox)

        price_layout.addWidget(QLabel("运费:"))
        self.shipping_spinbox = QDoubleSpinBox()
        self.shipping_spinbox.setRange(0, 9999.99)
        self.shipping_spinbox.setDecimals(2)
        self.shipping_spinbox.setSuffix(" 元")
        price_layout.addWidget(self.shipping_spinbox)

        frame_layout.addRow("价格:", price_layout)

        # 库存
        self.stock_spinbox = QSpinBox()
        self.stock_spinbox.setRange(0, 999999)
        self.stock_spinbox.setSpecialValueText("无限制")
        frame_layout.addRow("库存:", self.stock_spinbox)

        # 链接
        self.url_edit = QLineEdit()
        self.url_edit.setPlaceholderText("请输入商品链接")
        frame_layout.addRow("链接:", self.url_edit)

        # 备注
        self.note_edit = QLineEdit()
        self.note_edit.setPlaceholderText("备注信息（如：店铺名称、规格等）")
        frame_layout.addRow("备注:", self.note_edit)

        # 删除按钮
        delete_btn = QPushButton("删除此来源")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        delete_btn.clicked.connect(lambda: self.delete_requested.emit(self))
        frame_layout.addRow("", delete_btn)

    def load_data(self):
        """加载数据到控件"""
        if self.source_data.source_name:
            self.source_name_combo.setCurrentText(self.source_data.source_name)

        if self.source_data.price is not None:
            self.price_spinbox.setValue(self.source_data.price)

        self.shipping_spinbox.setValue(self.source_data.shipping)

        if self.source_data.stock is not None:
            self.stock_spinbox.setValue(self.source_data.stock)

        if self.source_data.url:
            self.url_edit.setText(self.source_data.url)

        if self.source_data.note:
            self.note_edit.setText(self.source_data.note)

    def get_data(self) -> ProductSource:
        """获取控件数据"""
        source = ProductSource()
        source.id = self.source_data.id
        source.product_id = self.source_data.product_id
        source.source_name = self.source_name_combo.currentText().strip()
        source.price = (
            self.price_spinbox.value() if self.price_spinbox.value() > 0 else None
        )
        source.shipping = self.shipping_spinbox.value()
        source.stock = (
            self.stock_spinbox.value() if self.stock_spinbox.value() > 0 else None
        )
        source.url = self.url_edit.text().strip()
        source.note = self.note_edit.text().strip()
        return source

    def validate(self) -> tuple[bool, str]:
        """验证数据"""
        if not self.source_name_combo.currentText().strip():
            return False, "来源平台不能为空"

        # 允许价格为0，支持快速建立商品
        # 价格验证改为可选，只要不为负数即可
        if self.price_spinbox.value() < 0:
            return False, "价格不能为负数"

        return True, ""


class AddProductDialog(QDialog):
    """添加/编辑商品对话框"""

    def __init__(self, group_id: int, product: Product = None, parent=None, default_platform: str = None, default_name: str = None):
        super().__init__(parent)
        self.group_id = group_id
        self.product = product or Product()
        self.product.group_id = group_id
        self.default_platform = default_platform
        self.default_name = default_name

        # 来源控件列表
        self.source_widgets: List[SourceWidget] = []

        # 图片路径
        self.image_path = self.product.image_path

        self.init_ui()
        self.load_data()

        # 设置窗口属性
        self.setWindowTitle("编辑商品" if product else "添加商品")
        self.setModal(True)
        self.resize(750, 1000)
        
        # 设置整体暗黑主题样式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QGroupBox {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 8px 0;
                padding-top: 15px;
                font-weight: bold;
                color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #ffffff;
                font-weight: bold;
            }
            QLabel {
                color: #ffffff;
                font-weight: bold;
                padding: 2px;
            }
            QLineEdit, QTextEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 6px;
                font-size: 12px;
            }
            QLineEdit:hover, QTextEdit:hover {
                border-color: #0078d4;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #0078d4;
                background-color: #454545;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QScrollArea {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 6px;
            }
        """)

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        # 商品名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入商品名称")
        basic_layout.addRow("商品名称*:", self.name_edit)

        # 商品描述
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("请输入商品描述")
        basic_layout.addRow("商品描述:", self.description_edit)

        # 商品图片
        image_layout = QHBoxLayout()

        self.image_label = QLabel()
        self.image_label.setFixedSize(120, 120)
        self.image_label.setStyleSheet("border: 1px solid gray; cursor: pointer;")
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setText("无图片")
        self.image_label.mousePressEvent = self.on_image_clicked
        image_layout.addWidget(self.image_label)

        image_btn_layout = QVBoxLayout()
        self.upload_image_btn = QPushButton("上传图片")
        self.upload_image_btn.clicked.connect(self.upload_image)
        image_btn_layout.addWidget(self.upload_image_btn)

        self.remove_image_btn = QPushButton("移除图片")
        self.remove_image_btn.clicked.connect(self.remove_image)
        self.remove_image_btn.setEnabled(False)
        image_btn_layout.addWidget(self.remove_image_btn)

        image_btn_layout.addStretch()
        image_layout.addLayout(image_btn_layout)
        image_layout.addStretch()

        basic_layout.addRow("商品图片:", image_layout)

        layout.addWidget(basic_group)

        # 来源信息组
        sources_group = QGroupBox("来源信息")
        sources_layout = QVBoxLayout(sources_group)

        # 添加来源按钮
        add_source_btn = QPushButton("添加来源")
        add_source_btn.setIcon(QIcon("assets/icons/add.png"))
        add_source_btn.clicked.connect(self.add_source)
        sources_layout.addWidget(add_source_btn)

        # 来源列表滚动区域
        self.sources_scroll = QScrollArea()
        self.sources_scroll.setWidgetResizable(True)
        self.sources_scroll.setMinimumHeight(200)

        self.sources_container = QWidget()
        self.sources_layout = QVBoxLayout(self.sources_container)
        self.sources_layout.addStretch()

        self.sources_scroll.setWidget(self.sources_container)
        sources_layout.addWidget(self.sources_scroll)

        layout.addWidget(sources_group)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_product)
        self.save_btn.setDefault(True)
        button_layout.addWidget(self.save_btn)

        layout.addLayout(button_layout)

    def load_data(self):
        """加载数据"""
        # 应用默认名称（如果提供且当前商品没有名称）
        if self.default_name and not self.product.name:
            self.name_edit.setText(self.default_name)
        elif self.product.name:
            self.name_edit.setText(self.product.name)

        if self.product.description:
            self.description_edit.setPlainText(self.product.description)

        if self.product.image_path:
            self.load_image(self.product.image_path)

        # 加载来源信息
        if self.product.sources:
            for source in self.product.sources:
                self.add_source(source)
        else:
            # 如果没有来源，添加一个空的，并应用默认平台
            if self.default_platform:
                default_source = ProductSource()
                default_source.source_name = self.default_platform
                self.add_source(default_source)
            else:
                self.add_source()

    def load_image(self, image_path: str):
        """加载图片"""
        pixmap = image_utils.load_pixmap(image_path, QSize(120, 120))
        if pixmap:
            self.image_label.setPixmap(pixmap)
            self.image_label.setScaledContents(True)
            self.remove_image_btn.setEnabled(True)
            self.image_path = image_path
        else:
            self.image_label.setText("图片加载失败")

    def upload_image(self):
        """上传图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择图片",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.webp *.tiff *.tif *.ico *.svg *.avif *.heic *.jp2 *.jpx *.j2k *.jpc);;常用格式 (*.png *.jpg *.jpeg *.bmp *.gif *.webp);;所有文件 (*)",
        )

        if file_path:
            try:
                # 保存图片到项目目录
                saved_path = image_utils.save_image(file_path, self.product.id)
                if saved_path:
                    self.load_image(saved_path)
                else:
                    QMessageBox.warning(self, "警告", "图片保存失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"上传图片失败: {e}")

    def remove_image(self):
        """移除图片"""
        self.image_label.clear()
        self.image_label.setText("无图片")
        self.remove_image_btn.setEnabled(False)
        self.image_path = ""

    def on_image_clicked(self, event):
        """图片点击事件"""
        if self.image_path and os.path.exists(self.image_path):
            # 显示图片预览
            new_path = show_image_preview(self.image_path, self)
            if new_path and new_path != self.image_path:
                # 图片被替换了
                self.image_path = new_path
                self.load_image(new_path)
        else:
            # 如果没有图片，点击上传
            self.upload_image()

    def add_source(self, source_data: ProductSource = None):
        """添加来源控件"""
        source_widget = SourceWidget(source_data, self)
        source_widget.delete_requested.connect(self.remove_source)

        # 插入到stretch之前
        self.sources_layout.insertWidget(self.sources_layout.count() - 1, source_widget)
        self.source_widgets.append(source_widget)

        # 更新滚动区域
        self.sources_container.updateGeometry()

    def remove_source(self, source_widget: SourceWidget):
        """移除来源控件"""
        if len(self.source_widgets) <= 1:
            QMessageBox.warning(self, "警告", "至少需要保留一个来源")
            return

        reply = QMessageBox.question(
            self,
            "确认删除",
            "确定要删除这个来源吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.sources_layout.removeWidget(source_widget)
            self.source_widgets.remove(source_widget)
            source_widget.deleteLater()

            # 更新滚动区域
            self.sources_container.updateGeometry()

    def validate_data(self) -> tuple[bool, str]:
        """验证数据"""
        # 验证基本信息
        if not self.name_edit.text().strip():
            return False, "商品名称不能为空"

        # 验证来源信息
        if not self.source_widgets:
            return False, "至少需要添加一个来源"

        valid_sources = 0
        for i, source_widget in enumerate(self.source_widgets):
            is_valid, error_msg = source_widget.validate()
            if not is_valid:
                return False, f"来源 {i+1}: {error_msg}"
            valid_sources += 1

        if valid_sources == 0:
            return False, "至少需要一个有效的来源"

        return True, ""

    def get_product_data(self) -> Product:
        """获取商品数据"""
        product = Product()
        product.id = self.product.id
        product.group_id = self.group_id
        product.name = self.name_edit.text().strip()
        product.description = self.description_edit.toPlainText().strip()
        product.image_path = self.image_path

        # 获取来源数据
        product.sources = []
        for source_widget in self.source_widgets:
            source_data = source_widget.get_data()
            if source_data.source_name:  # 只添加有效的来源
                product.sources.append(source_data)

        return product

    def save_product(self):
        """保存商品"""
        # 验证数据
        is_valid, error_msg = self.validate_data()
        if not is_valid:
            QMessageBox.warning(self, "数据验证失败", error_msg)
            return

        try:
            # 获取商品数据
            product_data = self.get_product_data()

            # 设置结果
            self.product = product_data
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存商品失败: {e}")

    def get_result(self) -> Optional[Product]:
        """获取结果"""
        return self.product if self.result() == QDialog.DialogCode.Accepted else None


# 便捷函数
def show_add_product_dialog(group_id: int, parent=None, default_platform: str = None, default_name: str = None) -> Optional[Product]:
    """显示添加商品对话框"""
    dialog = AddProductDialog(group_id, None, parent, default_platform, default_name)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_result()
    return None


def show_edit_product_dialog(
    group_id: int, product: Product, parent=None
) -> Optional[Product]:
    """显示编辑商品对话框"""
    dialog = AddProductDialog(group_id, product, parent)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_result()
    return None


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication
    from models.product import create_sample_data

    app = QApplication(sys.argv)

    # 测试添加商品对话框
    result = show_add_product_dialog(1)
    if result:
        print("添加商品成功:")
        print(f"名称: {result.name}")
        print(f"描述: {result.description}")
        print(f"来源数量: {len(result.sources)}")
        for i, source in enumerate(result.sources):
            print(f"  来源 {i+1}: {source.source_name} - ¥{source.price}")
    else:
        print("用户取消了操作")

    sys.exit(app.exec())
