from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QScrollArea,
    QLabel, QFrame, QPushButton, QTableWidget, QTableWidgetItem,
    QHeaderView, QSplitter, QTextEdit, QMessageBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap
from models.product import Product, ProductSource
from utils.logger import log_error_with_context
import os


class CompareWindow(QMainWindow):
    """商品对比窗口"""
    
    def __init__(self, products_data, parent=None):
        super().__init__(parent)
        self.products_data = products_data
        self.products = []
        self.setup_ui()
        self.load_products()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("商品对比")
        self.setGeometry(100, 100, 1200, 800)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
            }
            QFrame {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
            }
            QTableWidget {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 6px;
                gridline-color: #555555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #555555;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
            }
            QHeaderView::section {
                background-color: #353535;
                color: #ffffff;
                padding: 10px;
                border: 1px solid #555555;
                font-weight: bold;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QScrollArea {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 6px;
            }
        """)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title = QLabel("商品对比分析")
        title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                background-color: #353535;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：商品卡片展示
        self.create_product_cards_section(splitter)
        
        # 下半部分：对比表格
        self.create_comparison_table_section(splitter)
        
        # 设置分割器比例
        splitter.setSizes([300, 500])
        layout.addWidget(splitter)
        
        # 底部按钮
        self.create_bottom_buttons(layout)
        
    def create_product_cards_section(self, parent):
        """创建商品卡片展示区域"""
        cards_frame = QFrame()
        cards_layout = QVBoxLayout(cards_frame)
        cards_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        cards_title = QLabel("商品概览")
        cards_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        cards_layout.addWidget(cards_title)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(250)
        
        # 卡片容器
        cards_container = QWidget()
        self.cards_layout = QHBoxLayout(cards_container)
        self.cards_layout.setSpacing(10)
        self.cards_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        scroll_area.setWidget(cards_container)
        cards_layout.addWidget(scroll_area)
        
        parent.addWidget(cards_frame)
        
    def create_comparison_table_section(self, parent):
        """创建对比表格区域"""
        table_frame = QFrame()
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        table_title = QLabel("详细对比")
        table_title.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        table_layout.addWidget(table_title)
        
        # 对比表格
        self.comparison_table = QTableWidget()
        self.comparison_table.setAlternatingRowColors(True)
        table_layout.addWidget(self.comparison_table)
        
        parent.addWidget(table_frame)
        
    def create_bottom_buttons(self, parent_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        export_btn = QPushButton("导出对比报告")
        export_btn.clicked.connect(self.export_report)
        button_layout.addWidget(export_btn)
        
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)
        
        parent_layout.addLayout(button_layout)
        
    def load_products(self):
        """加载商品数据"""
        try:
            # 转换为Product对象
            for product_data in self.products_data:
                if isinstance(product_data, dict):
                    product = Product.from_dict(product_data)
                else:
                    product = product_data
                self.products.append(product)
            
            # 创建商品卡片
            self.create_product_cards()
            
            # 创建对比表格
            self.create_comparison_table()
            
        except Exception as e:
            log_error_with_context(e, "加载商品数据失败")
            QMessageBox.critical(self, "错误", f"加载商品数据失败: {str(e)}")
            
    def create_product_cards(self):
        """创建商品卡片"""
        for product in self.products:
            card = self.create_product_card(product)
            self.cards_layout.addWidget(card)
            
    def create_product_card(self, product: Product) -> QWidget:
        """创建单个商品卡片"""
        card = QFrame()
        card.setFixedSize(200, 220)
        card.setStyleSheet("""
            QFrame {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 2px;
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(6)
        
        # 商品图片
        image_label = QLabel()
        image_label.setFixedSize(180, 120)
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        image_label.setStyleSheet("""
            QLabel {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 4px;
            }
        """)
        
        if product.image_path and os.path.exists(product.image_path):
            pixmap = QPixmap(product.image_path)
            scaled_pixmap = pixmap.scaled(178, 118, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            image_label.setPixmap(scaled_pixmap)
        else:
            image_label.setText("无图片")
            image_label.setStyleSheet(image_label.styleSheet() + "color: #adb5bd; font-size: 12px;")
        
        layout.addWidget(image_label)
        
        # 商品名称
        name_label = QLabel(product.name or "未命名商品")
        name_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        name_label.setWordWrap(True)
        name_label.setMaximumHeight(30)
        layout.addWidget(name_label)
        
        # 来源数量
        sources_label = QLabel(f"来源: {len(product.sources)}")
        sources_label.setStyleSheet("""
            QLabel {
                color: #28a745;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        layout.addWidget(sources_label)
        
        # 价格范围
        if product.sources:
            prices = [s.price for s in product.sources if s.price is not None]
            if prices:
                min_price = min(prices)
                max_price = max(prices)
                if min_price == max_price:
                    price_text = f"¥{min_price:.2f}"
                else:
                    price_text = f"¥{min_price:.2f}-{max_price:.2f}"
            else:
                price_text = "价格待定"
        else:
            price_text = "无来源"
            
        price_label = QLabel(price_text)
        price_label.setStyleSheet("""
            QLabel {
                color: #dc3545;
                font-size: 10px;
                font-weight: bold;
            }
        """)
        layout.addWidget(price_label)
        
        layout.addStretch()
        return card
        
    def create_comparison_table(self):
        """创建对比表格"""
        if not self.products:
            return
            
        # 设置表格行列数
        rows = ["商品名称", "品牌", "标签", "来源数量", "最低价格", "最高价格", "平均价格", "价格差异"]
        self.comparison_table.setRowCount(len(rows))
        self.comparison_table.setColumnCount(len(self.products))
        
        # 设置表头
        self.comparison_table.setVerticalHeaderLabels(rows)
        column_headers = [f"商品 {i+1}" for i in range(len(self.products))]
        self.comparison_table.setHorizontalHeaderLabels(column_headers)
        
        # 填充数据
        for col, product in enumerate(self.products):
            # 商品名称
            self.comparison_table.setItem(0, col, QTableWidgetItem(product.name or "未命名"))
            
            # 品牌
            self.comparison_table.setItem(1, col, QTableWidgetItem(product.brand or "未知品牌"))
            
            # 标签
            self.comparison_table.setItem(2, col, QTableWidgetItem(product.tags or "无标签"))
            
            # 来源数量
            self.comparison_table.setItem(3, col, QTableWidgetItem(str(len(product.sources))))
            
            # 价格信息
            if product.sources:
                prices = [s.price for s in product.sources if s.price is not None]
                if prices:
                    min_price = min(prices)
                    max_price = max(prices)
                    avg_price = sum(prices) / len(prices)
                    price_diff = max_price - min_price
                    
                    self.comparison_table.setItem(4, col, QTableWidgetItem(f"¥{min_price:.2f}"))
                    self.comparison_table.setItem(5, col, QTableWidgetItem(f"¥{max_price:.2f}"))
                    self.comparison_table.setItem(6, col, QTableWidgetItem(f"¥{avg_price:.2f}"))
                    self.comparison_table.setItem(7, col, QTableWidgetItem(f"¥{price_diff:.2f}"))
                else:
                    for row in range(4, 8):
                        self.comparison_table.setItem(row, col, QTableWidgetItem("无价格数据"))
            else:
                for row in range(4, 8):
                    self.comparison_table.setItem(row, col, QTableWidgetItem("无来源"))
        
        # 调整列宽
        self.comparison_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        
    def export_report(self):
        """导出对比报告"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出对比报告", "商品对比报告.txt", "文本文件 (*.txt)"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("商品对比报告\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for i, product in enumerate(self.products):
                        f.write(f"商品 {i+1}: {product.name}\n")
                        f.write(f"品牌: {product.brand or '未知'}\n")
                        f.write(f"标签: {product.tags or '无'}\n")
                        f.write(f"来源数量: {len(product.sources)}\n")
                        
                        if product.sources:
                            prices = [s.price for s in product.sources if s.price is not None]
                            if prices:
                                f.write(f"价格范围: ¥{min(prices):.2f} - ¥{max(prices):.2f}\n")
                                f.write(f"平均价格: ¥{sum(prices)/len(prices):.2f}\n")
                        
                        f.write("-" * 30 + "\n")
                        
                QMessageBox.information(self, "成功", f"对比报告已导出到: {file_path}")
                
        except Exception as e:
            log_error_with_context(e, "导出报告失败")
            QMessageBox.critical(self, "错误", f"导出报告失败: {str(e)}")