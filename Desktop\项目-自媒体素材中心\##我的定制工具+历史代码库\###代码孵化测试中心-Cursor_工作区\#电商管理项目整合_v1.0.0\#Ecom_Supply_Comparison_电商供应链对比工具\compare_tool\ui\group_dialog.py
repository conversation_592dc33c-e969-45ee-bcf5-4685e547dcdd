from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QTextEdit, QPushButton, QLabel,
    QMessageBox, QFrame
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from models.compare_group import CompareGroup
from utils.logger import log_error_with_context


class GroupDialog(QDialog):
    """分组创建/编辑对话框"""
    
    def __init__(self, db_manager, group_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.group_id = group_id
        self.group = None
        
        if group_id:
            self.group = self.db_manager.get_group_by_id(group_id)
            
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("编辑分组" if self.group_id else "新建分组")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
            }
            QLineEdit, QTextEdit {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px;
                color: #ffffff;
                font-size: 14px;
            }
            QLineEdit:focus, QTextEdit:focus {
                border-color: #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton#cancel_btn {
                background-color: #6c757d;
            }
            QPushButton#cancel_btn:hover {
                background-color: #5a6268;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("编辑分组" if self.group_id else "新建分组")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setStyleSheet("QFrame { color: #555555; }")
        layout.addWidget(line)
        
        # 表单
        form_layout = QFormLayout()
        form_layout.setSpacing(12)
        
        # 分组名称
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入分组名称")
        form_layout.addRow("分组名称:", self.name_input)
        
        # 分组描述
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("请输入分组描述（可选）")
        self.description_input.setMaximumHeight(80)
        form_layout.addRow("分组描述:", self.description_input)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("cancel_btn")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.save_group)
        button_layout.addWidget(self.save_btn)
        
        layout.addLayout(button_layout)
        
    def load_data(self):
        """加载数据"""
        if self.group:
            self.name_input.setText(self.group.name)
            self.description_input.setPlainText(self.group.description or "")
            
    def save_group(self):
        """保存分组"""
        try:
            name = self.name_input.text().strip()
            description = self.description_input.toPlainText().strip()
            
            if not name:
                QMessageBox.warning(self, "警告", "请输入分组名称")
                self.name_input.setFocus()
                return
                
            # 检查名称是否重复
            existing_groups = self.db_manager.get_all_groups()
            for group in existing_groups:
                if group.name == name and (not self.group_id or group.id != self.group_id):
                    QMessageBox.warning(self, "警告", "分组名称已存在，请使用其他名称")
                    self.name_input.setFocus()
                    return
            
            if self.group_id:
                # 编辑现有分组
                self.group.name = name
                self.group.description = description
                self.db_manager.update_group(self.group)
            else:
                # 创建新分组
                group = CompareGroup(
                    name=name,
                    description=description
                )
                self.db_manager.create_group(group)
                
            self.accept()
            
        except Exception as e:
            log_error_with_context(e, "保存分组失败")
            QMessageBox.critical(self, "错误", f"保存分组失败: {str(e)}")