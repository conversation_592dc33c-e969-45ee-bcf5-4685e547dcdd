from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QMessageBox, QFrame, QAbstractItemView
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from ui.group_dialog import GroupDialog
from utils.logger import log_error_with_context


class GroupManagerDialog(QDialog):
    """分组管理对话框"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_groups()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("分组管理")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 设置样式
        self.setStyleSheet("""
            QDialog {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 14px;
            }
            QListWidget {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 4px;
            }
            QListWidget::item {
                padding: 10px;
                margin: 2px;
                border-radius: 4px;
                color: #ffffff;
                border-left: 3px solid transparent;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                border-left-color: #ffffff;
            }
            QListWidget::item:hover {
                background-color: #505050;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
            QPushButton#add_btn {
                background-color: #28a745;
            }
            QPushButton#add_btn:hover {
                background-color: #218838;
            }
            QPushButton#edit_btn {
                background-color: #ffc107;
                color: #212529;
            }
            QPushButton#edit_btn:hover {
                background-color: #e0a800;
            }
            QPushButton#delete_btn {
                background-color: #dc3545;
            }
            QPushButton#delete_btn:hover {
                background-color: #c82333;
            }
            QPushButton#close_btn {
                background-color: #6c757d;
            }
            QPushButton#close_btn:hover {
                background-color: #5a6268;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title = QLabel("分组管理")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setStyleSheet("QFrame { color: #555555; }")
        layout.addWidget(line)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧：分组列表
        list_layout = QVBoxLayout()
        
        list_label = QLabel("分组列表:")
        list_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        list_layout.addWidget(list_label)
        
        self.group_list = QListWidget()
        self.group_list.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.group_list.itemSelectionChanged.connect(self.on_selection_changed)
        list_layout.addWidget(self.group_list)
        
        content_layout.addLayout(list_layout, 2)
        
        # 右侧：操作按钮
        button_layout = QVBoxLayout()
        
        self.add_btn = QPushButton("+ 新建分组")
        self.add_btn.setObjectName("add_btn")
        self.add_btn.clicked.connect(self.add_group)
        button_layout.addWidget(self.add_btn)
        
        self.edit_btn = QPushButton("✏️ 编辑分组")
        self.edit_btn.setObjectName("edit_btn")
        self.edit_btn.setEnabled(False)
        self.edit_btn.clicked.connect(self.edit_group)
        button_layout.addWidget(self.edit_btn)
        
        self.delete_btn = QPushButton("🗑️ 删除分组")
        self.delete_btn.setObjectName("delete_btn")
        self.delete_btn.setEnabled(False)
        self.delete_btn.clicked.connect(self.delete_group)
        button_layout.addWidget(self.delete_btn)
        
        button_layout.addStretch()
        
        # 分组信息
        self.info_label = QLabel("选择分组查看详细信息")
        self.info_label.setStyleSheet("""
            QLabel {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 10px;
                color: #adb5bd;
            }
        """)
        self.info_label.setWordWrap(True)
        self.info_label.setMinimumHeight(80)
        button_layout.addWidget(self.info_label)
        
        content_layout.addLayout(button_layout, 1)
        layout.addLayout(content_layout)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.setObjectName("close_btn")
        self.close_btn.clicked.connect(self.accept)
        bottom_layout.addWidget(self.close_btn)
        
        layout.addLayout(bottom_layout)
        
    def load_groups(self):
        """加载分组列表"""
        try:
            self.group_list.clear()
            groups = self.db_manager.get_all_groups()
            
            for group in groups:
                item = QListWidgetItem(group.name)
                item.setData(Qt.ItemDataRole.UserRole, group)
                self.group_list.addItem(item)
                
        except Exception as e:
            log_error_with_context(e, "加载分组列表失败")
            QMessageBox.critical(self, "错误", f"加载分组列表失败: {str(e)}")
            
    def on_selection_changed(self):
        """选择改变事件"""
        current_item = self.group_list.currentItem()
        if current_item:
            group = current_item.data(Qt.ItemDataRole.UserRole)
            self.edit_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
            # 显示分组信息
            try:
                products = self.db_manager.get_products_by_group(group.id)
                info_text = f"分组名称: {group.name}\n"
                info_text += f"商品数量: {len(products)}\n"
                if group.description:
                    info_text += f"描述: {group.description}"
                else:
                    info_text += "描述: 无"
                self.info_label.setText(info_text)
            except Exception as e:
                self.info_label.setText(f"获取分组信息失败: {str(e)}")
        else:
            self.edit_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.info_label.setText("选择分组查看详细信息")
            
    def add_group(self):
        """添加分组"""
        try:
            dialog = GroupDialog(self.db_manager, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_groups()
        except Exception as e:
            log_error_with_context(e, "添加分组失败")
            QMessageBox.critical(self, "错误", f"添加分组失败: {str(e)}")
            
    def edit_group(self):
        """编辑分组"""
        current_item = self.group_list.currentItem()
        if not current_item:
            return
            
        try:
            group = current_item.data(Qt.ItemDataRole.UserRole)
            dialog = GroupDialog(self.db_manager, group_id=group.id, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_groups()
        except Exception as e:
            log_error_with_context(e, "编辑分组失败")
            QMessageBox.critical(self, "错误", f"编辑分组失败: {str(e)}")
            
    def delete_group(self):
        """删除分组"""
        current_item = self.group_list.currentItem()
        if not current_item:
            return
            
        try:
            group = current_item.data(Qt.ItemDataRole.UserRole)
            
            # 检查分组中是否有商品
            products = self.db_manager.get_products_by_group(group.id)
            if products:
                reply = QMessageBox.question(
                    self, "确认删除", 
                    f"分组 '{group.name}' 中包含 {len(products)} 个商品。\n删除分组后，这些商品将不再属于任何分组。\n确定要删除吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
            else:
                reply = QMessageBox.question(
                    self, "确认删除", 
                    f"确定要删除分组 '{group.name}' 吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                
            if reply == QMessageBox.StandardButton.Yes:
                self.db_manager.delete_group(group.id)
                self.load_groups()
                
        except Exception as e:
            log_error_with_context(e, "删除分组失败")
            QMessageBox.critical(self, "错误", f"删除分组失败: {str(e)}")