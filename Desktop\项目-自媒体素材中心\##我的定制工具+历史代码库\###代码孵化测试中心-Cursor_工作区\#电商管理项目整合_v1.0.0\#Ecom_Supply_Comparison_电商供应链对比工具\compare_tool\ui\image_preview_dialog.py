#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片预览对话框模块
提供图片的全屏预览和基本编辑功能
"""

import os
import sys
from typing import Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QFileDialog, QMessageBox, QSlider,
    QSpinBox, QGroupBox, QFormLayout
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QPixmap, QTransform, QPainter

# 导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.image_utils import image_utils


class ImagePreviewDialog(QDialog):
    """图片预览对话框"""
    
    # 信号定义
    image_changed = pyqtSignal(str)  # 图片路径改变信号
    
    def __init__(self, image_path: str = "", parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.original_pixmap = None
        self.current_pixmap = None
        self.scale_factor = 1.0
        self.rotation_angle = 0
        
        self.init_ui()
        self.load_image()
        
        # 设置窗口属性
        self.setWindowTitle("图片预览")
        self.setModal(True)
        self.resize(800, 600)
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 缩放控制
        zoom_group = QGroupBox("缩放")
        zoom_layout = QFormLayout(zoom_group)
        
        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        self.zoom_slider.setRange(10, 500)  # 10% - 500%
        self.zoom_slider.setValue(100)
        self.zoom_slider.valueChanged.connect(self.on_zoom_changed)
        zoom_layout.addRow("缩放:", self.zoom_slider)
        
        self.zoom_spinbox = QSpinBox()
        self.zoom_spinbox.setRange(10, 500)
        self.zoom_spinbox.setValue(100)
        self.zoom_spinbox.setSuffix("%")
        self.zoom_spinbox.valueChanged.connect(self.on_zoom_spinbox_changed)
        zoom_layout.addRow("", self.zoom_spinbox)
        
        toolbar_layout.addWidget(zoom_group)
        
        # 旋转控制
        rotation_group = QGroupBox("旋转")
        rotation_layout = QHBoxLayout(rotation_group)
        
        self.rotate_left_btn = QPushButton("左转90°")
        self.rotate_left_btn.clicked.connect(lambda: self.rotate_image(-90))
        rotation_layout.addWidget(self.rotate_left_btn)
        
        self.rotate_right_btn = QPushButton("右转90°")
        self.rotate_right_btn.clicked.connect(lambda: self.rotate_image(90))
        rotation_layout.addWidget(self.rotate_right_btn)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_image)
        rotation_layout.addWidget(self.reset_btn)
        
        toolbar_layout.addWidget(rotation_group)
        
        # 操作按钮
        action_group = QGroupBox("操作")
        action_layout = QVBoxLayout(action_group)
        
        self.save_as_btn = QPushButton("另存为...")
        self.save_as_btn.clicked.connect(self.save_as)
        action_layout.addWidget(self.save_as_btn)
        
        self.replace_btn = QPushButton("替换图片...")
        self.replace_btn.clicked.connect(self.replace_image)
        action_layout.addWidget(self.replace_btn)
        
        toolbar_layout.addWidget(action_group)
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # 图片显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        self.image_label.setMinimumSize(400, 300)
        
        self.scroll_area.setWidget(self.image_label)
        layout.addWidget(self.scroll_area)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        # 图片信息
        self.info_label = QLabel()
        self.info_label.setStyleSheet("color: #666666; font-size: 11px;")
        button_layout.addWidget(self.info_label)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def load_image(self):
        """加载图片"""
        if not self.image_path or not os.path.exists(self.image_path):
            self.image_label.setText("无图片或图片不存在")
            self.info_label.setText("")
            self.disable_controls()
            return
        
        try:
            # 加载原始图片
            self.original_pixmap = QPixmap(self.image_path)
            if self.original_pixmap.isNull():
                self.image_label.setText("图片格式不支持")
                self.info_label.setText("")
                self.disable_controls()
                return
            
            # 重置变换
            self.scale_factor = 1.0
            self.rotation_angle = 0
            self.zoom_slider.setValue(100)
            self.zoom_spinbox.setValue(100)
            
            # 显示图片
            self.update_image_display()
            
            # 更新图片信息
            self.update_image_info()
            
            # 启用控件
            self.enable_controls()
            
        except Exception as e:
            self.image_label.setText(f"加载图片失败: {e}")
            self.info_label.setText("")
            self.disable_controls()
    
    def update_image_display(self):
        """更新图片显示"""
        if not self.original_pixmap:
            return
        
        # 应用变换
        transform = QTransform()
        transform.scale(self.scale_factor, self.scale_factor)
        transform.rotate(self.rotation_angle)
        
        self.current_pixmap = self.original_pixmap.transformed(transform, Qt.TransformationMode.SmoothTransformation)
        
        # 显示图片
        self.image_label.setPixmap(self.current_pixmap)
        self.image_label.resize(self.current_pixmap.size())
    
    def update_image_info(self):
        """更新图片信息"""
        if not self.original_pixmap:
            return
        
        try:
            info = image_utils.get_image_info(self.image_path)
            if info:
                info_text = (
                    f"尺寸: {info['width']}×{info['height']} | "
                    f"格式: {info['format']} | "
                    f"大小: {info['file_size'] / 1024:.1f} KB"
                )
                self.info_label.setText(info_text)
        except:
            self.info_label.setText("无法获取图片信息")
    
    def enable_controls(self):
        """启用控件"""
        self.zoom_slider.setEnabled(True)
        self.zoom_spinbox.setEnabled(True)
        self.rotate_left_btn.setEnabled(True)
        self.rotate_right_btn.setEnabled(True)
        self.reset_btn.setEnabled(True)
        self.save_as_btn.setEnabled(True)
        self.replace_btn.setEnabled(True)
    
    def disable_controls(self):
        """禁用控件"""
        self.zoom_slider.setEnabled(False)
        self.zoom_spinbox.setEnabled(False)
        self.rotate_left_btn.setEnabled(False)
        self.rotate_right_btn.setEnabled(False)
        self.reset_btn.setEnabled(False)
        self.save_as_btn.setEnabled(False)
        self.replace_btn.setEnabled(False)
    
    def on_zoom_changed(self, value):
        """缩放滑块改变事件"""
        self.scale_factor = value / 100.0
        self.zoom_spinbox.blockSignals(True)
        self.zoom_spinbox.setValue(value)
        self.zoom_spinbox.blockSignals(False)
        self.update_image_display()
    
    def on_zoom_spinbox_changed(self, value):
        """缩放输入框改变事件"""
        self.scale_factor = value / 100.0
        self.zoom_slider.blockSignals(True)
        self.zoom_slider.setValue(value)
        self.zoom_slider.blockSignals(False)
        self.update_image_display()
    
    def rotate_image(self, angle):
        """旋转图片"""
        self.rotation_angle = (self.rotation_angle + angle) % 360
        self.update_image_display()
    
    def reset_image(self):
        """重置图片"""
        self.scale_factor = 1.0
        self.rotation_angle = 0
        self.zoom_slider.setValue(100)
        self.zoom_spinbox.setValue(100)
        self.update_image_display()
    
    def save_as(self):
        """另存为"""
        if not self.current_pixmap:
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存图片", "", 
            "PNG文件 (*.png);;JPEG文件 (*.jpg);;所有文件 (*)"
        )
        
        if file_path:
            try:
                success = self.current_pixmap.save(file_path)
                if success:
                    QMessageBox.information(self, "成功", "图片保存成功")
                else:
                    QMessageBox.warning(self, "失败", "图片保存失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存图片失败: {e}")
    
    def replace_image(self):
        """替换图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.webp);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 保存新图片到项目目录
                saved_path = image_utils.save_image(file_path)
                if saved_path:
                    # 删除旧图片（如果不是默认图片）
                    if self.image_path and os.path.exists(self.image_path):
                        try:
                            os.remove(self.image_path)
                        except:
                            pass
                    
                    # 更新图片路径
                    self.image_path = saved_path
                    self.load_image()
                    
                    # 发射信号
                    self.image_changed.emit(self.image_path)
                    
                    QMessageBox.information(self, "成功", "图片替换成功")
                else:
                    QMessageBox.warning(self, "失败", "图片保存失败")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"替换图片失败: {e}")
    
    def get_image_path(self) -> str:
        """获取当前图片路径"""
        return self.image_path


# 便捷函数
def show_image_preview(image_path: str, parent=None) -> Optional[str]:
    """显示图片预览对话框"""
    dialog = ImagePreviewDialog(image_path, parent)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_image_path()
    return None


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 测试图片预览对话框
    result = show_image_preview("test_image.jpg")
    if result:
        print(f"图片路径: {result}")
    else:
        print("用户取消了操作")
    
    sys.exit(app.exec())
