#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
导入导出对话框模块
提供数据导入导出的用户界面
"""

import os
import sys
from typing import Optional
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QTabWidget,
    QWidget,
    QGroupBox,
    QFormLayout,
    QLineEdit,
    QPushButton,
    QComboBox,
    QTextEdit,
    QFileDialog,
    QMessageBox,
    QProgressBar,
    QLabel,
    QCheckBox,
    QSpinBox,
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

# 导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.import_export import ImportExportManager
from db.db_manager import DBManager


class ImportExportWorker(QThread):
    """导入导出工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新
    status_updated = pyqtSignal(str)  # 状态更新
    finished = pyqtSignal(bool, str)  # 完成信号(成功, 消息)

    def __init__(self, operation_type: str, **kwargs):
        super().__init__()
        self.operation_type = operation_type
        self.kwargs = kwargs

    def run(self):
        """执行导入导出操作"""
        try:
            manager = self.kwargs.get("manager")

            if self.operation_type == "export_group":
                self.status_updated.emit("正在导出对比组数据...")
                self.progress_updated.emit(50)
                success = manager.export_group_to_excel(
                    self.kwargs["group_id"], self.kwargs["file_path"]
                )
                self.progress_updated.emit(100)

                if success:
                    self.finished.emit(True, "对比组数据导出成功")
                else:
                    self.finished.emit(False, "对比组数据导出失败")

            elif self.operation_type == "export_all":
                self.status_updated.emit("正在导出所有数据...")
                self.progress_updated.emit(50)
                success = manager.export_all_groups_to_excel(self.kwargs["file_path"])
                self.progress_updated.emit(100)

                if success:
                    self.finished.emit(True, "所有数据导出成功")
                else:
                    self.finished.emit(False, "数据导出失败")

            elif self.operation_type == "import":
                self.status_updated.emit("正在导入数据...")
                self.progress_updated.emit(30)
                success, message = manager.import_from_excel(
                    self.kwargs["file_path"], self.kwargs.get("group_name")
                )
                self.progress_updated.emit(100)
                self.finished.emit(success, message)

            elif self.operation_type == "template":
                self.status_updated.emit("正在生成模板...")
                self.progress_updated.emit(50)
                success = manager.get_excel_template(self.kwargs["file_path"])
                self.progress_updated.emit(100)

                if success:
                    self.finished.emit(True, "Excel模板生成成功")
                else:
                    self.finished.emit(False, "Excel模板生成失败")

        except Exception as e:
            self.finished.emit(False, f"操作失败: {e}")


class ImportExportDialog(QDialog):
    """导入导出对话框"""

    def __init__(self, db_manager: DBManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.import_export_manager = ImportExportManager(db_manager)
        self.worker = None

        self.init_ui()
        self.load_groups()

        # 设置窗口属性
        self.setWindowTitle("数据导入导出")
        self.setModal(True)
        self.resize(600, 500)

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 创建选项卡
        self.tab_widget = QTabWidget()

        # 导出选项卡
        self.create_export_tab()

        # 导入选项卡
        self.create_import_tab()

        layout.addWidget(self.tab_widget)

        # 进度条和状态
        progress_layout = QVBoxLayout()

        self.status_label = QLabel("就绪")
        progress_layout.addWidget(self.status_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        layout.addLayout(progress_layout)

        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def create_export_tab(self):
        """创建导出选项卡"""
        export_widget = QWidget()
        layout = QVBoxLayout(export_widget)

        # 导出单个对比组
        single_group = QGroupBox("导出单个对比组")
        single_layout = QFormLayout(single_group)

        self.export_group_combo = QComboBox()
        single_layout.addRow("选择对比组:", self.export_group_combo)

        self.export_group_path_edit = QLineEdit()
        self.export_group_path_edit.setPlaceholderText("选择导出文件路径...")
        export_group_path_layout = QHBoxLayout()
        export_group_path_layout.addWidget(self.export_group_path_edit)

        self.export_group_browse_btn = QPushButton("浏览...")
        self.export_group_browse_btn.clicked.connect(self.browse_export_group_path)
        export_group_path_layout.addWidget(self.export_group_browse_btn)

        single_layout.addRow("导出路径:", export_group_path_layout)

        self.export_group_btn = QPushButton("导出对比组")
        self.export_group_btn.clicked.connect(self.export_group)
        single_layout.addRow("", self.export_group_btn)

        layout.addWidget(single_group)

        # 导出所有数据
        all_group = QGroupBox("导出所有数据")
        all_layout = QFormLayout(all_group)

        self.export_all_path_edit = QLineEdit()
        self.export_all_path_edit.setPlaceholderText("选择导出文件路径...")
        export_all_path_layout = QHBoxLayout()
        export_all_path_layout.addWidget(self.export_all_path_edit)

        self.export_all_browse_btn = QPushButton("浏览...")
        self.export_all_browse_btn.clicked.connect(self.browse_export_all_path)
        export_all_path_layout.addWidget(self.export_all_browse_btn)

        all_layout.addRow("导出路径:", export_all_path_layout)

        self.export_all_btn = QPushButton("导出所有数据")
        self.export_all_btn.clicked.connect(self.export_all)
        all_layout.addRow("", self.export_all_btn)

        layout.addWidget(all_group)

        layout.addStretch()

        self.tab_widget.addTab(export_widget, "导出数据")

    def create_import_tab(self):
        """创建导入选项卡"""
        import_widget = QWidget()
        layout = QVBoxLayout(import_widget)

        # 生成模板
        template_group = QGroupBox("生成导入模板")
        template_layout = QFormLayout(template_group)

        self.template_path_edit = QLineEdit()
        self.template_path_edit.setPlaceholderText("选择模板保存路径...")
        template_path_layout = QHBoxLayout()
        template_path_layout.addWidget(self.template_path_edit)

        self.template_browse_btn = QPushButton("浏览...")
        self.template_browse_btn.clicked.connect(self.browse_template_path)
        template_path_layout.addWidget(self.template_browse_btn)

        template_layout.addRow("保存路径:", template_path_layout)

        self.generate_template_btn = QPushButton("生成Excel模板")
        self.generate_template_btn.clicked.connect(self.generate_template)
        template_layout.addRow("", self.generate_template_btn)

        layout.addWidget(template_group)

        # 导入数据
        import_group = QGroupBox("导入数据")
        import_layout = QFormLayout(import_group)

        self.import_path_edit = QLineEdit()
        self.import_path_edit.setPlaceholderText("选择要导入的Excel文件...")
        import_path_layout = QHBoxLayout()
        import_path_layout.addWidget(self.import_path_edit)

        self.import_browse_btn = QPushButton("浏览...")
        self.import_browse_btn.clicked.connect(self.browse_import_path)
        import_path_layout.addWidget(self.import_browse_btn)

        import_layout.addRow("Excel文件:", import_path_layout)

        self.import_group_name_edit = QLineEdit()
        self.import_group_name_edit.setPlaceholderText("留空则使用Excel中的对比组名称")
        import_layout.addRow("目标对比组名称:", self.import_group_name_edit)

        self.import_btn = QPushButton("导入数据")
        self.import_btn.clicked.connect(self.import_data)
        import_layout.addRow("", self.import_btn)

        layout.addWidget(import_group)

        # 说明
        help_group = QGroupBox("使用说明")
        help_layout = QVBoxLayout(help_group)

        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setMaximumHeight(120)
        help_text.setPlainText(
            "导入说明：\n"
            "1. 点击'生成Excel模板'下载标准格式的Excel文件\n"
            "2. 按照模板格式填写商品数据\n"
            "3. 必填列：商品名称、来源平台\n"
            "4. 可选列：对比组名称、商品描述、价格、运费、库存、链接、备注\n"
            "5. 同一商品的多个来源请分别占用一行\n"
            "6. 导入时会自动创建不存在的对比组"
        )
        help_layout.addWidget(help_text)

        layout.addWidget(help_group)

        self.tab_widget.addTab(import_widget, "导入数据")

    def load_groups(self):
        """加载对比组列表"""
        try:
            self.export_group_combo.clear()
            groups = self.db_manager.get_all_groups()

            for group in groups:
                self.export_group_combo.addItem(
                    f"{group['name']} ({group.get('product_count', 0)}个商品)",
                    group["id"],
                )
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载对比组失败: {e}")

    def browse_export_group_path(self):
        """浏览导出对比组路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择导出文件", "", "Excel文件 (*.xlsx);;所有文件 (*)"
        )
        if file_path:
            if not file_path.endswith(".xlsx"):
                file_path += ".xlsx"
            self.export_group_path_edit.setText(file_path)

    def browse_export_all_path(self):
        """浏览导出所有数据路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "选择导出文件", "", "Excel文件 (*.xlsx);;所有文件 (*)"
        )
        if file_path:
            if not file_path.endswith(".xlsx"):
                file_path += ".xlsx"
            self.export_all_path_edit.setText(file_path)

    def browse_template_path(self):
        """浏览模板保存路径"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择模板保存路径",
            "导入模板.xlsx",
            "Excel文件 (*.xlsx);;所有文件 (*)",
        )
        if file_path:
            if not file_path.endswith(".xlsx"):
                file_path += ".xlsx"
            self.template_path_edit.setText(file_path)

    def browse_import_path(self):
        """浏览导入文件路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择要导入的Excel文件", "", "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )
        if file_path:
            self.import_path_edit.setText(file_path)

    def export_group(self):
        """导出对比组"""
        if self.export_group_combo.currentIndex() < 0:
            QMessageBox.warning(self, "警告", "请选择要导出的对比组")
            return

        file_path = self.export_group_path_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请选择导出文件路径")
            return

        group_id = self.export_group_combo.currentData()

        # 启动工作线程
        self.start_worker(
            "export_group",
            manager=self.import_export_manager,
            group_id=group_id,
            file_path=file_path,
        )

    def export_all(self):
        """导出所有数据"""
        file_path = self.export_all_path_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请选择导出文件路径")
            return

        # 启动工作线程
        self.start_worker(
            "export_all", manager=self.import_export_manager, file_path=file_path
        )

    def generate_template(self):
        """生成Excel模板"""
        file_path = self.template_path_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请选择模板保存路径")
            return

        # 启动工作线程
        self.start_worker(
            "template", manager=self.import_export_manager, file_path=file_path
        )

    def import_data(self):
        """导入数据"""
        file_path = self.import_path_edit.text().strip()
        if not file_path:
            QMessageBox.warning(self, "警告", "请选择要导入的Excel文件")
            return

        if not os.path.exists(file_path):
            QMessageBox.warning(self, "警告", "选择的文件不存在")
            return

        group_name = self.import_group_name_edit.text().strip() or None

        # 启动工作线程
        self.start_worker(
            "import",
            manager=self.import_export_manager,
            file_path=file_path,
            group_name=group_name,
        )

    def start_worker(self, operation_type: str, **kwargs):
        """启动工作线程"""
        if self.worker and self.worker.isRunning():
            QMessageBox.warning(self, "警告", "有操作正在进行中，请稍候")
            return

        # 禁用按钮
        self.set_buttons_enabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 创建并启动工作线程
        self.worker = ImportExportWorker(operation_type, **kwargs)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.finished.connect(self.on_worker_finished)
        self.worker.start()

    def on_worker_finished(self, success: bool, message: str):
        """工作线程完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.set_buttons_enabled(True)

        # 更新状态
        self.status_label.setText("就绪")

        # 显示结果
        if success:
            QMessageBox.information(self, "成功", message)
            # 如果是导入操作，刷新对比组列表
            if "导入" in message:
                self.load_groups()
        else:
            QMessageBox.critical(self, "失败", message)

    def set_buttons_enabled(self, enabled: bool):
        """设置按钮启用状态"""
        self.export_group_btn.setEnabled(enabled)
        self.export_all_btn.setEnabled(enabled)
        self.generate_template_btn.setEnabled(enabled)
        self.import_btn.setEnabled(enabled)
        self.close_btn.setEnabled(enabled)

    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self,
                "确认关闭",
                "有操作正在进行中，确定要关闭吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.worker.terminate()
                self.worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


# 便捷函数
def show_import_export_dialog(db_manager: DBManager, parent=None):
    """显示导入导出对话框"""
    dialog = ImportExportDialog(db_manager, parent)
    dialog.exec()


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication
    from db.db_manager import DBManager

    app = QApplication(sys.argv)

    # 创建数据库管理器
    db_manager = DBManager("test.db")

    # 显示对话框
    show_import_export_dialog(db_manager)

    sys.exit(app.exec())
