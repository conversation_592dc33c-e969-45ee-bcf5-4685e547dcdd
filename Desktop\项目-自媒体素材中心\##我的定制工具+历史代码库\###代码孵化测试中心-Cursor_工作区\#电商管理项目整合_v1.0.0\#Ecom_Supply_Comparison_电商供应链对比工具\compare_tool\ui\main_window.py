#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口UI模块
实现商品对比工具的主界面
"""

import os
import sys
from PyQt6.QtWidgets import (
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QSplitter,
    QListWidget,
    QListWidgetItem,
    QTableWidget,
    QTableWidgetItem,
    QToolBar,
    QPushButton,
    QStatusBar,
    QMessageBox,
    QInputDialog,
    QHeaderView,
    QAbstractItemView,
    QMenu,
    QLabel,
    QFrame,
    QScrollArea,
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap
from PyQt6.QtWidgets import QApplication

# 导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db.db_manager import DBManager
from models.product import CompareGroup, Product, ProductSource
from utils.image_utils import image_utils
from utils.logger import logger, log_error_with_context, log_function_call
from ui.professional_product_dialog import (
    show_professional_add_dialog,
    show_professional_edit_dialog,
)
from ui.product_compare_view import ProductCompareView
from ui.import_export_dialog import show_import_export_dialog
from utils.theme_manager import theme_manager, ThemeType


class MainWindow(QMainWindow):
    """主窗口类"""

    # 信号定义
    group_selected = pyqtSignal(int)  # 选中对比组信号
    product_selected = pyqtSignal(int)  # 选中商品信号

    def __init__(self):
        super().__init__()

        try:
            logger.info("开始初始化主窗口")

            # 初始化数据库管理器
            self.db_manager = DBManager()

            # 当前选中的对比组ID
            self.current_group_id = None

            # 初始化UI
            self.init_ui()
            self.setup_style()
            self.apply_professional_button_styles()  # 应用专业按钮样式
            self.load_data()

            # 连接信号
            self.connect_signals()

            logger.info("主窗口初始化完成")

        except Exception as e:
            log_error_with_context(e, "主窗口初始化失败")
            # 显示错误对话框但不崩溃
            QMessageBox.critical(self, "初始化错误", f"窗口初始化失败: {str(e)}")

    def init_ui(self):
        """初始化用户界面 - 重新设计为商品管理为主的架构"""
        self.setWindowTitle("电商供应链对比工具 v2.0 - 商品管理版")
        # 设置更合适的窗口大小和位置
        self.setGeometry(50, 50, 1600, 1000)
        self.setMinimumSize(1200, 700)

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局 - 水平布局，左侧商品管理，右侧分组面板
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建商品管理面板
        self.create_product_management_panel(main_layout)

        # 创建工具栏
        self.create_toolbar()

        # 创建状态栏
        self.create_statusbar()

        # 创建菜单栏
        self.create_menubar()

        # 连接信号和槽
        self.connect_signals()

    def create_product_management_panel(self, parent_layout):
        """创建商品管理面板"""
        # 创建水平布局容器
        management_widget = QWidget()
        management_layout = QHBoxLayout(management_widget)
        management_layout.setContentsMargins(0, 0, 0, 0)
        management_layout.setSpacing(10)

        # 左侧：商品列表区域
        self.create_main_product_panel(management_layout)

        # 右侧：分组管理面板
        self.create_group_management_panel(management_layout)

        parent_layout.addWidget(management_widget)

    def create_main_product_panel(self, parent_layout):
        """创建主商品管理面板"""
        # 主面板容器
        main_panel = QWidget()
        main_panel_layout = QVBoxLayout(main_panel)
        main_panel_layout.setContentsMargins(0, 0, 0, 0)
        main_panel_layout.setSpacing(10)

        # 顶部工具栏
        self.create_product_toolbar(main_panel_layout)

        # 商品网格视图
        self.create_product_grid_view(main_panel_layout)

        # 设置主面板占据大部分空间
        parent_layout.addWidget(main_panel, 4)  # 占4/5的空间

    def create_product_toolbar(self, parent_layout):
        """创建商品工具栏"""
        toolbar_frame = QFrame()
        toolbar_frame.setMaximumHeight(60)
        toolbar_frame.setStyleSheet(
            """
            QFrame {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 2px;
            }
        """
        )
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)

        # 左侧：添加商品按钮
        self.add_product_btn = QPushButton("+ 添加商品")
        self.add_product_btn.setMinimumHeight(40)
        self.add_product_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """
        )
        toolbar_layout.addWidget(self.add_product_btn)

        # 中间：搜索框
        from PyQt6.QtWidgets import QLineEdit

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索商品名称、品牌、标签...")
        self.search_input.setMinimumHeight(40)
        self.search_input.setStyleSheet(
            """
            QLineEdit {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px 12px;
                color: white;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """
        )
        toolbar_layout.addWidget(self.search_input, 2)

        # 右侧：视图切换和统计
        self.view_toggle_btn = QPushButton("网格视图")
        self.view_toggle_btn.setMinimumHeight(40)
        self.view_toggle_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """
        )
        toolbar_layout.addWidget(self.view_toggle_btn)

        self.stats_label = QLabel("商品总数: 0")
        self.stats_label.setStyleSheet(
            """
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                padding: 8px;
            }
        """
        )
        toolbar_layout.addWidget(self.stats_label)

        parent_layout.addWidget(toolbar_frame)

    def create_product_grid_view(self, parent_layout):
        """创建商品网格视图"""
        # 创建滚动区域
        self.products_scroll = QScrollArea()
        self.products_scroll.setWidgetResizable(True)
        self.products_scroll.setStyleSheet(
            """
            QScrollArea {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 8px;
            }
            QScrollArea > QWidget > QWidget {
                background-color: #2b2b2b;
            }
        """
        )

        # 创建商品容器
        self.products_container = QWidget()
        self.products_layout = QVBoxLayout(self.products_container)
        self.products_layout.setContentsMargins(15, 15, 15, 15)
        self.products_layout.setSpacing(10)
        self.products_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.products_scroll.setWidget(self.products_container)
        parent_layout.addWidget(self.products_scroll)

    def create_group_management_panel(self, parent_layout):
        """创建分组管理面板（右侧）"""
        # 分组面板容器
        group_panel = QWidget()
        group_panel.setMaximumWidth(350)
        group_panel.setMinimumWidth(300)
        group_panel_layout = QVBoxLayout(group_panel)
        group_panel_layout.setContentsMargins(0, 0, 0, 0)
        group_panel_layout.setSpacing(10)

        # 分组标题和操作
        self.create_group_header(group_panel_layout)

        # 分组列表
        self.create_group_list(group_panel_layout)

        # 对比视图区域
        self.create_compare_view(group_panel_layout)

        # 设置分组面板占据较小空间
        parent_layout.addWidget(group_panel, 1)  # 占1/5的空间

    def create_group_header(self, parent_layout):
        """创建分组标题和操作区域"""
        header_frame = QFrame()
        header_frame.setMaximumHeight(60)
        header_frame.setStyleSheet(
            """
            QFrame {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 2px;
            }
        """
        )
        header_layout = QVBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 10, 15, 10)
        header_layout.setSpacing(8)

        # 标题
        title_label = QLabel("商品分组")
        title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(
            """
            QLabel {
                color: #ffffff;
                padding: 4px;
            }
        """
        )
        header_layout.addWidget(title_label)

        # 操作按钮
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(8)

        self.add_group_btn = QPushButton("+ 新建分组")
        self.add_group_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """
        )
        btn_layout.addWidget(self.add_group_btn)

        self.manage_groups_btn = QPushButton("管理")
        self.manage_groups_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """
        )
        btn_layout.addWidget(self.manage_groups_btn)

        header_layout.addLayout(btn_layout)
        parent_layout.addWidget(header_frame)

    def create_group_list(self, parent_layout):
        """创建分组列表"""
        # 分组列表容器
        list_frame = QFrame()
        list_frame.setStyleSheet(
            """
            QFrame {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 2px;
            }
        """
        )
        list_layout = QVBoxLayout(list_frame)
        list_layout.setContentsMargins(10, 10, 10, 10)
        list_layout.setSpacing(5)

        # 分组列表
        self.group_list = QListWidget()
        self.group_list.setMaximumHeight(200)
        self.group_list.setAlternatingRowColors(False)
        self.group_list.setSelectionMode(
            QAbstractItemView.SelectionMode.SingleSelection
        )
        self.group_list.setStyleSheet(
            """
            QListWidget {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 4px;
            }
            QListWidget::item {
                padding: 10px;
                margin: 2px;
                border-radius: 4px;
                color: #ffffff;
                border-left: 3px solid transparent;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                border-left-color: #ffffff;
            }
            QListWidget::item:hover {
                background-color: #404040;
            }
        """
        )
        list_layout.addWidget(self.group_list)

        parent_layout.addWidget(list_frame)

    def create_compare_view(self, parent_layout):
        """创建对比视图区域"""
        compare_frame = QFrame()
        compare_frame.setStyleSheet(
            """
            QFrame {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 2px;
            }
        """
        )
        compare_layout = QVBoxLayout(compare_frame)
        compare_layout.setContentsMargins(10, 10, 10, 10)
        compare_layout.setSpacing(8)

        # 对比标题
        compare_title = QLabel("商品对比")
        compare_title.setFont(QFont("Arial", 13, QFont.Weight.Bold))
        compare_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        compare_title.setStyleSheet(
            """
            QLabel {
                color: #ffffff;
                padding: 8px;
                background-color: #404040;
                border-radius: 6px;
            }
        """
        )
        compare_layout.addWidget(compare_title)

        # 对比按钮
        self.start_compare_btn = QPushButton("开始对比")
        self.start_compare_btn.setMinimumHeight(40)
        self.start_compare_btn.setEnabled(False)
        self.start_compare_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover:enabled {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: #adb5bd;
            }
        """
        )
        compare_layout.addWidget(self.start_compare_btn)

        # 对比信息
        self.compare_info_label = QLabel("选择分组查看商品对比")
        self.compare_info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.compare_info_label.setStyleSheet(
            """
            QLabel {
                color: #adb5bd;
                font-size: 12px;
                padding: 10px;
            }
        """
        )
        compare_layout.addWidget(self.compare_info_label)

        compare_layout.addStretch()
        parent_layout.addWidget(compare_frame)

    def connect_signals(self):
        """连接信号和槽"""
        # 商品管理相关
        self.add_product_btn.clicked.connect(self.add_product)
        self.search_input.textChanged.connect(self.on_search_changed)

        # 分组管理相关
        self.add_group_btn.clicked.connect(self.add_group)
        self.manage_groups_btn.clicked.connect(self.manage_groups)
        self.group_list.itemSelectionChanged.connect(self.on_group_selection_changed)
        self.group_list.itemDoubleClicked.connect(self.edit_group)
        self.start_compare_btn.clicked.connect(self.start_compare)

        # 视图和导出按钮
        self.view_toggle_btn.clicked.connect(self.toggle_view_mode)
        # self.export_compare_btn.clicked.connect(self.export_compare_data)  # 按钮未创建，暂时注释

        # 初始化数据
        self.load_groups()
        self.load_all_products()

    def on_search_changed(self):
        """搜索框内容改变"""
        self.load_all_products()

    def add_product(self):
        """添加商品"""
        try:
            from ui.product_dialog import ProductDialog

            dialog = ProductDialog(self.db_manager, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_all_products()
                self.statusBar().showMessage("商品添加成功", 2000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加商品失败: {str(e)}")

    def edit_product_by_id(self, product_id: int):
        """编辑商品"""
        try:
            from ui.product_dialog import ProductDialog

            dialog = ProductDialog(self.db_manager, product_id=product_id, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_all_products()
                self.statusBar().showMessage("商品编辑成功", 2000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑商品失败: {str(e)}")

    def delete_product_by_id(self, product_id: int):
        """删除商品"""
        try:
            reply = QMessageBox.question(
                self,
                "确认删除",
                "确定要删除这个商品吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.db_manager.delete_product(product_id)
                self.load_all_products()
                self.statusBar().showMessage("商品删除成功", 2000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除商品失败: {str(e)}")

    def add_group(self):
        """添加分组"""
        try:
            from ui.group_dialog import GroupDialog

            dialog = GroupDialog(self.db_manager, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_groups()
                self.statusBar().showMessage("分组创建成功", 2000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建分组失败: {str(e)}")

    def manage_groups(self):
        """管理分组"""
        try:
            from ui.group_manager import GroupManagerDialog

            dialog = GroupManagerDialog(self.db_manager, parent=self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_groups()
                self.statusBar().showMessage("分组管理完成", 2000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开分组管理失败: {str(e)}")

    def on_group_selection_changed(self):
        """分组选择改变"""
        current_item = self.group_list.currentItem()
        if current_item:
            group_id = current_item.data(Qt.ItemDataRole.UserRole)
            self.current_group_id = group_id
            self.start_compare_btn.setEnabled(True)

            # 更新对比信息
            group_name = current_item.text()
            products = self.db_manager.get_products_by_group(group_id)
            self.compare_info_label.setText(
                f"分组 '{group_name}' 包含 {len(products)} 个商品"
            )
        else:
            self.current_group_id = None
            self.start_compare_btn.setEnabled(False)
            self.compare_info_label.setText("选择分组查看商品对比")

    def start_compare(self):
        """开始对比"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择一个分组")
            return

        try:
            from ui.compare_window import CompareWindow

            products = self.db_manager.get_products_by_group(self.current_group_id)
            if not products:
                QMessageBox.information(self, "提示", "该分组中没有商品")
                return

            compare_window = CompareWindow(products, parent=self)
            compare_window.show()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开对比窗口失败: {str(e)}")

    def load_groups(self):
        """加载分组列表"""
        try:
            self.group_list.clear()
            groups = self.db_manager.get_all_groups()

            for group in groups:
                item = QListWidgetItem(group.name)
                item.setData(Qt.ItemDataRole.UserRole, group.id)
                self.group_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载分组失败: {str(e)}")

    def create_product_card(self, product: Product) -> QWidget:
        """创建商品卡片"""
        card = QFrame()
        card.setFixedSize(280, 350)
        card.setStyleSheet(
            """
            QFrame {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 12px;
                margin: 5px;
            }
            QFrame:hover {
                border-color: #0078d4;
                background-color: #454545;
            }
        """
        )
        card.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        card.customContextMenuRequested.connect(
            lambda pos: self.show_product_context_menu(product, pos, card)
        )

        layout = QVBoxLayout(card)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 商品图片
        image_label = QLabel()
        image_label.setFixedSize(250, 180)
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        image_label.setStyleSheet(
            """
            QLabel {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 8px;
            }
        """
        )

        if product.image_path and os.path.exists(product.image_path):
            pixmap = QPixmap(product.image_path)
            scaled_pixmap = pixmap.scaled(
                248,
                178,
                Qt.AspectRatioMode.KeepAspectRatio,
                Qt.TransformationMode.SmoothTransformation,
            )
            image_label.setPixmap(scaled_pixmap)
        else:
            image_label.setText("无图片")
            image_label.setStyleSheet(
                image_label.styleSheet() + "color: #adb5bd; font-size: 14px;"
            )

        layout.addWidget(image_label)

        # 商品名称
        name_label = QLabel(product.name or "未命名商品")
        name_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        name_label.setWordWrap(True)
        name_label.setMaximumHeight(40)
        name_label.setStyleSheet(
            """
            QLabel {
                color: #ffffff;
                padding: 4px;
            }
        """
        )
        layout.addWidget(name_label)

        # 商品信息
        info_layout = QHBoxLayout()
        info_layout.setSpacing(8)

        # 来源数量
        sources_label = QLabel(f"来源: {len(product.sources)}")
        sources_label.setStyleSheet(
            """
            QLabel {
                color: #28a745;
                font-size: 11px;
                font-weight: bold;
                background-color: #1e3a2e;
                padding: 4px 8px;
                border-radius: 4px;
            }
        """
        )
        info_layout.addWidget(sources_label)

        # 价格范围
        if product.sources:
            prices = [s.price for s in product.sources if s.price is not None]
            if prices:
                min_price = min(prices)
                max_price = max(prices)
                if min_price == max_price:
                    price_text = f"¥{min_price:.2f}"
                else:
                    price_text = f"¥{min_price:.2f}-{max_price:.2f}"
            else:
                price_text = "价格待定"
        else:
            price_text = "无来源"

        price_label = QLabel(price_text)
        price_label.setStyleSheet(
            """
            QLabel {
                color: #dc3545;
                font-size: 11px;
                font-weight: bold;
                background-color: #3a1e1e;
                padding: 4px 8px;
                border-radius: 4px;
            }
        """
        )
        info_layout.addWidget(price_label)

        info_layout.addStretch()
        layout.addLayout(info_layout)

        # 标签
        if product.tags:
            tags_label = QLabel(f"标签: {product.tags}")
            tags_label.setStyleSheet(
                """
                QLabel {
                    color: #6c757d;
                    font-size: 10px;
                    padding: 2px;
                }
            """
            )
            layout.addWidget(tags_label)

        layout.addStretch()
        return card

    def show_product_context_menu(self, product: Product, pos, card_widget):
        """显示商品右键菜单"""
        menu = QMenu(self)
        menu.setStyleSheet(
            """
            QMenu {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 4px;
            }
            QMenu::item {
                background-color: transparent;
                color: #ffffff;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #0078d4;
            }
        """
        )

        # 编辑商品
        edit_action = menu.addAction("✏️ 编辑商品")
        edit_action.triggered.connect(lambda: self.edit_product_by_id(product.id))

        menu.addSeparator()

        # 添加到分组
        add_to_group_menu = menu.addMenu("📁 添加到分组")
        groups = self.db_manager.get_all_groups()
        for group in groups:
            group_action = add_to_group_menu.addAction(group.name)
            group_action.triggered.connect(
                lambda checked, g=group: self.add_product_to_group(product, g)
            )

        if not groups:
            no_groups_action = add_to_group_menu.addAction("暂无分组")
            no_groups_action.setEnabled(False)

        menu.addSeparator()

        # 删除商品
        delete_action = menu.addAction("🗑️ 删除商品")
        delete_action.triggered.connect(lambda: self.delete_product_by_id(product.id))

        # 显示菜单
        global_pos = card_widget.mapToGlobal(pos)
        menu.exec(global_pos)

    def add_product_to_group(self, product: Product, group: CompareGroup):
        """将商品添加到分组"""
        try:
            # 检查商品是否已在分组中
            if product.group_id == group.id:
                QMessageBox.information(self, "提示", f"商品已在分组 '{group.name}' 中")
                return

            # 更新商品的分组ID
            product.group_id = group.id
            self.db_manager.update_product(product)

            QMessageBox.information(self, "成功", f"商品已添加到分组 '{group.name}'")
            self.refresh_ui()

        except Exception as e:
            log_error_with_context(e, "添加商品到分组失败")
            QMessageBox.critical(self, "错误", f"添加商品到分组失败: {str(e)}")

    def load_all_products(self):
        """加载并显示所有商品"""
        try:
            # 清空现有商品
            for i in reversed(range(self.products_layout.count())):
                child = self.products_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)

            # 获取所有商品
            products = self.db_manager.get_all_products()

            # 应用搜索过滤
            search_text = self.search_input.text().lower()
            if search_text:
                products = [
                    p
                    for p in products
                    if search_text in p.name.lower()
                    or search_text in p.brand.lower()
                    or search_text in p.tags.lower()
                ]

            # 创建商品卡片
            row_layout = None
            for i, product in enumerate(products):
                if i % 4 == 0:  # 每行4个商品
                    row_layout = QHBoxLayout()
                    row_layout.setSpacing(10)
                    row_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
                    self.products_layout.addLayout(row_layout)

                card = self.create_product_card(product)
                row_layout.addWidget(card)

            # 填充最后一行
            if row_layout and len(products) % 4 != 0:
                row_layout.addStretch()

            # 更新统计信息
            self.stats_label.setText(f"商品总数: {len(products)}")

        except Exception as e:
            log_error_with_context(e, "加载商品失败")
            QMessageBox.critical(self, "错误", f"加载商品失败: {str(e)}")

    def create_product_compare_panel(self, parent):
        """创建商品对比面板"""
        # 对比面板容器
        compare_widget = QWidget()
        compare_widget.setStyleSheet(
            """
            QWidget {
                background-color: #353535;
                border: 1px solid #555555;
                border-radius: 8px;
                margin: 2px;
            }
        """
        )
        compare_layout = QVBoxLayout(compare_widget)
        compare_layout.setContentsMargins(12, 12, 12, 12)
        compare_layout.setSpacing(8)

        # 标题区域
        title_frame = QFrame()
        title_frame.setStyleSheet(
            """
            QFrame {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 6px;
                padding: 8px;
            }
        """
        )
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(12, 8, 12, 8)

        self.compare_title_label = QLabel("商品对比")
        self.compare_title_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        self.compare_title_label.setStyleSheet(
            """
            QLabel {
                color: #ffffff;
                font-weight: bold;
            }
        """
        )
        title_layout.addWidget(self.compare_title_label)

        title_layout.addStretch()

        # 对比视图控制按钮
        self.view_mode_btn = QPushButton("切换视图")
        self.export_compare_btn = QPushButton("导出对比")

        button_style = """
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """

        for btn in [self.view_mode_btn, self.export_compare_btn]:
            btn.setStyleSheet(button_style)
            btn.setMinimumHeight(30)
            title_layout.addWidget(btn)

        compare_layout.addWidget(title_frame)

        # 商品对比视图（重新设计）
        self.product_compare_view = ProductCompareView()
        self.product_compare_view.product_edit_requested.connect(
            self.edit_product_by_id
        )
        self.product_compare_view.product_delete_requested.connect(
            self.delete_product_by_id
        )
        self.product_compare_view.add_product_requested.connect(
            self.add_product_by_platform
        )
        compare_layout.addWidget(self.product_compare_view)

        parent.addWidget(compare_widget)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar("主工具栏")
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)

        # 添加对比组
        add_group_action = QAction("添加对比组", self)
        add_group_action.setIcon(QIcon("assets/icons/add.png"))
        add_group_action.setStatusTip("创建新的对比组")
        add_group_action.triggered.connect(self.add_group)
        toolbar.addAction(add_group_action)

        toolbar.addSeparator()

        # 添加商品
        add_product_action = QAction("添加商品", self)
        add_product_action.setIcon(QIcon("assets/icons/add_product.png"))
        add_product_action.setStatusTip("添加新商品到当前对比组")
        add_product_action.triggered.connect(self.add_product)
        toolbar.addAction(add_product_action)
        self.add_product_action = add_product_action
        self.add_product_action.setEnabled(False)

        toolbar.addSeparator()

        # 导出数据
        export_action = QAction("导出数据", self)
        export_action.setIcon(QIcon("assets/icons/export.png"))
        export_action.setStatusTip("导出对比数据到Excel")
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)

        # 导入数据
        import_action = QAction("导入数据", self)
        import_action.setIcon(QIcon("assets/icons/import.png"))
        import_action.setStatusTip("从Excel导入商品数据")
        import_action.triggered.connect(self.import_data)
        toolbar.addAction(import_action)

        toolbar.addSeparator()

        # 设置
        settings_action = QAction("设置", self)
        settings_action.setIcon(QIcon("assets/icons/settings.png"))
        settings_action.setStatusTip("打开设置对话框")
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)

    def create_statusbar(self):
        """创建状态栏"""
        # statusBar()方法会自动创建状态栏，不需要手动创建
        pass

        # 显示就绪状态
        self.statusBar().showMessage("就绪", 2000)

        # 添加永久显示的标签
        self.status_label = QLabel("对比组: 0 | 商品: 0")
        self.statusBar().addPermanentWidget(self.status_label)

    def create_menubar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 导入
        import_action = QAction("导入数据(&I)", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)

        # 导出
        export_action = QAction("导出数据(&E)", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        # 添加对比组
        add_group_action = QAction("添加对比组(&G)", self)
        add_group_action.setShortcut("Ctrl+G")
        add_group_action.triggered.connect(self.add_group)
        edit_menu.addAction(add_group_action)

        # 添加商品
        add_product_action = QAction("添加商品(&P)", self)
        add_product_action.setShortcut("Ctrl+P")
        add_product_action.triggered.connect(self.add_product)
        edit_menu.addAction(add_product_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 主题切换
        theme_menu = view_menu.addMenu("主题(&T)")

        dark_theme_action = QAction("暗黑主题(&D)", self)
        dark_theme_action.setCheckable(True)
        dark_theme_action.setChecked(
            theme_manager.get_current_theme() == ThemeType.DARK
        )
        dark_theme_action.triggered.connect(lambda: self.switch_theme(ThemeType.DARK))
        theme_menu.addAction(dark_theme_action)

        light_theme_action = QAction("浅色主题(&L)", self)
        light_theme_action.setCheckable(True)
        light_theme_action.setChecked(
            theme_manager.get_current_theme() == ThemeType.LIGHT
        )
        light_theme_action.triggered.connect(lambda: self.switch_theme(ThemeType.LIGHT))
        theme_menu.addAction(light_theme_action)

        # 创建主题动作组（互斥选择）
        from PyQt6.QtGui import QActionGroup

        self.theme_action_group = QActionGroup(self)
        self.theme_action_group.addAction(dark_theme_action)
        self.theme_action_group.addAction(light_theme_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_style(self):
        """设置样式"""
        try:
            # 加载专业样式表
            professional_style_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                "style",
                "professional.qss",
            )

            if os.path.exists(professional_style_path):
                with open(professional_style_path, "r", encoding="utf-8") as f:
                    style_sheet = f.read()
                self.setStyleSheet(style_sheet)
                logger.info("已加载专业样式表")
            else:
                logger.warning(f"专业样式表文件不存在: {professional_style_path}")
                # 使用默认的暗黑主题
                theme_manager.apply_theme(QApplication.instance())

        except Exception as e:
            log_error_with_context(e, "加载样式表时出错")
            # 回退到默认主题
            theme_manager.apply_theme(QApplication.instance())

    def apply_professional_button_styles(self):
        """应用专业按钮样式"""
        try:
            # 主要操作按钮
            # self.quick_add_btn.setProperty("class", "primary")  # 按钮不存在，已注释
            # self.auto_group_btn.setProperty("class", "success")  # 按钮不存在，已注释
            self.add_group_btn.setProperty("class", "primary")

            # 警告按钮
            # self.edit_group_btn.setProperty("class", "warning")  # 按钮不存在，已注释

            # 危险按钮
            # self.delete_group_btn.setProperty("class", "danger")  # 按钮不存在，已注释

            # 刷新按钮样式
            # self.quick_add_btn.style().unpolish(self.quick_add_btn)  # 按钮不存在，已注释
            # self.quick_add_btn.style().polish(self.quick_add_btn)  # 按钮不存在，已注释

            # self.auto_group_btn.style().unpolish(self.auto_group_btn)  # 按钮不存在，已注释
            # self.auto_group_btn.style().polish(self.auto_group_btn)  # 按钮不存在，已注释

            self.add_group_btn.style().unpolish(self.add_group_btn)
            self.add_group_btn.style().polish(self.add_group_btn)

            # self.edit_group_btn.style().unpolish(self.edit_group_btn)  # 按钮不存在，已注释
            # self.edit_group_btn.style().polish(self.edit_group_btn)  # 按钮不存在，已注释

            # self.delete_group_btn.style().unpolish(self.delete_group_btn)  # 按钮不存在，已注释
            # self.delete_group_btn.style().polish(self.delete_group_btn)  # 按钮不存在，已注释

            logger.debug("已应用专业按钮样式")

        except Exception as e:
            log_error_with_context(e, "应用专业按钮样式时出错")

    # 重复的connect_signals方法已删除，信号连接已合并到上面的方法中

    def load_data(self):
        """加载数据"""
        self.load_groups()
        self.update_status()

    def load_groups(self):
        """加载对比组列表"""
        try:
            self.group_list.clear()
            groups = self.db_manager.get_all_groups_with_counts()

            for group in groups:
                item = QListWidgetItem()
                item.setText(f"{group['name']} ({group['product_count']})")
                item.setData(Qt.ItemDataRole.UserRole, group["id"])
                item.setToolTip(
                    f"描述: {group['description']}\n创建时间: {group['created_at']}"
                )
                self.group_list.addItem(item)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载对比组失败: {e}")

    def load_products_by_group(self, group_id: int):
        """加载指定对比组的商品"""
        try:
            # 获取对比组信息
            group_data = self.db_manager.get_group_with_products_and_sources(group_id)
            if not group_data:
                self.product_compare_view.set_products([])
                return

            # 更新标题
            self.compare_title_label.setText(f"商品对比 - {group_data['name']}")

            # 构建商品对象列表
            products = []
            for product_data in group_data["products"]:
                product = Product.from_dict(product_data)
                products.append(product)

            # 设置到对比视图
            self.product_compare_view.set_products(products)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载商品失败: {e}")

    def update_status(self):
        """更新状态栏信息"""
        try:
            log_function_call("update_status")

            groups = self.db_manager.get_all_groups()
            group_count = len(groups)

            total_products = 0
            for group in groups:
                group_id = (
                    group.get("id")
                    if isinstance(group, dict)
                    else getattr(group, "id", None)
                )
                if group_id:
                    products = self.db_manager.get_products_by_group(group_id)
                    total_products += len(products)

            # 更新统计标签
            self.stats_label.setText(
                f"商品总数: {total_products} | 对比组: {group_count}"
            )

            # 更新状态栏
            self.statusBar().showMessage(
                f"商品总数: {total_products} | 对比组: {group_count} | "
                f"当前选中: {self.current_group_id or '无'}"
            )

            logger.debug(f"状态更新完成 - 商品:{total_products}, 组:{group_count}")

        except Exception as e:
            log_error_with_context(e, "更新状态信息时出错")
            # 设置默认状态
            self.stats_label.setText("状态更新失败")
            self.statusBar().showMessage("状态更新失败")

    def auto_group_products(self):
        """智能分组功能 - 根据商品名称相似度自动分组"""
        try:
            from difflib import SequenceMatcher

            # 获取所有商品
            all_products = []
            groups = self.db_manager.get_all_groups()
            for group in groups:
                group_id = (
                    group.get("id")
                    if isinstance(group, dict)
                    else getattr(group, "id", None)
                )
                if group_id:
                    products = self.db_manager.get_products_by_group(group_id)
                    # 将字典转换为Product对象
                    from models.product import Product, ProductSource

                    product_objects = []
                    for product_dict in products:
                        product = Product.from_dict(product_dict)
                        # 获取来源
                        sources_data = self.db_manager.get_sources_by_product(
                            product.id
                        )
                        product.sources = [
                            ProductSource.from_dict(s) for s in sources_data
                        ]
                        product_objects.append(product)
                    all_products.extend(product_objects)

            if len(all_products) <= 1:
                QMessageBox.information(self, "提示", "商品数量不足，无法进行智能分组")
                return

            # 相似度分组算法
            groups_to_create = []
            processed_products = set()

            for i, product1 in enumerate(all_products):
                if product1.id in processed_products:
                    continue

                similar_products = [product1]
                processed_products.add(product1.id)

                for j, product2 in enumerate(all_products[i + 1 :], i + 1):
                    if product2.id in processed_products:
                        continue

                    # 计算名称相似度
                    similarity = SequenceMatcher(
                        None, product1.name.lower(), product2.name.lower()
                    ).ratio()

                    # 相似度阈值为0.6
                    if similarity >= 0.6:
                        similar_products.append(product2)
                        processed_products.add(product2.id)

                # 如果有相似商品，创建新组
                if len(similar_products) > 1:
                    # 提取共同关键词作为组名
                    common_words = self.extract_common_keywords(
                        [p.name for p in similar_products]
                    )
                    group_name = (
                        f"智能分组-{common_words[:20]}"
                        if common_words
                        else f"智能分组-{len(groups_to_create)+1}"
                    )

                    groups_to_create.append(
                        {"name": group_name, "products": similar_products}
                    )

            if not groups_to_create:
                QMessageBox.information(self, "提示", "未找到可以自动分组的相似商品")
                return

            # 创建分组
            created_count = 0
            for group_info in groups_to_create:
                try:
                    # 创建新的对比组
                    group_id = self.db_manager.add_group(
                        name=group_info["name"],
                        description=f"基于商品名称相似度自动创建的分组",
                    )

                    if group_id:
                        # 将商品移动到新组
                        for product in group_info["products"]:
                            # 更新商品的组ID
                            self.db_manager.update_product(
                                product_id=product.id,
                                name=product.name,
                                description=product.description,
                                image_path=product.image_path,
                            )
                            # 手动更新组ID - 需要修改数据库
                            with self.db_manager.get_connection() as conn:
                                cursor = conn.cursor()
                                cursor.execute(
                                    "UPDATE product SET group_id = ? WHERE id = ?",
                                    (group_id, product.id),
                                )
                                conn.commit()
                        created_count += 1

                except Exception as e:
                    print(f"创建智能分组时出错: {e}")

            if created_count > 0:
                QMessageBox.information(
                    self, "成功", f"成功创建 {created_count} 个智能分组"
                )
                self.refresh_data()
            else:
                QMessageBox.warning(self, "失败", "智能分组创建失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"智能分组过程中出错: {str(e)}")

    def extract_common_keywords(self, names):
        """提取商品名称的共同关键词"""
        if not names:
            return ""

        # 简单的关键词提取
        words_lists = [name.split() for name in names]
        if not words_lists:
            return ""

        # 找到出现在所有名称中的词
        common_words = set(words_lists[0])
        for words in words_lists[1:]:
            common_words &= set(words)

        # 如果没有共同词，取第一个名称的前几个字符
        if not common_words:
            return names[0][:10] if names[0] else "未知商品"

        return " ".join(sorted(common_words))

    def toggle_view_mode(self):
        """切换对比视图模式"""
        # 这里可以实现不同的对比视图模式
        # 比如表格模式、卡片模式、详细模式等
        QMessageBox.information(self, "提示", "视图模式切换功能开发中...")

    def export_compare_data(self):
        """导出对比数据"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择要导出的对比组")
            return
        self.export_data()

    def create_product_card(self, product: Product):
        """创建专业商品卡片 - 增强版"""
        try:
            card = QFrame()
            card.setProperty("class", "product-card")  # 设置专业样式类
            card.setFixedSize(280, 360)  # 增大卡片尺寸

            # 设置右键菜单
            card.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
            card.customContextMenuRequested.connect(
                lambda pos: self.show_product_context_menu(pos, product, card)
            )

            # 存储商品ID用于右键菜单
            card.product_id = product.id

            layout = QVBoxLayout(card)
            layout.setContentsMargins(12, 12, 12, 12)
            layout.setSpacing(8)

            # 商品图片 - 居中且更大
            image_container = QFrame()
            image_container.setFixedHeight(150)
            image_layout = QVBoxLayout(image_container)
            image_layout.setContentsMargins(0, 0, 0, 0)

            image_label = QLabel()
            image_label.setFixedSize(200, 130)
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setStyleSheet(
                """
                QLabel {
                    border: 1px solid #464647;
                    border-radius: 4px;
                    background-color: #2d2d2d;
                    color: #888888;
                }
            """
            )

            if product.has_image():
                pixmap = image_utils.load_pixmap(product.image_path, QSize(200, 130))
                if pixmap:
                    image_label.setPixmap(pixmap)
                    # 取消拉伸显示，保持原始宽高比
                    image_label.setScaledContents(False)
                else:
                    image_label.setText("图片加载失败")
            else:
                image_label.setText("无图片")

            image_layout.addWidget(image_label, 0, Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(image_container)

            # 商品信息区域
            info_container = QFrame()
            info_layout = QVBoxLayout(info_container)
            info_layout.setContentsMargins(0, 0, 0, 0)
            info_layout.setSpacing(4)

            # 商品名称
            name_label = QLabel(
                product.name[:20] + "..." if len(product.name) > 20 else product.name
            )
            name_label.setProperty("class", "subtitle")
            name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            name_label.setWordWrap(True)
            name_label.setMaximumHeight(40)
            info_layout.addWidget(name_label)

            # 品牌和分类
            if product.brand or product.category:
                brand_cat_text = []
                if product.brand:
                    brand_cat_text.append(f"品牌: {product.brand}")
                if product.category:
                    brand_cat_text.append(f"分类: {product.category}")

                brand_cat_label = QLabel(" | ".join(brand_cat_text))
                brand_cat_label.setProperty("class", "caption")
                brand_cat_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                info_layout.addWidget(brand_cat_label)

            # 颜色、尺寸、款式
            details = []
            if product.color:
                details.append(f"颜色: {product.color}")
            if product.size:
                details.append(f"规格: {product.size}")
            if product.style:
                details.append(f"款式: {product.style}")

            if details:
                details_label = QLabel(" | ".join(details))
                details_label.setProperty("class", "caption")
                details_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                details_label.setWordWrap(True)
                info_layout.addWidget(details_label)

            # 评分和销量
            rating_sales = []
            # 评分（可选）
            if product.rating is not None:
                rating_sales.append(f"评分: {product.rating:.1f}★")

            # 销量
            if product.sales_volume is not None:
                sales_str = (
                    f"{product.sales_volume//10000}万+"
                    if product.sales_volume >= 10000
                    else str(product.sales_volume)
                )
                rating_sales.append(f"销量: {sales_str}")

            if rating_sales:
                rating_sales_label = QLabel(" | ".join(rating_sales))
                rating_sales_label.setStyleSheet("color: #4ec9b0; font-weight: 500;")
                rating_sales_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                info_layout.addWidget(rating_sales_label)

            # 价格范围
            price_label = QLabel(product.get_price_range_text())
            price_label.setStyleSheet(
                "color: #f14c4c; background: transparent; font-weight: bold; font-size: 14px;"
            )
            price_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            info_layout.addWidget(price_label)

            # 来源平台名称
            source_label = QLabel(product.get_platforms_text())
            source_label.setProperty("class", "caption")
            source_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            info_layout.addWidget(source_label)

            layout.addWidget(info_container)

            # 操作按钮
            btn_layout = QHBoxLayout()
            btn_layout.setSpacing(2)

            edit_btn = QPushButton("编辑")
            edit_btn.setProperty("class", "warning")  # 专业样式类
            edit_btn.setMaximumHeight(25)
            edit_btn.clicked.connect(lambda: self.edit_product_by_id(product.id))
            btn_layout.addWidget(edit_btn)

            delete_btn = QPushButton("删除")
            delete_btn.setProperty("class", "danger")  # 专业样式类
            delete_btn.setMaximumHeight(25)
            delete_btn.clicked.connect(lambda: self.delete_product_by_id(product.id))
            btn_layout.addWidget(delete_btn)

            layout.addLayout(btn_layout)

            # 刷新样式
            card.style().unpolish(card)
            card.style().polish(card)
            edit_btn.style().unpolish(edit_btn)
            edit_btn.style().polish(edit_btn)
            delete_btn.style().unpolish(delete_btn)
            delete_btn.style().polish(delete_btn)

            return card

        except Exception as e:
            log_error_with_context(
                e, f"创建商品卡片失败: {product.name if product else 'Unknown'}"
            )
            # 返回一个错误卡片
            error_card = QFrame()
            error_layout = QVBoxLayout(error_card)
            error_label = QLabel("卡片\n创建失败")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_layout.addWidget(error_label)
            return error_card

    def refresh_product_cards(self):
        """刷新商品卡片显示"""
        try:
            log_function_call("refresh_product_cards")

            # 清除现有的商品卡片
            while self.products_layout.count():
                child = self.products_layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

            if not self.current_group_id:
                logger.debug("当前没有选中的对比组，跳过刷新")
                return

                products = self.db_manager.get_products_by_group(self.current_group_id)
            logger.debug(f"获取到 {len(products)} 个商品")

            # 将字典转换为Product对象
            from models.product import Product, ProductSource

            product_objects = []
            for product_dict in products:
                product = Product.from_dict(product_dict)
                # 获取来源
                sources_data = self.db_manager.get_sources_by_product(product.id)
                product.sources = [ProductSource.from_dict(s) for s in sources_data]
                product_objects.append(product)

            for product in product_objects:
                card = self.create_product_card(product)
                self.products_layout.addWidget(card)

            # 添加弹性空间
            self.products_layout.addStretch()

            logger.debug(f"商品卡片刷新完成，显示 {len(product_objects)} 个商品")

        except Exception as e:
            log_error_with_context(e, "刷新商品卡片时出错")

    def show_product_context_menu(self, pos, product, card):
        """显示商品右键菜单"""
        try:
            menu = QMenu(card)
            menu.setStyleSheet(
                """
                QMenu {
                    background-color: #353535;
                    border: 1px solid #555555;
                    border-radius: 6px;
                    color: #ffffff;
                    padding: 4px;
                }
                QMenu::item {
                    padding: 8px 16px;
                    border-radius: 4px;
                }
                QMenu::item:selected {
                    background-color: #0078d4;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #555555;
                    margin: 4px 8px;
                }
            """
            )

            # 添加到标签分组
            add_to_tag_action = QAction("添加标签分组", menu)
            add_to_tag_action.triggered.connect(
                lambda: self.add_product_to_tag_group(product)
            )
            menu.addAction(add_to_tag_action)

            # 查看已有分组
            view_groups_action = QAction("查看已有分组", menu)
            view_groups_action.triggered.connect(
                lambda: self.show_existing_tag_groups(product)
            )
            menu.addAction(view_groups_action)

            menu.addSeparator()

            # 编辑商品
            edit_action = QAction("编辑商品", menu)
            edit_action.triggered.connect(lambda: self.edit_product_by_id(product.id))
            menu.addAction(edit_action)

            # 删除商品
            delete_action = QAction("删除商品", menu)
            delete_action.triggered.connect(
                lambda: self.delete_product_by_id(product.id)
            )
            menu.addAction(delete_action)

            menu.addSeparator()

            # 对比同分组商品
            compare_action = QAction("对比同分组商品", menu)
            compare_action.triggered.connect(
                lambda: self.compare_products_by_tag(product)
            )
            menu.addAction(compare_action)

            # 在卡片位置显示菜单
            global_pos = card.mapToGlobal(pos)
            menu.exec(global_pos)

        except Exception as e:
            log_error_with_context(e, "显示右键菜单失败")

    def add_product_to_tag_group(self, product):
        """添加商品到标签分组"""
        try:
            # 输入标签名称
            tag_name, ok = QInputDialog.getText(
                self,
                "添加标签分组",
                f"为商品 '{product.name}' 添加标签分组:",
                text=product.tags or "",
            )

            if ok and tag_name.strip():
                tag_name = tag_name.strip()

                # 更新商品标签
                try:
                    # 获取现有标签
                    existing_tags = product.tags.split(",") if product.tags else []
                    existing_tags = [
                        tag.strip() for tag in existing_tags if tag.strip()
                    ]

                    # 添加新标签（如果不存在）
                    if tag_name not in existing_tags:
                        existing_tags.append(tag_name)

                    # 更新数据库
                    updated_tags = ",".join(existing_tags)

                    # 使用数据库管理器更新商品
                    with self.db_manager.get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            "UPDATE product SET tags = ? WHERE id = ?",
                            (updated_tags, product.id),
                        )
                        conn.commit()

                    # 更新本地对象
                    product.tags = updated_tags

                    QMessageBox.information(
                        self,
                        "成功",
                        f"已将商品 '{product.name}' 添加到标签分组 '{tag_name}'",
                    )

                    # 刷新显示
                    self.load_all_products()

                except Exception as e:
                    log_error_with_context(e, "更新商品标签失败")
                    QMessageBox.critical(self, "错误", f"更新标签失败: {str(e)}")

        except Exception as e:
            log_error_with_context(e, "添加标签分组失败")
            QMessageBox.critical(self, "错误", f"添加标签分组失败: {str(e)}")

    def show_existing_tag_groups(self, product):
        """显示已有的标签分组"""
        try:
            # 获取所有商品的标签
            all_tags = set()

            # 从数据库获取所有商品
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT tags FROM product WHERE tags IS NOT NULL AND tags != ''"
                )
                results = cursor.fetchall()

                for row in results:
                    if row[0]:
                        tags = [tag.strip() for tag in row[0].split(",") if tag.strip()]
                        all_tags.update(tags)

            if not all_tags:
                QMessageBox.information(self, "提示", "当前没有任何标签分组")
                return

            # 显示标签选择对话框
            from PyQt6.QtWidgets import QDialog, QListWidget, QDialogButtonBox

            dialog = QDialog(self)
            dialog.setWindowTitle("选择标签分组")
            dialog.setFixedSize(400, 300)
            dialog.setStyleSheet(
                """
                QDialog {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QListWidget {
                    background-color: #353535;
                    border: 1px solid #555555;
                    border-radius: 6px;
                    color: #ffffff;
                    padding: 8px;
                }
                QListWidget::item {
                    padding: 8px;
                    border-radius: 4px;
                }
                QListWidget::item:selected {
                    background-color: #0078d4;
                }
            """
            )

            layout = QVBoxLayout(dialog)

            # 标签列表
            tag_list = QListWidget()
            for tag in sorted(all_tags):
                # 统计该标签下的商品数量
                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        "SELECT COUNT(*) FROM product WHERE tags LIKE ?", (f"%{tag}%",)
                    )
                    count = cursor.fetchone()[0]

                item_text = f"{tag} ({count} 个商品)"
                item = QListWidgetItem(item_text)
                item.setData(Qt.ItemDataRole.UserRole, tag)
                tag_list.addItem(item)

            layout.addWidget(QLabel("已有标签分组:"))
            layout.addWidget(tag_list)

            # 按钮
            button_box = QDialogButtonBox(
                QDialogButtonBox.StandardButton.Ok
                | QDialogButtonBox.StandardButton.Cancel
            )
            button_box.accepted.connect(dialog.accept)
            button_box.rejected.connect(dialog.reject)
            layout.addWidget(button_box)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                current_item = tag_list.currentItem()
                if current_item:
                    selected_tag = current_item.data(Qt.ItemDataRole.UserRole)
                    # 将商品添加到选中的标签分组
                    self.add_product_to_existing_tag(product, selected_tag)

        except Exception as e:
            log_error_with_context(e, "显示标签分组失败")
            QMessageBox.critical(self, "错误", f"显示标签分组失败: {str(e)}")

    def add_product_to_existing_tag(self, product, tag_name):
        """将商品添加到现有标签分组"""
        try:
            # 获取现有标签
            existing_tags = product.tags.split(",") if product.tags else []
            existing_tags = [tag.strip() for tag in existing_tags if tag.strip()]

            # 添加新标签（如果不存在）
            if tag_name not in existing_tags:
                existing_tags.append(tag_name)

                # 更新数据库
                updated_tags = ",".join(existing_tags)

                with self.db_manager.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        "UPDATE product SET tags = ? WHERE id = ?",
                        (updated_tags, product.id),
                    )
                    conn.commit()

                # 更新本地对象
                product.tags = updated_tags

                QMessageBox.information(
                    self,
                    "成功",
                    f"已将商品 '{product.name}' 添加到标签分组 '{tag_name}'",
                )

                # 刷新显示
                self.load_all_products()
            else:
                QMessageBox.information(
                    self,
                    "提示",
                    f"商品 '{product.name}' 已经在标签分组 '{tag_name}' 中",
                )

        except Exception as e:
            log_error_with_context(e, "添加到现有标签失败")
            QMessageBox.critical(self, "错误", f"添加到现有标签失败: {str(e)}")

    def compare_products_by_tag(self, product):
        """对比同标签分组的商品"""
        try:
            if not product.tags:
                QMessageBox.information(self, "提示", "该商品没有标签分组")
                return

            # 获取商品的所有标签
            product_tags = [
                tag.strip() for tag in product.tags.split(",") if tag.strip()
            ]

            if not product_tags:
                QMessageBox.information(self, "提示", "该商品没有有效的标签分组")
                return

            # 让用户选择要对比的标签
            if len(product_tags) == 1:
                selected_tag = product_tags[0]
            else:
                selected_tag, ok = QInputDialog.getItem(
                    self, "选择标签", "选择要对比的标签分组:", product_tags, 0, False
                )
                if not ok:
                    return

            # 获取同标签的所有商品
            with self.db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT * FROM product WHERE tags LIKE ? AND id != ?",
                    (f"%{selected_tag}%", product.id),
                )
                results = cursor.fetchall()

            if not results:
                QMessageBox.information(
                    self,
                    "提示",
                    f"标签分组 '{selected_tag}' 中只有当前商品，无法进行对比",
                )
                return

            # 转换为Product对象
            from models.product import Product, ProductSource

            compare_products = [product]  # 包含当前商品

            for row in results:
                product_dict = {
                    "id": row[0],
                    "name": row[1],
                    "description": row[2],
                    "image_path": row[3],
                    "brand": row[4],
                    "category": row[5],
                    "tags": row[6],
                    "color": row[7],
                    "size": row[8],
                    "style": row[9],
                    "rating": row[10],
                    "sales_volume": row[11],
                }

                compare_product = Product.from_dict(product_dict)

                # 获取来源
                sources_data = self.db_manager.get_sources_by_product(
                    compare_product.id
                )
                compare_product.sources = [
                    ProductSource.from_dict(s) for s in sources_data
                ]

                compare_products.append(compare_product)

            # 打开对比窗口
            from ui.compare_window import CompareWindow

            compare_window = CompareWindow(compare_products, self)
            compare_window.setWindowTitle(f"标签分组对比 - {selected_tag}")
            compare_window.show()

        except Exception as e:
            log_error_with_context(e, "对比同标签商品失败")
            QMessageBox.critical(self, "错误", f"对比同标签商品失败: {str(e)}")

    # 事件处理方法
    def on_group_selection_changed(self):
        """对比组选择改变事件"""
        current_item = self.group_list.currentItem()
        if current_item:
            group_id = current_item.data(Qt.ItemDataRole.UserRole)
            self.current_group_id = group_id

            # 启用相关按钮
            # self.edit_group_btn.setEnabled(True)  # 按钮不存在，已注释
            # self.delete_group_btn.setEnabled(True)  # 按钮不存在，已注释

            # 更新对比标题
            try:
                groups = self.db_manager.get_all_groups()
                current_group = next((g for g in groups if g["id"] == group_id), None)
                if current_group:
                    self.compare_title_label.setText(
                        f"商品对比 - {current_group['name']}"
                    )
                else:
                    self.compare_title_label.setText("商品对比 - 组未找到")
            except Exception as e:
                print(f"获取对比组信息失败: {e}")
                self.compare_title_label.setText("商品对比 - 获取信息失败")

            # 加载商品
            self.load_products_by_group(group_id)

            # 刷新商品卡片
            self.refresh_product_cards()

            # 发射信号
            self.group_selected.emit(group_id)
        else:
            self.current_group_id = None

            # 禁用相关按钮
            # self.edit_group_btn.setEnabled(False)  # 按钮不存在，已注释
            # self.delete_group_btn.setEnabled(False)  # 按钮不存在，已注释

            # 清空显示
            self.product_compare_view.set_products([])
            self.compare_title_label.setText("商品对比")
            self.refresh_product_cards()

        self.update_status()

    def add_group(self):
        """添加对比组"""
        name, ok = QInputDialog.getText(self, "添加对比组", "请输入对比组名称:")
        if ok and name.strip():
            try:
                description, ok2 = QInputDialog.getText(
                    self, "添加对比组", "请输入描述（可选）:"
                )
                if not ok2:
                    description = ""

                group_id = self.db_manager.add_group(name.strip(), description.strip())
                self.load_groups()
                self.update_status()
                self.statusBar().showMessage(f"成功添加对比组: {name}", 3000)

                # 选中新添加的组
                for i in range(self.group_list.count()):
                    item = self.group_list.item(i)
                    if item.data(Qt.ItemDataRole.UserRole) == group_id:
                        self.group_list.setCurrentItem(item)
                        break

            except ValueError as e:
                QMessageBox.warning(self, "警告", str(e))
            except Exception as e:
                QMessageBox.critical(self, "错误", f"添加对比组失败: {e}")

    def edit_group(self):
        """编辑对比组"""
        current_item = self.group_list.currentItem()
        if not current_item:
            return

        group_id = current_item.data(Qt.ItemDataRole.UserRole)
        try:
            # 获取当前组信息
            groups = self.db_manager.get_all_groups()
            current_group = next((g for g in groups if g["id"] == group_id), None)
            if not current_group:
                QMessageBox.warning(self, "警告", "对比组不存在")
                return

            # 编辑名称
            name, ok = QInputDialog.getText(
                self, "编辑对比组", "请输入新的对比组名称:", text=current_group["name"]
            )
            if ok and name.strip():
                # 编辑描述
                description, ok2 = QInputDialog.getText(
                    self,
                    "编辑对比组",
                    "请输入新的描述:",
                    text=current_group["description"] or "",
                )
                if not ok2:
                    description = current_group["description"]

                success = self.db_manager.update_group(
                    group_id, name.strip(), description.strip()
                )
                if success:
                    self.load_groups()
                    self.statusBar().showMessage(f"成功更新对比组: {name}", 3000)

                    # 重新选中该组
                    for i in range(self.group_list.count()):
                        item = self.group_list.item(i)
                        if item.data(Qt.ItemDataRole.UserRole) == group_id:
                            self.group_list.setCurrentItem(item)
                            break
                else:
                    QMessageBox.warning(self, "警告", "更新对比组失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑对比组失败: {e}")

    def delete_group(self):
        """删除对比组"""
        current_item = self.group_list.currentItem()
        if not current_item:
            return

        group_id = current_item.data(Qt.ItemDataRole.UserRole)
        group_name = current_item.text().split(" (")[0]  # 去掉商品数量部分

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除对比组 '{group_name}' 吗？\n这将同时删除该组下的所有商品和来源信息。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                success = self.db_manager.delete_group(group_id)
                if success:
                    self.load_groups()
                    self.update_status()
                    self.statusBar().showMessage(f"成功删除对比组: {group_name}", 3000)
                else:
                    QMessageBox.warning(self, "警告", "删除对比组失败")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"删除对比组失败: {e}")

    def add_product(self):
        """添加商品"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择一个对比组")
            return

        try:
            # 打开添加商品对话框
            product = show_professional_add_dialog(self.current_group_id, self)
            if product:
                # 保存商品到数据库
                product_id = self.db_manager.add_product(
                    product.group_id,
                    product.name,
                    product.description,
                    product.image_path,
                )

                # 保存来源信息
                for source in product.sources:
                    self.db_manager.add_source(
                        product_id,
                        source.source_name,
                        source.price,
                        source.shipping,
                        source.stock,
                        source.url,
                        source.note,
                    )

                    # 刷新界面
            self.load_products_by_group(self.current_group_id)
            self.load_groups()  # 更新商品数量
            self.refresh_product_cards()  # 刷新商品卡片
            self.update_status()
            self.statusBar().showMessage(f"成功添加商品: {product.name}", 3000)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加商品失败: {e}")

    def add_product_by_platform(self, platform: str):
        """按平台添加商品"""
        if not self.current_group_id:
            QMessageBox.warning(self, "警告", "请先选择一个对比组")
            return

        try:
            # 获取商品名称输入
            product_name = self.product_compare_view.get_product_name_input()

            # 如果启用了同步名称且有输入，使用输入的名称
            if self.product_compare_view.sync_name and product_name:
                default_name = product_name
            else:
                default_name = f"{platform}商品"

            # 打开添加商品对话框，预设平台和名称
            product = show_professional_add_dialog(
                self.current_group_id,
                self,
                default_platform=platform,
                default_name=default_name,
            )

            if product:
                # 保存商品到数据库
                product_id = self.db_manager.add_product(
                    product.group_id,
                    product.name,
                    product.description,
                    product.image_path,
                )

                # 保存来源信息
                for source in product.sources:
                    self.db_manager.add_source(
                        product_id,
                        source.source_name,
                        source.price,
                        source.shipping,
                        source.stock,
                        source.url,
                        source.note,
                    )

                # 如果是同步添加，清空输入框
                if self.product_compare_view.sync_name and product_name:
                    self.product_compare_view.clear_product_name_input()

                # 刷新界面
                self.load_products_by_group(self.current_group_id)
                self.load_groups()  # 更新商品数量
                self.refresh_product_cards()  # 刷新商品卡片
                self.update_status()
                self.statusBar().showMessage(
                    f"成功添加{platform}商品: {product.name}", 3000
                )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加{platform}商品失败: {e}")

    def edit_product_by_id(self, product_id: int):
        """根据ID编辑商品"""
        try:
            # 获取商品完整信息
            product_data = self.db_manager.get_product_by_id(product_id)
            if not product_data:
                QMessageBox.warning(self, "警告", "商品不存在")
                return

            # 获取商品来源
            sources_data = self.db_manager.get_sources_by_product(product_id)

            # 构建Product对象
            product = Product.from_dict(product_data)
            product.sources = [ProductSource.from_dict(s) for s in sources_data]

            # 打开专业编辑对话框
            edited_product = show_professional_edit_dialog(
                self.current_group_id, product, self
            )
            if edited_product:
                # 更新商品基本信息
                self.db_manager.update_product(
                    product_id,
                    edited_product.name,
                    edited_product.description,
                    edited_product.image_path,
                )

                # 删除原有来源
                for source_data in sources_data:
                    self.db_manager.delete_source(source_data["id"])

                # 添加新的来源
                for source in edited_product.sources:
                    self.db_manager.add_source(
                        product_id,
                        source.source_name,
                        source.price,
                        source.shipping,
                        source.stock,
                        source.url,
                        source.note,
                    )

                # 刷新界面
                self.load_products_by_group(self.current_group_id)
                self.refresh_product_cards()  # 刷新商品卡片
                self.statusBar().showMessage(
                    f"成功更新商品: {edited_product.name}", 3000
                )

        except Exception as e:
            QMessageBox.critical(self, "错误", f"编辑商品失败: {e}")

    def delete_product_by_id(self, product_id: int):
        """根据ID删除商品"""
        try:
            product = self.db_manager.get_product_by_id(product_id)
            if not product:
                QMessageBox.warning(self, "警告", "商品不存在")
                return

            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除商品 '{product['name']}' 吗？\n这将同时删除该商品的所有来源信息。",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                success = self.db_manager.delete_product(product_id)
                if success:
                    self.load_products_by_group(self.current_group_id)
                    self.load_groups()  # 更新商品数量
                    self.refresh_product_cards()  # 刷新商品卡片
                    self.update_status()
                    self.statusBar().showMessage(
                        f"成功删除商品: {product['name']}", 3000
                    )
                else:
                    QMessageBox.warning(self, "警告", "删除商品失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"删除商品失败: {e}")

    def refresh_data(self):
        """刷新数据"""
        try:
            # 保存当前选中的组ID
            current_group_id = self.current_group_id

            # 重新加载组列表
            self.load_groups()

            # 如果之前有选中的组，尝试重新选中
            if current_group_id:
                for i in range(self.group_list.count()):
                    item = self.group_list.item(i)
                    if item.data(Qt.ItemDataRole.UserRole) == current_group_id:
                        self.group_list.setCurrentItem(item)
                        break
                self.load_products_by_group(current_group_id)

            # 刷新商品卡片
            self.refresh_product_cards()

            # 更新状态
            self.update_status()
            self.statusBar().showMessage("数据刷新完成", 2000)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"刷新数据失败: {e}")

    def export_data(self):
        """导出数据"""
        try:
            show_import_export_dialog(self.db_manager, self)
            # 刷新界面以防有新数据导入
            self.load_groups()
            if self.current_group_id:
                self.load_products_by_group(self.current_group_id)
            self.update_status()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开导入导出对话框失败: {e}")

    def import_data(self):
        """导入数据"""
        try:
            show_import_export_dialog(self.db_manager, self)
            # 刷新界面以防有新数据导入
            self.load_groups()
            if self.current_group_id:
                self.load_products_by_group(self.current_group_id)
            self.update_status()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开导入导出对话框失败: {e}")

    def open_settings(self):
        """打开设置"""
        # 这里应该打开设置对话框
        QMessageBox.information(self, "提示", "设置功能正在开发中")

    def switch_theme(self, theme: ThemeType):
        """切换主题"""
        try:
            app = QApplication.instance()
            if app:
                theme_manager.apply_theme(app, theme)
                self.statusBar().showMessage(
                    f"已切换到{'暗黑' if theme == ThemeType.DARK else '浅色'}主题", 2000
                )
        except Exception as e:
            QMessageBox.critical(self, "错误", f"切换主题失败: {e}")

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h2>商品对比工具 v1.0</h2>
        <p>基于 PyQt6 的可视化商品对比工具</p>
        <p><b>主要功能：</b></p>
        <ul>
        <li>多平台商品信息管理</li>
        <li>价格对比分析</li>
        <li>图片管理</li>
        <li>数据导入导出</li>
        </ul>
        <p><b>技术栈：</b></p>
        <ul>
        <li>Python 3.10+</li>
        <li>PyQt6</li>
        <li>SQLite3</li>
        <li>Pillow</li>
        </ul>
        <p>© 2024 商品对比工具开发团队</p>
        """

        QMessageBox.about(self, "关于商品对比工具", about_text)

    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出商品对比工具吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 清理资源
            try:
                self.db_manager.close()
            except:
                pass
            event.accept()
        else:
            event.ignore()


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
