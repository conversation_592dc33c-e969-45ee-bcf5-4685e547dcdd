#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的商品对比视图模块
实现更专业的独立商品对比功能
"""

import os
import sys
from typing import List, Dict, Optional
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QAbstractItemView,
    QLabel,
    QPushButton,
    QFrame,
    QScrollArea,
    QGroupBox,
    QGridLayout,
    QSizePolicy,
    QSpacerItem,
    QTabWidget,
    QSplitter,
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QPalette, QPixmap

# 导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.product import Product, ProductSource
from utils.image_utils import image_utils
from ui.image_preview_dialog import show_image_preview


class ModernSourceCard(QFrame):
    """现代化的来源对比卡片"""

    def __init__(self, source: ProductSource, is_best: bool = False, parent=None):
        super().__init__(parent)
        self.source = source
        self.is_best = is_best
        self.init_ui()
        self.apply_style()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)

        # 来源标题区域
        header_layout = QHBoxLayout()

        # 来源名称
        name_label = QLabel(self.source.get_display_name())
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(12)
        name_label.setFont(name_font)
        header_layout.addWidget(name_label)

        header_layout.addStretch()

        # 最优标识
        if self.is_best:
            best_label = QLabel("🏆 最优")
            best_label.setStyleSheet(
                """
                QLabel {
                    color: #FFD700;
                    font-weight: bold;
                    background-color: rgba(255, 215, 0, 0.2);
                    border-radius: 4px;
                    padding: 2px 6px;
                }
            """
            )
            header_layout.addWidget(best_label)

        layout.addLayout(header_layout)

        # 价格信息区域
        price_frame = QFrame()
        price_frame.setStyleSheet(
            """
            QFrame {
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                padding: 8px;
            }
        """
        )
        price_layout = QVBoxLayout(price_frame)
        price_layout.setSpacing(4)

        if self.source.price is not None and self.source.price >= 0:
            # 主价格
            price_label = QLabel(f"¥{self.source.price:.2f}")
            price_font = QFont()
            price_font.setPointSize(16)
            price_font.setBold(True)
            price_label.setFont(price_font)
            price_label.setStyleSheet("color: #4CAF50;")
            price_layout.addWidget(price_label)

            # 运费信息
            if self.source.shipping > 0:
                shipping_label = QLabel(f"+ 运费 ¥{self.source.shipping:.2f}")
                shipping_label.setStyleSheet("color: #FF9800; font-size: 11px;")
                price_layout.addWidget(shipping_label)

                # 总价
                total_price = self.source.get_total_price()
                total_label = QLabel(f"总计: ¥{total_price:.2f}")
                total_font = QFont()
                total_font.setBold(True)
                total_font.setPointSize(13)
                total_label.setFont(total_font)
                total_label.setStyleSheet("color: #2196F3;")
                price_layout.addWidget(total_label)
            elif self.source.shipping == 0:
                shipping_label = QLabel("包邮")
                shipping_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
                price_layout.addWidget(shipping_label)
        else:
            price_label = QLabel("价格待询")
            price_label.setStyleSheet("color: #757575; font-style: italic;")
            price_layout.addWidget(price_label)

        layout.addWidget(price_frame)

        # 库存和其他信息
        info_layout = QHBoxLayout()

        # 库存状态
        stock_widget = self.create_info_widget(
            "库存", self.get_stock_text(), self.get_stock_color()
        )
        info_layout.addWidget(stock_widget)

        info_layout.addStretch()

        layout.addLayout(info_layout)

        # 操作按钮
        if self.source.url:
            btn_layout = QHBoxLayout()
            btn_layout.addStretch()

            link_btn = QPushButton("查看商品")
            link_btn.setStyleSheet(
                """
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """
            )
            link_btn.clicked.connect(self.open_link)
            btn_layout.addWidget(link_btn)

            layout.addLayout(btn_layout)

    def create_info_widget(self, label: str, value: str, color: str):
        """创建信息显示小组件"""
        widget = QFrame()
        widget.setMaximumWidth(80)
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(2)

        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: #888888; font-size: 10px;")
        label_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label_widget)

        value_widget = QLabel(value)
        value_widget.setStyleSheet(
            f"color: {color}; font-weight: bold; font-size: 11px;"
        )
        value_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_widget)

        return widget

    def get_stock_text(self):
        """获取库存文本"""
        if self.source.stock is None:
            return "充足"
        elif self.source.stock > 0:
            return str(self.source.stock)
        else:
            return "缺货"

    def get_stock_color(self):
        """获取库存颜色"""
        if self.source.stock is None or self.source.stock > 0:
            return "#4CAF50"
        else:
            return "#F44336"

    def apply_style(self):
        """应用样式"""
        if self.is_best:
            # 最优来源高亮显示
            self.setStyleSheet(
                """
                QFrame {
                    border: 2px solid #4CAF50;
                    border-radius: 12px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(76, 175, 80, 0.15),
                        stop:1 rgba(76, 175, 80, 0.05));
                }
            """
            )
        else:
            self.setStyleSheet(
                """
                QFrame {
                    border: 1px solid #555555;
                    border-radius: 8px;
                    background-color: rgba(255, 255, 255, 0.05);
                }
                QFrame:hover {
                    border-color: #0078d4;
                    background-color: rgba(255, 255, 255, 0.1);
                }
            """
            )

    def open_link(self):
        """打开链接"""
        if self.source.url:
            import webbrowser

            webbrowser.open(self.source.url)


class IndependentProductCard(QFrame):
    """独立的商品卡片"""

    # 信号定义
    edit_requested = pyqtSignal(int)  # 编辑商品信号
    delete_requested = pyqtSignal(int)  # 删除商品信号

    def __init__(self, product: Product, parent=None):
        super().__init__(parent)
        self.product = product
        self.init_ui()
        self.apply_style()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 商品标题区域
        self.create_header_section(layout)

        # 商品图片和基本信息
        self.create_info_section(layout)

        # 来源对比区域
        self.create_sources_section(layout)

        # 操作按钮区域
        self.create_actions_section(layout)

    def create_header_section(self, parent_layout):
        """创建头部区域"""
        header_layout = QHBoxLayout()

        # 商品名称
        name_label = QLabel(self.product.name)
        name_font = QFont()
        name_font.setPointSize(14)
        name_font.setBold(True)
        name_label.setFont(name_font)
        name_label.setStyleSheet("color: #ffffff;")
        header_layout.addWidget(name_label)

        header_layout.addStretch()

        # 价格范围
        price_range_label = QLabel(self.product.get_price_range_text())
        price_range_label.setStyleSheet(
            """
            QLabel {
                color: #4CAF50;
                font-weight: bold;
                font-size: 13px;
                background-color: rgba(76, 175, 80, 0.2);
                border-radius: 4px;
                padding: 4px 8px;
            }
        """
        )
        header_layout.addWidget(price_range_label)

        parent_layout.addLayout(header_layout)

    def create_info_section(self, parent_layout):
        """创建信息区域"""
        info_layout = QHBoxLayout()

        # 商品图片
        image_label = QLabel()
        image_label.setFixedSize(120, 120)
        image_label.setStyleSheet(
            """
            border: 2px solid #555555;
            border-radius: 8px;
            background-color: #2b2b2b;
        """
        )
        image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        if self.product.has_image():
            pixmap = image_utils.load_pixmap(self.product.image_path, QSize(120, 120))
            if pixmap:
                image_label.setPixmap(pixmap)
                image_label.setScaledContents(True)
                image_label.mousePressEvent = lambda event: self.show_image_preview()
                image_label.setStyleSheet(image_label.styleSheet() + "cursor: pointer;")
            else:
                image_label.setText("图片\n加载失败")
                image_label.setStyleSheet(image_label.styleSheet() + "color: #888888;")
        else:
            image_label.setText("无图片")
            image_label.setStyleSheet(image_label.styleSheet() + "color: #888888;")

        info_layout.addWidget(image_label)

        # 商品描述和统计信息
        desc_layout = QVBoxLayout()

        if self.product.description:
            desc_label = QLabel(
                self.product.description[:100] + "..."
                if len(self.product.description) > 100
                else self.product.description
            )
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet("color: #cccccc; line-height: 1.4;")
            desc_layout.addWidget(desc_label)

        desc_layout.addStretch()

        # 统计信息
        stats_layout = QHBoxLayout()

        # 来源数量
        sources_count = self.product.get_source_count()
        sources_label = QLabel(f"{sources_count} 个来源")
        sources_label.setStyleSheet("color: #2196F3; font-weight: bold;")
        stats_layout.addWidget(sources_label)

        # 最低价格
        min_price = self.product.get_min_total_price()
        if min_price:
            min_price_label = QLabel(f"最低 ¥{min_price:.2f}")
            min_price_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
            stats_layout.addWidget(min_price_label)

        stats_layout.addStretch()
        desc_layout.addLayout(stats_layout)

        info_layout.addLayout(desc_layout)
        parent_layout.addLayout(info_layout)

    def create_sources_section(self, parent_layout):
        """创建来源对比区域"""
        if not self.product.sources:
            no_sources_label = QLabel("暂无来源信息")
            no_sources_label.setStyleSheet("color: #888888; font-style: italic;")
            no_sources_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            parent_layout.addWidget(no_sources_label)
            return

        sources_frame = QFrame()
        sources_frame.setStyleSheet(
            """
            QFrame {
                background-color: rgba(255, 255, 255, 0.03);
                border-radius: 8px;
                border: 1px solid #444444;
            }
        """
        )
        sources_layout = QVBoxLayout(sources_frame)
        sources_layout.setContentsMargins(12, 12, 12, 12)

        # 来源标题
        sources_title = QLabel("来源对比")
        sources_title.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        sources_title.setStyleSheet("color: #ffffff; margin-bottom: 8px;")
        sources_layout.addWidget(sources_title)

        # 来源卡片网格
        sources_grid = QGridLayout()
        sources_grid.setSpacing(8)

        # 获取最优来源
        best_source = self.product.get_best_source()

        # 创建来源卡片
        for i, source in enumerate(self.product.sources):
            is_best = best_source and source.id == best_source.id
            source_card = ModernSourceCard(source, is_best)

            row = i // 3  # 每行3个
            col = i % 3
            sources_grid.addWidget(source_card, row, col)

        sources_layout.addLayout(sources_grid)
        parent_layout.addWidget(sources_frame)

    def create_actions_section(self, parent_layout):
        """创建操作区域"""
        actions_layout = QHBoxLayout()
        actions_layout.addStretch()

        # 编辑按钮
        edit_btn = QPushButton("编辑商品")
        edit_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """
        )
        edit_btn.clicked.connect(lambda: self.edit_requested.emit(self.product.id))
        actions_layout.addWidget(edit_btn)

        # 删除按钮
        delete_btn = QPushButton("删除商品")
        delete_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
        """
        )
        delete_btn.clicked.connect(lambda: self.delete_requested.emit(self.product.id))
        actions_layout.addWidget(delete_btn)

        parent_layout.addLayout(actions_layout)

    def apply_style(self):
        """应用样式"""
        self.setStyleSheet(
            """
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:1 rgba(255, 255, 255, 0.02));
                border: 1px solid #555555;
                border-radius: 12px;
                margin: 8px;
            }
            QFrame:hover {
                border-color: #0078d4;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(0, 120, 212, 0.1),
                    stop:1 rgba(0, 120, 212, 0.02));
            }
        """
        )

    def show_image_preview(self):
        """显示图片预览"""
        if self.product.has_image():
            show_image_preview(self.product.image_path, self.product.name, self)


class ProductCompareView(QWidget):
    """现代化的商品对比视图"""

    # 信号定义
    product_edit_requested = pyqtSignal(int)
    product_delete_requested = pyqtSignal(int)
    add_product_requested = pyqtSignal(str)  # 添加商品信号，参数为平台类型

    def __init__(self, parent=None):
        super().__init__(parent)
        self.products = []
        self.view_mode = "table"  # 默认使用表格视图
        self.sync_name = True  # 是否同步商品名称
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(12)

        # 创建顶部商品添加区域
        self.create_add_product_section(layout)

        # 创建商品对比表格区域
        self.create_compare_table_section(layout)

    def create_add_product_section(self, parent_layout):
        """创建商品添加区域"""
        add_frame = QFrame()
        add_frame.setStyleSheet("""
            QFrame {
                background-color: #404040;
                border: 1px solid #555555;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        add_layout = QVBoxLayout(add_frame)
        add_layout.setSpacing(12)

        # 标题
        title_label = QLabel("快速添加商品")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #ffffff; margin-bottom: 8px;")
        add_layout.addWidget(title_label)

        # 商品名称输入区域
        name_layout = QHBoxLayout()
        name_label = QLabel("商品名称:")
        name_label.setStyleSheet("color: #ffffff; min-width: 80px;")
        name_layout.addWidget(name_label)

        from PyQt6.QtWidgets import QLineEdit, QCheckBox
        self.product_name_input = QLineEdit()
        self.product_name_input.setPlaceholderText("输入商品名称")
        self.product_name_input.setStyleSheet("""
            QLineEdit {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 4px;
                padding: 8px;
                color: #ffffff;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #0078d4;
            }
        """)
        name_layout.addWidget(self.product_name_input)

        # 同步名称复选框
        self.sync_checkbox = QCheckBox("同步名称")
        self.sync_checkbox.setChecked(True)
        self.sync_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ffffff;
                spacing: 5px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #0078d4;
                border: 1px solid #0078d4;
                border-radius: 3px;
            }
        """)
        self.sync_checkbox.toggled.connect(self.on_sync_toggled)
        name_layout.addWidget(self.sync_checkbox)

        add_layout.addLayout(name_layout)

        # 平台添加按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(12)

        # 淘宝添加区域
        taobao_frame = QFrame()
        taobao_frame.setStyleSheet("""
            QFrame {
                background-color: #FF6600;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        taobao_layout = QVBoxLayout(taobao_frame)
        taobao_layout.setSpacing(8)

        taobao_title = QLabel("淘宝")
        taobao_title.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        taobao_title.setStyleSheet("color: white; text-align: center;")
        taobao_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        taobao_layout.addWidget(taobao_title)

        taobao_btn = QPushButton("添加淘宝商品")
        taobao_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        taobao_btn.clicked.connect(lambda: self.add_product_requested.emit("淘宝"))
        taobao_layout.addWidget(taobao_btn)

        buttons_layout.addWidget(taobao_frame)

        # 1688添加区域
        alibaba_frame = QFrame()
        alibaba_frame.setStyleSheet("""
            QFrame {
                background-color: #FF6A00;
                border-radius: 8px;
                padding: 12px;
            }
        """)
        alibaba_layout = QVBoxLayout(alibaba_frame)
        alibaba_layout.setSpacing(8)

        alibaba_title = QLabel("1688")
        alibaba_title.setFont(QFont("Arial", 11, QFont.Weight.Bold))
        alibaba_title.setStyleSheet("color: white; text-align: center;")
        alibaba_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        alibaba_layout.addWidget(alibaba_title)

        alibaba_btn = QPushButton("添加1688商品")
        alibaba_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        alibaba_btn.clicked.connect(lambda: self.add_product_requested.emit("1688"))
        alibaba_layout.addWidget(alibaba_btn)

        buttons_layout.addWidget(alibaba_frame)

        # 同步添加按钮
        sync_btn = QPushButton("同步添加两个平台")
        sync_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
        """)
        sync_btn.clicked.connect(self.sync_add_products)
        buttons_layout.addWidget(sync_btn)

        add_layout.addLayout(buttons_layout)
        parent_layout.addWidget(add_frame)

    def create_compare_table_section(self, parent_layout):
        """创建商品对比表格区域"""
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAsNeeded
        )
        self.scroll_area.setVerticalScrollBarPolicy(
            Qt.ScrollBarPolicy.ScrollBarAsNeeded
        )
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 6px;
            }
        """)

        # 创建内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setSpacing(16)
        self.content_layout.setContentsMargins(16, 16, 16, 16)

        self.scroll_area.setWidget(self.content_widget)
        parent_layout.addWidget(self.scroll_area)

        # 初始显示
        self.show_empty_state()

    def on_sync_toggled(self, checked):
        """同步名称切换"""
        self.sync_name = checked

    def sync_add_products(self):
        """同步添加两个平台的商品"""
        product_name = self.product_name_input.text().strip()
        if not product_name:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "提示", "请先输入商品名称")
            return
        
        # 发送信号添加两个平台的商品
        self.add_product_requested.emit("淘宝")
        self.add_product_requested.emit("1688")

    def get_product_name_input(self):
        """获取商品名称输入"""
        return self.product_name_input.text().strip()

    def clear_product_name_input(self):
        """清空商品名称输入"""
        self.product_name_input.clear()

    def set_products(self, products: List[Product]):
        """设置商品列表"""
        self.products = products
        self.refresh_view()

    def refresh_view(self):
        """刷新视图"""
        # 清除现有内容
        self.clear_content()

        if not self.products:
            self.show_empty_state()
            return

        if self.view_mode == "independent":
            self.show_independent_view()
        elif self.view_mode == "table":
            self.show_table_view()
        elif self.view_mode == "grid":
            self.show_grid_view()

    def clear_content(self):
        """清除内容"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def show_empty_state(self):
        """显示空状态"""
        empty_frame = QFrame()
        empty_frame.setMinimumHeight(300)
        empty_layout = QVBoxLayout(empty_frame)
        empty_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        empty_label = QLabel("暂无商品")
        empty_label.setFont(QFont("Arial", 16))
        empty_label.setStyleSheet("color: #888888;")
        empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_layout.addWidget(empty_label)

        hint_label = QLabel("点击 '快速添加商品' 按钮开始添加")
        hint_label.setStyleSheet("color: #666666; font-style: italic;")
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        empty_layout.addWidget(hint_label)

        self.content_layout.addWidget(empty_frame)

    def show_independent_view(self):
        """显示独立视图模式"""
        for product in self.products:
            product_card = IndependentProductCard(product)
            product_card.edit_requested.connect(self.product_edit_requested.emit)
            product_card.delete_requested.connect(self.product_delete_requested.emit)
            self.content_layout.addWidget(product_card)

        # 添加弹性空间
        self.content_layout.addStretch()

    def show_table_view(self):
        """显示表格视图"""
        if not self.products:
            self.show_empty_state()
            return

        # 创建表格对比视图
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setSpacing(0)
        table_layout.setContentsMargins(0, 0, 0, 0)

        # 创建表格
        from PyQt6.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
        table = QTableWidget()
        
        # 设置表格行列数
        num_products = min(len(self.products), 5)  # 最多显示5个商品
        table.setRowCount(6)  # 商品名称、图片、价格、销量、平台、操作
        table.setColumnCount(num_products)
        
        # 设置行标题
        row_headers = ["商品名称", "商品图片", "价格信息", "销量信息", "平台信息", "操作"]
        table.setVerticalHeaderLabels(row_headers)
        
        # 设置列标题（商品序号）
        col_headers = [f"商品 {i+1}" for i in range(num_products)]
        table.setHorizontalHeaderLabels(col_headers)
        
        # 填充表格数据
        for col, product in enumerate(self.products[:num_products]):
            # 商品名称
            name_item = QTableWidgetItem(product.name or "未命名商品")
            name_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            table.setItem(0, col, name_item)
            
            # 商品图片
            image_widget = self.create_image_cell(product)
            table.setCellWidget(1, col, image_widget)
            
            # 价格信息
            price_widget = self.create_price_cell(product)
            table.setCellWidget(2, col, price_widget)
            
            # 销量信息
            sales_widget = self.create_sales_cell(product)
            table.setCellWidget(3, col, sales_widget)
            
            # 平台信息
            platform_widget = self.create_platform_cell(product)
            table.setCellWidget(4, col, platform_widget)
            
            # 操作按钮
            action_widget = self.create_action_cell(product)
            table.setCellWidget(5, col, action_widget)
        
        # 设置表格样式
        table.setStyleSheet("""
            QTableWidget {
                background-color: #2b2b2b;
                border: 1px solid #555555;
                border-radius: 6px;
                gridline-color: #555555;
                color: #ffffff;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #555555;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #ffffff;
                padding: 8px;
                border: 1px solid #555555;
                font-weight: bold;
            }
        """)
        
        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        table.setRowHeight(1, 120)  # 图片行高度
        table.setRowHeight(2, 80)   # 价格行高度
        table.setRowHeight(3, 60)   # 销量行高度
        table.setRowHeight(4, 80)   # 平台行高度
        table.setRowHeight(5, 60)   # 操作行高度
        
        table_layout.addWidget(table)
        self.content_layout.addWidget(table_widget)

    def create_image_cell(self, product):
        """创建图片单元格"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 显示第一个来源的图片
        if product.sources and product.sources[0].image_url:
            image_label = QLabel()
            image_label.setFixedSize(100, 100)
            image_label.setStyleSheet("""
                QLabel {
                    border: 1px solid #555555;
                    border-radius: 4px;
                    background-color: #404040;
                }
            """)
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setText("图片加载中...")
            layout.addWidget(image_label)
            
            # TODO: 实际加载图片
        else:
            no_image_label = QLabel("暂无图片")
            no_image_label.setFixedSize(100, 100)
            no_image_label.setStyleSheet("""
                QLabel {
                    border: 1px solid #555555;
                    border-radius: 4px;
                    background-color: #404040;
                    color: #888888;
                }
            """)
            no_image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_image_label)
        
        return widget

    def create_price_cell(self, product):
        """创建价格单元格"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(2)
        
        if product.sources:
            for source in product.sources:
                price_label = QLabel(f"{source.source_name}: ¥{source.price or '未知'}")
                price_label.setStyleSheet("color: #ffffff; font-size: 11px;")
                layout.addWidget(price_label)
        else:
            no_price_label = QLabel("暂无价格信息")
            no_price_label.setStyleSheet("color: #888888; font-size: 11px;")
            layout.addWidget(no_price_label)
        
        return widget

    def create_sales_cell(self, product):
        """创建销量单元格"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(2)
        
        if product.sources:
            for source in product.sources:
                sales_text = f"{source.source_name}: {source.sales or '未知'}"
                sales_label = QLabel(sales_text)
                sales_label.setStyleSheet("color: #ffffff; font-size: 11px;")
                layout.addWidget(sales_label)
        else:
            no_sales_label = QLabel("暂无销量信息")
            no_sales_label.setStyleSheet("color: #888888; font-size: 11px;")
            layout.addWidget(no_sales_label)
        
        return widget

    def create_platform_cell(self, product):
        """创建平台单元格"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(2)
        
        if product.sources:
            platforms = list(set([source.source_name for source in product.sources]))
            for platform in platforms:
                platform_label = QLabel(platform)
                platform_label.setStyleSheet("""
                    QLabel {
                        background-color: #0078d4;
                        color: white;
                        padding: 2px 6px;
                        border-radius: 3px;
                        font-size: 10px;
                        font-weight: bold;
                    }
                """)
                platform_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                layout.addWidget(platform_label)
        else:
            no_platform_label = QLabel("暂无平台信息")
            no_platform_label.setStyleSheet("color: #888888; font-size: 11px;")
            layout.addWidget(no_platform_label)
        
        return widget

    def create_action_cell(self, product):
        """创建操作单元格"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(4)
        
        # 编辑按钮
        edit_btn = QPushButton("编辑")
        edit_btn.setFixedSize(50, 25)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
        """)
        edit_btn.clicked.connect(lambda: self.product_edit_requested.emit(product.id))
        layout.addWidget(edit_btn)
        
        # 删除按钮
        delete_btn = QPushButton("删除")
        delete_btn.setFixedSize(50, 25)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #d13438;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #b71c1c;
            }
        """)
        delete_btn.clicked.connect(lambda: self.product_delete_requested.emit(product.id))
        layout.addWidget(delete_btn)
        
        return widget

    def show_grid_view(self):
        """显示网格视图模式"""
        # TODO: 实现网格视图
        placeholder = QLabel("网格视图开发中...")
        placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder.setStyleSheet("color: #888888; font-size: 14px;")
        self.content_layout.addWidget(placeholder)

    def set_view_mode(self, mode: str):
        """设置视图模式"""
        if mode in ["independent", "table", "grid"]:
            self.view_mode = mode
            self.refresh_view()

    def add_product(self, product: Product):
        """添加商品"""
        if product not in self.products:
            self.products.append(product)
            self.refresh_view()

    def remove_product(self, product_id: int):
        """移除商品"""
        self.products = [p for p in self.products if p.id != product_id]
        self.refresh_view()

    def update_product(self, product: Product):
        """更新商品"""
        for i, p in enumerate(self.products):
            if p.id == product.id:
                self.products[i] = product
                break
        self.refresh_view()


# 测试代码
if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication, QMainWindow
    from models.product import create_sample_data

    app = QApplication(sys.argv)

    # 创建测试窗口
    window = QMainWindow()
    window.setWindowTitle("商品对比视图测试")
    window.resize(800, 600)

    # 创建对比视图
    compare_view = ProductCompareView()
    window.setCentralWidget(compare_view)

    # 添加测试数据
    sample_group = create_sample_data()
    compare_view.set_products(sample_group.products)

    window.show()
    sys.exit(app.exec())
