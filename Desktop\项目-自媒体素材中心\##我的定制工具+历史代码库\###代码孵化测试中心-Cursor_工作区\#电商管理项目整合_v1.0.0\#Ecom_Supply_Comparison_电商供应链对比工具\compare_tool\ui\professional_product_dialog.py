#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业商品编辑对话框 - 简化版
"""

import os
import sys
from typing import List, Optional
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QLineEdit,
    QTextEdit,
    QComboBox,
    QPushButton,
    QLabel,
    QGroupBox,
    QScrollArea,
    QWidget,
    QFileDialog,
    QMessageBox,
    QSpinBox,
    QDoubleSpinBox,
    QFrame,
    QTabWidget,
)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QPixmap, QIcon

# 导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.product import Product, ProductSource
from utils.image_utils import image_utils
from utils.logger import logger, log_error_with_context


def show_professional_add_dialog(group_id: int, parent=None, default_platform: str = None, default_name: str = None) -> Optional[Product]:
    """显示专业添加商品对话框"""
    # 临时回退到原对话框，确保功能可用
    from ui.add_product_dialog import show_add_product_dialog

    return show_add_product_dialog(group_id, parent, default_platform, default_name)


def show_professional_edit_dialog(
    group_id: int, product: Product, parent=None
) -> Optional[Product]:
    """显示专业编辑商品对话框"""
    # 临时回退到原对话框，确保功能可用
    from ui.add_product_dialog import show_edit_product_dialog

    return show_edit_product_dialog(group_id, product, parent)
