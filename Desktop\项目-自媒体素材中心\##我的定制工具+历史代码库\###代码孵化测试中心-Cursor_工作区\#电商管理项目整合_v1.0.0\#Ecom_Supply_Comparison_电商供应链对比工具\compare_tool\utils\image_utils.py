#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理工具模块
提供图片上传、缩放、格式转换等功能
"""

import os
import shutil
import uuid
from typing import Optional, Tuple
from PIL import Image, ImageQt
from PyQt6.QtGui import QPixmap, QIcon
from PyQt6.QtCore import QSize
import logging

logger = logging.getLogger(__name__)


class ImageUtils:
    """图片处理工具类"""

    # 支持的图片格式 - 扩展更多格式
    SUPPORTED_FORMATS = {
        ".jpg",
        ".jpeg",
        ".png",
        ".bmp",
        ".gif",
        ".webp",
        ".tiff",
        ".tif",
        ".ico",
        ".svg",
        ".avif",
        ".heic",
        ".jp2",
        ".jpx",
        ".j2k",
        ".jpc",
    }

    # 默认图片存储目录
    DEFAULT_IMAGE_DIR = "assets/images"

    # 缩略图尺寸
    THUMBNAIL_SIZE = (200, 200)
    PREVIEW_SIZE = (400, 400)

    def __init__(self, image_dir: str = None):
        """
        初始化图片工具

        Args:
            image_dir: 图片存储目录
        """
        self.image_dir = image_dir or self.DEFAULT_IMAGE_DIR
        self.ensure_image_dir()

    def ensure_image_dir(self):
        """确保图片目录存在"""
        if not os.path.exists(self.image_dir):
            os.makedirs(self.image_dir, exist_ok=True)
            logger.info(f"创建图片目录: {self.image_dir}")

    def is_supported_format(self, file_path: str) -> bool:
        """
        检查文件格式是否支持

        Args:
            file_path: 文件路径

        Returns:
            是否支持该格式
        """
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.SUPPORTED_FORMATS

    def save_image(self, source_path: str, product_id: int = None) -> Optional[str]:
        """
        保存图片到项目目录

        Args:
            source_path: 源图片路径
            product_id: 商品ID（用于命名）

        Returns:
            保存后的相对路径，失败返回None
        """
        try:
            if not os.path.exists(source_path):
                logger.error(f"源图片不存在: {source_path}")
                return None

            if not self.is_supported_format(source_path):
                logger.error(f"不支持的图片格式: {source_path}")
                return None

            # 生成新的文件名
            _, ext = os.path.splitext(source_path)
            if product_id:
                filename = f"product_{product_id}_{uuid.uuid4().hex[:8]}{ext}"
            else:
                filename = f"image_{uuid.uuid4().hex[:8]}{ext}"

            # 目标路径
            target_path = os.path.join(self.image_dir, filename)

            # 复制文件
            shutil.copy2(source_path, target_path)

            # 返回相对路径
            relative_path = os.path.relpath(target_path)
            logger.info(f"图片保存成功: {relative_path}")
            return relative_path

        except Exception as e:
            logger.error(f"保存图片失败: {e}")
            return None

    def resize_image(
        self, image_path: str, size: Tuple[int, int], keep_aspect_ratio: bool = True
    ) -> Optional[str]:
        """
        调整图片尺寸

        Args:
            image_path: 图片路径
            size: 目标尺寸 (width, height)
            keep_aspect_ratio: 是否保持宽高比

        Returns:
            调整后的图片路径，失败返回None
        """
        try:
            if not os.path.exists(image_path):
                logger.error(f"图片不存在: {image_path}")
                return None

            with Image.open(image_path) as img:
                if keep_aspect_ratio:
                    img.thumbnail(size, Image.Resampling.LANCZOS)
                else:
                    img = img.resize(size, Image.Resampling.LANCZOS)

                # 生成新文件名
                name, ext = os.path.splitext(image_path)
                resized_path = f"{name}_resized_{size[0]}x{size[1]}{ext}"

                img.save(resized_path)
                logger.info(f"图片调整尺寸成功: {resized_path}")
                return resized_path

        except Exception as e:
            logger.error(f"调整图片尺寸失败: {e}")
            return None

    def create_thumbnail(self, image_path: str) -> Optional[str]:
        """
        创建缩略图

        Args:
            image_path: 原图片路径

        Returns:
            缩略图路径，失败返回None
        """
        return self.resize_image(
            image_path, self.THUMBNAIL_SIZE, keep_aspect_ratio=True
        )

    def load_pixmap(self, image_path: str, size: QSize = None) -> Optional[QPixmap]:
        """
        加载图片为QPixmap

        Args:
            image_path: 图片路径
            size: 目标尺寸（可选）

        Returns:
            QPixmap对象，失败返回None
        """
        try:
            if not image_path or not os.path.exists(image_path):
                return None

            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                logger.error(f"无法加载图片: {image_path}")
                return None

            if size:
                from PyQt6.QtCore import Qt

                pixmap = pixmap.scaled(
                    size,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )

            return pixmap

        except Exception as e:
            logger.error(f"加载图片失败: {e}")
            return None

    def load_icon(self, image_path: str, size: QSize = QSize(32, 32)) -> QIcon:
        """
        加载图片为QIcon

        Args:
            image_path: 图片路径
            size: 图标尺寸

        Returns:
            QIcon对象
        """
        pixmap = self.load_pixmap(image_path, size)
        if pixmap:
            return QIcon(pixmap)
        else:
            return QIcon()  # 返回空图标

    def get_image_info(self, image_path: str) -> Optional[dict]:
        """
        获取图片信息

        Args:
            image_path: 图片路径

        Returns:
            包含图片信息的字典
        """
        try:
            if not os.path.exists(image_path):
                return None

            with Image.open(image_path) as img:
                info = {
                    "path": image_path,
                    "format": img.format,
                    "mode": img.mode,
                    "size": img.size,
                    "width": img.width,
                    "height": img.height,
                    "file_size": os.path.getsize(image_path),
                }

                return info

        except Exception as e:
            logger.error(f"获取图片信息失败: {e}")
            return None

    def delete_image(self, image_path: str) -> bool:
        """
        删除图片文件

        Args:
            image_path: 图片路径

        Returns:
            是否删除成功
        """
        try:
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"删除图片成功: {image_path}")
                return True
            else:
                logger.warning(f"图片不存在: {image_path}")
                return False

        except Exception as e:
            logger.error(f"删除图片失败: {e}")
            return False

    def convert_format(self, image_path: str, target_format: str) -> Optional[str]:
        """
        转换图片格式

        Args:
            image_path: 源图片路径
            target_format: 目标格式（如：'PNG', 'JPEG'）

        Returns:
            转换后的图片路径，失败返回None
        """
        try:
            if not os.path.exists(image_path):
                logger.error(f"图片不存在: {image_path}")
                return None

            with Image.open(image_path) as img:
                # 生成新文件名
                name, _ = os.path.splitext(image_path)
                ext = (
                    ".jpg"
                    if target_format.upper() == "JPEG"
                    else f".{target_format.lower()}"
                )
                converted_path = f"{name}_converted{ext}"

                # 如果是JPEG格式，需要转换为RGB模式
                if target_format.upper() == "JPEG" and img.mode in ("RGBA", "LA", "P"):
                    img = img.convert("RGB")

                img.save(converted_path, target_format.upper())
                logger.info(f"图片格式转换成功: {converted_path}")
                return converted_path

        except Exception as e:
            logger.error(f"转换图片格式失败: {e}")
            return None

    def cleanup_unused_images(self, used_paths: list) -> int:
        """
        清理未使用的图片文件

        Args:
            used_paths: 正在使用的图片路径列表

        Returns:
            清理的文件数量
        """
        try:
            if not os.path.exists(self.image_dir):
                return 0

            # 获取所有图片文件
            all_images = []
            for filename in os.listdir(self.image_dir):
                file_path = os.path.join(self.image_dir, filename)
                if os.path.isfile(file_path) and self.is_supported_format(filename):
                    all_images.append(file_path)

            # 找出未使用的图片
            used_full_paths = [os.path.abspath(path) for path in used_paths if path]
            unused_images = [
                img for img in all_images if os.path.abspath(img) not in used_full_paths
            ]

            # 删除未使用的图片
            deleted_count = 0
            for img_path in unused_images:
                if self.delete_image(img_path):
                    deleted_count += 1

            logger.info(f"清理未使用图片完成，删除了 {deleted_count} 个文件")
            return deleted_count

        except Exception as e:
            logger.error(f"清理图片失败: {e}")
            return 0


# 全局图片工具实例
image_utils = ImageUtils()
