#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导入导出工具模块
支持Excel格式的数据导入导出
"""

import os
import sys
from typing import List, Dict, Optional, Tuple
import pandas as pd
from datetime import datetime
import logging

# 导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.product import Product, ProductSource, CompareGroup
from db.db_manager import DBManager

logger = logging.getLogger(__name__)


class ImportExportManager:
    """导入导出管理器"""
    
    def __init__(self, db_manager: DBManager):
        self.db_manager = db_manager
    
    def export_group_to_excel(self, group_id: int, file_path: str) -> bool:
        """
        导出对比组数据到Excel文件
        
        Args:
            group_id: 对比组ID
            file_path: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            # 获取对比组完整数据
            group_data = self.db_manager.get_group_with_products_and_sources(group_id)
            if not group_data:
                logger.error(f"对比组不存在: {group_id}")
                return False
            
            # 准备数据
            export_data = []
            
            for product_data in group_data['products']:
                product = Product.from_dict(product_data)
                
                if product.sources:
                    for source in product.sources:
                        row = {
                            '对比组名称': group_data['name'],
                            '对比组描述': group_data['description'] or '',
                            '商品名称': product.name,
                            '商品描述': product.description or '',
                            '商品图片路径': product.image_path or '',
                            '来源平台': source.source_name,
                            '价格': source.price,
                            '运费': source.shipping,
                            '库存': source.stock,
                            '链接': source.url or '',
                            '备注': source.note or '',
                            '总价': source.get_total_price(),
                            '创建时间': product_data.get('created_at', ''),
                            '更新时间': product_data.get('updated_at', '')
                        }
                        export_data.append(row)
                else:
                    # 没有来源的商品也要导出
                    row = {
                        '对比组名称': group_data['name'],
                        '对比组描述': group_data['description'] or '',
                        '商品名称': product.name,
                        '商品描述': product.description or '',
                        '商品图片路径': product.image_path or '',
                        '来源平台': '',
                        '价格': None,
                        '运费': 0,
                        '库存': None,
                        '链接': '',
                        '备注': '',
                        '总价': None,
                        '创建时间': product_data.get('created_at', ''),
                        '更新时间': product_data.get('updated_at', '')
                    }
                    export_data.append(row)
            
            # 创建DataFrame
            df = pd.DataFrame(export_data)
            
            # 导出到Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='商品对比数据', index=False)
                
                # 获取工作表对象进行格式化
                worksheet = writer.sheets['商品对比数据']
                
                # 调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"成功导出对比组数据到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return False
    
    def export_all_groups_to_excel(self, file_path: str) -> bool:
        """
        导出所有对比组数据到Excel文件
        
        Args:
            file_path: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            # 获取所有对比组
            groups = self.db_manager.get_all_groups()
            if not groups:
                logger.warning("没有对比组数据可导出")
                return False
            
            all_data = []
            
            for group in groups:
                group_data = self.db_manager.get_group_with_products_and_sources(group['id'])
                if not group_data:
                    continue
                
                for product_data in group_data['products']:
                    product = Product.from_dict(product_data)
                    
                    if product.sources:
                        for source in product.sources:
                            row = {
                                '对比组名称': group_data['name'],
                                '对比组描述': group_data['description'] or '',
                                '商品名称': product.name,
                                '商品描述': product.description or '',
                                '商品图片路径': product.image_path or '',
                                '来源平台': source.source_name,
                                '价格': source.price,
                                '运费': source.shipping,
                                '库存': source.stock,
                                '链接': source.url or '',
                                '备注': source.note or '',
                                '总价': source.get_total_price(),
                                '创建时间': product_data.get('created_at', ''),
                                '更新时间': product_data.get('updated_at', '')
                            }
                            all_data.append(row)
                    else:
                        row = {
                            '对比组名称': group_data['name'],
                            '对比组描述': group_data['description'] or '',
                            '商品名称': product.name,
                            '商品描述': product.description or '',
                            '商品图片路径': product.image_path or '',
                            '来源平台': '',
                            '价格': None,
                            '运费': 0,
                            '库存': None,
                            '链接': '',
                            '备注': '',
                            '总价': None,
                            '创建时间': product_data.get('created_at', ''),
                            '更新时间': product_data.get('updated_at', '')
                        }
                        all_data.append(row)
            
            # 创建DataFrame并导出
            df = pd.DataFrame(all_data)
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='所有商品对比数据', index=False)
                
                # 格式化
                worksheet = writer.sheets['所有商品对比数据']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"成功导出所有对比组数据到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出所有数据失败: {e}")
            return False
    
    def import_from_excel(self, file_path: str, group_name: str = None) -> Tuple[bool, str]:
        """
        从Excel文件导入数据
        
        Args:
            file_path: Excel文件路径
            group_name: 目标对比组名称（如果为None，使用Excel中的对比组名称）
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, sheet_name=0)
            
            # 验证必要的列
            required_columns = ['商品名称', '来源平台']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"Excel文件缺少必要的列: {', '.join(missing_columns)}"
            
            # 按对比组分组数据
            if '对比组名称' in df.columns:
                groups_data = df.groupby('对比组名称')
            else:
                # 如果没有对比组名称列，创建一个默认组
                default_group_name = group_name or f"导入数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                groups_data = [(default_group_name, df)]
            
            imported_groups = 0
            imported_products = 0
            imported_sources = 0
            
            for group_name_in_excel, group_df in groups_data:
                try:
                    # 创建或获取对比组
                    target_group_name = group_name or group_name_in_excel
                    group_description = group_df['对比组描述'].iloc[0] if '对比组描述' in group_df.columns else ""
                    
                    try:
                        group_id = self.db_manager.add_group(target_group_name, group_description)
                        imported_groups += 1
                    except ValueError:
                        # 对比组已存在，获取ID
                        groups = self.db_manager.get_all_groups()
                        existing_group = next((g for g in groups if g['name'] == target_group_name), None)
                        if existing_group:
                            group_id = existing_group['id']
                        else:
                            continue
                    
                    # 按商品分组
                    products_data = group_df.groupby('商品名称')
                    
                    for product_name, product_df in products_data:
                        try:
                            # 创建商品
                            product_description = product_df['商品描述'].iloc[0] if '商品描述' in product_df.columns else ""
                            product_image_path = product_df['商品图片路径'].iloc[0] if '商品图片路径' in product_df.columns else ""
                            
                            product_id = self.db_manager.add_product(
                                group_id, product_name, product_description, product_image_path
                            )
                            imported_products += 1
                            
                            # 添加来源
                            for _, row in product_df.iterrows():
                                if pd.notna(row['来源平台']) and row['来源平台'].strip():
                                    source_name = row['来源平台']
                                    price = row.get('价格') if pd.notna(row.get('价格')) else None
                                    shipping = row.get('运费', 0) if pd.notna(row.get('运费')) else 0
                                    stock = row.get('库存') if pd.notna(row.get('库存')) else None
                                    url = row.get('链接', '') if pd.notna(row.get('链接')) else ''
                                    note = row.get('备注', '') if pd.notna(row.get('备注')) else ''
                                    
                                    self.db_manager.add_source(
                                        product_id, source_name, price, shipping, stock, url, note
                                    )
                                    imported_sources += 1
                        
                        except Exception as e:
                            logger.warning(f"导入商品失败: {product_name}, 错误: {e}")
                            continue
                
                except Exception as e:
                    logger.warning(f"导入对比组失败: {group_name_in_excel}, 错误: {e}")
                    continue
            
            success_msg = f"导入完成: {imported_groups}个对比组, {imported_products}个商品, {imported_sources}个来源"
            logger.info(success_msg)
            return True, success_msg
            
        except Exception as e:
            error_msg = f"导入数据失败: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_excel_template(self, file_path: str) -> bool:
        """
        生成Excel导入模板
        
        Args:
            file_path: 模板文件路径
            
        Returns:
            是否生成成功
        """
        try:
            # 创建模板数据
            template_data = [
                {
                    '对比组名称': '示例对比组',
                    '对比组描述': '这是一个示例对比组',
                    '商品名称': '示例商品1',
                    '商品描述': '这是示例商品的描述',
                    '商品图片路径': '',
                    '来源平台': '淘宝',
                    '价格': 99.99,
                    '运费': 10.0,
                    '库存': 100,
                    '链接': 'https://example.com/product1',
                    '备注': '官方旗舰店'
                },
                {
                    '对比组名称': '示例对比组',
                    '对比组描述': '这是一个示例对比组',
                    '商品名称': '示例商品1',
                    '商品描述': '这是示例商品的描述',
                    '商品图片路径': '',
                    '来源平台': '京东',
                    '价格': 109.99,
                    '运费': 0,
                    '库存': 50,
                    '链接': 'https://example.com/product2',
                    '备注': '自营'
                }
            ]
            
            df = pd.DataFrame(template_data)
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='导入模板', index=False)
                
                # 格式化
                worksheet = writer.sheets['导入模板']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 30)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            logger.info(f"成功生成Excel导入模板: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成Excel模板失败: {e}")
            return False
