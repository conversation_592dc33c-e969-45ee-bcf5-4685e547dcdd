#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业日志系统
提供统一的日志管理和错误追踪
"""

import logging
import os
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler
from pathlib import Path


class ProfessionalLogger:
    """专业日志管理器"""

    _instance = None
    _logger = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialize_logger()
        return cls._instance

    def _initialize_logger(self):
        """初始化日志器"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 创建日志器
        self._logger = logging.getLogger("CompareToolLogger")
        self._logger.setLevel(logging.DEBUG)

        # 避免重复添加处理器
        if self._logger.handlers:
            return

        # 创建格式器
        formatter = logging.Formatter(
            "%(asctime)s | %(levelname)-8s | %(module)s.%(funcName)s:%(lineno)d | %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # 文件处理器 - 详细日志
        file_handler = RotatingFileHandler(
            log_dir / "compare_tool.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding="utf-8",
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # 错误文件处理器
        error_handler = RotatingFileHandler(
            log_dir / "errors.log",
            maxBytes=5 * 1024 * 1024,  # 5MB
            backupCount=3,
            encoding="utf-8",
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)

        # 控制台处理器 - 简化输出
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter("%(levelname)-8s | %(message)s")
        console_handler.setFormatter(console_formatter)

        # 添加处理器
        self._logger.addHandler(file_handler)
        self._logger.addHandler(error_handler)
        self._logger.addHandler(console_handler)

        # 记录启动信息
        self._logger.info("=" * 50)
        self._logger.info("商品对比工具启动")
        self._logger.info(f"Python版本: {sys.version}")
        self._logger.info(f"工作目录: {os.getcwd()}")
        self._logger.info("=" * 50)

    @property
    def logger(self):
        """获取日志器实例"""
        return self._logger

    def debug(self, message, *args, **kwargs):
        """调试信息"""
        self._logger.debug(message, *args, **kwargs)

    def info(self, message, *args, **kwargs):
        """一般信息"""
        self._logger.info(message, *args, **kwargs)

    def warning(self, message, *args, **kwargs):
        """警告信息"""
        self._logger.warning(message, *args, **kwargs)

    def error(self, message, *args, **kwargs):
        """错误信息"""
        self._logger.error(message, *args, **kwargs)

    def critical(self, message, *args, **kwargs):
        """严重错误"""
        self._logger.critical(message, *args, **kwargs)

    def exception(self, message, *args, **kwargs):
        """异常信息（包含堆栈跟踪）"""
        self._logger.exception(message, *args, **kwargs)


# 创建全局日志器实例
logger = ProfessionalLogger()


# 导出便捷函数
def debug(message, *args, **kwargs):
    logger.debug(message, *args, **kwargs)


def info(message, *args, **kwargs):
    logger.info(message, *args, **kwargs)


def warning(message, *args, **kwargs):
    logger.warning(message, *args, **kwargs)


def error(message, *args, **kwargs):
    logger.error(message, *args, **kwargs)


def critical(message, *args, **kwargs):
    logger.critical(message, *args, **kwargs)


def exception(message, *args, **kwargs):
    logger.exception(message, *args, **kwargs)


def log_function_call(func_name, args=None, kwargs=None):
    """记录函数调用"""
    args_str = f"args={args}" if args else ""
    kwargs_str = f"kwargs={kwargs}" if kwargs else ""
    params = ", ".join(filter(None, [args_str, kwargs_str]))
    debug(f"调用函数: {func_name}({params})")


def log_error_with_context(error, context=""):
    """记录带上下文的错误"""
    error_msg = f"错误: {str(error)}"
    if context:
        error_msg += f" | 上下文: {context}"
    exception(error_msg)
