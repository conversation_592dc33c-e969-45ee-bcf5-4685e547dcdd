#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化工具模块
提供性能监控、优化建议和错误处理改进
"""

import os
import sys
import time
import psutil
import logging
from functools import wraps
from typing import Callable, Any, Dict, List
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('performance.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.start_time = time.time()
        self.memory_usage = []
        self.cpu_usage = []
    
    def measure_time(self, func_name: str = None):
        """时间测量装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                name = func_name or f"{func.__module__}.{func.__name__}"
                start_time = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    # 记录执行时间
                    if name not in self.metrics:
                        self.metrics[name] = []
                    self.metrics[name].append(execution_time)
                    
                    # 如果执行时间过长，记录警告
                    if execution_time > 1.0:  # 超过1秒
                        logger.warning(f"慢查询警告: {name} 耗时 {execution_time:.2f}秒")
                    
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    logger.error(f"函数执行失败: {name}, 耗时: {execution_time:.2f}秒, 错误: {e}")
                    raise
                    
            return wrapper
        return decorator
    
    def monitor_memory(self):
        """监控内存使用"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            self.memory_usage.append((datetime.now(), memory_mb))
            
            # 保留最近100个记录
            if len(self.memory_usage) > 100:
                self.memory_usage = self.memory_usage[-100:]
            
            # 内存使用过高警告
            if memory_mb > 500:  # 超过500MB
                logger.warning(f"内存使用过高: {memory_mb:.2f}MB")
                
        except Exception as e:
            logger.error(f"内存监控失败: {e}")
    
    def monitor_cpu(self):
        """监控CPU使用"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage.append((datetime.now(), cpu_percent))
            
            # 保留最近100个记录
            if len(self.cpu_usage) > 100:
                self.cpu_usage = self.cpu_usage[-100:]
            
            # CPU使用过高警告
            if cpu_percent > 80:  # 超过80%
                logger.warning(f"CPU使用过高: {cpu_percent:.1f}%")
                
        except Exception as e:
            logger.error(f"CPU监控失败: {e}")
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        report = {
            'uptime': time.time() - self.start_time,
            'function_metrics': {},
            'memory_stats': {},
            'cpu_stats': {}
        }
        
        # 函数执行统计
        for func_name, times in self.metrics.items():
            report['function_metrics'][func_name] = {
                'call_count': len(times),
                'total_time': sum(times),
                'avg_time': sum(times) / len(times),
                'max_time': max(times),
                'min_time': min(times)
            }
        
        # 内存统计
        if self.memory_usage:
            memory_values = [usage[1] for usage in self.memory_usage]
            report['memory_stats'] = {
                'current_mb': memory_values[-1] if memory_values else 0,
                'max_mb': max(memory_values),
                'avg_mb': sum(memory_values) / len(memory_values)
            }
        
        # CPU统计
        if self.cpu_usage:
            cpu_values = [usage[1] for usage in self.cpu_usage]
            report['cpu_stats'] = {
                'current_percent': cpu_values[-1] if cpu_values else 0,
                'max_percent': max(cpu_values),
                'avg_percent': sum(cpu_values) / len(cpu_values)
            }
        
        return report
    
    def print_performance_report(self):
        """打印性能报告"""
        report = self.get_performance_report()
        
        print("\n" + "=" * 60)
        print("性能监控报告")
        print("=" * 60)
        
        print(f"运行时间: {report['uptime']:.2f}秒")
        
        # 函数执行统计
        if report['function_metrics']:
            print("\n函数执行统计:")
            print("-" * 40)
            for func_name, stats in report['function_metrics'].items():
                print(f"函数: {func_name}")
                print(f"  调用次数: {stats['call_count']}")
                print(f"  总耗时: {stats['total_time']:.3f}秒")
                print(f"  平均耗时: {stats['avg_time']:.3f}秒")
                print(f"  最大耗时: {stats['max_time']:.3f}秒")
                print()
        
        # 内存统计
        if report['memory_stats']:
            print("内存使用统计:")
            print("-" * 40)
            print(f"当前内存: {report['memory_stats']['current_mb']:.2f}MB")
            print(f"最大内存: {report['memory_stats']['max_mb']:.2f}MB")
            print(f"平均内存: {report['memory_stats']['avg_mb']:.2f}MB")
            print()
        
        # CPU统计
        if report['cpu_stats']:
            print("CPU使用统计:")
            print("-" * 40)
            print(f"当前CPU: {report['cpu_stats']['current_percent']:.1f}%")
            print(f"最大CPU: {report['cpu_stats']['max_percent']:.1f}%")
            print(f"平均CPU: {report['cpu_stats']['avg_percent']:.1f}%")
            print()


class DatabaseOptimizer:
    """数据库优化器"""
    
    @staticmethod
    def optimize_database(db_path: str) -> List[str]:
        """优化数据库"""
        import sqlite3
        
        optimizations = []
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 分析表统计信息
                cursor.execute("ANALYZE")
                optimizations.append("执行了数据库统计分析")
                
                # 重建索引
                cursor.execute("REINDEX")
                optimizations.append("重建了所有索引")
                
                # 清理数据库
                cursor.execute("VACUUM")
                optimizations.append("执行了数据库清理")
                
                # 检查数据库完整性
                cursor.execute("PRAGMA integrity_check")
                result = cursor.fetchone()
                if result[0] == "ok":
                    optimizations.append("数据库完整性检查通过")
                else:
                    optimizations.append(f"数据库完整性检查失败: {result[0]}")
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"数据库优化失败: {e}")
            optimizations.append(f"优化失败: {e}")
        
        return optimizations
    
    @staticmethod
    def get_database_stats(db_path: str) -> Dict:
        """获取数据库统计信息"""
        import sqlite3
        
        stats = {}
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # 数据库大小
                stats['file_size_mb'] = os.path.getsize(db_path) / 1024 / 1024
                
                # 表统计
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                
                table_stats = {}
                for table in tables:
                    table_name = table[0]
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    table_stats[table_name] = count
                
                stats['table_counts'] = table_stats
                
                # 索引统计
                cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
                indexes = cursor.fetchall()
                stats['index_count'] = len(indexes)
                
        except Exception as e:
            logger.error(f"获取数据库统计失败: {e}")
            stats['error'] = str(e)
        
        return stats


class ErrorHandler:
    """错误处理器"""
    
    @staticmethod
    def safe_execute(func: Callable, *args, **kwargs) -> tuple:
        """安全执行函数"""
        try:
            result = func(*args, **kwargs)
            return True, result
        except Exception as e:
            logger.error(f"函数执行失败: {func.__name__}, 错误: {e}")
            return False, str(e)
    
    @staticmethod
    def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
        """失败重试装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Any:
                last_exception = None
                
                for attempt in range(max_retries):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        last_exception = e
                        if attempt < max_retries - 1:
                            logger.warning(f"函数 {func.__name__} 第{attempt + 1}次执行失败，{delay}秒后重试: {e}")
                            time.sleep(delay)
                        else:
                            logger.error(f"函数 {func.__name__} 重试{max_retries}次后仍然失败: {e}")
                
                raise last_exception
            return wrapper
        return decorator


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def optimize_application():
    """优化应用程序"""
    print("开始应用程序优化...")
    
    optimizations = []
    
    # 1. 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 8):
        optimizations.append("建议升级到Python 3.8+以获得更好的性能")
    else:
        optimizations.append(f"Python版本检查通过: {python_version.major}.{python_version.minor}")
    
    # 2. 检查依赖包
    try:
        import PyQt6
        optimizations.append("PyQt6依赖检查通过")
    except ImportError:
        optimizations.append("警告: PyQt6未安装")
    
    try:
        import pandas
        optimizations.append("pandas依赖检查通过")
    except ImportError:
        optimizations.append("警告: pandas未安装，导入导出功能可能不可用")
    
    # 3. 检查系统资源
    memory = psutil.virtual_memory()
    if memory.available < 1024 * 1024 * 1024:  # 小于1GB
        optimizations.append("警告: 可用内存不足1GB，可能影响性能")
    else:
        optimizations.append(f"系统内存检查通过: {memory.available / 1024 / 1024 / 1024:.1f}GB可用")
    
    # 4. 检查磁盘空间
    disk = psutil.disk_usage('.')
    if disk.free < 1024 * 1024 * 1024:  # 小于1GB
        optimizations.append("警告: 磁盘空间不足1GB")
    else:
        optimizations.append(f"磁盘空间检查通过: {disk.free / 1024 / 1024 / 1024:.1f}GB可用")
    
    print("优化检查完成:")
    for opt in optimizations:
        print(f"- {opt}")
    
    return optimizations


if __name__ == "__main__":
    # 运行优化检查
    optimize_application()
    
    # 打印性能报告
    performance_monitor.print_performance_report()
