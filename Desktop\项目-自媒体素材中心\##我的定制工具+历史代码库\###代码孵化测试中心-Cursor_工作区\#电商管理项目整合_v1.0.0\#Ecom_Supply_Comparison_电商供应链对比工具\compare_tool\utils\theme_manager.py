#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器模块
负责应用程序的主题管理和样式设置
"""

import os
import sys
from enum import Enum
from typing import Optional
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSettings


class ThemeType(Enum):
    """主题类型枚举"""

    LIGHT = "light"
    DARK = "dark"
    AUTO = "auto"  # 跟随系统


class ThemeManager:
    """主题管理器"""

    def __init__(self):
        self.settings = QSettings("商品对比工具", "主题设置")
        self.current_theme = ThemeType.DARK  # 默认暗黑主题
        self.load_theme_preference()

    def load_theme_preference(self):
        """加载主题偏好设置"""
        theme_name = self.settings.value("theme", ThemeType.DARK.value)
        try:
            self.current_theme = ThemeType(theme_name)
        except ValueError:
            self.current_theme = ThemeType.DARK

    def save_theme_preference(self, theme: ThemeType):
        """保存主题偏好设置"""
        self.settings.setValue("theme", theme.value)
        self.current_theme = theme

    def get_custom_dark_style(self) -> str:
        """获取自定义暗黑样式"""
        return """
        /* 主窗口和基础控件 */
        QMainWindow, QDialog, QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
        }
        
        /* 工具栏 */
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            spacing: 3px;
            padding: 5px;
        }
        
        QToolBar::separator {
            background-color: #555555;
            width: 1px;
            margin: 5px;
        }
        
        /* 按钮 */
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 8px 16px;
            color: #ffffff;
            font-weight: bold;
            min-height: 20px;
        }
        
        QPushButton:hover {
            background-color: #505050;
            border-color: #777777;
        }
        
        QPushButton:pressed {
            background-color: #353535;
        }
        
        QPushButton:disabled {
            background-color: #2b2b2b;
            color: #666666;
            border-color: #444444;
        }
        
        QPushButton:default {
            background-color: #0078d4;
            border-color: #106ebe;
        }
        
        QPushButton:default:hover {
            background-color: #106ebe;
        }
        
        /* 列表控件 */
        QListWidget {
            background-color: #353535;
            border: 1px solid #555555;
            border-radius: 6px;
            color: #ffffff;
            selection-background-color: #0078d4;
            outline: none;
        }
        
        QListWidget::item {
            padding: 10px;
            border-bottom: 1px solid #444444;
            border-radius: 4px;
            margin: 2px;
        }
        
        QListWidget::item:hover {
            background-color: #404040;
        }
        
        QListWidget::item:selected {
            background-color: #0078d4;
            color: #ffffff;
        }
        
        /* 表格 */
        QTableWidget {
            background-color: #353535;
            border: 1px solid #555555;
            border-radius: 6px;
            color: #ffffff;
            gridline-color: #555555;
            selection-background-color: #0078d4;
        }
        
        QTableWidget::item {
            padding: 8px;
            border: none;
        }
        
        QTableWidget::item:hover {
            background-color: #404040;
        }
        
        QTableWidget::item:selected {
            background-color: #0078d4;
        }
        
        QHeaderView::section {
            background-color: #404040;
            color: #ffffff;
            padding: 10px;
            border: 1px solid #555555;
            font-weight: bold;
        }
        
        /* 输入框 */
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 8px;
            color: #ffffff;
            selection-background-color: #0078d4;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
            border-color: #0078d4;
            border-width: 2px;
        }
        
        /* 下拉框 */
        QComboBox {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 8px;
            color: #ffffff;
            min-width: 100px;
        }
        
        QComboBox:hover {
            border-color: #777777;
        }
        
        QComboBox:focus {
            border-color: #0078d4;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            width: 12px;
            height: 12px;
        }
        
        QComboBox QAbstractItemView {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            selection-background-color: #0078d4;
        }
        
        /* 数字输入框 */
        QSpinBox, QDoubleSpinBox {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 6px;
            color: #ffffff;
        }
        
        QSpinBox:focus, QDoubleSpinBox:focus {
            border-color: #0078d4;
        }
        
        /* 标签 */
        QLabel {
            color: #ffffff;
            background-color: transparent;
        }
        
        /* 分组框 */
        QGroupBox {
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 6px;
            margin-top: 15px;
            padding-top: 15px;
            font-weight: bold;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            background-color: #2b2b2b;
        }
        
        /* 滚动条 */
        QScrollBar:vertical {
            background-color: #2b2b2b;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #555555;
            border-radius: 6px;
            min-height: 20px;
            margin: 2px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #666666;
        }
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
            border: none;
            background: none;
            height: 0px;
        }
        
        QScrollBar:horizontal {
            background-color: #2b2b2b;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #555555;
            border-radius: 6px;
            min-width: 20px;
            margin: 2px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #666666;
        }
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
            border: none;
            background: none;
            width: 0px;
        }
        
        /* 状态栏 */
        QStatusBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-top: 1px solid #555555;
        }
        
        /* 菜单栏 */
        QMenuBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-bottom: 1px solid #555555;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: #0078d4;
        }
        
        QMenu {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
        }
        
        QMenu::item {
            padding: 8px 20px;
        }
        
        QMenu::item:selected {
            background-color: #0078d4;
        }
        
        QMenu::separator {
            height: 1px;
            background-color: #555555;
            margin: 5px 0;
        }
        
        /* 分割器 */
        QSplitter::handle {
            background-color: #555555;
        }
        
        QSplitter::handle:horizontal {
            width: 2px;
        }
        
        QSplitter::handle:vertical {
            height: 2px;
        }
        
        /* 框架和对话框 */
        QFrame {
            background-color: #2b2b2b;
            border: 1px solid #555555;
            border-radius: 6px;
        }
        
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        /* 标签页 */
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }
        
        QTabBar::tab {
            background-color: #404040;
            color: #ffffff;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #505050;
        }
        
        /* 进度条 */
        QProgressBar {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            text-align: center;
            color: #ffffff;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 5px;
        }
        
        /* 复选框和单选框 */
        QCheckBox, QRadioButton {
            color: #ffffff;
            spacing: 8px;
        }
        
        QCheckBox::indicator, QRadioButton::indicator {
            width: 16px;
            height: 16px;
        }
        
        QCheckBox::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 3px;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
            border-radius: 3px;
        }
        
        QRadioButton::indicator:unchecked {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 8px;
        }
        
        QRadioButton::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #0078d4;
            border-radius: 8px;
        }
        
        /* 滑块 */
        QSlider::groove:horizontal {
            background-color: #404040;
            height: 6px;
            border-radius: 3px;
        }
        
        QSlider::handle:horizontal {
            background-color: #0078d4;
            width: 16px;
            height: 16px;
            border-radius: 8px;
            margin: -5px 0;
        }
        
        /* 工具提示 */
        QToolTip {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 6px;
        }
        
        /* 滚动区域 */
        QScrollArea {
            background-color: #353535;
            border: 1px solid #555555;
            border-radius: 6px;
        }
        """

    def get_light_style(self) -> str:
        """获取浅色主题样式"""
        return """
        QMainWindow, QDialog, QWidget {
            background-color: #ffffff;
            color: #000000;
            font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
        }
        
        QPushButton {
            background-color: #f0f0f0;
            border: 1px solid #cccccc;
            border-radius: 6px;
            padding: 8px 16px;
            color: #000000;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #e0e0e0;
        }
        
        QPushButton:pressed {
            background-color: #d0d0d0;
        }
        """

    def apply_theme(self, app: QApplication, theme: Optional[ThemeType] = None):
        """
        应用主题

        Args:
            app: QApplication 实例
            theme: 要应用的主题，如果为 None，则使用当前主题
        """
        current_theme = theme if theme is not None else self.current_theme

        # 清除现有样式
        app.setStyleSheet("")

        if current_theme == ThemeType.DARK:
            # 应用暗黑主题
            app.setStyleSheet(self.get_custom_dark_style())
        elif current_theme == ThemeType.LIGHT:
            # 应用浅色主题
            app.setStyleSheet(self.get_custom_light_style())
        
        # 保存当前主题
        self.current_theme = current_theme
        self.save_theme_preference(current_theme)

        # current_theme = theme if theme is not None else self.current_theme

        # # 停止使用 qdarkstyle，因为它可能与自定义样式冲突
        # app.setStyleSheet("")

        # if current_theme == ThemeType.DARK:
        #     # 优先使用 professional.qss
        #     style_path = os.path.join(os.path.dirname(__file__), '..', 'style', 'professional.qss')
        #     if os.path.exists(style_path):
        #         with open(style_path, "r", encoding="utf-8") as f:
        #             app.setStyleSheet(f.read())
        #     else:
        #         # 如果 professional.qss 不存在，则使用内置的暗黑主题
        #         app.setStyleSheet(self.get_custom_dark_style())

        # elif current_theme == ThemeType.LIGHT:
        #     app.setStyleSheet(self.get_light_style())

        # self.save_theme_preference(current_theme)

    def get_current_theme(self) -> ThemeType:
        """获取当前主题"""
        return self.current_theme

    def toggle_theme(self, app: QApplication):
        """切换主题"""
        if self.current_theme == ThemeType.DARK:
            new_theme = ThemeType.LIGHT
        else:
            new_theme = ThemeType.DARK

        self.apply_theme(app, new_theme)


# 全局主题管理器实例
theme_manager = ThemeManager()
