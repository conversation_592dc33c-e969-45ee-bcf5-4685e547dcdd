# 商品对比工具 - 使用说明

## 快速开始

### 1. 启动程序
双击运行 `main.py` 或在命令行中执行：
```bash
python main.py
```

### 2. 创建第一个对比组
1. 点击左侧的"添加对比组"按钮
2. 输入对比组名称（如："手机对比"）
3. 输入描述（可选）
4. 点击确定

### 3. 添加商品
1. 选中刚创建的对比组
2. 点击"添加商品"按钮
3. 填写商品信息：
   - 商品名称（必填）
   - 商品描述（可选）
   - 上传商品图片（可选）
4. 添加来源信息：
   - 来源平台（如：淘宝、京东）
   - 价格
   - 运费
   - 库存
   - 商品链接
   - 备注
5. 可以添加多个来源进行对比
6. 点击保存

### 4. 查看对比结果
- 在右侧可以看到商品的对比卡片
- 最优来源会用绿色边框高亮显示
- 点击商品图片可以放大预览
- 可以编辑或删除商品

## 详细功能说明

### 对比组管理
- **添加对比组**：创建新的商品对比分类
- **编辑对比组**：双击对比组名称或右键编辑
- **删除对比组**：选中对比组后点击删除按钮
- **查看统计**：对比组名称后显示商品数量

### 商品管理
- **添加商品**：在对比组中添加新商品
- **编辑商品**：点击商品卡片上的编辑按钮
- **删除商品**：点击商品卡片上的删除按钮
- **图片管理**：支持上传、预览、替换商品图片

### 来源对比
- **多来源支持**：每个商品可以添加多个购买来源
- **价格对比**：自动计算最低价、最高价、平均价
- **总价计算**：自动计算商品价格+运费的总价
- **最优推荐**：自动标识价格最低且有库存的来源
- **库存状态**：显示各来源的库存情况

### 图片功能
- **上传图片**：支持 PNG、JPG、JPEG、BMP、GIF、WebP 格式
- **图片预览**：点击图片可以放大查看
- **图片编辑**：在预览窗口中可以缩放、旋转图片
- **图片替换**：在预览窗口中可以替换图片
- **图片保存**：可以将编辑后的图片另存为新文件

### 数据导入导出
- **生成模板**：下载标准的Excel导入模板
- **导入数据**：从Excel文件批量导入商品数据
- **导出单组**：导出指定对比组的数据
- **导出全部**：导出所有对比组的数据

### 主题设置
- **暗黑主题**：默认使用现代化的暗黑主题
- **浅色主题**：可以切换到浅色主题
- **主题切换**：通过菜单栏"视图"->"主题"切换

## 操作技巧

### 快捷键
- `Ctrl+G`：添加对比组
- `Ctrl+P`：添加商品
- `Ctrl+I`：导入数据
- `Ctrl+E`：导出数据
- `Ctrl+Q`：退出程序

### 界面操作
- **双击对比组**：快速编辑对比组
- **右键菜单**：在对比组列表中右键可以看到更多操作
- **拖拽调整**：可以拖拽调整左右面板的大小
- **滚动查看**：商品列表支持滚动查看

### 数据管理
- **自动保存**：所有数据自动保存到本地数据库
- **数据备份**：定期导出数据作为备份
- **数据恢复**：可以通过导入功能恢复数据

## Excel导入格式说明

### 必填列
- **商品名称**：商品的名称
- **来源平台**：购买平台（如：淘宝、京东）

### 可选列
- **对比组名称**：商品所属的对比组
- **对比组描述**：对比组的描述
- **商品描述**：商品的详细描述
- **商品图片路径**：图片文件的路径
- **价格**：商品价格（数字）
- **运费**：运费（数字，默认0）
- **库存**：库存数量（数字）
- **链接**：商品购买链接
- **备注**：其他备注信息

### 导入规则
1. 同一商品的多个来源请分别占用一行
2. 相同商品名称的行会被合并为一个商品
3. 如果对比组不存在会自动创建
4. 价格和库存必须是数字格式
5. 空白行会被忽略

### 示例数据
```
对比组名称    商品名称      来源平台    价格     运费    库存    链接                    备注
手机对比      iPhone 15     淘宝       8999     0      100     https://taobao.com     官方旗舰店
手机对比      iPhone 15     京东       9199     0      50      https://jd.com         自营
手机对比      小米14        淘宝       3999     10     200     https://taobao.com     小米官方店
```

## 常见问题

### Q: 程序启动失败怎么办？
A: 请检查是否安装了所有依赖包，运行 `pip install -r requirements.txt`

### Q: 图片无法显示怎么办？
A: 请检查图片格式是否支持，建议使用 PNG 或 JPG 格式

### Q: 导入Excel失败怎么办？
A: 请检查Excel文件格式是否正确，建议先下载模板填写

### Q: 数据丢失怎么办？
A: 数据保存在 `compare_tool.db` 文件中，请定期备份该文件

### Q: 程序运行缓慢怎么办？
A: 可以尝试清理数据库或减少图片文件大小

### Q: 如何备份数据？
A: 使用导出功能将数据导出为Excel文件，或直接备份数据库文件

### Q: 如何恢复数据？
A: 使用导入功能从Excel文件恢复，或替换数据库文件

## 技术支持

如果遇到问题或有改进建议，请：
1. 检查日志文件中的错误信息
2. 尝试重启程序
3. 检查系统环境和依赖包
4. 联系技术支持

## 更新日志

### v1.0.0 (2024-12-22)
- 初始版本发布
- 实现基础的商品对比功能
- 支持暗黑主题
- 支持数据导入导出
- 支持图片管理

---

**祝您使用愉快！** 🎉
