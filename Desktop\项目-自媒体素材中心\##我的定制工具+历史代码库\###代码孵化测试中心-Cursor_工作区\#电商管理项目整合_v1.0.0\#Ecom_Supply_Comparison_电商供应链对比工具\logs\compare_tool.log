2025-06-28 22:45:19 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 22:45:19 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 22:45:19 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 22:45:19 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 22:45:19 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 22:47:35 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 22:47:35 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 22:47:35 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 22:47:35 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 22:47:35 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:32:35 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:32:35 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:32:35 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:32:35 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:32:35 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:33:44 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:33:44 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:33:44 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:33:44 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:33:44 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:34:42 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:34:42 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:34:42 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:34:42 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:34:42 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:35:43 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:35:43 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:35:43 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:35:43 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:35:43 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:37:12 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:37:12 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:37:12 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:37:12 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:37:12 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:38:42 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:38:42 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:38:42 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:38:42 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:38:42 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:39:46 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:39:46 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:39:46 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:39:46 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:39:46 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:43:19 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:43:19 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:43:19 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:43:19 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:43:19 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:43:25 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-28 23:43:26 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-28 23:43:26 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-28 23:43:26 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-28 23:43:26 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-28 23:43:26 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-28 23:43:27 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-28 23:43:27 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-28 23:43:27 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 23:43:27 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:0, 组:0
2025-06-28 23:43:27 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-28 23:43:55 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 23:43:55 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:0, 组:1
2025-06-28 23:43:55 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 23:43:55 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 906, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-28 23:43:55 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 23:43:55 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:0, 组:1
2025-06-28 23:44:19 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 23:44:19 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 906, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-28 23:44:19 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 23:44:19 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-28 23:44:19 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-28 23:44:19 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 906, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-28 23:44:19 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-28 23:44:19 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:03:35 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:03:35 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:03:35 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:03:35 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:03:35 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:03:36 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 00:03:37 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 00:03:37 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 00:03:37 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:03:37 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:03:37 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 00:05:02 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:05:02 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:05:02 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:05:02 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:05:02 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:05:03 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 00:05:03 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 00:05:03 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 00:05:03 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:05:03 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:05:03 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 00:05:19 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:05:19 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:05:19 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:05:19 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:05:25 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:05:25 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:05:25 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:05:25 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:05:25 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:05:25 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:05:25 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:05:25 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:08:22 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:08:22 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:08:22 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:08:22 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:08:22 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:08:23 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 00:08:23 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 00:08:23 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 00:08:23 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:08:23 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:08:23 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 00:21:08 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:21:08 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:21:08 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:21:08 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:21:08 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:21:10 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 00:21:10 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 00:21:10 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 00:21:10 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:10 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:21:10 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 00:21:17 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:21:17 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:17 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:17 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:21:22 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:21:22 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:22 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:22 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:0, 组:0
2025-06-29 00:21:22 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:22 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:0, 组:0
2025-06-29 00:21:27 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:27 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:0, 组:1
2025-06-29 00:21:27 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:21:27 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:27 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:27 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:0, 组:1
2025-06-29 00:21:39 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:21:39 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:39 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:39 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:21:39 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:21:39 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:39 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:21:39 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:28:13 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:28:13 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:28:13 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:28:13 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:28:13 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:30:34 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:30:34 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:30:34 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:30:34 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:30:34 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:33:29 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:33:29 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:33:29 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:33:29 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:33:29 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:37:53 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:37:53 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:37:53 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:37:53 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:37:53 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:38:00 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:38:00 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:38:00 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:38:00 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:38:00 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 00:38:02 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 00:38:03 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 00:38:03 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 00:38:03 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:38:03 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:1
2025-06-29 00:38:03 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 00:38:16 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:38:16 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:2
2025-06-29 00:38:16 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:38:16 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:38:16 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:38:16 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:1, 组:2
2025-06-29 00:38:22 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:38:22 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:38:22 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:38:22 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-29 00:38:22 | DEBUG    | logger.debug:93 | 调用函数: refresh_product_cards()
2025-06-29 00:38:22 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:38:22 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 00:38:22 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-29 00:52:17 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 00:52:17 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 00:52:17 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 00:52:17 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 00:52:17 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 01:08:07 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 01:08:07 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 01:08:07 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 01:08:07 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 01:08:07 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 01:08:08 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 01:08:09 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 01:08:09 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 01:08:09 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 01:08:09 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-29 01:08:09 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 01:53:12 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 01:53:12 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 01:53:12 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 01:53:12 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 01:53:12 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 01:55:50 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 01:55:50 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 01:55:50 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 01:55:50 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 01:55:50 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 01:55:52 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 01:55:52 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'edit_group_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1119, in connect_signals
    self.edit_group_btn.clicked.connect(self.edit_group)
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'edit_group_btn'. Did you mean: 'add_group_btn'?
2025-06-29 01:56:03 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 01:56:03 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 01:56:03 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 01:56:03 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 01:56:03 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:02:31 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:02:31 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:02:31 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:02:31 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:02:31 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:02:32 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:02:32 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 02:02:32 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 02:02:32 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 02:02:32 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-29 02:02:32 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 02:03:13 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:03:13 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:03:13 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:03:13 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:03:13 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:03:14 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:03:14 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 02:03:14 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 02:03:14 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 02:03:14 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:2, 组:2
2025-06-29 02:03:14 | INFO     | logger.info:97 | 主窗口初始化完成
2025-06-29 02:03:56 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:03:56 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:03:56 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:03:56 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:03:56 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:15:17 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:15:17 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:15:17 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:15:17 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:15:17 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:15:18 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:15:18 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'view_mode_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 481, in connect_signals
    self.view_mode_btn.clicked.connect(self.toggle_view_mode)
    ^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'view_mode_btn'. Did you mean: 'view_toggle_btn'?
2025-06-29 02:25:08 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:25:08 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:25:08 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:25:08 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:25:08 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:25:13 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:25:13 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:25:13 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:25:13 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:25:13 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:25:15 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:25:15 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'view_mode_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 481, in connect_signals
    self.view_mode_btn.clicked.connect(self.toggle_view_mode)
    ^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'view_mode_btn'. Did you mean: 'view_toggle_btn'?
2025-06-29 02:26:37 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:26:37 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:26:37 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:26:37 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:26:37 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:26:39 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:26:39 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'export_compare_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 482, in connect_signals
    self.export_compare_btn.clicked.connect(self.export_compare_data)
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'export_compare_btn'. Did you mean: 'export_compare_data'?
2025-06-29 02:36:19 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:36:19 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:36:19 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:36:19 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:36:19 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:36:21 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:36:21 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'export_compare_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 482, in connect_signals
    self.export_compare_btn.clicked.connect(self.export_compare_data)
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'export_compare_btn'. Did you mean: 'export_compare_data'?
2025-06-29 02:39:40 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:39:40 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:39:40 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:39:40 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:39:40 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:39:41 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:39:42 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 02:40:35 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:40:35 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:40:35 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:40:35 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:40:35 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:40:35 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:40:35 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 02:45:36 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:45:36 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:45:36 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:45:36 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:45:36 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:45:37 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:45:37 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 02:46:09 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:46:09 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:46:09 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:46:10 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:46:10 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 02:46:11 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 02:46:11 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 02:46:18 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 02:46:18 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 02:46:18 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 02:46:18 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 02:46:18 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 23:29:27 | INFO     | logger._initialize_logger:80 | ==================================================
2025-06-29 23:29:27 | INFO     | logger._initialize_logger:81 | 商品对比工具启动
2025-06-29 23:29:27 | INFO     | logger._initialize_logger:82 | Python版本: 3.13.0 | packaged by conda-forge | (main, Nov 27 2024, 19:03:49) [MSC v.1942 64 bit (AMD64)]
2025-06-29 23:29:27 | INFO     | logger._initialize_logger:83 | 工作目录: C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具
2025-06-29 23:29:27 | INFO     | logger._initialize_logger:84 | ==================================================
2025-06-29 23:29:29 | INFO     | logger.info:97 | 开始初始化主窗口
2025-06-29 23:29:30 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
2025-06-29 23:29:31 | INFO     | logger.info:97 | 已加载专业样式表
2025-06-29 23:29:31 | DEBUG    | logger.debug:93 | 已应用专业按钮样式
2025-06-29 23:29:31 | DEBUG    | logger.debug:93 | 调用函数: update_status()
2025-06-29 23:29:31 | DEBUG    | logger.debug:93 | 状态更新完成 - 商品:5, 组:2
2025-06-29 23:29:31 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
2025-06-29 23:29:32 | INFO     | logger.info:97 | 主窗口初始化完成
