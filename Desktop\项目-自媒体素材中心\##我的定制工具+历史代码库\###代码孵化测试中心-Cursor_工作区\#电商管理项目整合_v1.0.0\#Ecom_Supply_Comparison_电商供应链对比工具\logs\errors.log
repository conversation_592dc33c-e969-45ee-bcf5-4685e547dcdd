2025-06-28 23:43:55 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 906, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-28 23:44:19 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 906, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-28 23:44:19 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 906, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:05:19 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:05:25 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:05:25 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:17 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:22 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:27 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:39 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:21:39 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:38:16 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:38:22 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 00:38:22 | ERROR    | logger.exception:113 | 错误: cannot access local variable 'products' where it is not associated with a value | 上下文: 刷新商品卡片时出错
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1099, in refresh_product_cards
    logger.debug(f"获取到 {len(products)} 个商品")
                               ^^^^^^^^
UnboundLocalError: cannot access local variable 'products' where it is not associated with a value
2025-06-29 01:55:52 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'edit_group_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 1119, in connect_signals
    self.edit_group_btn.clicked.connect(self.edit_group)
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'edit_group_btn'. Did you mean: 'add_group_btn'?
2025-06-29 02:15:18 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'view_mode_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 481, in connect_signals
    self.view_mode_btn.clicked.connect(self.toggle_view_mode)
    ^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'view_mode_btn'. Did you mean: 'view_toggle_btn'?
2025-06-29 02:25:15 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'view_mode_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 481, in connect_signals
    self.view_mode_btn.clicked.connect(self.toggle_view_mode)
    ^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'view_mode_btn'. Did you mean: 'view_toggle_btn'?
2025-06-29 02:26:39 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'export_compare_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 482, in connect_signals
    self.export_compare_btn.clicked.connect(self.export_compare_data)
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'export_compare_btn'. Did you mean: 'export_compare_data'?
2025-06-29 02:36:21 | ERROR    | logger.exception:113 | 错误: 'MainWindow' object has no attribute 'export_compare_btn' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 482, in connect_signals
    self.export_compare_btn.clicked.connect(self.export_compare_data)
    ^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'export_compare_btn'. Did you mean: 'export_compare_data'?
2025-06-29 02:39:42 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 02:40:35 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 02:45:37 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 02:46:11 | ERROR    | logger.exception:113 | 错误: MainWindow.load_products() missing 1 required positional argument: 'group_id' | 上下文: 主窗口初始化失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 71, in __init__
    self.init_ui()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 115, in init_ui
    self.connect_signals()
    ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 486, in connect_signals
    self.load_products()
    ~~~~~~~~~~~~~~~~~~^^
TypeError: MainWindow.load_products() missing 1 required positional argument: 'group_id'
2025-06-29 23:29:30 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
2025-06-29 23:29:31 | ERROR    | logger.exception:113 | 错误: 'DBManager' object has no attribute 'get_all_products' | 上下文: 加载商品失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\#Ecom_Supply_Comparison_电商供应链对比工具\compare_tool\ui\main_window.py", line 867, in load_all_products
    products = self.db_manager.get_all_products()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBManager' object has no attribute 'get_all_products'. Did you mean: 'get_all_groups'?
