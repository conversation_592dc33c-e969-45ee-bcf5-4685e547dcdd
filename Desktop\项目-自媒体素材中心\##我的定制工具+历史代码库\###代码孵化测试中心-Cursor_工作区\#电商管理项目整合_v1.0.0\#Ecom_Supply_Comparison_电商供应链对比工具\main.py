#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目根启动脚本
在项目根目录直接执行 `python main.py` 时，会自动定位并调用
`compare_tool/main.py` 中的 `main()` 函数。

这样用户双击本文件或在终端运行 `python main.py` 即可启动 GUI，
避免出现找不到入口文件导致的闪退问题。
"""

from __future__ import annotations

import runpy
import sys
from pathlib import Path

# ------------------------------
# 将 compare_tool 目录加入模块搜索路径
# ------------------------------
ROOT_DIR: Path = Path(__file__).resolve().parent
COMPARE_DIR: Path = ROOT_DIR / "compare_tool"
if str(COMPARE_DIR) not in sys.path:
    sys.path.insert(0, str(COMPARE_DIR))


def main() -> None:
    """运行 compare_tool.main 中的 main()。"""

    # 使用 runpy 执行文件，等价于 `python compare_tool/main.py`
    runpy.run_path(COMPARE_DIR / "main.py", run_name="__main__")


if __name__ == "__main__":
    main()
