#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 绕过可能的问题模块
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
compare_tool_path = os.path.join(project_root, 'compare_tool')
if compare_tool_path not in sys.path:
    sys.path.insert(0, compare_tool_path)

try:
    print("正在启动商品对比工具...")
    
    # 导入PyQt6
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    
    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName("商品对比工具")
    
    # 导入并创建主窗口
    from ui.main_window import MainWindow
    
    print("创建主窗口...")
    window = MainWindow()
    
    print("显示窗口...")
    window.show()
    
    print("启动成功！")
    
    # 运行应用
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装所需依赖: pip install -r requirements.txt")
    input("按回车键退出...")
except Exception as e:
    print(f"启动失败: {e}")
    import traceback
    print(f"详细错误: {traceback.format_exc()}")
    input("按回车键退出...")