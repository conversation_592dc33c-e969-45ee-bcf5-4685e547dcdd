#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'compare_tool'))

from PyQt6.QtWidgets import QApplication, QMessageBox
from ui.add_product_dialog import show_add_product_dialog
from db.db_manager import DBManager

def test_add_product_dialog():
    """测试添加商品对话框"""
    app = QApplication(sys.argv)
    
    try:
        # 初始化数据库
        db_manager = DBManager("compare_tool/compare_tool.db")
        
        # 获取第一个对比组ID
        groups = db_manager.get_all_groups()
        if not groups:
            # 创建一个测试对比组
            group_id = db_manager.add_group("测试对比组", "用于测试的对比组")
            print(f"创建了测试对比组，ID: {group_id}")
        else:
            group_id = groups[0]['id']
            print(f"使用现有对比组，ID: {group_id}, 名称: {groups[0]['name']}")
        
        # 测试打开添加商品对话框
        print("正在打开添加商品对话框...")
        result = show_add_product_dialog(group_id)
        
        if result:
            print("添加商品成功:")
            print(f"名称: {result.name}")
            print(f"描述: {result.description}")
            print(f"来源数量: {len(result.sources)}")
            for i, source in enumerate(result.sources):
                print(f"  来源 {i+1}: {source.source_name} - ¥{source.price}")
        else:
            print("用户取消了操作")
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 不需要 app.exec()，因为对话框是模态的

if __name__ == "__main__":
    test_add_product_dialog()