#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的主窗口是否能正常初始化
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'compare_tool'))

try:
    from PyQt6.QtWidgets import QApplication
    from ui.main_window import MainWindow
    
    print("正在测试主窗口初始化...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 尝试创建主窗口
    main_window = MainWindow()
    
    print("✅ 主窗口初始化成功！")
    print("✅ AttributeError 错误已修复")
    
    # 不显示窗口，直接退出
    app.quit()
    
except AttributeError as e:
    print(f"❌ AttributeError 仍然存在: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ 其他错误: {e}")
    sys.exit(1)

print("🎉 测试完成，程序修复成功！")