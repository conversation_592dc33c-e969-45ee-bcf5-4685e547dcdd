PyQt6 商品对比工具 - 完整开发文档
🧩 一、项目定位与目标
1.1 项目目标
打造一款基于 PyQt6、支持暗黑主题的可视化商品对比工具，主要功能包括：

添加多个商品对比组

对比组中可添加多个来源（淘宝、拼多多、1688等）

每个商品支持图文信息展示、价格、库存、链接等字段

本地数据库管理商品信息，支持持久化和更新

支持商品图像查看与保存

🧪 二、技术栈与依赖
技术	用途
Python 3.10+	主体语言
PyQt6	GUI 框架
QDarkStyle 或自定义 QSS	暗黑主题支持
SQLite3	内嵌数据库
Pillow	图片处理与显示支持
requests + BeautifulSoup4（可选）	网页抓取（如后期自动采集）

🧱 三、系统架构概览
bash
复制
编辑
商品对比工具
├── 数据库模块（sqlite）
│   ├── 商品表（product）
│   ├── 对比组表（group）
│   └── 来源表（source）
├── UI模块（PyQt6 + QDarkStyle）
│   ├── 主窗口（带侧栏）
│   ├── 商品添加对话框
│   └── 图片预览面板
├── 数据管理模块
│   ├── 读取/写入数据库
│   └── 图片存储处理
└── 对比逻辑模块
    └── 多商品横向对比显示
🗃️ 四、数据库设计（SQLite）
sql
复制
编辑
-- 对比组表
CREATE TABLE compare_group (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL
);

-- 商品表
CREATE TABLE product (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER,
    name TEXT,
    image_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(group_id) REFERENCES compare_group(id)
);

-- 来源信息（可有多个）
CREATE TABLE product_source (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER,
    source_name TEXT,           -- 淘宝、拼多多、1688等
    price REAL,
    shipping REAL,
    stock INTEGER,
    url TEXT,
    note TEXT,                  -- 可备注商家名、规格等
    FOREIGN KEY(product_id) REFERENCES product(id)
);
🖼️ 五、UI设计结构
5.1 主界面组成（QMainWindow）
左侧：对比组列表（QListWidget）

右侧：对比商品表格视图（QTableView / QTableWidget）

顶部按钮栏：

添加对比组

添加商品

导出对比

底部状态栏：提示信息、保存状态

5.2 添加商品弹窗
输入字段：

商品名称

所属对比组（下拉）

多个来源添加区域（动态添加行）

来源名称

链接

价格

运费

库存

上传图片（或粘贴URL）

备注字段

🎯 六、核心功能点详解
6.1 多商品多来源对比视图
以“商品为单位”横向排列

每个商品行下可以展开显示其来源项

每个来源用卡片或表格显示字段

支持对比价格差、库存等（颜色高亮）

6.2 暗黑主题支持
使用 QDarkStyle 或自定义 QSS：

python
复制
编辑
from qdarkstyle import load_stylesheet
app.setStyleSheet(load_stylesheet())
或者使用 .qss 自定义暗黑样式文件加载。

🧮 七、数据交互模块
数据增删改查 (CRUD)
封装数据操作类 DBManager：

python
复制
编辑
class DBManager:
    def add_group(name): ...
    def add_product(group_id, name, image_path): ...
    def add_source(product_id, info_dict): ...
    def get_products_by_group(group_id): ...
    def get_sources_by_product(product_id): ...
📷 八、图片处理建议
图片可以存储为文件，数据库记录路径

也可以存为 base64（二进制 BLOB）

显示用 QLabel + QPixmap 缩放加载

🚀 九、后期扩展方向
自动从淘宝、1688 抓取信息（需防止封禁）

支持批量导入 Excel 表格商品

价格走势记录（添加历史价格表）

对比报告导出（PDF / Excel）

登录系统，多用户分组使用

📁 十、项目目录结构建议
css
复制
编辑
compare_tool/
├── main.py
├── db/
│   └── db_manager.py
├── ui/
│   ├── main_window.py
│   ├── add_product_dialog.py
│   └── resources.qrc
├── assets/
│   └── images/
├── style/
│   └── dark.qss
├── models/
│   └── product.py
└── utils/
    └── image_utils.py
✅ 十一、关键实现建议
UI设计使用Qt Designer 初始布局（.ui 转 .py）

对比区采用 QTableWidget + 自定义 cell widget

图片上传用 QFileDialog 或 QClipboard

所有操作封装为类，使用 MVC 思路管理