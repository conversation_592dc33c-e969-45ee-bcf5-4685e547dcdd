-- =====================================================
-- 电商管理系统整合版 v2.0.0 - 统一数据库设计
-- =====================================================
-- 整合三个项目的数据结构，创建统一的数据库架构
-- 基于SQLite，支持完整的电商管理业务流程

-- =====================================================
-- 1. 商品管理相关表 (基于Inventory_Management_v1.7.3)
-- =====================================================

-- 商品基础信息表
CREATE TABLE IF NOT EXISTS products (
    product_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT UNIQUE,
    category TEXT,
    description TEXT,
    price DECIMAL(10,2) DEFAULT 0,
    cost_price DECIMAL(10,2) DEFAULT 0,
    quantity INTEGER DEFAULT 0,
    unit TEXT DEFAULT '个',
    status TEXT DEFAULT '在库' CHECK (status IN ('在库', '缺货', '停售', '预售')),
    image_path TEXT,
    images TEXT, -- JSON格式存储多张图片
    brand TEXT,
    supplier_info TEXT, -- JSON格式存储供应商信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 批次管理表
CREATE TABLE IF NOT EXISTS batches (
    batch_id TEXT PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    name TEXT,
    description TEXT,
    status TEXT DEFAULT '活跃' CHECK (status IN ('活跃', '已完成', '已取消')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 批次商品关联表
CREATE TABLE IF NOT EXISTS batch_products (
    id TEXT PRIMARY KEY,
    batch_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 0,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_value DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (batch_id) REFERENCES batches (batch_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (product_id) ON DELETE CASCADE,
    UNIQUE(batch_id, product_id)
);

-- 交易记录表
CREATE TABLE IF NOT EXISTS transactions (
    transaction_id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    batch_id TEXT,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('入库', '出库', '调整', '盘点')),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    reference_id TEXT, -- 关联订单ID或其他业务ID
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products (product_id) ON DELETE CASCADE,
    FOREIGN KEY (batch_id) REFERENCES batches (batch_id) ON DELETE SET NULL
);

-- =====================================================
-- 2. 平台和店铺管理表 (基于Agent_Order_Management)
-- =====================================================

-- 电商平台表
CREATE TABLE IF NOT EXISTS platforms (
    platform_id TEXT PRIMARY KEY,
    platform_name TEXT NOT NULL UNIQUE,
    platform_code TEXT NOT NULL UNIQUE, -- 如: 1688, taobao, pdd
    api_endpoint TEXT,
    auth_type TEXT DEFAULT 'oauth2' CHECK (auth_type IN ('oauth2', 'api_key', 'basic')),
    auth_config TEXT, -- JSON格式存储认证配置
    rate_limit INTEGER DEFAULT 1000, -- API调用频率限制
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 店铺信息表
CREATE TABLE IF NOT EXISTS stores (
    store_id TEXT PRIMARY KEY,
    platform_id TEXT NOT NULL,
    store_name TEXT NOT NULL,
    store_code TEXT,
    store_url TEXT,
    credentials TEXT, -- JSON格式存储店铺凭证
    contact_info TEXT, -- JSON格式存储联系信息
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    last_sync TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (platform_id) REFERENCES platforms (platform_id) ON DELETE CASCADE,
    UNIQUE(platform_id, store_code)
);

-- =====================================================
-- 3. 供应商和代发管理表
-- =====================================================

-- 供应商信息表
CREATE TABLE IF NOT EXISTS suppliers (
    supplier_id TEXT PRIMARY KEY,
    supplier_name TEXT NOT NULL,
    platform_id TEXT,
    supplier_code TEXT, -- 平台上的供应商编码
    contact_person TEXT,
    contact_phone TEXT,
    contact_email TEXT,
    address TEXT,
    api_credentials TEXT, -- JSON格式存储API凭证
    credit_rating INTEGER DEFAULT 5 CHECK (credit_rating BETWEEN 1 AND 10),
    cooperation_status TEXT DEFAULT 'active' CHECK (cooperation_status IN ('active', 'inactive', 'blacklist')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (platform_id) REFERENCES platforms (platform_id) ON DELETE SET NULL
);

-- 供应商商品表
CREATE TABLE IF NOT EXISTS supplier_products (
    id TEXT PRIMARY KEY,
    supplier_id TEXT NOT NULL,
    supplier_product_id TEXT NOT NULL, -- 供应商平台的商品ID
    product_name TEXT NOT NULL,
    product_code TEXT,
    category TEXT,
    supplier_price DECIMAL(10,2) NOT NULL,
    min_order_quantity INTEGER DEFAULT 1,
    available_stock INTEGER DEFAULT 0,
    product_url TEXT,
    image_url TEXT,
    description TEXT,
    shipping_info TEXT, -- JSON格式存储物流信息
    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'available' CHECK (status IN ('available', 'unavailable', 'discontinued')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers (supplier_id) ON DELETE CASCADE,
    UNIQUE(supplier_id, supplier_product_id)
);

-- 订单信息表
CREATE TABLE IF NOT EXISTS orders (
    order_id TEXT PRIMARY KEY,
    store_id TEXT,
    platform_order_id TEXT, -- 平台订单号
    customer_name TEXT NOT NULL,
    customer_phone TEXT,
    customer_email TEXT,
    shipping_address TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    shipping_fee DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    final_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount + shipping_fee - discount_amount) STORED,
    currency TEXT DEFAULT 'CNY',
    order_status TEXT DEFAULT 'pending' CHECK (order_status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    payment_status TEXT DEFAULT 'unpaid' CHECK (payment_status IN ('unpaid', 'paid', 'partial', 'refunded')),
    shipping_status TEXT DEFAULT 'unshipped' CHECK (shipping_status IN ('unshipped', 'processing', 'shipped', 'delivered')),
    order_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    payment_time TIMESTAMP,
    shipping_time TIMESTAMP,
    delivery_time TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (store_id) REFERENCES stores (store_id) ON DELETE SET NULL
);

-- 订单商品明细表
CREATE TABLE IF NOT EXISTS order_items (
    id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    product_id TEXT,
    product_name TEXT NOT NULL,
    product_code TEXT,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    product_image TEXT,
    specifications TEXT, -- JSON格式存储商品规格
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders (order_id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products (product_id) ON DELETE SET NULL
);

-- 代发订单表
CREATE TABLE IF NOT EXISTS dropship_orders (
    dropship_id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    supplier_id TEXT NOT NULL,
    supplier_order_id TEXT, -- 供应商平台的订单号
    supplier_product_id TEXT,
    quantity INTEGER NOT NULL DEFAULT 1,
    cost_amount DECIMAL(10,2) NOT NULL, -- 成本金额
    selling_amount DECIMAL(10,2) NOT NULL, -- 销售金额
    profit_amount DECIMAL(10,2) GENERATED ALWAYS AS (selling_amount - cost_amount) STORED,
    profit_rate DECIMAL(5,2) GENERATED ALWAYS AS (CASE WHEN cost_amount > 0 THEN (profit_amount / cost_amount * 100) ELSE 0 END) STORED,
    tracking_number TEXT,
    shipping_company TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders (order_id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES suppliers (supplier_id) ON DELETE CASCADE
);

-- =====================================================
-- 4. 供应链对比分析表 (基于Ecom_Supply_Comparison)
-- =====================================================

-- 对比组表
CREATE TABLE IF NOT EXISTS comparison_groups (
    group_id TEXT PRIMARY KEY,
    group_name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'archived')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 对比项目表
CREATE TABLE IF NOT EXISTS comparison_items (
    item_id TEXT PRIMARY KEY,
    group_id TEXT NOT NULL,
    product_name TEXT NOT NULL,
    source_platform TEXT NOT NULL, -- 来源平台
    source_label TEXT, -- 来源标签 (如: 1688, 我的店1, 我的店2)
    source_url TEXT,
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    stock INTEGER DEFAULT 0,
    min_order_quantity INTEGER DEFAULT 1,
    shipping_fee DECIMAL(10,2) DEFAULT 0,
    free_shipping_threshold DECIMAL(10,2), -- 包邮门槛
    image_url TEXT,
    specifications TEXT, -- JSON格式存储规格信息
    supplier_info TEXT, -- JSON格式存储供应商信息
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'out_of_stock')),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES comparison_groups (group_id) ON DELETE CASCADE
);

-- =====================================================
-- 5. 系统管理和日志表
-- =====================================================

-- 数据同步日志表
CREATE TABLE IF NOT EXISTS sync_logs (
    log_id TEXT PRIMARY KEY,
    sync_type TEXT NOT NULL CHECK (sync_type IN ('product', 'order', 'inventory', 'supplier', 'full')),
    platform_id TEXT,
    store_id TEXT,
    status TEXT NOT NULL CHECK (status IN ('started', 'success', 'failed', 'partial')),
    records_processed INTEGER DEFAULT 0,
    records_success INTEGER DEFAULT 0,
    records_failed INTEGER DEFAULT 0,
    error_message TEXT,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    duration INTEGER GENERATED ALWAYS AS (
        CASE WHEN end_time IS NOT NULL
        THEN (julianday(end_time) - julianday(start_time)) * 86400
        ELSE NULL END
    ) STORED,
    FOREIGN KEY (platform_id) REFERENCES platforms (platform_id) ON DELETE SET NULL,
    FOREIGN KEY (store_id) REFERENCES stores (store_id) ON DELETE SET NULL
);

-- 系统设置表
CREATE TABLE IF NOT EXISTS system_settings (
    setting_key TEXT PRIMARY KEY,
    setting_value TEXT,
    setting_type TEXT DEFAULT 'string' CHECK (setting_type IN ('string', 'integer', 'float', 'boolean', 'json')),
    description TEXT,
    category TEXT DEFAULT 'general',
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户偏好设置表
CREATE TABLE IF NOT EXISTS user_preferences (
    preference_id TEXT PRIMARY KEY,
    user_id TEXT DEFAULT 'default_user',
    preference_key TEXT NOT NULL,
    preference_value TEXT,
    preference_type TEXT DEFAULT 'string' CHECK (preference_type IN ('string', 'integer', 'float', 'boolean', 'json')),
    category TEXT DEFAULT 'ui',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, preference_key)
);

-- API调用日志表
CREATE TABLE IF NOT EXISTS api_logs (
    log_id TEXT PRIMARY KEY,
    platform_id TEXT,
    api_endpoint TEXT NOT NULL,
    http_method TEXT DEFAULT 'GET',
    request_data TEXT, -- JSON格式存储请求数据
    response_code INTEGER,
    response_data TEXT, -- JSON格式存储响应数据
    response_time INTEGER, -- 响应时间(毫秒)
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (platform_id) REFERENCES platforms (platform_id) ON DELETE SET NULL
);

-- =====================================================
-- 6. 索引创建 (优化查询性能)
-- =====================================================

-- 商品相关索引
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);

-- 批次相关索引
CREATE INDEX IF NOT EXISTS idx_batch_products_batch_id ON batch_products(batch_id);
CREATE INDEX IF NOT EXISTS idx_batch_products_product_id ON batch_products(product_id);

-- 交易记录索引
CREATE INDEX IF NOT EXISTS idx_transactions_product_id ON transactions(product_id);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- 订单相关索引
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_store_id ON orders(store_id);
CREATE INDEX IF NOT EXISTS idx_orders_order_time ON orders(order_time);
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- 代发订单索引
CREATE INDEX IF NOT EXISTS idx_dropship_orders_order_id ON dropship_orders(order_id);
CREATE INDEX IF NOT EXISTS idx_dropship_orders_supplier_id ON dropship_orders(supplier_id);
CREATE INDEX IF NOT EXISTS idx_dropship_orders_status ON dropship_orders(status);

-- 供应商相关索引
CREATE INDEX IF NOT EXISTS idx_suppliers_platform_id ON suppliers(platform_id);
CREATE INDEX IF NOT EXISTS idx_supplier_products_supplier_id ON supplier_products(supplier_id);
CREATE INDEX IF NOT EXISTS idx_supplier_products_last_sync ON supplier_products(last_sync);

-- 对比分析索引
CREATE INDEX IF NOT EXISTS idx_comparison_items_group_id ON comparison_items(group_id);
CREATE INDEX IF NOT EXISTS idx_comparison_items_platform ON comparison_items(source_platform);
CREATE INDEX IF NOT EXISTS idx_comparison_items_price ON comparison_items(price);

-- 系统日志索引
CREATE INDEX IF NOT EXISTS idx_sync_logs_platform_id ON sync_logs(platform_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_start_time ON sync_logs(start_time);
CREATE INDEX IF NOT EXISTS idx_api_logs_platform_id ON api_logs(platform_id);
CREATE INDEX IF NOT EXISTS idx_api_logs_created_at ON api_logs(created_at);

-- =====================================================
-- 7. 触发器 (自动更新时间戳)
-- =====================================================

-- 商品表更新时间触发器
CREATE TRIGGER IF NOT EXISTS update_products_timestamp
    AFTER UPDATE ON products
    FOR EACH ROW
    BEGIN
        UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE product_id = NEW.product_id;
    END;

-- 批次表更新时间触发器
CREATE TRIGGER IF NOT EXISTS update_batches_timestamp
    AFTER UPDATE ON batches
    FOR EACH ROW
    BEGIN
        UPDATE batches SET updated_at = CURRENT_TIMESTAMP WHERE batch_id = NEW.batch_id;
    END;

-- 订单表更新时间触发器
CREATE TRIGGER IF NOT EXISTS update_orders_timestamp
    AFTER UPDATE ON orders
    FOR EACH ROW
    BEGIN
        UPDATE orders SET updated_at = CURRENT_TIMESTAMP WHERE order_id = NEW.order_id;
    END;

-- 代发订单表更新时间触发器
CREATE TRIGGER IF NOT EXISTS update_dropship_orders_timestamp
    AFTER UPDATE ON dropship_orders
    FOR EACH ROW
    BEGIN
        UPDATE dropship_orders SET updated_at = CURRENT_TIMESTAMP WHERE dropship_id = NEW.dropship_id;
    END;

-- =====================================================
-- 8. 初始化数据
-- =====================================================

-- 插入默认平台数据
INSERT OR IGNORE INTO platforms (platform_id, platform_name, platform_code, api_endpoint, status) VALUES
('1688', '1688批发网', '1688', 'https://gw.open.1688.com/openapi', 'active'),
('taobao', '淘宝网', 'taobao', 'https://eco.taobao.com/router/rest', 'active'),
('tmall', '天猫', 'tmall', 'https://eco.taobao.com/router/rest', 'active'),
('pdd', '拼多多', 'pdd', 'https://open-api.pinduoduo.com', 'active'),
('jd', '京东', 'jd', 'https://api.jd.com/routerjson', 'inactive'),
('douyin', '抖音电商', 'douyin', 'https://openapi-fxg.jinritemai.com', 'inactive');

-- 插入默认系统设置
INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, category) VALUES
('app_version', '2.0.0', 'string', '应用程序版本', 'system'),
('database_version', '1.0', 'string', '数据库版本', 'system'),
('auto_sync_enabled', 'true', 'boolean', '是否启用自动同步', 'sync'),
('sync_interval_minutes', '30', 'integer', '同步间隔(分钟)', 'sync'),
('max_api_calls_per_hour', '1000', 'integer', '每小时最大API调用次数', 'api'),
('default_currency', 'CNY', 'string', '默认货币', 'general'),
('image_storage_path', './images', 'string', '图片存储路径', 'storage'),
('backup_enabled', 'true', 'boolean', '是否启用自动备份', 'backup'),
('backup_interval_hours', '24', 'integer', '备份间隔(小时)', 'backup'),
('log_retention_days', '30', 'integer', '日志保留天数', 'logging');

-- 插入默认用户偏好
INSERT OR IGNORE INTO user_preferences (preference_id, preference_key, preference_value, preference_type, category) VALUES
('ui_theme', 'theme', 'dark', 'string', 'ui'),
('ui_language', 'language', 'zh_CN', 'string', 'ui'),
('table_page_size', 'page_size', '50', 'integer', 'ui'),
('auto_refresh_enabled', 'auto_refresh', 'true', 'boolean', 'ui'),
('notification_enabled', 'notifications', 'true', 'boolean', 'ui');

-- =====================================================
-- 数据库设计完成
-- =====================================================
