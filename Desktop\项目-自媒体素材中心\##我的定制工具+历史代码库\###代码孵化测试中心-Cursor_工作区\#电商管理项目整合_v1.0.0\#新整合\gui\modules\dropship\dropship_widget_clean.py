#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代发管理模块主界面

代发管理功能的完整实现。
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QSplitter,
    QGroupBox,
    QTableWidget,
    QTableWidgetItem,
    QPushButton,
    QLineEdit,
    QComboBox,
    QLabel,
    QMessageBox,
    QHeaderView,
    QDateEdit,
    QToolBar,
    QFrame,
    QTextEdit,
    QProgressBar,
    QGridLayout,
    QScrollArea,
    QSpinBox,
    QDoubleSpinBox,
    QCheckBox,
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QDate
from PyQt6.QtGui import QFont, QPalette, QColor

from utils.logger import LoggerMixin


class DropshipWidget(QWidget, LoggerMixin):
    """代发管理主界面"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager

        # 当前选中的供应商
        self.current_supplier = None

        # 示例数据
        self.suppliers_data = []
        self.orders_data = []

        # 初始化界面
        self.init_ui()

        # 加载示例数据
        self.load_sample_data()

        self.logger.info("代发管理界面初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(8, 8, 8, 8)

        # 标题区域
        title_frame = self.create_title_section()
        layout.addWidget(title_frame)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Vertical)

        # 上半部分：统计卡片
        stats_frame = self.create_stats_section()
        main_splitter.addWidget(stats_frame)

        # 下半部分：订单管理
        order_frame = self.create_order_section()
        main_splitter.addWidget(order_frame)

        # 设置分割器比例
        main_splitter.setSizes([200, 500])
        layout.addWidget(main_splitter)

    def create_title_section(self):
        """创建标题区域"""
        frame = QFrame()
        frame.setFixedHeight(60)
        frame.setStyleSheet(
            """
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #FF9800, stop:1 #F57C00);
                border-radius: 8px;
                margin-bottom: 8px;
            }
        """
        )

        layout = QHBoxLayout(frame)
        layout.setContentsMargins(20, 10, 20, 10)

        # 标题
        title = QLabel("🚚 代发管理系统")
        title.setStyleSheet(
            """
            font-size: 20px;
            font-weight: bold;
            color: white;
            background: transparent;
        """
        )
        layout.addWidget(title)

        layout.addStretch()

        # 操作按钮
        add_btn = QPushButton("新建订单")
        add_btn.setStyleSheet(
            """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """
        )
        add_btn.clicked.connect(self.add_order)
        layout.addWidget(add_btn)

        return frame

    def create_stats_section(self):
        """创建统计区域"""
        frame = QFrame()
        frame.setStyleSheet(
            """
            QFrame {
                background-color: transparent;
                border: none;
            }
        """
        )

        layout = QGridLayout(frame)
        layout.setSpacing(12)
        layout.setContentsMargins(5, 5, 5, 5)

        # 统计卡片数据
        stats_data = [
            ("📦", "待处理订单", "12", "#FF9800", "需要处理"),
            ("🚚", "运输中", "8", "#2196F3", "物流跟踪"),
            ("✅", "已完成", "156", "#4CAF50", "本月完成"),
            ("💰", "总利润", "¥12,340", "#4CAF50", "本月收益"),
        ]

        for i, (icon, title, value, color, trend) in enumerate(stats_data):
            card = self.create_stat_card(icon, title, value, color, trend)
            row = i // 4
            col = i % 4
            layout.addWidget(card, row, col)

        return frame

    def create_stat_card(self, icon, title, value, color, trend):
        """创建统计卡片"""
        card = QFrame()
        card.setFixedSize(200, 80)
        card.setStyleSheet(
            f"""
            QFrame {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d2d2d, stop:1 #1a1a1a);
                border-left: 4px solid {color};
                border-radius: 8px;
                padding: 8px;
            }}
            QFrame:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3d3d3d, stop:1 #2a2a2a);
            }}
        """
        )

        layout = QVBoxLayout(card)
        layout.setContentsMargins(12, 6, 12, 6)
        layout.setSpacing(2)

        # 顶部：图标和标题
        top_layout = QHBoxLayout()

        icon_label = QLabel(icon)
        icon_label.setStyleSheet(
            f"font-size: 18px; color: {color}; background: transparent;"
        )
        top_layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        title_label.setStyleSheet(
            "font-size: 9px; color: #aaaaaa; background: transparent;"
        )
        top_layout.addWidget(title_label)

        layout.addLayout(top_layout)

        # 中间：数值
        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        value_label.setStyleSheet(
            f"font-size: 16px; font-weight: bold; color: {color}; background: transparent; margin: 2px 0px;"
        )
        layout.addWidget(value_label)

        # 底部：趋势
        trend_label = QLabel(trend)
        trend_label.setStyleSheet(
            "font-size: 8px; color: #888888; background: transparent;"
        )
        layout.addWidget(trend_label)

        return card

    def create_order_section(self):
        """创建订单管理区域"""
        frame = QGroupBox("📋 代发订单管理")
        frame.setStyleSheet(
            """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #2b2b2b;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #FF9800;
            }
        """
        )

        layout = QVBoxLayout(frame)

        # 搜索栏
        search_layout = QHBoxLayout()

        search_label = QLabel("搜索:")
        search_label.setStyleSheet("color: white; font-weight: bold;")
        search_layout.addWidget(search_label)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入订单号、商品名称或供应商...")
        self.search_edit.setStyleSheet(
            """
            QLineEdit {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                padding: 6px;
            }
        """
        )
        search_layout.addWidget(self.search_edit)

        search_btn = QPushButton("搜索")
        search_btn.setStyleSheet(
            """
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """
        )
        search_btn.clicked.connect(self.search_orders)
        search_layout.addWidget(search_btn)

        layout.addLayout(search_layout)

        # 订单表格
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(8)
        self.orders_table.setHorizontalHeaderLabels(
            ["订单号", "商品名称", "供应商", "数量", "成本", "售价", "利润", "状态"]
        )

        # 设置表格样式
        self.orders_table.setStyleSheet(
            """
            QTableWidget {
                background-color: #1e1e1e;
                border: 1px solid #555;
                border-radius: 4px;
                color: white;
                gridline-color: #555;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #333;
            }
            QTableWidget::item:selected {
                background-color: #FF9800;
            }
            QHeaderView::section {
                background-color: #2b2b2b;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
            }
        """
        )

        # 设置表格属性
        self.orders_table.setAlternatingRowColors(True)
        self.orders_table.setSelectionBehavior(
            QTableWidget.SelectionBehavior.SelectRows
        )
        header = self.orders_table.horizontalHeader()
        header.setStretchLastSection(True)

        layout.addWidget(self.orders_table)

        return frame

    def load_sample_data(self):
        """加载示例数据"""
        # 示例订单数据
        sample_orders = [
            [
                "ORD001",
                "iPhone 14 手机壳",
                "阿里巴巴供应商A",
                "50",
                "¥15.00",
                "¥25.00",
                "¥10.00",
                "待处理",
            ],
            [
                "ORD002",
                "蓝牙耳机",
                "1688供应商B",
                "30",
                "¥45.00",
                "¥89.00",
                "¥44.00",
                "运输中",
            ],
            [
                "ORD003",
                "数据线",
                "淘宝供应商C",
                "100",
                "¥8.00",
                "¥18.00",
                "¥10.00",
                "已完成",
            ],
            [
                "ORD004",
                "充电宝",
                "阿里巴巴供应商D",
                "25",
                "¥35.00",
                "¥68.00",
                "¥33.00",
                "待处理",
            ],
            [
                "ORD005",
                "手机支架",
                "1688供应商E",
                "80",
                "¥12.00",
                "¥22.00",
                "¥10.00",
                "运输中",
            ],
        ]

        self.orders_table.setRowCount(len(sample_orders))

        for row, order_data in enumerate(sample_orders):
            for col, data in enumerate(order_data):
                item = QTableWidgetItem(str(data))

                # 根据状态设置颜色
                if col == 7:  # 状态列
                    if data == "待处理":
                        item.setBackground(QColor("#FF9800"))
                    elif data == "运输中":
                        item.setBackground(QColor("#2196F3"))
                    elif data == "已完成":
                        item.setBackground(QColor("#4CAF50"))

                self.orders_table.setItem(row, col, item)

    def add_order(self):
        """添加新订单"""
        QMessageBox.information(self, "提示", "新建订单功能正在开发中...")

    def search_orders(self):
        """搜索订单"""
        search_text = self.search_edit.text().strip()
        if search_text:
            QMessageBox.information(
                self, "搜索", f"搜索关键词: {search_text}\n搜索功能正在开发中..."
            )
        else:
            QMessageBox.warning(self, "提示", "请输入搜索关键词")
