# =====================================================
# 电商管理系统整合版 v2.0.0 - 依赖清单
# =====================================================

# 核心GUI框架
PyQt6>=6.4.0
PyQt6-Qt6>=6.4.0

# 数据库相关
sqlite3  # Python内置，无需安装

# HTTP请求和API客户端
requests>=2.28.0
urllib3>=1.26.0

# 数据处理
pandas>=1.5.0
numpy>=1.21.0

# 图像处理
Pillow>=9.0.0
opencv-python>=4.6.0  # 可选，用于高级图像处理

# 二维码和条码处理
pyzbar>=0.1.9  # 条码识别
qrcode>=7.3.1  # 二维码生成

# 任务调度
APScheduler>=3.9.0

# 日志管理
loguru>=0.6.0

# 配置文件处理
PyYAML>=6.0
configparser  # Python内置

# 加密和安全
cryptography>=3.4.0
bcrypt>=3.2.0

# 网络和OAuth认证
oauthlib>=3.2.0
requests-oauthlib>=1.3.0

# 数据验证
pydantic>=1.10.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2022.1

# 文件处理
openpyxl>=3.0.0  # Excel文件处理
xlsxwriter>=3.0.0  # Excel写入
python-docx>=0.8.11  # Word文档处理

# 网页解析 (可选，用于数据抓取)
beautifulsoup4>=4.11.0
lxml>=4.9.0

# 数据可视化
matplotlib>=3.5.0
seaborn>=0.11.0

# 系统工具
psutil>=5.9.0  # 系统监控
pathlib  # Python内置

# 开发和测试工具
pytest>=7.0.0
pytest-qt>=4.0.0
pytest-cov>=3.0.0
black>=22.0.0  # 代码格式化
flake8>=4.0.0  # 代码检查
mypy>=0.950  # 类型检查

# 文档生成
sphinx>=4.5.0
sphinx-rtd-theme>=1.0.0

# 打包和分发
pyinstaller>=5.0.0
cx-Freeze>=6.11.0

# 其他工具库
tqdm>=4.64.0  # 进度条
click>=8.0.0  # 命令行工具
rich>=12.0.0  # 富文本终端输出

# 可选的高级功能
# selenium>=4.0.0  # 网页自动化 (如需要)
# scrapy>=2.6.0    # 网页爬虫 (如需要)
# redis>=4.3.0     # Redis缓存 (如需要)
# celery>=5.2.0    # 异步任务队列 (如需要)

# =====================================================
# 平台特定依赖
# =====================================================

# Windows特定
# pywin32>=304  # Windows API (仅Windows需要)

# macOS特定
# pyobjc>=8.5  # macOS API (仅macOS需要)

# Linux特定
# python3-dev  # 开发头文件 (仅Linux需要)

# =====================================================
# 版本兼容性说明
# =====================================================
# Python版本要求: >= 3.8
# 推荐Python版本: 3.10+
# 
# 主要依赖版本说明:
# - PyQt6: 现代化GUI框架，支持最新特性
# - requests: 稳定的HTTP客户端库
# - pandas: 强大的数据处理库
# - APScheduler: 灵活的任务调度器
# - loguru: 现代化的日志库
# - cryptography: 安全加密库
# 
# 可选依赖说明:
# - opencv-python: 高级图像处理功能
# - selenium: 网页自动化测试
# - scrapy: 专业网页爬虫框架
# - redis: 高性能缓存数据库
# - celery: 分布式任务队列
# 
# 开发依赖说明:
# - pytest: 单元测试框架
# - black: 代码自动格式化
# - flake8: 代码质量检查
# - mypy: 静态类型检查
# - sphinx: 文档生成工具
