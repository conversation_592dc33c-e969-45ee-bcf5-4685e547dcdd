# 📊 Cursor SVG流程图生成器规则

## 🎯 概述

这是一套专为Cursor IDE设计的MDC规则文件，能够自动分析项目结构并生成多角度的专业SVG流程图。基于库存管理系统项目的实践经验，提取了通用的流程图生成规则和样式规范。

## 📁 文件结构

```
.cursor/
├── cursor.json                           # Cursor配置文件
├── README.md                             # 使用说明文档
└── rules/                                # 规则文件目录
    ├── svg-flowchart-generator.mdc       # 主要生成规则
    ├── svg-templates.mdc                 # SVG模板定义
    ├── color-schemes.mdc                 # 颜色方案配置
    └── project-analysis-examples.mdc     # 项目分析示例
```

## 🚀 快速开始

### 1. 安装规则

将整个 `.cursor` 文件夹复制到您的新项目根目录：

```bash
# 复制规则文件到新项目
cp -r .cursor /path/to/your/new/project/
```

### 2. 在Cursor中使用

1. 打开Cursor IDE
2. 打开包含 `.cursor` 规则的项目
3. 在聊天窗口中输入以下任一触发词：
   - "生成SVG流程图"
   - "分析项目架构"
   - "创建系统架构图"
   - "生成项目文档图表"
   - "建立项目可视化文档"

### 3. 自动生成结果

系统将自动：
- 🔍 分析项目结构和技术栈
- 📁 创建 `svg/` 文件夹
- 📊 生成适合的流程图类型
- 📝 创建配套的README文档

## 📋 支持的项目类型

### Web应用项目
**识别标志**: `package.json` + `src/` + `public/`
**生成图表**:
- ✅ 系统架构图
- ✅ 用户交互流程图
- ✅ 部署架构图

### 桌面应用项目
**识别标志**: `main.py` + `gui/` + `requirements.txt`
**生成图表**:
- ✅ 系统架构图
- ✅ 用户交互流程图
- ✅ 业务流程图

### API服务项目
**识别标志**: `api/` + `routes/` + `controllers/`
**生成图表**:
- ✅ 系统架构图
- ✅ 数据流程图
- ✅ 部署架构图

### 数据处理项目
**识别标志**: `data/` + `etl/` + `pipeline/`
**生成图表**:
- ✅ 数据流程图
- ✅ 业务流程图
- ✅ 系统架构图

### 移动应用项目
**识别标志**: `android/` + `ios/` + `lib/`
**生成图表**:
- ✅ 用户交互流程图
- ✅ 系统架构图
- ✅ 部署架构图

## 🎨 样式和主题

### 默认主题
- **专业主题** (Professional): 适合商业项目
- **暗色主题** (Dark): 适合暗色环境
- **企业主题** (Enterprise): 适合企业级项目
- **现代主题** (Modern): 适合现代化项目
- **柔和主题** (Soft): 适合展示和演示

### 自定义配置

在项目根目录创建 `.svg-config.json` 文件：

```json
{
  "project_type": "web_application",
  "diagrams": ["system_architecture", "user_interaction"],
  "style_theme": "professional",
  "custom_colors": {
    "primary": "#2c3e50",
    "accent": "#3498db",
    "success": "#27ae60"
  }
}
```

## 📊 生成的图表类型

### 1. 系统架构图 (system_architecture.svg)
- 展示分层架构设计
- 模块间的依赖关系
- 数据流向示意

### 2. 业务流程图 (business_process_flow.svg)
- 核心业务流程
- 用户操作路径
- 决策点和分支

### 3. 数据流程图 (data_flow_diagram.svg)
- 数据流向和处理过程
- 外部实体交互
- 数据存储设计

### 4. 用户交互流程图 (user_interaction_flow.svg)
- 用户界面交互
- 系统响应机制
- 错误处理流程

### 5. 数据库关系图 (database_schema.svg)
- 表结构设计
- 关系约束
- 索引和优化

### 6. 部署架构图 (deployment_architecture.svg)
- 系统部署架构
- 技术栈展示
- 环境配置

## 🔧 高级配置

### 规则优先级

在 `cursor.json` 中配置规则优先级：

```json
{
  "settings": {
    "auto_apply_rules": false,
    "rule_priority": "user_defined",
    "context_window": 8000,
    "enable_rule_suggestions": true
  }
}
```

### 自定义触发词

添加新的触发词到规则配置：

```json
{
  "triggers": [
    "生成SVG流程图",
    "分析项目架构",
    "创建系统架构图",
    "自定义触发词"
  ]
}
```

### 扩展项目类型

在 `project-analysis-examples.mdc` 中添加新的项目类型识别规则：

```markdown
#### 新项目类型识别
**文件标识**:
```
特定文件或目录结构
```

**推荐图表**: 图表类型列表
```

## 🛠️ 故障排除

### 常见问题

#### 1. 规则未生效
**解决方案**:
- 检查 `.cursor` 文件夹是否在项目根目录
- 确认 `cursor.json` 配置正确
- 重启Cursor IDE

#### 2. 项目类型识别错误
**解决方案**:
- 手动指定项目类型在 `.svg-config.json`
- 检查项目结构是否符合识别模式
- 添加自定义识别规则

#### 3. SVG文件无法显示
**解决方案**:
- 检查SVG语法是否正确
- 验证文件编码为UTF-8
- 在浏览器中测试显示

#### 4. 样式显示异常
**解决方案**:
- 检查CSS语法
- 验证颜色值格式
- 确认字体名称正确

## 📈 扩展和定制

### 添加新图表类型

1. 在 `svg-templates.mdc` 中添加新模板
2. 在 `svg-flowchart-generator.mdc` 中添加生成逻辑
3. 在 `project-analysis-examples.mdc` 中添加识别规则

### 创建自定义主题

1. 在 `color-schemes.mdc` 中定义新主题
2. 添加颜色变量和应用规则
3. 在配置文件中引用新主题

### 集成CI/CD

```yaml
# .github/workflows/svg-generation.yml
name: Generate SVG Diagrams
on: [push, pull_request]
jobs:
  generate-diagrams:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Generate SVG Diagrams
        run: |
          # 使用Cursor CLI或API生成图表
          cursor-cli generate-svg-diagrams
          git add svg/
          git commit -m "docs: 更新SVG流程图"
```

## 📞 支持和反馈

### 获取帮助
- 📖 查看详细的规则文档
- 🐛 报告问题和Bug
- 💡 提出功能建议
- 🤝 贡献代码和模板

### 版本更新
定期检查规则的更新：
- 新的图表类型支持
- 样式和主题改进
- 项目类型识别增强
- 性能和稳定性优化

## 📄 许可证

MIT License - 可自由使用、修改和分发

---

**创建日期**: 2025-06-23  
**版本**: v1.0.0  
**兼容性**: Cursor IDE v0.40+  
**维护者**: Development Team
