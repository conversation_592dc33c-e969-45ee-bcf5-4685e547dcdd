{"rules": [{"name": "SVG流程图生成器", "description": "自动分析项目并生成多角度的SVG流程图", "file": "rules/svg-flowchart-generator.mdc", "enabled": true, "triggers": ["生成SVG流程图", "分析项目架构", "创建系统架构图", "生成项目文档图表", "建立项目可视化文档"]}, {"name": "SVG模板库", "description": "SVG模板和样式定义", "file": "rules/svg-templates.mdc", "enabled": true, "triggers": ["SVG模板", "流程图样式", "图表模板"]}, {"name": "颜色方案", "description": "SVG流程图颜色方案和主题定义", "file": "rules/color-schemes.mdc", "enabled": true, "triggers": ["颜色方案", "主题配置", "样式定制"]}, {"name": "项目分析示例", "description": "项目分析示例和模式识别规则", "file": "rules/project-analysis-examples.mdc", "enabled": true, "triggers": ["项目分析", "技术栈识别", "架构模式"]}], "settings": {"auto_apply_rules": false, "rule_priority": "user_defined", "context_window": 8000, "enable_rule_suggestions": true}}