---
description: SVG流程图颜色方案和主题定义
globs: ["svg/**/*.svg"]
alwaysApply: false
---

# 颜色方案和主题定义

## 默认专业主题 (Professional Theme)

### 主要颜色
```css
:root {
  --primary-color: #2c3e50;      /* 深蓝灰 - 主要文字和边框 */
  --secondary-color: #34495e;    /* 中蓝灰 - 次要文字 */
  --accent-color: #3498db;       /* 蓝色 - 强调和处理过程 */
  --success-color: #27ae60;      /* 绿色 - 成功状态和数据存储 */
  --warning-color: #f39c12;      /* 橙色 - 警告和决策点 */
  --danger-color: #e74c3c;       /* 红色 - 错误和外部实体 */
  --info-color: #9b59b6;         /* 紫色 - 信息和系统组件 */
  --light-color: #ecf0f1;        /* 浅灰 - 背景色 */
  --dark-color: #7f8c8d;         /* 深灰 - 辅助文字 */
  --white-color: #ffffff;        /* 白色 - 组件背景 */
}
```

### 应用场景
- **主色调** (#2c3e50): 标题、主要文字、连接线
- **强调色** (#3498db): 处理过程、按钮、重要组件
- **成功色** (#27ae60): 数据存储、成功状态、完成节点
- **警告色** (#f39c12): 决策点、警告信息、注意事项
- **错误色** (#e74c3c): 外部实体、错误状态、删除操作
- **信息色** (#9b59b6): 系统组件、配置项、工具模块

## 暗色主题 (Dark Theme)

### 主要颜色
```css
:root {
  --primary-color: #ecf0f1;      /* 浅灰 - 主要文字 */
  --secondary-color: #bdc3c7;    /* 中灰 - 次要文字 */
  --accent-color: #5dade2;       /* 亮蓝 - 强调色 */
  --success-color: #58d68d;      /* 亮绿 - 成功状态 */
  --warning-color: #f7dc6f;      /* 亮黄 - 警告 */
  --danger-color: #ec7063;       /* 亮红 - 错误 */
  --info-color: #bb8fce;         /* 亮紫 - 信息 */
  --light-color: #2c3e50;        /* 深蓝灰 - 背景 */
  --dark-color: #34495e;         /* 深灰 - 组件背景 */
  --white-color: #2c3e50;        /* 深色 - 替代白色 */
}
```

## 企业主题 (Enterprise Theme)

### 主要颜色
```css
:root {
  --primary-color: #003366;      /* 深蓝 - 企业蓝 */
  --secondary-color: #004080;    /* 中蓝 - 次要色 */
  --accent-color: #0066cc;       /* 亮蓝 - 强调色 */
  --success-color: #009900;      /* 深绿 - 成功 */
  --warning-color: #ff6600;      /* 橙色 - 警告 */
  --danger-color: #cc0000;       /* 深红 - 错误 */
  --info-color: #6600cc;         /* 深紫 - 信息 */
  --light-color: #f5f5f5;        /* 浅灰 - 背景 */
  --dark-color: #666666;         /* 深灰 - 辅助 */
  --white-color: #ffffff;        /* 白色 - 组件背景 */
}
```

## 现代主题 (Modern Theme)

### 主要颜色
```css
:root {
  --primary-color: #1a1a1a;      /* 深黑 - 主要文字 */
  --secondary-color: #4a4a4a;    /* 深灰 - 次要文字 */
  --accent-color: #00ff88;       /* 荧光绿 - 强调色 */
  --success-color: #00cc66;      /* 绿色 - 成功 */
  --warning-color: #ffaa00;      /* 橙色 - 警告 */
  --danger-color: #ff4444;       /* 红色 - 错误 */
  --info-color: #8844ff;         /* 紫色 - 信息 */
  --light-color: #f8f9fa;        /* 浅色 - 背景 */
  --dark-color: #6c757d;         /* 灰色 - 辅助 */
  --white-color: #ffffff;        /* 白色 - 组件背景 */
}
```

## 柔和主题 (Soft Theme)

### 主要颜色
```css
:root {
  --primary-color: #5d4e75;      /* 柔和紫 - 主要文字 */
  --secondary-color: #7d6e85;    /* 浅紫 - 次要文字 */
  --accent-color: #a8dadc;       /* 柔和蓝 - 强调色 */
  --success-color: #81b29a;      /* 柔和绿 - 成功 */
  --warning-color: #f1faee;      /* 柔和黄 - 警告 */
  --danger-color: #e07a5f;       /* 柔和红 - 错误 */
  --info-color: #457b9d;         /* 柔和蓝 - 信息 */
  --light-color: #f1faee;        /* 米白 - 背景 */
  --dark-color: #3d5a80;         /* 深蓝 - 辅助 */
  --white-color: #ffffff;        /* 白色 - 组件背景 */
}
```

## 高对比度主题 (High Contrast Theme)

### 主要颜色
```css
:root {
  --primary-color: #000000;      /* 纯黑 - 主要文字 */
  --secondary-color: #333333;    /* 深灰 - 次要文字 */
  --accent-color: #0000ff;       /* 纯蓝 - 强调色 */
  --success-color: #008000;      /* 纯绿 - 成功 */
  --warning-color: #ffa500;      /* 橙色 - 警告 */
  --danger-color: #ff0000;       /* 纯红 - 错误 */
  --info-color: #800080;         /* 紫色 - 信息 */
  --light-color: #ffffff;        /* 纯白 - 背景 */
  --dark-color: #666666;         /* 灰色 - 辅助 */
  --white-color: #ffffff;        /* 白色 - 组件背景 */
}
```

## 颜色使用指南

### 图形元素颜色映射

#### 系统架构图
- **表现层**: accent-color (蓝色系)
- **业务逻辑层**: success-color (绿色系)
- **数据访问层**: danger-color (红色系)
- **基础设施层**: info-color (紫色系)

#### 业务流程图
- **开始/结束**: success-color (绿色)
- **处理过程**: accent-color (蓝色)
- **决策点**: warning-color (橙色)
- **错误处理**: danger-color (红色)

#### 数据流程图
- **外部实体**: danger-color (红色)
- **处理过程**: accent-color (蓝色)
- **数据存储**: success-color (绿色)
- **控制流**: warning-color (橙色)

#### 用户交互图
- **用户操作**: danger-color (红色)
- **界面元素**: accent-color (蓝色)
- **系统响应**: success-color (绿色)
- **错误状态**: warning-color (橙色)

#### 数据库关系图
- **主键字段**: danger-color (红色背景)
- **外键字段**: accent-color (蓝色背景)
- **普通字段**: white-color (白色背景)
- **表关系线**: primary-color (主色调)

#### 部署架构图
- **客户端层**: accent-color (蓝色)
- **应用层**: success-color (绿色)
- **数据层**: danger-color (红色)
- **基础设施层**: info-color (紫色)

### 可访问性考虑

#### 对比度要求
- 文字与背景对比度 ≥ 4.5:1
- 大文字与背景对比度 ≥ 3:1
- 图形元素与背景对比度 ≥ 3:1

#### 色盲友好
- 避免仅依赖颜色传达信息
- 使用形状、图案、文字标签辅助
- 红绿色盲友好的颜色组合

### 主题切换实现

```css
/* 主题变量定义 */
[data-theme="professional"] {
  --primary: #2c3e50;
  --accent: #3498db;
  /* ... 其他颜色 */
}

[data-theme="dark"] {
  --primary: #ecf0f1;
  --accent: #5dade2;
  /* ... 其他颜色 */
}

[data-theme="enterprise"] {
  --primary: #003366;
  --accent: #0066cc;
  /* ... 其他颜色 */
}
```

### 自定义颜色配置

用户可以通过 `.svg-config.json` 文件自定义颜色：

```json
{
  "style_theme": "custom",
  "custom_colors": {
    "primary": "#1a1a1a",
    "accent": "#00ff88",
    "success": "#00cc66",
    "warning": "#ffaa00",
    "danger": "#ff4444",
    "info": "#8844ff"
  }
}
```
