---
description: 项目分析示例和模式识别规则
globs: ["**/*"]
alwaysApply: false
---

# 项目分析示例和模式识别

## 项目类型识别模式

### 1. Web应用项目

#### React项目识别
**文件标识**:
```
package.json (包含 "react")
src/
├── components/
├── pages/ 或 views/
├── hooks/
├── utils/
└── App.js 或 App.tsx
public/
├── index.html
└── favicon.ico
```

**推荐图表**: 系统架构图、用户交互流程图、部署架构图

**架构分析**:
- 表现层: React组件、路由管理
- 业务逻辑层: 自定义Hooks、状态管理
- 数据访问层: API调用、数据获取
- 基础设施层: 构建工具、环境配置

#### Vue项目识别
**文件标识**:
```
package.json (包含 "vue")
src/
├── components/
├── views/
├── router/
├── store/
└── main.js
public/
└── index.html
```

#### Angular项目识别
**文件标识**:
```
package.json (包含 "@angular")
src/
├── app/
│   ├── components/
│   ├── services/
│   └── modules/
├── assets/
└── main.ts
angular.json
```

### 2. 桌面应用项目

#### PyQt/PySide项目识别
**文件标识**:
```
main.py
requirements.txt (包含 PyQt6/PyQt5/PySide)
gui/ 或 ui/
├── main_window.py
├── dialogs/
└── widgets/
models/
database/
utils/
```

**推荐图表**: 系统架构图、用户交互流程图、业务流程图

**架构分析**:
- 表现层: PyQt窗口、对话框、控件
- 业务逻辑层: 数据模型、业务规则
- 数据访问层: 数据库操作、文件I/O
- 基础设施层: 配置管理、日志系统

#### Electron项目识别
**文件标识**:
```
package.json (包含 "electron")
main.js 或 main.ts
src/
├── renderer/
├── main/
└── preload/
```

#### WPF项目识别
**文件标识**:
```
*.csproj
*.xaml
App.xaml
MainWindow.xaml
Models/
ViewModels/
Views/
```

### 3. API服务项目

#### Express.js项目识别
**文件标识**:
```
package.json (包含 "express")
server.js 或 app.js
routes/
controllers/
middleware/
models/
```

**推荐图表**: 系统架构图、数据流程图、部署架构图

#### Django项目识别
**文件标识**:
```
manage.py
requirements.txt (包含 "django")
settings.py
urls.py
models.py
views.py
```

#### Spring Boot项目识别
**文件标识**:
```
pom.xml 或 build.gradle
src/main/java/
├── controller/
├── service/
├── repository/
└── model/
application.properties
```

### 4. 数据处理项目

#### 数据科学项目识别
**文件标识**:
```
requirements.txt (包含 pandas, numpy, matplotlib)
*.ipynb
data/
├── raw/
├── processed/
└── external/
notebooks/
src/
├── data/
├── features/
├── models/
└── visualization/
```

**推荐图表**: 数据流程图、业务流程图、系统架构图

#### ETL项目识别
**文件标识**:
```
pipeline/
etl/
config/
├── database.yml
├── pipeline.yml
└── schedule.yml
scripts/
logs/
```

### 5. 移动应用项目

#### Flutter项目识别
**文件标识**:
```
pubspec.yaml
lib/
├── main.dart
├── screens/
├── widgets/
├── models/
└── services/
android/
ios/
```

**推荐图表**: 用户交互流程图、系统架构图、部署架构图

#### React Native项目识别
**文件标识**:
```
package.json (包含 "react-native")
App.js 或 App.tsx
src/
├── components/
├── screens/
├── navigation/
└── services/
android/
ios/
```

## 技术栈分析规则

### 前端技术栈识别

#### 框架检测
```javascript
// package.json 分析
{
  "dependencies": {
    "react": "^18.0.0",           // React项目
    "vue": "^3.0.0",              // Vue项目
    "@angular/core": "^15.0.0",   // Angular项目
    "svelte": "^3.0.0",           // Svelte项目
    "next": "^13.0.0",            // Next.js项目
    "nuxt": "^3.0.0",             // Nuxt.js项目
    "gatsby": "^5.0.0"            // Gatsby项目
  }
}
```

#### 状态管理检测
```javascript
{
  "dependencies": {
    "redux": "^4.0.0",            // Redux
    "mobx": "^6.0.0",             // MobX
    "zustand": "^4.0.0",          // Zustand
    "recoil": "^0.7.0",           // Recoil
    "vuex": "^4.0.0",             // Vuex (Vue)
    "pinia": "^2.0.0"             // Pinia (Vue)
  }
}
```

### 后端技术栈识别

#### Node.js生态
```javascript
{
  "dependencies": {
    "express": "^4.0.0",          // Express.js
    "koa": "^2.0.0",              // Koa.js
    "fastify": "^4.0.0",          // Fastify
    "nest": "^9.0.0",             // NestJS
    "apollo-server": "^3.0.0"     // GraphQL Apollo
  }
}
```

#### Python生态
```python
# requirements.txt 分析
Django>=4.0.0                    # Django框架
Flask>=2.0.0                     # Flask框架
FastAPI>=0.95.0                  # FastAPI框架
Tornado>=6.0.0                   # Tornado框架
Pyramid>=2.0.0                   # Pyramid框架
```

#### Java生态
```xml
<!-- pom.xml 分析 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>
```

### 数据库技术栈识别

#### 关系型数据库
```yaml
# docker-compose.yml 或配置文件
services:
  postgres:                       # PostgreSQL
  mysql:                          # MySQL
  mariadb:                        # MariaDB
  mssql:                          # SQL Server
```

#### NoSQL数据库
```yaml
services:
  mongodb:                        # MongoDB
  redis:                          # Redis
  elasticsearch:                  # Elasticsearch
  cassandra:                      # Cassandra
```

#### 文件数据库
```
*.db                              # SQLite
*.sqlite
*.sqlite3
```

## 架构模式识别

### MVC模式
**标识**:
```
models/          # 数据模型
views/           # 视图层
controllers/     # 控制器
```

### MVP模式
**标识**:
```
models/          # 数据模型
views/           # 视图层
presenters/      # 展示器
```

### MVVM模式
**标识**:
```
models/          # 数据模型
views/           # 视图层
viewmodels/      # 视图模型
```

### 微服务架构
**标识**:
```
services/
├── user-service/
├── order-service/
├── payment-service/
└── notification-service/
docker-compose.yml
kubernetes/
```

### 分层架构
**标识**:
```
presentation/    # 表现层
business/        # 业务层
data/           # 数据层
infrastructure/ # 基础设施层
```

## 部署方式识别

### 容器化部署
**标识**:
```
Dockerfile
docker-compose.yml
.dockerignore
kubernetes/
├── deployment.yaml
├── service.yaml
└── ingress.yaml
```

### 云平台部署
**标识**:
```
vercel.json                      # Vercel
netlify.toml                     # Netlify
.platform.app.yaml              # Platform.sh
app.yaml                         # Google App Engine
buildspec.yml                    # AWS CodeBuild
azure-pipelines.yml             # Azure DevOps
```

### 传统部署
**标识**:
```
requirements.txt                 # Python
package.json                     # Node.js
pom.xml                         # Java Maven
build.gradle                    # Java Gradle
Gemfile                         # Ruby
composer.json                   # PHP
```

## 项目规模评估

### 小型项目 (< 10k LOC)
- 单一技术栈
- 简单的目录结构
- 基础的配置文件

### 中型项目 (10k - 100k LOC)
- 多层架构
- 模块化设计
- 完整的测试覆盖
- CI/CD配置

### 大型项目 (> 100k LOC)
- 微服务架构
- 多技术栈
- 复杂的部署配置
- 完整的监控和日志

## 特殊项目类型

### 开源项目
**标识**:
```
LICENSE
CONTRIBUTING.md
CODE_OF_CONDUCT.md
.github/
├── ISSUE_TEMPLATE/
├── PULL_REQUEST_TEMPLATE/
└── workflows/
```

### 企业项目
**标识**:
```
docs/
├── architecture/
├── deployment/
└── security/
config/
├── production/
├── staging/
└── development/
```

### 学习项目
**标识**:
```
README.md (包含学习目标)
tutorials/
examples/
exercises/
```
