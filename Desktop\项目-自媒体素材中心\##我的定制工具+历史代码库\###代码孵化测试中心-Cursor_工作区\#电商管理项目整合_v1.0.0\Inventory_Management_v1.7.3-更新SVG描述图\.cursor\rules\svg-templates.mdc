---
description: SVG模板和样式定义
globs: ["svg/**/*.svg"]
alwaysApply: false
---

# SVG模板定义

## 基础SVG模板结构

```xml
<?xml version="1.0" encoding="UTF-8"?>
<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      {styles}
    </style>
    {markers}
  </defs>
  
  <!-- 标题 -->
  <text x="{title_x}" y="30" text-anchor="middle" class="title">{title}</text>
  
  <!-- 内容区域 -->
  {content}
  
  <!-- 图例 -->
  {legend}
</svg>
```

## 标准样式定义

```css
/* 文字样式 */
.title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
.section-title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
.component-title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
.description { font-family: Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
.detail { font-family: Arial, sans-serif; font-size: 11px; fill: #95a5a6; }

/* 图形样式 */
.layer-bg { fill: #ecf0f1; stroke: #bdc3c7; stroke-width: 2; }
.component-bg { fill: #ffffff; stroke: #3498db; stroke-width: 2; rx: 8; }
.process { fill: #3498db; stroke: #2980b9; stroke-width: 2; rx: 8; }
.decision { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
.data-store { fill: #27ae60; stroke: #1e8449; stroke-width: 2; }
.external { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
.system { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }

/* 连接线样式 */
.connection { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
.data-flow { stroke: #27ae60; stroke-width: 2; fill: none; marker-end: url(#arrowhead-green); stroke-dasharray: 5,5; }
.error-flow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead-red); stroke-dasharray: 3,3; }
```

## 箭头标记定义

```xml
<marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
  <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
</marker>
<marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
  <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
</marker>
<marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
  <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
</marker>
```

## 系统架构图模板

```xml
<!-- 分层架构模板 -->
<!-- 表现层 -->
<rect x="50" y="80" width="1100" height="150" class="layer-bg"/>
<text x="70" y="105" class="section-title">表现层 (Presentation Layer)</text>

<!-- 业务逻辑层 -->
<rect x="50" y="260" width="1100" height="150" class="layer-bg"/>
<text x="70" y="285" class="section-title">业务逻辑层 (Business Logic Layer)</text>

<!-- 数据访问层 -->
<rect x="50" y="440" width="1100" height="150" class="layer-bg"/>
<text x="70" y="465" class="section-title">数据访问层 (Data Access Layer)</text>

<!-- 基础设施层 -->
<rect x="50" y="620" width="1100" height="150" class="layer-bg"/>
<text x="70" y="645" class="section-title">基础设施层 (Infrastructure Layer)</text>
```

## 业务流程图模板

```xml
<!-- 开始节点 -->
<ellipse cx="100" cy="80" rx="60" ry="25" class="process"/>
<text x="100" y="87" text-anchor="middle" class="component-title">开始</text>

<!-- 处理节点 -->
<rect x="50" y="150" width="100" height="60" class="process"/>
<text x="100" y="175" text-anchor="middle" class="component-title">处理过程</text>

<!-- 决策节点 -->
<polygon points="100,250 140,280 100,310 60,280" class="decision"/>
<text x="100" y="285" text-anchor="middle" class="component-title">决策</text>

<!-- 结束节点 -->
<ellipse cx="100" cy="380" rx="60" ry="25" class="process"/>
<text x="100" y="387" text-anchor="middle" class="component-title">结束</text>
```

## 数据流程图模板

```xml
<!-- 外部实体 -->
<rect x="50" y="100" width="100" height="60" class="external"/>
<text x="100" y="125" text-anchor="middle" class="component-title">外部实体</text>

<!-- 处理过程 -->
<ellipse cx="300" cy="130" rx="80" ry="40" class="process"/>
<text x="300" y="135" text-anchor="middle" class="component-title">处理过程</text>

<!-- 数据存储 -->
<rect x="450" y="100" width="20" height="60" class="data-store"/>
<rect x="470" y="100" width="100" height="60" class="data-store"/>
<text x="520" y="135" text-anchor="middle" class="component-title">数据存储</text>
```

## 用户交互流程图模板

```xml
<!-- 用户操作 -->
<rect x="50" y="100" width="120" height="50" class="external"/>
<text x="110" y="130" text-anchor="middle" class="component-title">用户操作</text>

<!-- 界面响应 -->
<rect x="200" y="100" width="120" height="50" class="process"/>
<text x="260" y="130" text-anchor="middle" class="component-title">界面响应</text>

<!-- 系统处理 -->
<rect x="350" y="100" width="120" height="50" class="system"/>
<text x="410" y="130" text-anchor="middle" class="component-title">系统处理</text>
```

## 数据库关系图模板

```xml
<!-- 表头 -->
<rect x="50" y="80" width="300" height="30" class="data-store"/>
<text x="200" y="100" text-anchor="middle" class="component-title">表名</text>

<!-- 表体 -->
<rect x="50" y="110" width="300" height="200" class="component-bg"/>

<!-- 主键字段 -->
<rect x="50" y="110" width="300" height="20" style="fill: #fdf2f2;"/>
<text x="60" y="125" class="detail" style="fill: #e74c3c; font-weight: bold;">主键字段</text>

<!-- 普通字段 -->
<rect x="50" y="130" width="300" height="20" class="component-bg"/>
<text x="60" y="145" class="detail">普通字段</text>
```

## 部署架构图模板

```xml
<!-- 客户端层 -->
<rect x="50" y="90" width="200" height="120" class="external"/>
<text x="150" y="115" text-anchor="middle" class="component-title">客户端层</text>

<!-- 应用层 -->
<rect x="300" y="90" width="200" height="120" class="process"/>
<text x="400" y="115" text-anchor="middle" class="component-title">应用层</text>

<!-- 数据层 -->
<rect x="550" y="90" width="200" height="120" class="data-store"/>
<text x="650" y="115" text-anchor="middle" class="component-title">数据层</text>
```

## 图例模板

```xml
<!-- 图例区域 -->
<rect x="50" y="750" width="400" height="120" fill="none" stroke="#bdc3c7" stroke-width="1"/>
<text x="60" y="770" class="section-title">图例说明</text>

<!-- 处理过程 -->
<rect x="60" y="780" width="60" height="25" class="process"/>
<text x="130" y="797" class="description">处理过程</text>

<!-- 决策点 -->
<polygon points="70,815 90,825 70,835 50,825" class="decision"/>
<text x="100" y="830" class="description">决策点</text>

<!-- 数据存储 -->
<rect x="200" y="780" width="60" height="25" class="data-store"/>
<text x="270" y="797" class="description">数据存储</text>

<!-- 外部实体 -->
<rect x="200" y="810" width="60" height="25" class="external"/>
<text x="270" y="827" class="description">外部实体</text>
```
