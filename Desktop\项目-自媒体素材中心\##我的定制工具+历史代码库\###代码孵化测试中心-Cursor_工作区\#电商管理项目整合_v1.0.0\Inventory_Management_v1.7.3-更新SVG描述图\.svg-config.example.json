{"project_info": {"name": "示例项目", "type": "auto_detect", "version": "1.0.0", "description": "项目描述"}, "diagram_settings": {"enabled_diagrams": ["system_architecture", "business_process_flow", "data_flow_diagram", "user_interaction_flow", "database_schema", "deployment_architecture"], "auto_generate": true, "default_dimensions": {"system_architecture": "1200x800", "business_process_flow": "1400x1000", "data_flow_diagram": "1200x900", "user_interaction_flow": "1400x1200", "database_schema": "1400x1000", "deployment_architecture": "1400x1000"}}, "style_configuration": {"theme": "professional", "color_scheme": {"primary": "#2c3e50", "secondary": "#34495e", "accent": "#3498db", "success": "#27ae60", "warning": "#f39c12", "danger": "#e74c3c", "info": "#9b59b6", "light": "#ecf0f1", "dark": "#7f8c8d"}, "fonts": {"title_size": "24px", "section_size": "18px", "component_size": "14px", "description_size": "12px", "detail_size": "11px", "font_family": "<PERSON>l, sans-serif"}, "custom_css": ""}, "project_analysis": {"force_project_type": null, "custom_tech_stack": {}, "ignore_patterns": ["node_modules/", ".git/", "dist/", "build/", "__pycache__/", "*.pyc", ".DS_Store"], "include_patterns": ["src/", "lib/", "app/", "components/", "models/", "views/", "controllers/"]}, "custom_mappings": {"module_categories": {"gui/": "表现层", "ui/": "表现层", "components/": "表现层", "views/": "表现层", "models/": "数据模型层", "entities/": "数据模型层", "database/": "数据访问层", "data/": "数据访问层", "services/": "业务逻辑层", "business/": "业务逻辑层", "utils/": "工具层", "helpers/": "工具层", "config/": "配置层", "tests/": "测试层"}, "business_processes": [], "external_entities": [], "data_stores": []}, "generation_options": {"include_legend": true, "include_annotations": true, "include_version_info": true, "include_creation_date": true, "optimize_for_print": false, "generate_thumbnails": false, "create_documentation": true, "multilingual_support": false, "accessibility_features": true}, "output_settings": {"svg_folder": "svg", "documentation_file": "svg/README.md", "backup_existing": true, "file_naming_pattern": "{diagram_type}.svg", "include_timestamp": false, "compression": false, "export_formats": ["svg"]}, "validation_rules": {"check_svg_syntax": true, "validate_accessibility": true, "check_file_size": true, "max_file_size_kb": 500, "validate_colors": true, "check_text_contrast": true, "min_contrast_ratio": 4.5, "validate_structure": true}, "advanced_features": {"auto_update_on_code_change": false, "generate_interactive_svg": false, "include_hyperlinks": false, "export_to_png": false, "export_to_pdf": false, "integration_with_git": true, "version_control_tracking": false, "collaborative_editing": false}, "templates": {"custom_templates_path": "svg/templates/", "override_default_templates": false, "template_variables": {}}, "integrations": {"jira": {"enabled": false, "server_url": "", "project_key": "", "credentials": {}}, "confluence": {"enabled": false, "server_url": "", "space_key": "", "credentials": {}}, "github": {"enabled": false, "repository": "", "auto_commit": false, "commit_message": "docs: 更新SVG流程图"}}}