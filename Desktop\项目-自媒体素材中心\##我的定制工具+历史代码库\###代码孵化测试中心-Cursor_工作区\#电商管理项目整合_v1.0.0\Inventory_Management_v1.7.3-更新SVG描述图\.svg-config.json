{"project_info": {"name": "库存管理系统", "type": "desktop_application", "version": "1.8.0", "description": "基于PyQt6的现代化库存管理系统"}, "diagram_settings": {"enabled_diagrams": ["system_architecture", "product_management_flow", "data_flow_diagram", "user_interaction_flow", "database_schema", "deployment_architecture"], "default_dimensions": {"system_architecture": "1200x800", "business_process": "1400x1000", "data_flow": "1200x900", "user_interaction": "1400x1200", "database_schema": "1400x1000", "deployment_architecture": "1400x1000"}}, "style_configuration": {"theme": "professional", "color_scheme": {"primary": "#2c3e50", "secondary": "#34495e", "accent": "#3498db", "success": "#27ae60", "warning": "#f39c12", "danger": "#e74c3c", "info": "#9b59b6", "light": "#ecf0f1", "dark": "#7f8c8d"}, "fonts": {"title_size": "24px", "section_size": "18px", "component_size": "14px", "description_size": "12px", "detail_size": "11px", "font_family": "<PERSON>l, sans-serif"}}, "project_analysis": {"tech_stack": {"frontend": "PyQt6", "backend": "Python 3.10+", "database": "SQLite3", "image_processing": "PIL/Pillow", "data_processing": "pandas"}, "architecture_pattern": "MVC", "deployment_type": "standalone_desktop", "target_platforms": ["Windows", "macOS", "Linux"]}, "custom_mappings": {"module_categories": {"gui/": "表现层", "models/": "数据模型层", "database/": "数据访问层", "utils/": "工具层", "finance/": "业务逻辑层", "tests/": "测试层"}, "business_processes": ["商品管理流程", "批次管理流程", "图片管理流程", "财务统计流程", "数据导入导出流程"]}, "generation_options": {"include_legend": true, "include_annotations": true, "include_version_info": true, "optimize_for_print": false, "generate_thumbnails": true, "create_documentation": true}, "output_settings": {"svg_folder": "svg", "documentation_file": "svg/README.md", "backup_existing": true, "file_naming_pattern": "{diagram_type}.svg", "include_timestamp": false}, "validation_rules": {"check_svg_syntax": true, "validate_accessibility": true, "check_file_size": true, "max_file_size_kb": 500, "validate_colors": true, "check_text_contrast": true}, "advanced_features": {"auto_update_on_code_change": false, "generate_interactive_svg": false, "include_hyperlinks": false, "export_to_png": false, "export_to_pdf": false, "integration_with_git": true}}