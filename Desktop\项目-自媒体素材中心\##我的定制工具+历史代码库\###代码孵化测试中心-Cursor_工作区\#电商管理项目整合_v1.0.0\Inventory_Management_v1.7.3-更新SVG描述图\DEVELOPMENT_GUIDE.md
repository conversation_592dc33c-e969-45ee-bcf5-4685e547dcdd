# 📚 库存管理系统开发文档

## 🎯 项目概述

### 基本信息
- **项目名称**: 库存管理系统 (Inventory Management System)
- **版本**: v1.7.2 → v1.8.0 (现代化升级)
- **开发语言**: Python 3.10+
- **GUI框架**: PyQt6
- **数据库**: SQLite3
- **架构模式**: MVC (Model-View-Controller)

### 项目特点
- 🖥️ 现代化桌面应用程序
- 📊 完整的库存管理功能
- 🎨 支持暗色/亮色主题
- 📸 图片管理和预览
- 📈 财务统计和分析
- 🔍 二维码扫描支持
- 📤 数据导入导出

## 🏗️ 项目架构

### 目录结构
```
inventory_management/
├── 📁 gui/                    # 用户界面层
│   ├── 📁 dialogs/           # 对话框组件
│   ├── 📁 widgets/           # 自定义控件
│   └── 📄 main_window.py     # 主窗口
├── 📁 models/                # 数据模型层
│   ├── 📄 product.py         # 商品模型
│   ├── 📄 batch.py           # 批次模型
│   └── 📄 transaction.py     # 交易模型
├── 📁 database/              # 数据访问层
│   ├── 📄 db_utils.py        # 数据库工具
│   ├── 📄 db_manager.py      # 数据库管理
│   └── 📄 image_utils.py     # 图片处理
├── 📁 utils/                 # 工具模块
│   ├── 📄 config.py          # 配置管理
│   ├── 📄 error_handler.py   # 错误处理
│   ├── 📄 theme_manager.py   # 主题管理
│   └── 📄 scanner.py         # 二维码扫描
├── 📁 finance/               # 财务模块
├── 📁 resources/             # 资源文件
├── 📁 tests/                 # 测试用例
├── 📁 config/                # 配置文件
├── 📁 logs/                  # 日志文件
└── 📁 svg/                   # 流程图文件
    ├── 📄 system_architecture.svg      # 系统架构图
    ├── 📄 product_management_flow.svg   # 商品管理流程图
    ├── 📄 data_flow_diagram.svg        # 数据流程图
    ├── 📄 user_interaction_flow.svg    # 用户交互流程图
    ├── 📄 database_schema.svg          # 数据库关系图
    └── 📄 deployment_architecture.svg  # 部署架构图
```

### 分层架构设计

#### 1. 表现层 (Presentation Layer)
- **主窗口**: `gui/main_window.py`
- **对话框**: `gui/dialogs/`
- **自定义控件**: `gui/widgets/`
- **主题管理**: `utils/theme_manager.py`

#### 2. 业务逻辑层 (Business Logic Layer)
- **商品管理**: `models/product.py`
- **批次管理**: `models/batch.py`
- **交易处理**: `models/transaction.py`
- **财务计算**: `finance/finances.py`

#### 3. 数据访问层 (Data Access Layer)
- **数据库操作**: `database/db_utils.py`
- **图片处理**: `database/image_utils.py`
- **数据管理**: `database/db_manager.py`

#### 4. 基础设施层 (Infrastructure Layer)
- **配置管理**: `utils/config.py`
- **错误处理**: `utils/error_handler.py`
- **日志系统**: 内置logging模块

## 🔧 开发环境搭建

### 1. 环境要求
```bash
# Python版本
Python >= 3.10

# 操作系统
Windows 10/11, macOS 10.15+, Ubuntu 20.04+
```

### 2. 依赖安装
```bash
# 克隆项目
git clone <repository-url>
cd inventory_management

# 安装依赖
pip install -r requirements.txt

# 或使用现代化配置
pip install -e .
```

### 3. 数据库初始化
```bash
# 初始化数据库
python -c "from database.db_utils import create_tables; create_tables()"
```

### 4. 运行程序
```bash
# 启动应用
python main.py

# 或使用模块方式
python -m inventory_management
```

## 📋 核心功能模块

### 1. 商品管理 (Product Management)

#### 数据模型
```python
class Product:
    - product_id: int           # 商品ID
    - name: str                 # 商品名称
    - category: str             # 商品分类
    - quantity: int             # 库存数量
    - purchase_price: float     # 采购价格
    - selling_price: float      # 销售价格
    - image_path: str           # 图片路径
    # ... 其他字段
```

#### 主要功能
- ✅ 商品信息CRUD操作
- ✅ 图片上传和管理
- ✅ 库存数量跟踪
- ✅ 价格计算和利润分析
- ✅ 批量导入导出

### 2. 批次管理 (Batch Management)

#### 数据模型
```python
class Batch:
    - batch_id: int             # 批次ID
    - code: str                 # 批次编码
    - name: str                 # 批次名称
    - status: str               # 批次状态
    - created_at: datetime      # 创建时间
```

#### 主要功能
- ✅ 批次创建和管理
- ✅ 商品批次关联
- ✅ 批次状态跟踪
- ✅ 批次统计分析

### 3. 财务管理 (Finance Management)

#### 功能特性
- 📊 销售统计
- 💰 利润分析
- 📈 趋势图表
- 📤 财务报表导出

### 4. 图片管理 (Image Management)

#### 功能特性
- 🖼️ 图片上传和存储
- 🔍 图片预览和缩略图
- 📁 图片库管理
- 🏷️ 图片标签系统

## 🎨 用户界面设计

### 主题系统
```python
# 支持的主题
themes = {
    "dark": "暗色主题",
    "light": "亮色主题"
}

# 主题切换
ThemeManager.apply_theme(app, "dark")
```

### 界面组件
- **主窗口**: 标签页式布局
- **商品列表**: 表格视图 + 搜索过滤
- **批次管理**: 树形视图 + 详情面板
- **图片预览**: 缩略图网格 + 大图预览
- **统计图表**: 基于matplotlib的图表

## 🗄️ 数据库设计

### 核心表结构

#### products (商品表)
```sql
CREATE TABLE products (
    product_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT,
    quantity INTEGER DEFAULT 0,
    unit TEXT DEFAULT '个',
    status TEXT DEFAULT '在库',
    purchase_price REAL DEFAULT 0,
    selling_price REAL DEFAULT 0,
    image_path TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### batches (批次表)
```sql
CREATE TABLE batches (
    batch_id TEXT PRIMARY KEY,
    code TEXT UNIQUE,
    name TEXT,
    status TEXT DEFAULT '活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### product_batches (商品批次关联表)
```sql
CREATE TABLE product_batches (
    product_id TEXT,
    batch_id TEXT,
    quantity INTEGER DEFAULT 1,
    PRIMARY KEY (product_id, batch_id)
);
```

#### product_images (商品图片表)
```sql
CREATE TABLE product_images (
    image_id TEXT PRIMARY KEY,
    product_id TEXT,
    image_path TEXT,
    is_primary INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 数据关系
- 商品 ↔ 批次: 多对多关系
- 商品 ↔ 图片: 一对多关系
- 商品 ↔ 交易: 一对多关系

## 🔌 API接口设计

### 商品管理API
```python
# 商品CRUD操作
def create_product(product_data: dict) -> str
def get_product(product_id: str) -> Product
def update_product(product_id: str, data: dict) -> bool
def delete_product(product_id: str) -> bool
def search_products(filters: dict) -> List[Product]
```

### 批次管理API
```python
# 批次CRUD操作
def create_batch(batch_data: dict) -> str
def get_batch(batch_id: str) -> Batch
def add_product_to_batch(product_id: str, batch_id: str) -> bool
def get_batch_products(batch_id: str) -> List[Product]
```

### 图片管理API
```python
# 图片处理API
def save_product_image(product_id: str, image_path: str) -> str
def get_product_images(product_id: str) -> List[dict]
def delete_product_image(image_id: str) -> bool
def create_thumbnail(image_path: str) -> str
```

## 🧪 测试策略

### 测试分类
1. **单元测试**: 测试单个函数和类
2. **集成测试**: 测试模块间交互
3. **GUI测试**: 测试用户界面
4. **性能测试**: 测试系统性能

### 测试工具
- **pytest**: 单元测试框架
- **pytest-qt**: GUI测试
- **pytest-cov**: 代码覆盖率
- **mock**: 模拟对象

### 运行测试
```bash
# 运行所有测试
pytest tests/

# 运行特定测试
pytest tests/test_product.py

# 生成覆盖率报告
pytest --cov=inventory_management tests/
```

## 📦 部署和分发

### 打包方式
1. **PyInstaller**: 生成可执行文件
2. **cx_Freeze**: 跨平台打包
3. **Nuitka**: 编译为原生代码

### 打包命令
```bash
# 使用PyInstaller
pyinstaller --onefile --windowed main.py

# 包含资源文件
pyinstaller --onefile --windowed \
    --add-data "resources:resources" \
    --add-data "config:config" \
    main.py
```

## 🔧 配置管理

### 配置文件结构
```json
{
    "database": {
        "path": "data/inventory.db",
        "backup_enabled": true
    },
    "ui": {
        "theme": "dark",
        "language": "zh_CN"
    },
    "image": {
        "max_size": 1024,
        "quality": 85
    }
}
```

### 环境变量支持
```bash
# 数据库路径
export DB_PATH="/custom/path/inventory.db"

# 主题设置
export UI_THEME="light"

# 调试模式
export DEBUG=true
```

## 📈 性能优化

### 数据库优化
- 使用索引提高查询速度
- 实现连接池管理
- 批量操作减少I/O

### 界面优化
- 延迟加载大量数据
- 虚拟化长列表
- 异步图片加载

### 内存优化
- 及时释放不用的对象
- 使用生成器处理大数据
- 图片缓存管理

## 🐛 调试和日志

### 日志配置
```python
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
```

### 调试技巧
- 使用断点调试
- 添加详细日志
- 使用性能分析工具
- 监控内存使用

## 🔄 版本控制

### Git工作流
```bash
# 功能开发
git checkout -b feature/new-feature
git commit -m "feat: 添加新功能"
git push origin feature/new-feature

# 发布版本
git tag v1.8.0
git push origin v1.8.0
```

### 版本规范
- 使用语义化版本 (Semantic Versioning)
- 主版本.次版本.修订版本
- 例: v1.8.0, v1.8.1, v2.0.0

---

## 📞 联系方式

- **项目维护**: Development Team
- **技术支持**: [技术支持邮箱]
- **问题反馈**: [GitHub Issues]

## 📊 系统流程图解析

### 流程图文件说明

项目包含6个详细的SVG流程图，从不同角度解析系统逻辑：

#### 1. 系统架构图 (`svg/system_architecture.svg`)
**用途**: 展示系统的分层架构设计
**内容**:
- 表现层 (Presentation Layer): GUI组件、主题管理
- 业务逻辑层 (Business Logic Layer): 核心业务模块
- 数据访问层 (Data Access Layer): 数据库操作、图片处理
- 基础设施层 (Infrastructure Layer): 配置、日志、工具

#### 2. 商品管理流程图 (`svg/product_management_flow.svg`)
**用途**: 详细展示商品管理的业务流程
**内容**:
- 创建商品流程: 输入验证 → 数据保存
- 查看商品流程: 列表加载 → 搜索过滤 → 详情显示
- 编辑商品流程: 选择商品 → 编辑对话框 → 确认修改
- 删除商品流程: 选择商品 → 确认删除 → 数据库删除
- 图片管理流程: 上传/删除图片操作
- 批量操作流程: 批量导入/导出功能

#### 3. 数据流程图 (`svg/data_flow_diagram.svg`)
**用途**: 展示系统的数据流向和处理过程
**内容**:
- 外部实体: 用户、供应商、客户、外部系统
- 核心处理过程: 商品管理、库存管理、批次管理、财务管理等
- 数据存储: 商品数据、批次数据、交易记录、图片文件、配置数据
- 数据流: 展示数据在各组件间的流动路径

#### 4. 用户交互流程图 (`svg/user_interaction_flow.svg`)
**用途**: 展示用户与系统的交互过程
**内容**:
- 应用启动流程: 启动界面 → 系统初始化 → 主窗口
- 主界面交互: 菜单栏、工具栏、标签页、状态栏
- 商品管理交互: 列表点击 → 详情显示 → 编辑操作
- 搜索过滤流程: 用户输入 → 实时过滤 → 结果更新
- 图片管理流程: 拖拽上传 → 文件处理 → 预览更新
- 错误处理流程: 错误捕获 → 错误对话框 → 日志记录

#### 5. 数据库关系图 (`svg/database_schema.svg`)
**用途**: 展示数据库表结构和关系设计
**内容**:
- 核心表: products(商品)、batches(批次)、transactions(交易)
- 关联表: product_batches(商品批次关联)、product_images(商品图片)
- 财务表: finance_records(财务记录)
- 关系类型: 一对多、多对多关系
- 约束说明: 主键、外键、唯一约束、检查约束

#### 6. 部署架构图 (`svg/deployment_architecture.svg`)
**用途**: 展示系统的部署架构和技术栈
**内容**:
- 客户端层: Windows/macOS/Linux客户端
- 应用程序层: 核心应用、UI层、业务逻辑层
- 数据层: 本地数据库、文件存储、配置存储
- 系统层: 操作系统、Python运行时、硬件资源
- 网络层: 云同步、API接口(未来功能)
- 部署方式: 单机部署、便携部署
- 开发工具链: 构建工具、版本控制、测试工具

### 流程图使用指南

#### 查看流程图
```bash
# 在浏览器中打开SVG文件
start svg/system_architecture.svg        # Windows
open svg/system_architecture.svg         # macOS
xdg-open svg/system_architecture.svg     # Linux
```

#### 编辑流程图
推荐使用以下工具编辑SVG流程图：
- **在线工具**: draw.io, Lucidchart
- **桌面软件**: Inkscape, Adobe Illustrator
- **代码编辑**: 直接编辑SVG源码

#### 流程图维护
- 当系统架构发生变化时，及时更新对应的流程图
- 新增功能模块时，更新相关的流程图
- 定期检查流程图与实际代码的一致性

---

*最后更新: 2025-06-23*
