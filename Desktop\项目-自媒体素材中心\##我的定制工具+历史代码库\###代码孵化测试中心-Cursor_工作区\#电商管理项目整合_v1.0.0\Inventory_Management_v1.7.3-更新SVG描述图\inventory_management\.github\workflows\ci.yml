name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  PYTHON_VERSION: "3.10"
  POETRY_VERSION: "1.7.1"

jobs:
  # 代码质量检查
  quality:
    runs-on: ubuntu-latest
    name: Code Quality
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root --with dev
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Run pre-commit hooks
      run: |
        poetry run pre-commit install
        poetry run pre-commit run --all-files
    
    - name: Type checking with mypy
      run: poetry run mypy inventory_management/
    
    - name: Security check with bandit
      run: poetry run bandit -r inventory_management/ -f json -o bandit-report.json
    
    - name: Upload bandit report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: bandit-report
        path: bandit-report.json

  # 测试
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ["3.10", "3.11", "3.12"]
    
    name: Test Python ${{ matrix.python-version }} on ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
    
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ matrix.os }}-${{ matrix.python-version }}-${{ hashFiles('**/poetry.lock') }}
    
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root --with test
    
    - name: Install project
      run: poetry install --no-interaction
    
    - name: Setup display (Linux)
      if: runner.os == 'Linux'
      run: |
        sudo apt-get update
        sudo apt-get install -y xvfb
        export DISPLAY=:99
        Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
    
    - name: Run tests
      run: |
        poetry run pytest tests/ \
          --cov=inventory_management \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --junit-xml=pytest-report.xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.os }}-${{ matrix.python-version }}
        path: |
          pytest-report.xml
          htmlcov/
          coverage.xml

  # 构建
  build:
    runs-on: ubuntu-latest
    needs: [quality, test]
    name: Build Package
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Build package
      run: poetry build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  # 发布到PyPI (仅在release时)
  publish:
    runs-on: ubuntu-latest
    needs: [quality, test, build]
    name: Publish to PyPI
    if: github.event_name == 'release' && github.event.action == 'published'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/
    
    - name: Publish to PyPI
      env:
        POETRY_PYPI_TOKEN_PYPI: ${{ secrets.PYPI_TOKEN }}
      run: poetry publish

  # 创建可执行文件
  executable:
    runs-on: ${{ matrix.os }}
    needs: [quality, test]
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    
    name: Build Executable for ${{ matrix.os }}
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
    
    - name: Install dependencies
      run: |
        poetry install --no-interaction
        poetry add pyinstaller
    
    - name: Build executable
      run: |
        poetry run pyinstaller \
          --onefile \
          --windowed \
          --name inventory-management \
          --add-data "inventory_management/resources:resources" \
          --add-data "inventory_management/config:config" \
          inventory_management/main.py
    
    - name: Upload executable
      uses: actions/upload-artifact@v3
      with:
        name: executable-${{ matrix.os }}
        path: dist/inventory-management*
