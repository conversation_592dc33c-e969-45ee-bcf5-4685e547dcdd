# 库存管理系统

## 概述
基于 PyQt6 开发的现代化库存管理系统，提供完整的产品和批次管理功能，支持数据统计和分析。

## 主要功能

### 1. 产品管理
- 产品信息维护
- 图片管理
- 库存跟踪
- 批次关联

### 2. 批次管理
- 批次信息维护
- 产品关联
- 库存变动
- 统计分析

### 3. 财务管理
- 销售统计
- 利润分析
- 趋势图表
- 数据导出

### 4. 系统功能
- 数据库管理
- 用户设置
- 数据导入导出
- 系统日志

## 技术特点

### 1. 现代化界面
- 基于 PyQt6 开发
- 响应式设计
- 美观的界面
- 流畅的交互

### 2. 高性能
- 异步数据处理
- 延迟加载机制
- 数据缓存优化
- 内存管理

### 3. 可靠性
- 完整的错误处理
- 数据验证机制
- 自动备份恢复
- 日志记录

### 4. 可扩展性
- 模块化设计
- 标准化接口
- 插件系统
- 自定义功能

## 系统要求

### 1. 运行环境
- Python >= 3.8
- PyQt6 >= 6.0.0
- SQLite3
- pandas >= 1.0.0

### 2. 推荐配置
- CPU: 双核及以上
- 内存: 4GB 及以上
- 硬盘: 1GB 可用空间
- 显示器: 1920x1080

## 安装说明

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 初始化数据库
```bash
python -m utils.database_init
```

### 3. 运行程序
```bash
python -m gui.main_window
```

## 使用指南

### 1. 快速开始
1. 添加产品信息
2. 创建批次
3. 关联产品和批次
4. 查看统计数据

### 2. 常用功能
- 产品搜索和过滤
- 批次管理
- 数据导入导出
- 财务统计

### 3. 高级功能
- 自定义统计
- 数据库优化
- 批量操作
- 自动化任务

## 开发指南

### 1. 项目结构
```
inventory_management/
├── gui/                # 界面模块
│   ├── dialogs/       # 对话框
│   ├── widgets/       # 自定义控件
│   └── main_window.py # 主窗口
├── utils/             # 工具模块
│   ├── database.py    # 数据库操作
│   ├── error_handler.py # 错误处理
│   └── config.py      # 配置管理
├── resources/         # 资源文件
├── tests/            # 测试用例
└── requirements.txt  # 依赖清单
```

### 2. 开发规范
1. 遵循 PEP 8 编码规范
2. 使用类型注解
3. 编写单元测试
4. 添加文档注释

### 3. 版本控制
- 使用语义化版本
- 保持向后兼容
- 记录更新日志
- 标记重要版本

## 更新日志

### v1.6.5 (2024-03)
- 升级到 PyQt6
- 优化界面布局
- 改进错误处理
- 增强数据验证
- 提升运行性能

### v1.6.4 (2024-02)
- 修复已知问题
- 改进用户体验
- 优化数据处理
- 更新依赖版本

## 贡献指南

### 1. 提交问题
- 描述问题现象
- 提供复现步骤
- 附加错误日志
- 说明运行环境

### 2. 代码贡献
- Fork 项目
- 创建分支
- 提交更改
- 发起合并请求

## 许可证
本项目采用 MIT 许可证 