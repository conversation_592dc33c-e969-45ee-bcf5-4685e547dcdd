2025-01-26 11:04:00,644 [INFO] QSS file successfully loaded.
2025-01-26 11:04:00,644 [INFO] Found version patches to be applied.
2025-01-26 11:04:00,646 [INFO] Found application patches to be applied.
2025-01-26 11:04:00,682 [INFO] 启动库存管理系统
2025-01-26 11:04:00,688 [INFO] 数据库表结构已更新
2025-01-26 11:04:01,190 [INFO] 批次管理选项卡设置完成
2025-01-26 11:04:01,195 [INFO] 状态栏初始化完成
2025-01-26 11:04:01,206 [INFO] UI初始化完成
2025-01-26 11:04:01,210 [INFO] 加载了 1 个分类
2025-01-26 11:04:01,217 [INFO] 加载了 1 个批次
2025-01-26 11:04:01,223 [INFO] 系统初始化完成
2025-01-26 11:04:07,059 [INFO] 初始化商品编辑对话框: product_id=#水果-#0001-00009, is_merged_view=False
2025-01-26 11:04:07,094 [INFO] UI初始化完成
2025-01-26 11:04:07,095 [INFO] 所有信号连接完成
2025-01-26 11:04:07,095 [INFO] 信号连接完成
2025-01-26 11:04:07,095 [INFO] 编辑模式，开始加载商品数据: #水果-#0001-00009
2025-01-26 11:04:07,095 [INFO] 开始加载商品数据: #水果-#0001-00009
2025-01-26 11:04:07,101 [INFO] 商品数据加载完成
2025-01-26 11:04:56,172 [INFO] QSS file successfully loaded.
2025-01-26 11:04:56,172 [INFO] Found version patches to be applied.
2025-01-26 11:04:56,175 [INFO] Found application patches to be applied.
2025-01-26 11:04:56,189 [INFO] 启动库存管理系统
2025-01-26 11:04:56,191 [INFO] 数据库表结构已更新
2025-01-26 11:04:56,507 [INFO] 批次管理选项卡设置完成
2025-01-26 11:04:56,511 [INFO] 状态栏初始化完成
2025-01-26 11:04:56,519 [INFO] UI初始化完成
2025-01-26 11:04:56,523 [INFO] 加载了 1 个分类
2025-01-26 11:04:56,528 [INFO] 加载了 1 个批次
2025-01-26 11:04:56,537 [INFO] 系统初始化完成
2025-01-26 11:05:29,589 [INFO] 初始化商品编辑对话框: product_id=#水果-#0001-00002, is_merged_view=False
2025-01-26 11:05:29,763 [INFO] UI初始化完成
2025-01-26 11:05:29,780 [INFO] 所有信号连接完成
2025-01-26 11:05:29,793 [INFO] 信号连接完成
2025-01-26 11:05:29,797 [INFO] 编辑模式，开始加载商品数据: #水果-#0001-00002
2025-01-26 11:05:29,809 [INFO] 开始加载商品数据: #水果-#0001-00002
2025-01-26 11:05:29,840 [INFO] 商品数据加载完成
2025-01-26 11:08:12,609 [INFO] QSS file successfully loaded.
2025-01-26 11:08:12,610 [INFO] Found version patches to be applied.
2025-01-26 11:08:12,610 [INFO] Found application patches to be applied.
2025-01-26 11:08:12,622 [INFO] 启动库存管理系统
2025-01-26 11:08:12,622 [ERROR] 程序启动失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\app.py", line 38, in run_app
    window = MainWindow()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 79, in __init__
    self.refresh_timer.timeout.connect(self.auto_refresh)
AttributeError: 'MainWindow' object has no attribute 'auto_refresh'
2025-01-26 11:10:43,011 [INFO] QSS file successfully loaded.
2025-01-26 11:10:43,011 [INFO] Found version patches to be applied.
2025-01-26 11:10:43,012 [INFO] Found application patches to be applied.
2025-01-26 11:10:43,024 [INFO] 启动库存管理系统
2025-01-26 11:10:43,026 [INFO] 数据库表结构已更新
2025-01-26 11:10:43,043 [ERROR] 商品管理选项卡设置失败: 'MainWindow' object has no attribute 'show_add_dialog'
2025-01-26 11:10:43,043 [ERROR] UI初始化失败: 'MainWindow' object has no attribute 'show_add_dialog'
2025-01-26 11:10:43,044 [ERROR] 错误类型: AttributeError
2025-01-26 11:10:43,044 [ERROR] 错误信息: 'MainWindow' object has no attribute 'show_add_dialog'
2025-01-26 11:10:43,044 [ERROR] 堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 91, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 130, in init_ui
    self.setup_product_tab()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 238, in setup_product_tab
    self.add_btn.clicked.connect(self.show_add_dialog)
AttributeError: 'MainWindow' object has no attribute 'show_add_dialog'

2025-01-26 11:12:07,916 [INFO] QSS file successfully loaded.
2025-01-26 11:12:07,916 [INFO] Found version patches to be applied.
2025-01-26 11:12:07,917 [INFO] Found application patches to be applied.
2025-01-26 11:12:07,929 [INFO] 启动库存管理系统
2025-01-26 11:12:07,930 [INFO] 数据库表结构已更新
2025-01-26 11:12:08,178 [INFO] 批次管理选项卡设置完成
2025-01-26 11:12:08,183 [INFO] 状态栏初始化完成
2025-01-26 11:12:08,190 [INFO] UI初始化完成
2025-01-26 11:12:08,194 [INFO] 加载了 1 个分类
2025-01-26 11:12:08,199 [INFO] 加载了 1 个批次
2025-01-26 11:12:08,206 [INFO] 系统初始化完成
2025-01-26 11:15:05,686 [INFO] QSS file successfully loaded.
2025-01-26 11:15:05,686 [INFO] Found version patches to be applied.
2025-01-26 11:15:05,687 [INFO] Found application patches to be applied.
2025-01-26 11:15:05,699 [INFO] 启动库存管理系统
2025-01-26 11:15:05,701 [INFO] 数据库表结构已更新
2025-01-26 11:15:05,954 [INFO] 批次管理选项卡设置完成
2025-01-26 11:15:05,958 [INFO] 状态栏初始化完成
2025-01-26 11:15:05,965 [INFO] UI初始化完成
2025-01-26 11:15:05,968 [INFO] 加载了 1 个分类
2025-01-26 11:15:05,974 [INFO] 加载了 1 个批次
2025-01-26 11:15:05,982 [INFO] 系统初始化完成
2025-01-26 11:15:10,577 [INFO] 初始化商品编辑对话框: product_id=None, is_merged_view=False
2025-01-26 11:15:10,676 [INFO] UI初始化完成
2025-01-26 11:15:10,702 [INFO] 所有信号连接完成
2025-01-26 11:15:10,754 [INFO] 信号连接完成
2025-01-26 11:15:10,773 [INFO] 新建模式，触发分类改变事件
2025-01-26 11:15:10,818 [INFO] 设置合并复选框状态: False
2025-01-26 11:15:29,620 [INFO] 成功添加第 1 个商品: #水果-#0003-00001
2025-01-26 11:15:29,622 [INFO] 成功添加第 2 个商品: #水果-#0003-00002
2025-01-26 11:15:29,624 [INFO] 成功添加第 3 个商品: #水果-#0003-00003
2025-01-26 11:15:29,625 [INFO] 成功添加第 4 个商品: #水果-#0003-00004
2025-01-26 11:15:29,626 [INFO] 成功添加第 5 个商品: #水果-#0003-00005
2025-01-26 11:15:29,628 [INFO] 成功添加第 6 个商品: #水果-#0003-00006
2025-01-26 11:15:29,629 [INFO] 成功添加第 7 个商品: #水果-#0003-00007
2025-01-26 11:15:29,632 [INFO] 成功添加第 8 个商品: #水果-#0003-00008
2025-01-26 11:15:29,634 [INFO] 成功添加第 9 个商品: #水果-#0003-00009
2025-01-26 11:15:29,636 [INFO] 成功添加第 10 个商品: #水果-#0003-00010
2025-01-26 11:15:29,636 [INFO] 成功添加 10/10 个商品
2025-01-26 11:15:31,078 [INFO] 加载了 1 个分类
2025-01-26 11:15:31,081 [INFO] 加载了 1 个批次
2025-01-26 11:15:31,083 [INFO] 数据已刷新
2025-01-26 11:15:31,101 [INFO] 加载了 1 个分类
2025-01-26 11:15:31,105 [INFO] 加载了 1 个批次
2025-01-26 11:15:31,107 [INFO] 数据已刷新
2025-01-26 11:29:01,245 [INFO] QSS file successfully loaded.
2025-01-26 11:29:01,246 [INFO] Found version patches to be applied.
2025-01-26 11:29:01,246 [INFO] Found application patches to be applied.
2025-01-26 11:29:01,259 [INFO] 启动库存管理系统
2025-01-26 11:29:01,260 [ERROR] 错误类型: NameError
2025-01-26 11:29:01,260 [ERROR] 错误信息: name 'create_tables' is not defined
2025-01-26 11:29:01,260 [ERROR] 堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 87, in __init__
    create_tables()
NameError: name 'create_tables' is not defined

2025-01-26 11:33:15,771 [INFO] QSS file successfully loaded.
2025-01-26 11:33:15,771 [INFO] Found version patches to be applied.
2025-01-26 11:33:15,772 [INFO] Found application patches to be applied.
2025-01-26 11:33:15,784 [INFO] 启动库存管理系统
2025-01-26 11:33:15,786 [INFO] 数据库表结构已更新
2025-01-26 11:33:16,071 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,072 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,074 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,074 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,080 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,080 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,082 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,082 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,083 [ERROR] 更新状态栏信息失败: 'MainWindow' object has no attribute 'db_label'
2025-01-26 11:33:16,110 [ERROR] 批次管理选项卡设置失败: 'MainWindow' object has no attribute 'refresh_batch_data'
2025-01-26 11:33:16,110 [ERROR] UI初始化失败: 'MainWindow' object has no attribute 'refresh_batch_data'
2025-01-26 11:33:16,111 [ERROR] 错误类型: AttributeError
2025-01-26 11:33:16,111 [ERROR] 错误信息: 'MainWindow' object has no attribute 'refresh_batch_data'
2025-01-26 11:33:16,111 [ERROR] 堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 91, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 134, in init_ui
    self.setup_batch_tab()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 1272, in setup_batch_tab
    self.batch_toolbar.refresh_data.connect(self.refresh_batch_data)
AttributeError: 'MainWindow' object has no attribute 'refresh_batch_data'

2025-01-26 11:34:47,809 [INFO] QSS file successfully loaded.
2025-01-26 11:34:47,809 [INFO] Found version patches to be applied.
2025-01-26 11:34:47,809 [INFO] Found application patches to be applied.
2025-01-26 11:34:47,821 [INFO] 启动库存管理系统
2025-01-26 11:34:47,823 [INFO] 数据库表结构已更新
2025-01-26 11:34:48,130 [INFO] 批次管理选项卡设置完成
2025-01-26 11:34:48,131 [ERROR] UI初始化失败: 'MainWindow' object has no attribute 'setup_statusbar'
2025-01-26 11:34:48,132 [ERROR] 错误类型: AttributeError
2025-01-26 11:34:48,132 [ERROR] 错误信息: 'MainWindow' object has no attribute 'setup_statusbar'
2025-01-26 11:34:48,132 [ERROR] 堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 94, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 141, in init_ui
    self.setup_statusbar()
AttributeError: 'MainWindow' object has no attribute 'setup_statusbar'

2025-01-26 11:36:06,988 [INFO] QSS file successfully loaded.
2025-01-26 11:36:06,988 [INFO] Found version patches to be applied.
2025-01-26 11:36:06,988 [INFO] Found application patches to be applied.
2025-01-26 11:36:07,000 [INFO] 启动库存管理系统
2025-01-26 11:36:07,003 [INFO] 数据库表结构已更新
2025-01-26 11:36:07,330 [INFO] 批次管理选项卡设置完成
2025-01-26 11:36:07,335 [ERROR] UI初始化失败: 'MainWindow' object has no attribute 'load_stylesheet'
2025-01-26 11:36:07,336 [ERROR] 错误类型: AttributeError
2025-01-26 11:36:07,336 [ERROR] 错误信息: 'MainWindow' object has no attribute 'load_stylesheet'
2025-01-26 11:36:07,336 [ERROR] 堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 94, in __init__
    self.init_ui()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 146, in init_ui
    self.load_stylesheet()
AttributeError: 'MainWindow' object has no attribute 'load_stylesheet'

2025-01-26 11:37:37,670 [INFO] QSS file successfully loaded.
2025-01-26 11:37:37,670 [INFO] Found version patches to be applied.
2025-01-26 11:37:37,670 [INFO] Found application patches to be applied.
2025-01-26 11:37:37,682 [INFO] 启动库存管理系统
2025-01-26 11:37:37,683 [INFO] 数据库表结构已更新
2025-01-26 11:37:37,989 [INFO] 批次管理选项卡设置完成
2025-01-26 11:37:37,999 [INFO] QSS file successfully loaded.
2025-01-26 11:37:37,999 [INFO] UI初始化完成
2025-01-26 11:37:38,003 [ERROR] 加载商品列表失败
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 324, in load_products
    merge = self.merge_check.isChecked()
AttributeError: 'MainWindow' object has no attribute 'merge_check'
2025-01-26 11:39:33,729 [INFO] QSS file successfully loaded.
2025-01-26 11:39:33,729 [INFO] Found version patches to be applied.
2025-01-26 11:39:33,729 [INFO] Found application patches to be applied.
2025-01-26 11:39:33,742 [INFO] 启动库存管理系统
2025-01-26 11:39:33,744 [INFO] 数据库表结构已更新
2025-01-26 11:39:33,759 [ERROR] 错误类型: TypeError
2025-01-26 11:39:33,759 [ERROR] 错误信息: connect() failed between stateChanged(int) and merge_changed()
2025-01-26 11:39:33,759 [ERROR] 堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 234, in setup_product_tab
    self.product_toolbar = ProductToolBar()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\widgets\product_widgets.py", line 169, in __init__
    self.setup_ui()
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\widgets\product_widgets.py", line 199, in setup_ui
    self.merge_check.stateChanged.connect(self.merge_changed)
TypeError: connect() failed between stateChanged(int) and merge_changed()

2025-01-26 11:40:48,966 [INFO] QSS file successfully loaded.
2025-01-26 11:40:48,966 [INFO] Found version patches to be applied.
2025-01-26 11:40:48,967 [INFO] Found application patches to be applied.
2025-01-26 11:40:48,978 [INFO] 启动库存管理系统
2025-01-26 11:40:48,980 [INFO] 数据库表结构已更新
2025-01-26 11:40:49,286 [ERROR] 错误类型: TypeError
2025-01-26 11:40:49,286 [ERROR] 错误信息: ProductTable.load_products() missing 1 required positional argument: 'products'
2025-01-26 11:40:49,286 [ERROR] 堆栈跟踪:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\项目-自媒体素材中心\##我的定制工具+历史代码库\###代码孵化测试中心-Cursor_工作区\Inventory_Management_v1.6.4\Inventory_Management_v1.6.5-\inventory_management\gui\main_window.py", line 299, in refresh_data
    self.product_table.load_products()
TypeError: ProductTable.load_products() missing 1 required positional argument: 'products'

2025-01-26 11:42:24,035 [INFO] QSS file successfully loaded.
2025-01-26 11:42:24,036 [INFO] Found version patches to be applied.
2025-01-26 11:42:24,036 [INFO] Found application patches to be applied.
2025-01-26 11:42:24,048 [INFO] 启动库存管理系统
2025-01-26 11:42:24,049 [INFO] 数据库表结构已更新
2025-01-26 11:42:24,353 [INFO] 批次管理选项卡设置完成
2025-01-26 11:42:24,362 [INFO] QSS file successfully loaded.
2025-01-26 11:42:24,362 [INFO] UI初始化完成
2025-01-26 11:42:24,371 [INFO] 系统初始化完成
2025-01-26 11:42:35,043 [INFO] 初始化商品编辑对话框: product_id=None, is_merged_view=True
2025-01-26 11:42:35,068 [INFO] UI初始化完成
2025-01-26 11:42:35,068 [INFO] 所有信号连接完成
2025-01-26 11:42:35,068 [INFO] 信号连接完成
2025-01-26 11:42:35,068 [INFO] 新建模式，触发分类改变事件
2025-01-26 11:42:35,071 [INFO] 设置合并复选框状态: True
2025-01-26 11:42:44,932 [INFO] 成功添加第 1 个商品: #水果-#0004-00001
2025-01-26 11:42:44,935 [INFO] 成功添加第 2 个商品: #水果-#0004-00002
2025-01-26 11:42:44,936 [INFO] 成功添加第 3 个商品: #水果-#0004-00003
2025-01-26 11:42:44,938 [INFO] 成功添加第 4 个商品: #水果-#0004-00004
2025-01-26 11:42:44,939 [INFO] 成功添加第 5 个商品: #水果-#0004-00005
2025-01-26 11:42:44,942 [INFO] 成功添加第 6 个商品: #水果-#0004-00006
2025-01-26 11:42:44,943 [INFO] 成功添加第 7 个商品: #水果-#0004-00007
2025-01-26 11:42:44,945 [INFO] 成功添加第 8 个商品: #水果-#0004-00008
2025-01-26 11:42:44,946 [INFO] 成功添加第 9 个商品: #水果-#0004-00009
2025-01-26 11:42:44,948 [INFO] 成功添加第 10 个商品: #水果-#0004-00010
2025-01-26 11:42:44,948 [INFO] 成功添加 10/10 个商品
2025-01-26 11:45:29,584 [INFO] QSS file successfully loaded.
2025-01-26 11:45:29,584 [INFO] Found version patches to be applied.
2025-01-26 11:45:29,584 [INFO] Found application patches to be applied.
2025-01-26 11:45:29,597 [INFO] 启动库存管理系统
2025-01-26 11:45:29,599 [INFO] 数据库表结构已更新
2025-01-26 11:45:29,940 [INFO] 批次管理选项卡设置完成
2025-01-26 11:45:29,950 [INFO] QSS file successfully loaded.
2025-01-26 11:45:29,950 [INFO] UI初始化完成
2025-01-26 11:45:29,957 [INFO] 系统初始化完成
2025-01-26 11:45:47,924 [INFO] 初始化商品编辑对话框: product_id=#水果-#0001-00006, is_merged_view=False
2025-01-26 11:45:47,950 [INFO] UI初始化完成
2025-01-26 11:45:47,950 [INFO] 所有信号连接完成
2025-01-26 11:45:47,950 [INFO] 信号连接完成
2025-01-26 11:45:47,950 [INFO] 编辑模式，开始加载商品数据: #水果-#0001-00006
2025-01-26 11:45:47,951 [INFO] 开始加载商品数据: #水果-#0001-00006
2025-01-26 11:45:47,956 [INFO] 商品数据加载完成
2025-01-26 11:45:50,940 [INFO] 系统正常关闭
2025-01-26 11:53:21,423 [INFO] QSS file successfully loaded.
2025-01-26 11:53:21,423 [INFO] Found version patches to be applied.
2025-01-26 11:53:21,423 [INFO] Found application patches to be applied.
2025-01-26 11:53:21,435 [INFO] 启动库存管理系统
2025-01-26 11:53:21,437 [INFO] 数据库表结构已更新
2025-01-26 11:53:21,753 [INFO] 批次管理选项卡设置完成
2025-01-26 11:53:21,763 [INFO] QSS file successfully loaded.
2025-01-26 11:53:21,763 [INFO] UI初始化完成
2025-01-26 11:53:21,771 [INFO] 系统初始化完成
2025-01-26 11:53:30,149 [INFO] 系统正常关闭
2025-01-26 12:51:57,825 [INFO] QSS file successfully loaded.
2025-01-26 12:51:57,825 [INFO] Found version patches to be applied.
2025-01-26 12:51:57,825 [INFO] Found application patches to be applied.
2025-01-26 12:51:57,825 [WARNING] No QCoreApplication instance found. Application patches not applied. You have to call load_stylesheet function after instantiation of QApplication to take effect. 
2025-01-26 12:51:57,861 [INFO] 启动库存管理系统
2025-01-26 12:51:57,863 [INFO] 数据库表结构已更新
2025-01-26 12:51:57,925 [INFO] 批次管理选项卡设置完成
2025-01-26 12:51:57,937 [INFO] QSS file successfully loaded.
2025-01-26 12:51:57,937 [INFO] UI初始化完成
2025-01-26 12:51:57,945 [INFO] 系统初始化完成
2025-01-26 12:52:08,568 [INFO] 系统正常关闭
2025-01-26 13:03:27,406 [INFO] QSS file successfully loaded.
2025-01-26 13:03:27,407 [INFO] Found version patches to be applied.
2025-01-26 13:03:27,407 [INFO] Found application patches to be applied.
2025-01-26 13:03:27,407 [WARNING] No QCoreApplication instance found. Application patches not applied. You have to call load_stylesheet function after instantiation of QApplication to take effect. 
2025-01-26 13:03:27,438 [INFO] 启动库存管理系统
2025-01-26 13:03:27,440 [INFO] 数据库表结构已更新
2025-01-26 13:03:27,493 [INFO] 批次管理选项卡设置完成
2025-01-26 13:03:27,501 [INFO] QSS file successfully loaded.
2025-01-26 13:03:27,501 [INFO] UI初始化完成
2025-01-26 13:03:27,511 [INFO] 系统初始化完成
2025-01-26 13:03:29,273 [INFO] 系统正常关闭
2025-01-26 13:03:32,752 [INFO] QSS file successfully loaded.
2025-01-26 13:03:32,752 [INFO] Found version patches to be applied.
2025-01-26 13:03:32,752 [INFO] Found application patches to be applied.
2025-01-26 13:03:32,752 [WARNING] No QCoreApplication instance found. Application patches not applied. You have to call load_stylesheet function after instantiation of QApplication to take effect. 
2025-01-26 13:03:32,785 [INFO] 启动库存管理系统
2025-01-26 13:03:32,788 [INFO] 数据库表结构已更新
2025-01-26 13:03:32,836 [INFO] 批次管理选项卡设置完成
2025-01-26 13:03:32,844 [INFO] QSS file successfully loaded.
2025-01-26 13:03:32,845 [INFO] UI初始化完成
2025-01-26 13:03:32,852 [INFO] 系统初始化完成
2025-01-26 13:03:38,272 [INFO] 系统正常关闭
2025-01-26 13:03:44,788 [INFO] QSS file successfully loaded.
2025-01-26 13:03:44,789 [INFO] Found version patches to be applied.
2025-01-26 13:03:44,789 [INFO] Found application patches to be applied.
2025-01-26 13:03:44,789 [WARNING] No QCoreApplication instance found. Application patches not applied. You have to call load_stylesheet function after instantiation of QApplication to take effect. 
2025-01-26 13:03:44,822 [INFO] 启动库存管理系统
2025-01-26 13:03:44,824 [INFO] 数据库表结构已更新
2025-01-26 13:03:44,874 [INFO] 批次管理选项卡设置完成
2025-01-26 13:03:44,882 [INFO] QSS file successfully loaded.
2025-01-26 13:03:44,883 [INFO] UI初始化完成
2025-01-26 13:03:44,891 [INFO] 系统初始化完成
2025-01-26 13:07:11,744 [INFO] QSS file successfully loaded.
2025-01-26 13:07:11,744 [INFO] Found version patches to be applied.
2025-01-26 13:07:11,745 [INFO] Found application patches to be applied.
2025-01-26 13:07:11,745 [WARNING] No QCoreApplication instance found. Application patches not applied. You have to call load_stylesheet function after instantiation of QApplication to take effect. 
2025-01-26 13:07:11,777 [INFO] 启动库存管理系统
2025-01-26 13:07:11,778 [INFO] 数据库表结构已更新
2025-01-26 13:07:11,831 [INFO] 批次管理选项卡设置完成
2025-01-26 13:07:11,839 [INFO] QSS file successfully loaded.
2025-01-26 13:07:11,839 [INFO] UI初始化完成
2025-01-26 13:07:11,848 [INFO] 系统初始化完成
2025-01-26 13:08:32,328 [INFO] QSS file successfully loaded.
2025-01-26 13:08:32,328 [INFO] Found version patches to be applied.
2025-01-26 13:08:32,328 [INFO] Found application patches to be applied.
2025-01-26 13:08:32,328 [WARNING] No QCoreApplication instance found. Application patches not applied. You have to call load_stylesheet function after instantiation of QApplication to take effect. 
2025-01-26 13:08:32,360 [INFO] 启动库存管理系统
2025-01-26 13:08:32,362 [INFO] 数据库表结构已更新
2025-01-26 13:08:32,418 [INFO] 批次管理选项卡设置完成
2025-01-26 13:08:32,426 [INFO] QSS file successfully loaded.
2025-01-26 13:08:32,426 [INFO] UI初始化完成
2025-01-26 13:08:32,437 [INFO] 系统初始化完成
2025-01-26 13:08:33,896 [INFO] 系统正常关闭
