# 应用程序主模块 (app.py)

## 功能概述
`app.py` 是应用程序的主要模块,负责初始化和启动图形用户界面。该模块设置了应用程序的基本环境,包括界面样式、日志系统和数据库连接。基于 PyQt6 框架开发。

## 功能实现

### 1. 日志配置
```python
def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.FileHandler("app.log", encoding="utf-8"),
            logging.StreamHandler(sys.stdout)
        ]
    )
```

### 2. 主题设置
```python
def load_theme(theme_name="light"):
    """
    加载应用主题
    :param theme_name: 主题名称 ("light" 或 "dark")
    """
    try:
        # 加载主题文件
        theme_file = f"resources/themes/{theme_name}.qss"
        with open(theme_file, "r", encoding="utf-8") as f:
            return f.read()
    except Exception as e:
        logging.warning(f"加载主题失败: {str(e)}，使用默认主题")
        return ""
```

### 3. 应用程序运行
```python
def run_app():
    """
    运行应用程序
    :return: 退出代码
    """
    try:
        # 设置高DPI策略
        QGuiApplication.setHighDpiScaleFactorRoundingPolicy(
            Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
        )
        
        # 初始化数据库目录
        init_database_dir()
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置应用样式
        app.setStyle("Fusion")
        
        # 加载主题
        theme = load_theme(Config.get("theme", "light"))
        if theme:
            app.setStyleSheet(theme)
        
        # 设置高DPI支持
        app.setAttribute(Qt.AA_EnableHighDpiScaling)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps)
        
        # 创建主窗口
        window = MainWindow()
        window.show()
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        logging.exception("程序启动失败")
        QMessageBox.critical(None, "错误", f"程序启动失败: {str(e)}")
        return 1
```

## 界面设置

### 1. 应用程序样式
- 使用 Fusion 风格作为基础样式
- 自定义主题系统
  - 支持亮色/暗色主题
  - 主题动态切换
  - 自定义主题颜色
- 支持高 DPI 显示
- 现代化界面风格

### 2. 主题文件结构
```
resources/
└── themes/
    ├── light.qss    # 亮色主题
    ├── dark.qss     # 暗色主题
    └── custom.qss   # 自定义主题
```

### 3. 窗口初始化
- 创建主窗口实例
- 应用当前主题
- 显示主窗口
- 进入事件循环

## 错误处理

### 1. 异常捕获
```python
try:
    # 应用程序初始化和运行代码
except Exception as e:
    logging.exception("程序启动失败")
    QMessageBox.critical(None, "错误", f"程序启动失败: {str(e)}")
    return 1
```

### 2. 错误提示
- 记录详细的错误日志
- 显示用户友好的错误消息对话框
- 支持错误追踪和调试

## 依赖关系

### 1. PyQt6 组件
- QApplication
- QGuiApplication
- QMessageBox
- Qt (核心功能)

### 2. 自定义模块
- gui.main_window (MainWindow 类)
- database.db_utils (init_database_dir 函数)
- utils.config (Config 类)

## 使用方法

### 1. 作为主程序运行
```python
if __name__ == "__main__":
    sys.exit(run_app())
```

### 2. 作为模块导入
```python
from app import run_app
exit_code = run_app()
```

### 3. 主题切换
```python
# 切换到暗色主题
Config.set("theme", "dark")
theme = load_theme("dark")
app.setStyleSheet(theme)
```

## 注意事项
1. 数据库初始化的完整性
2. 界面样式的兼容性
3. 高DPI支持的正确性
4. 错误处理的可靠性
5. 资源清理的及时性
6. PyQt6 特性的正确使用
7. 主题系统的灵活性
8. 国际化支持的完整性
9. 在创建 QApplication 实例前设置高DPI策略
10. 确保主题文件存在且格式正确 