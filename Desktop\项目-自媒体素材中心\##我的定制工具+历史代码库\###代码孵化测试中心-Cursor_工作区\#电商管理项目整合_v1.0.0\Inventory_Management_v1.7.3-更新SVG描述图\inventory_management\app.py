import sys
import os
import logging
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QCoreApplication
from gui.main_window import MainWindow
from utils.config import Config
from utils.error_handler import ErrorHandler
from utils.theme_manager import ThemeManager


def setup_logging():
    """配置日志系统"""
    if not os.path.exists("logs"):
        os.makedirs("logs")

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
        handlers=[
            logging.FileHandler("logs/app.log", encoding="utf-8"),
            logging.StreamHandler(sys.stdout),
        ],
    )


def load_theme(theme_name="light"):
    """
    加载应用主题
    :param theme_name: 主题名称 ("light" 或 "dark")
    """
    try:
        theme_file = os.path.join("resources", "themes", f"{theme_name}.qss")
        if os.path.exists(theme_file):
            with open(theme_file, "r", encoding="utf-8") as f:
                return f.read()
        else:
            logging.warning(f"主题文件不存在: {theme_file}")
            return ""
    except Exception as e:
        logging.warning(f"加载主题失败: {str(e)}")
        return ""


def run_app():
    """运行应用程序"""
    try:
        # 初始化配置
        config = Config.instance()

        # 设置高 DPI 支持
        QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_DontUseNativeMenuBar)
        QCoreApplication.setAttribute(Qt.ApplicationAttribute.AA_Use96Dpi)

        # 创建应用实例
        app = QApplication(sys.argv)

        # 设置应用程序信息
        app.setApplicationName("库存管理系统")
        app.setApplicationVersion("1.6.5")
        app.setOrganizationName("MyCompany")
        app.setOrganizationDomain("mycompany.com")

        # 加载主题
        theme = config.get("ui.theme", "dark")
        ThemeManager.apply_theme(app, theme)

        # 创建主窗口
        window = MainWindow()
        window.resize(1720, 1200)  # 设置默认窗口尺寸
        window.show()

        # 运行应用
        return app.exec()

    except Exception as e:
        logging.exception("程序启动失败")
        return 1


if __name__ == "__main__":
    sys.exit(run_app())
