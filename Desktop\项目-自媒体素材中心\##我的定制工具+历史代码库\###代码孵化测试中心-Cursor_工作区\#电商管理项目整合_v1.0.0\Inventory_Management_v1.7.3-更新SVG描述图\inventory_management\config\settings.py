"""
现代化配置管理
使用Pydantic Settings进行类型安全的配置管理
"""
from __future__ import annotations
from typing import List, Tuple, Optional
from pathlib import Path
from pydantic import BaseSettings, Field, validator
from pydantic.types import DirectoryPath


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    url: str = Field(default="sqlite:///inventory.db", env="DATABASE_URL")
    echo: bool = Field(default=False, env="DATABASE_ECHO")
    pool_size: int = Field(default=5, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=10, env="DATABASE_MAX_OVERFLOW")
    
    # 备份设置
    backup_enabled: bool = Field(default=True, env="DATABASE_BACKUP_ENABLED")
    backup_dir: DirectoryPath = Field(default=Path("backup"), env="DATABASE_BACKUP_DIR")
    backup_interval_hours: int = Field(default=24, env="DATABASE_BACKUP_INTERVAL")
    max_backup_files: int = Field(default=30, env="DATABASE_MAX_BACKUP_FILES")
    
    class Config:
        env_prefix = "DB_"


class UISettings(BaseSettings):
    """界面配置"""
    theme: str = Field(default="dark", env="UI_THEME")
    language: str = Field(default="zh_CN", env="UI_LANGUAGE")
    font_family: str = Field(default="Microsoft YaHei", env="UI_FONT_FAMILY")
    font_size: int = Field(default=12, env="UI_FONT_SIZE", ge=8, le=24)
    
    # 窗口设置
    window_width: int = Field(default=1200, env="UI_WINDOW_WIDTH", ge=800)
    window_height: int = Field(default=800, env="UI_WINDOW_HEIGHT", ge=600)
    window_x: int = Field(default=100, env="UI_WINDOW_X", ge=0)
    window_y: int = Field(default=100, env="UI_WINDOW_Y", ge=0)
    
    # 表格设置
    table_row_height: int = Field(default=30, env="UI_TABLE_ROW_HEIGHT", ge=20, le=50)
    table_page_size: int = Field(default=100, env="UI_TABLE_PAGE_SIZE", ge=10, le=1000)
    
    @validator('theme')
    def validate_theme(cls, v):
        allowed_themes = ['light', 'dark', 'auto']
        if v not in allowed_themes:
            raise ValueError(f'Theme must be one of {allowed_themes}')
        return v
    
    @property
    def window_size(self) -> Tuple[int, int]:
        return (self.window_width, self.window_height)
    
    @property
    def window_position(self) -> Tuple[int, int]:
        return (self.window_x, self.window_y)
    
    class Config:
        env_prefix = "UI_"


class ImageSettings(BaseSettings):
    """图片处理配置"""
    max_size: int = Field(default=1024, env="IMAGE_MAX_SIZE", ge=256, le=4096)
    quality: int = Field(default=85, env="IMAGE_QUALITY", ge=1, le=100)
    thumbnail_width: int = Field(default=100, env="IMAGE_THUMBNAIL_WIDTH", ge=50, le=300)
    thumbnail_height: int = Field(default=100, env="IMAGE_THUMBNAIL_HEIGHT", ge=50, le=300)
    preview_width: int = Field(default=800, env="IMAGE_PREVIEW_WIDTH", ge=400, le=1920)
    preview_height: int = Field(default=800, env="IMAGE_PREVIEW_HEIGHT", ge=400, le=1080)
    
    # 支持的图片格式
    supported_formats: List[str] = Field(
        default=["jpg", "jpeg", "png", "bmp", "gif", "webp"],
        env="IMAGE_SUPPORTED_FORMATS"
    )
    
    # 存储路径
    storage_dir: DirectoryPath = Field(default=Path("images"), env="IMAGE_STORAGE_DIR")
    cache_dir: DirectoryPath = Field(default=Path("cache/images"), env="IMAGE_CACHE_DIR")
    
    @property
    def thumbnail_size(self) -> Tuple[int, int]:
        return (self.thumbnail_width, self.thumbnail_height)
    
    @property
    def preview_size(self) -> Tuple[int, int]:
        return (self.preview_width, self.preview_height)
    
    class Config:
        env_prefix = "IMAGE_"


class ProductSettings(BaseSettings):
    """商品配置"""
    code_prefix: str = Field(default="P", env="PRODUCT_CODE_PREFIX")
    code_length: int = Field(default=8, env="PRODUCT_CODE_LENGTH", ge=4, le=20)
    default_unit: str = Field(default="个", env="PRODUCT_DEFAULT_UNIT")
    default_status: str = Field(default="在库", env="PRODUCT_DEFAULT_STATUS")
    
    # 批量操作设置
    batch_size: int = Field(default=100, env="PRODUCT_BATCH_SIZE", ge=10, le=1000)
    import_chunk_size: int = Field(default=50, env="PRODUCT_IMPORT_CHUNK_SIZE", ge=10, le=500)
    
    # 验证设置
    name_max_length: int = Field(default=255, env="PRODUCT_NAME_MAX_LENGTH", ge=10, le=500)
    category_max_length: int = Field(default=100, env="PRODUCT_CATEGORY_MAX_LENGTH", ge=5, le=200)
    
    class Config:
        env_prefix = "PRODUCT_"


class BatchSettings(BaseSettings):
    """批次配置"""
    code_prefix: str = Field(default="B", env="BATCH_CODE_PREFIX")
    code_length: int = Field(default=8, env="BATCH_CODE_LENGTH", ge=4, le=20)
    default_status: str = Field(default="活跃", env="BATCH_DEFAULT_STATUS")
    
    # 分页设置
    items_per_page: int = Field(default=50, env="BATCH_ITEMS_PER_PAGE", ge=10, le=200)
    
    class Config:
        env_prefix = "BATCH_"


class ExportSettings(BaseSettings):
    """导出配置"""
    default_format: str = Field(default="xlsx", env="EXPORT_DEFAULT_FORMAT")
    encoding: str = Field(default="utf-8", env="EXPORT_ENCODING")
    date_format: str = Field(default="%Y-%m-%d %H:%M:%S", env="EXPORT_DATE_FORMAT")
    
    # 导出路径
    output_dir: DirectoryPath = Field(default=Path("exports"), env="EXPORT_OUTPUT_DIR")
    
    # Excel设置
    excel_sheet_name: str = Field(default="数据", env="EXPORT_EXCEL_SHEET_NAME")
    excel_max_rows: int = Field(default=1000000, env="EXPORT_EXCEL_MAX_ROWS", ge=1000)
    
    @validator('default_format')
    def validate_format(cls, v):
        allowed_formats = ['xlsx', 'csv', 'json']
        if v not in allowed_formats:
            raise ValueError(f'Format must be one of {allowed_formats}')
        return v
    
    class Config:
        env_prefix = "EXPORT_"


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        env="LOG_FORMAT"
    )
    date_format: str = Field(default="%Y-%m-%d %H:%M:%S", env="LOG_DATE_FORMAT")
    
    # 文件日志
    file_enabled: bool = Field(default=True, env="LOG_FILE_ENABLED")
    file_path: Path = Field(default=Path("logs/app.log"), env="LOG_FILE_PATH")
    file_max_size: int = Field(default=10*1024*1024, env="LOG_FILE_MAX_SIZE")  # 10MB
    file_backup_count: int = Field(default=5, env="LOG_FILE_BACKUP_COUNT")
    
    # 控制台日志
    console_enabled: bool = Field(default=True, env="LOG_CONSOLE_ENABLED")
    
    @validator('level')
    def validate_level(cls, v):
        allowed_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in allowed_levels:
            raise ValueError(f'Log level must be one of {allowed_levels}')
        return v.upper()
    
    class Config:
        env_prefix = "LOG_"


class AppSettings(BaseSettings):
    """应用程序主配置"""
    name: str = Field(default="库存管理系统", env="APP_NAME")
    version: str = Field(default="1.8.0", env="APP_VERSION")
    description: str = Field(default="现代化库存管理系统", env="APP_DESCRIPTION")
    
    # 环境设置
    environment: str = Field(default="production", env="APP_ENVIRONMENT")
    debug: bool = Field(default=False, env="APP_DEBUG")
    
    # 数据目录
    data_dir: DirectoryPath = Field(default=Path("data"), env="APP_DATA_DIR")
    config_dir: DirectoryPath = Field(default=Path("config"), env="APP_CONFIG_DIR")
    
    # 子配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    ui: UISettings = Field(default_factory=UISettings)
    image: ImageSettings = Field(default_factory=ImageSettings)
    product: ProductSettings = Field(default_factory=ProductSettings)
    batch: BatchSettings = Field(default_factory=BatchSettings)
    export: ExportSettings = Field(default_factory=ExportSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    
    @validator('environment')
    def validate_environment(cls, v):
        allowed_envs = ['development', 'testing', 'production']
        if v not in allowed_envs:
            raise ValueError(f'Environment must be one of {allowed_envs}')
        return v
    
    def create_directories(self) -> None:
        """创建必要的目录"""
        directories = [
            self.data_dir,
            self.config_dir,
            self.image.storage_dir,
            self.image.cache_dir,
            self.database.backup_dir,
            self.export.output_dir,
            self.logging.file_path.parent,
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局设置实例
settings = AppSettings()

# 确保目录存在
settings.create_directories()
