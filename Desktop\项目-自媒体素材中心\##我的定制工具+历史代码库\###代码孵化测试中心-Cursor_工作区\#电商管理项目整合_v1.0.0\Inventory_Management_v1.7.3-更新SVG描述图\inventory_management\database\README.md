# 数据库模块

## 概述
数据库模块提供了系统的数据存储和管理功能，基于 SQLite3 实现，支持多数据库切换和性能优化。

## 数据库设计

### 1. 产品表 (products)
```sql
CREATE TABLE products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE,
    category TEXT,
    description TEXT,
    image_path TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 批次表 (batches)
```sql
CREATE TABLE batches (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE,
    name TEXT,
    status TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 产品批次关联表 (product_batch)
```sql
CREATE TABLE product_batch (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER,
    batch_id INTEGER,
    quantity INTEGER DEFAULT 0,
    price DECIMAL(10,2),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (batch_id) REFERENCES batches(id)
);
```

### 4. 交易记录表 (transactions)
```sql
CREATE TABLE transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER,
    batch_id INTEGER,
    type TEXT,
    quantity INTEGER,
    price DECIMAL(10,2),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (batch_id) REFERENCES batches(id)
);
```

## 核心功能

### 1. 数据库管理
- 数据库创建
- 数据库切换
- 数据库备份
- 数据库恢复

### 2. 数据操作
- CRUD操作
- 批量处理
- 事务管理
- 数据验证

### 3. 性能优化
- 索引优化
- 查询优化
- 连接池
- 缓存机制

### 4. 数据维护
- 数据备份
- 数据清理
- 完整性检查
- 性能监控

## 技术实现

### 1. 连接管理
```python
class DatabaseManager:
    def __init__(self):
        self.pool = ConnectionPool(max_connections=10)
        
    def get_connection(self):
        return self.pool.get_connection()
```

### 2. 查询执行
```python
def execute_query(self, query, params=None):
    with self.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(query, params or ())
        return cursor.fetchall()
```

### 3. 事务处理
```python
def transaction(self):
    conn = self.get_connection()
    try:
        yield conn
        conn.commit()
    except:
        conn.rollback()
        raise
    finally:
        conn.close()
```

## 使用示例

### 1. 基本查询
```python
def get_product(product_id):
    query = "SELECT * FROM products WHERE id = ?"
    return db.execute_query(query, (product_id,))
```

### 2. 事务操作
```python
def update_batch(batch_id, data):
    with db.transaction() as conn:
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE batches SET name = ? WHERE id = ?",
            (data['name'], batch_id)
        )
```

### 3. 批量处理
```python
def bulk_insert_products(products):
    query = "INSERT INTO products (name, code) VALUES (?, ?)"
    with db.transaction() as conn:
        cursor = conn.cursor()
        cursor.executemany(query, products)
```

## 性能优化

### 1. 索引设计
```sql
CREATE INDEX idx_products_code ON products(code);
CREATE INDEX idx_batches_code ON batches(code);
CREATE INDEX idx_transactions_date ON transactions(created_at);
```

### 2. 查询优化
```sql
-- 使用子查询优化
SELECT p.*, 
       (SELECT COUNT(*) FROM product_batch WHERE product_id = p.id) as batch_count
FROM products p
WHERE p.category = ?;
```

### 3. 连接优化
```sql
-- 使用 JOIN 优化
SELECT p.*, b.name as batch_name
FROM products p
LEFT JOIN product_batch pb ON p.id = pb.product_id
LEFT JOIN batches b ON pb.batch_id = b.id
WHERE p.category = ?;
```

## 注意事项
1. 使用参数化查询
2. 正确处理事务
3. 及时释放连接
4. 定期备份数据
5. 监控性能指标
6. 处理并发访问
7. 维护数据一致性
8. 注意 SQL 注入 