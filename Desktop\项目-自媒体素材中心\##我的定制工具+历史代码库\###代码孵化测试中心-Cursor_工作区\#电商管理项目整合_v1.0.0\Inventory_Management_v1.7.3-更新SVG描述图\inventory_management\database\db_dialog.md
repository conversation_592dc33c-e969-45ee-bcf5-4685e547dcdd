# 数据库管理对话框 (db_dialog.py)

## 功能概述
`db_dialog.py` 实现了一个图形化的数据库管理界面，提供了数据库的创建、切换、备份和删除等操作的用户界面。该模块基于 PyQt5 框架开发，提供了友好的交互体验。

## 类说明

### DatabaseDialog 类
继承自 `QDialog`，实现数据库管理的图形界面。

#### 初始化
```python
def __init__(self, parent=None):
```
- 功能：初始化对话框
- 参数：
  - parent: 父窗口对象（可选）
- 操作：
  - 初始化UI组件
  - 加载数据库列表

#### UI 组件
界面包含以下主要元素：
1. 说明标签
2. 数据库列表（QListWidget）
3. 操作按钮组：
   - 创建新数据库
   - 切换到选中数据库
   - 备份选中数据库
   - 删除选中数据库
4. 关闭按钮

#### 主要方法

1. **初始化UI**
```python
def init_ui(self):
```
- 功能：设置窗口布局和组件
- 组件布局：
  - 垂直布局（主布局）
  - 水平布局（按钮区域）
  - 左右分组的按钮布局

2. **加载数据库列表**
```python
def load_databases(self):
```
- 功能：刷新数据库列表显示
- 操作：
  - 清空现有列表
  - 获取并显示所有数据库
  - 标记当前使用的数据库

3. **获取选中数据库**
```python
def get_selected_database(self):
```
- 功能：获取列表中当前选中的数据库名称
- 返回：数据库名称或 None
- 特点：自动处理"(当前使用)"标记

4. **创建数据库**
```python
def create_database(self):
```
- 功能：创建新数据库
- 流程：
  1. 弹出输入对话框
  2. 验证输入
  3. 创建数据库
  4. 刷新列表
  5. 显示结果消息

5. **切换数据库**
```python
def switch_to_selected_database(self):
```
- 功能：切换到选中的数据库
- 操作：
  1. 获取选中数据库
  2. 执行切换操作
  3. 刷新界面
  4. 通知主窗口更新数据

6. **备份数据库**
```python
def backup_selected_database(self):
```
- 功能：备份选中的数据库
- 操作：
  1. 执行备份操作
  2. 刷新列表
  3. 显示成功消息

7. **删除数据库**
```python
def delete_selected_database(self):
```
- 功能：删除选中的数据库
- 安全措施：
  - 双重确认机制
  - 警告提示
- 操作流程：
  1. 第一次确认
  2. 最终确认
  3. 执行删除
  4. 刷新列表

## 依赖关系
- PyQt5 组件：
  - QDialog
  - QVBoxLayout
  - QHBoxLayout
  - QLabel
  - QPushButton
  - QListWidget
  - QMessageBox
  - QInputDialog
- 自定义模块：
  - database.db_utils

## 错误处理
- 使用 try-except 块处理所有操作
- 针对不同类型的错误显示不同的提示信息
- 使用 logging 模块记录详细错误信息
- 用户友好的错误提示对话框

## 界面特点
1. 清晰的视觉层次
2. 直观的操作按钮
3. 危险操作（删除）使用红色标识
4. 双重确认机制保护重要操作
5. 实时反馈操作结果

## 使用示例
```python
# 创建并显示数据库管理对话框
dialog = DatabaseDialog(parent_window)
dialog.exec_()
```

## 注意事项
1. 删除操作需要双重确认
2. 所有操作都有适当的错误处理
3. 界面会自动刷新以反映最新状态
4. 切换数据库后会自动刷新主窗口数据
5. 数据库名称不需要包含 .db 后缀 