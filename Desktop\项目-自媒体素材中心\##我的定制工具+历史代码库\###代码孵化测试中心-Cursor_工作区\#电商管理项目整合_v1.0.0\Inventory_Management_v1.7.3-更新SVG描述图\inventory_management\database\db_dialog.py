import os
import logging
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QListWidget,
    QMessageBox,
    QInputDialog,
)
from database.db_utils import (
    get_database_list,
    create_new_database,
    switch_database,
    delete_database,
    backup_database,
    get_current_database,
)


class DatabaseDialog(QDialog):
    """数据库管理对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("数据库管理")
        self.resize(400, 300)
        self.init_ui()
        self.load_databases()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 当前数据库标签
        self.current_db_label = QLabel()
        layout.addWidget(self.current_db_label)

        # 数据库列表
        self.db_list = QListWidget()
        layout.addWidget(self.db_list)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 新建按钮
        self.new_button = QPushButton("新建")
        self.new_button.clicked.connect(self.create_database)
        button_layout.addWidget(self.new_button)

        # 切换按钮
        self.switch_button = QPushButton("切换")
        self.switch_button.clicked.connect(self.switch_to_database)
        button_layout.addWidget(self.switch_button)

        # 备份按钮
        self.backup_button = QPushButton("备份")
        self.backup_button.clicked.connect(self.backup_database)
        button_layout.addWidget(self.backup_button)

        # 删除按钮
        self.delete_button = QPushButton("删除")
        self.delete_button.clicked.connect(self.delete_database)
        button_layout.addWidget(self.delete_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_databases(self):
        """加载数据库列表"""
        try:
            # 获取当前数据库
            current_db = get_current_database()
            self.current_db_label.setText(f"当前数据库: {current_db}")

            # 获取数据库列表
            databases = get_database_list()
            self.db_list.clear()
            self.db_list.addItems(databases)

            # 选中当前数据库
            for i in range(self.db_list.count()):
                if self.db_list.item(i).text() == current_db:
                    self.db_list.setCurrentRow(i)
                    break

        except Exception as e:
            logging.exception("加载数据库列表失败")
            QMessageBox.critical(self, "错误", f"加载数据库列表失败: {str(e)}")

    def create_database(self):
        """创建新数据库"""
        try:
            name, ok = QInputDialog.getText(self, "新建数据库", "请输入数据库名称:")

            if ok and name:
                create_new_database(name)
                self.load_databases()
                QMessageBox.information(self, "成功", "数据库创建成功")

        except Exception as e:
            logging.exception("创建数据库失败")
            QMessageBox.critical(self, "错误", f"创建数据库失败: {str(e)}")

    def switch_to_database(self):
        """切换数据库"""
        try:
            current_item = self.db_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "警告", "请选择要切换的数据库")
                return

            db_name = current_item.text()
            switch_database(db_name)
            self.load_databases()
            QMessageBox.information(self, "成功", f"已切换到数据库: {db_name}")

        except Exception as e:
            logging.exception("切换数据库失败")
            QMessageBox.critical(self, "错误", f"切换数据库失败: {str(e)}")

    def backup_database(self):
        """备份数据库"""
        try:
            current_item = self.db_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "警告", "请选择要备份的数据库")
                return

            db_name = current_item.text()
            backup_path = backup_database(db_name)
            QMessageBox.information(self, "成功", f"数据库已备份到: {backup_path}")

        except Exception as e:
            logging.exception("备份数据库失败")
            QMessageBox.critical(self, "错误", f"备份数据库失败: {str(e)}")

    def delete_database(self):
        """删除数据库"""
        try:
            current_item = self.db_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "警告", "请选择要删除的数据库")
                return

            db_name = current_item.text()

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除数据库 {db_name} 吗？此操作不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                delete_database(db_name)
                self.load_databases()
                QMessageBox.information(self, "成功", "数据库删除成功")

        except Exception as e:
            logging.exception("删除数据库失败")
            QMessageBox.critical(self, "错误", f"删除数据库失败: {str(e)}")
