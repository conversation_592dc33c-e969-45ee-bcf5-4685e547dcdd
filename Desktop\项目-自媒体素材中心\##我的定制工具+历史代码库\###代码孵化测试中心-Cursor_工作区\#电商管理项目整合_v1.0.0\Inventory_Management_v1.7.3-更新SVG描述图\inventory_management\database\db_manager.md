# 数据库管理器 (db_manager.py)

## 功能概述
`db_manager.py` 提供了数据库管理的核心功能，包括数据库的创建、切换、备份和删除等操作。该模块封装了所有与数据库管理相关的高级操作，提供了一个统一的接口。

## 类说明

### DatabaseManager 类

#### 初始化
```python
def __init__(self):
```
- 功能：初始化数据库管理器
- 操作：获取当前使用的数据库路径

#### 主要方法

1. **列出数据库**
```python
def list_databases(self) -> List[str]:
```
- 功能：获取所有可用的数据库列表
- 返回：数据库名称列表

2. **创建数据库**
```python
def create_database(self, name: str) -> str:
```
- 功能：创建新的数据库
- 参数：
  - name: 数据库名称
- 返回：新创建的数据库路径
- 异常：创建失败时抛出异常

3. **删除数据库**
```python
def remove_database(self, name: str) -> bool:
```
- 功能：删除指定的数据库
- 参数：
  - name: 要删除的数据库名称
- 返回：删除是否成功
- 异常：删除失败时抛出异常

4. **切换数据库**
```python
def switch_to_database(self, db_path: str) -> bool:
```
- 功能：切换到指定的数据库
- 参数：
  - db_path: 目标数据库路径
- 返回：切换是否成功
- 异常：切换失败时抛出异常

5. **备份数据库**
```python
def backup_database(self, db_name: str, backup_name: Optional[str] = None) -> str:
```
- 功能：备份指定的数据库
- 参数：
  - db_name: 要备份的数据库名称
  - backup_name: 备份文件名（可选）
- 返回：备份文件路径
- 异常：备份失败时抛出异常

6. **获取当前数据库**
```python
def get_current_db(self) -> str:
```
- 功能：获取当前使用的数据库路径
- 返回：当前数据库路径

7. **获取数据库大小**
```python
def get_database_size(self, db_name: str) -> str:
```
- 功能：获取指定数据库的文件大小
- 参数：
  - db_name: 数据库名称
- 返回：人类可读的大小字符串（如："1.5 MB"）
- 异常：获取失败时返回"未知"

## 依赖关系
- 依赖 `db_utils.py` 中的底层数据库操作函数
- 使用 Python 标准库：
  - os：文件系统操作
  - logging：日志记录
  - typing：类型注解

## 错误处理
- 所有方法都包含完整的错误处理
- 使用 logging 模块记录错误信息
- 在发生错误时抛出异常，由上层调用者处理

## 使用示例
```python
# 创建数据库管理器实例
db_manager = DatabaseManager()

# 创建新数据库
new_db_path = db_manager.create_database("new_database")

# 切换到新数据库
db_manager.switch_to_database(new_db_path)

# 备份数据库
backup_path = db_manager.backup_database("new_database", "backup_001")

# 获取数据库大小
size = db_manager.get_database_size("new_database")
```

## 注意事项
1. 所有数据库操作都会记录日志
2. 文件大小显示会自动选择合适的单位（B、KB、MB、GB、TB）
3. 数据库切换操作会更新当前数据库路径
4. 所有操作都有适当的异常处理 