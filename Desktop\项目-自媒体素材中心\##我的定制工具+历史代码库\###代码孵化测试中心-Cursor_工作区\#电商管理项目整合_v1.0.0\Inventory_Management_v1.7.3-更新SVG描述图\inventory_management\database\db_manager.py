import os
import logging
from typing import List, Optional
from .db_utils import (
    DATABASE_DIR,
    get_database_list,
    create_new_database,
    delete_database,
    switch_database,
    backup_database,
    get_current_database,
)


class DatabaseManager:
    def __init__(self):
        """初始化数据库管理器"""
        self.current_database = get_current_database()

    def list_databases(self) -> List[str]:
        """获取所有可用的数据库列表"""
        return get_database_list()

    def create_database(self, name: str) -> str:
        """创建新数据库"""
        try:
            db_path = create_new_database(name)
            logging.info(f"成功创建数据库: {name}")
            return db_path
        except Exception as e:
            logging.error(f"创建数据库失败: {str(e)}")
            raise

    def remove_database(self, name: str) -> bool:
        """删除数据库"""
        try:
            delete_database(name)
            logging.info(f"成功删除数据库: {name}")
            return True
        except Exception as e:
            logging.error(f"删除数据库失败: {str(e)}")
            raise

    def switch_to_database(self, db_path: str) -> bool:
        """切换到指定数据库"""
        try:
            success = switch_database(db_path)
            if success:
                self.current_database = db_path
                logging.info(f"成功切换到数据库: {db_path}")
            return success
        except Exception as e:
            logging.error(f"切换数据库失败: {str(e)}")
            raise

    def backup_database(self, db_name: str, backup_name: Optional[str] = None) -> str:
        """备份数据库"""
        try:
            backup_file = backup_database(db_name, backup_name)
            logging.info(f"成功备份数据库 {db_name} 到 {backup_file}")
            return backup_file
        except Exception as e:
            logging.error(f"备份数据库失败: {str(e)}")
            raise

    def get_current_db(self) -> str:
        """获取当前使用的数据库路径"""
        return self.current_database

    def get_database_size(self, db_name: str) -> str:
        """获取数据库文件大小"""
        try:
            db_path = os.path.join(DATABASE_DIR, db_name)
            size_bytes = os.path.getsize(db_path)
            # 转换为人类可读的格式
            for unit in ["B", "KB", "MB", "GB"]:
                if size_bytes < 1024:
                    return f"{size_bytes:.2f} {unit}"
                size_bytes /= 1024
            return f"{size_bytes:.2f} TB"
        except Exception as e:
            logging.error(f"获取数据库大小失败: {str(e)}")
            return "未知"
