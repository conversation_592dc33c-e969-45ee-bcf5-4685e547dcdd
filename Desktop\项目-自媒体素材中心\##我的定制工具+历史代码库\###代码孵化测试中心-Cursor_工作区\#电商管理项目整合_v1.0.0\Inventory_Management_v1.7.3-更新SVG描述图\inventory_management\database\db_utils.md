# 数据库工具模块 (db_utils.py)

## 功能概述
`db_utils.py` 是整个系统的数据库核心模块，提供了完整的数据库操作接口，包括数据库的创建、管理、数据操作等功能。该模块基于 SQLite3，实现了完整的事务支持和错误处理，并提供了性能优化和数据缓存机制。

## 常量定义

```python
APP_NAME = "inventory_management"
APP_DATA = os.path.join(os.environ["APPDATA"], APP_NAME)  # Windows
DATABASE_DIR = os.path.join(APP_DATA, "database")
DEFAULT_DATABASE = os.path.join(DATABASE_DIR, "inventory.db")
CURRENT_DATABASE = os.environ.get("DB_PATH", DEFAULT_DATABASE)
```

## 核心功能

### 1. 数据库管理

#### 初始化和配置
```python
def init_database_dir():
```
- 功能：初始化数据库目录
- 操作：创建必要的目录结构
- 特点：自动创建缺失目录

```python
def create_tables():
```
- 功能：创建数据库表结构
- 主要表：
  - products：商品信息表
  - batches：批次信息表
  - product_batches：商品批次关联表
  - product_images：商品图片表
  - transactions：交易记录表
- 特点：支持表结构自动升级

#### 数据库操作
```python
def get_database_list():
```
- 功能：获取所有可用数据库列表
- 特点：
  - 自动验证数据库有效性
  - 显示数据库大小和状态
  - 支持数据库排序

```python
def create_new_database(db_name):
```
- 功能：创建新数据库
- 参数：db_name - 数据库名称
- 返回：数据库路径
- 特点：自动初始化表结构

```python
def switch_database(db_path):
```
- 功能：切换当前数据库
- 特点：
  - 自动创建缺失的表
  - 自动更新表结构
  - 处理数据库迁移
  - 维护连接池

### 2. 数据操作

#### 商品管理
```python
def add_product(product_data):
```
- 功能：添加新商品
- 参数：product_data - 商品信息字典
- 特点：
  - 自动生成商品ID
  - 计算财务数据
  - 处理图片关联
  - 事务保护

```python
def update_product(product_id, product_data):
```
- 功能：更新商品信息
- 特点：
  - 自动更新时间戳
  - 重新计算财务数据
  - 保持数据一致性
  - 优化更新性能

```python
def delete_product(product_id):
```
- 功能：删除商品
- 特点：
  - 级联删除相关数据
  - 清理关联资源
  - 事务保护
  - 备份删除数据

#### 批次管理
```python
@execute_transaction
def add_batch(batch_name, remarks=None):
```
- 功能：创建新批次
- 装饰器：事务保护
- 特点：自动生成批次编号

```python
@execute_transaction
def update_batch(batch_id, batch_name=None, remarks=None):
```
- 功能：更新批次信息
- 装饰器：事务保护
- 特点：支持部分字段更新

### 3. 查询功能

```python
def get_product(product_id):
```
- 功能：获取商品详细信息
- 特点：支持缓存机制

```python
def get_product_batches(product_id):
```
- 功能：获取商品相关批次
- 特点：优化关联查询

```python
def get_financial_stats():
```
- 功能：获取财务统计信息
- 返回：
  - 总成本
  - 总收入
  - 利润率等
- 特点：支持实时统计

### 4. 数据导出

```python
def export_to_excel(filename):
```
- 功能：导出数据到Excel
- 包含内容：
  - 商品信息
  - 批次信息
  - 财务数据
- 特点：支持自定义导出模板

```python
def export_to_csv(filename):
```
- 功能：导出数据到CSV格式
- 特点：支持大数据量导出

### 5. 工具函数

#### ID生成
```python
def generate_product_id(category, type_num=None, batch_num=None, cursor=None):
```
- 功能：生成商品ID
- 格式：`类别-类型编号-批次编号`
- 特点：
  - 自动递增编号
  - 保证唯一性
  - 支持自定义规则

#### 事务处理
```python
def execute_transaction(func):
```
- 功能：事务装饰器
- 特点：
  - 自动提交/回滚
  - 异常处理
  - 日志记录
  - 性能监控

## 性能优化

### 1. 连接池
- 动态连接池大小
- 连接复用机制
- 超时处理
- 负载均衡

### 2. 查询优化
- 索引优化
- SQL语句优化
- 查询缓存
- 批量操作

### 3. 数据缓存
- 内存缓存
- 缓存策略
- 自动更新
- 过期处理

## 注意事项
1. 使用参数化查询
2. 正确处理事务
3. 及时释放连接
4. 定期备份数据
5. 监控性能指标
6. 处理并发访问
7. 维护数据一致性
8. 注意 SQL 注入
9. 优化大数据量操作
10. 实现数据验证

## 数据库结构

### products 表
- product_id (TEXT): 主键
- name (TEXT): 商品名称
- category (TEXT): 类别
- quantity (INTEGER): 数量
- unit (TEXT): 单位
- status (TEXT): 状态
- location (TEXT): 位置
- supplier (TEXT): 供应商
- purchase_price (REAL): 采购价
- selling_price (REAL): 售价
- total_profit (REAL): 总利润
- image_path (TEXT): 图片路径
- created_at (TIMESTAMP): 创建时间
- updated_at (TIMESTAMP): 更新时间

## 错误处理
- 完整的异常捕获和处理
- 事务保护关键操作
- 详细的日志记录
- 用户友好的错误消息

## 使用示例
```python
# 初始化数据库
init_database_dir()
create_tables()

# 添加商品
product_data = {
    "name": "测试商品",
    "category": "电子产品",
    "price": 199.99
}
product_id = add_product(product_data)

# 更新商品
update_product(product_id, {"price": 188.88})

# 导出数据
export_to_excel("inventory_report.xlsx")
```

## 注意事项
1. 所有数据库操作都有事务保护
2. 自动维护时间戳
3. ID生成规则固定
4. 关键操作有日志记录
5. 支持数据库备份和恢复 