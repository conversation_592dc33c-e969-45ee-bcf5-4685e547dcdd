import sqlite3
import logging
import os
import shutil
from datetime import datetime

# 确保数据库文件存储在用户的应用数据目录中
APP_NAME = "inventory_management"
if os.name == "nt":  # Windows
    APP_DATA = os.path.join(os.environ["APPDATA"], APP_NAME)
else:  # Linux/Mac
    APP_DATA = os.path.join(os.path.expanduser("~"), f".{APP_NAME}")

DATABASE_DIR = os.path.join(APP_DATA, "database")
DEFAULT_DATABASE = os.path.join(DATABASE_DIR, "inventory.db")
CURRENT_DATABASE = os.environ.get("DB_PATH", DEFAULT_DATABASE)


def init_database_dir():
    """初始化数据库目录"""
    if not os.path.exists(DATABASE_DIR):
        os.makedirs(DATABASE_DIR)
        logging.info(f"创建数据库目录: {DATABASE_DIR}")


def get_database_list():
    """获取所有可用的数据库列表"""
    init_database_dir()
    databases = []
    for file in os.listdir(DATABASE_DIR):
        if file.endswith(".db"):
            db_path = os.path.join(DATABASE_DIR, file)
            try:
                # 验证数据库文件是否可用
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    if tables:  # 如果数据库包含表，则认为是有效的数据库
                        databases.append(file)
            except sqlite3.Error:
                logging.warning(f"数据库文件损坏或无效: {file}")
    return databases


def create_new_database(db_name):
    """创建新的数据库"""
    if not db_name.endswith(".db"):
        db_name += ".db"
    db_path = os.path.join(DATABASE_DIR, db_name)
    if os.path.exists(db_path):
        raise ValueError(f"数据库 '{db_name}' 已存在")

    # 创建新数据库并初始化表结构
    global CURRENT_DATABASE
    CURRENT_DATABASE = db_path
    create_tables()
    return db_path


def switch_database(db_path):
    """切换到指定的数据库"""
    global CURRENT_DATABASE
    try:
        # 验证数据库文件是否存在
        if not os.path.exists(db_path):
            # 如果不存在，创建新数据库
            os.makedirs(os.path.dirname(db_path), exist_ok=True)
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                # 创建商品表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS products (
                        product_id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        category TEXT NOT NULL,
                        quantity INTEGER DEFAULT 1,
                        unit TEXT DEFAULT '个',
                        status TEXT DEFAULT '在售',
                        location TEXT,
                        supplier TEXT,
                        supplier_link TEXT,
                        purchase_link TEXT,
                        purchase_price REAL DEFAULT 0,
                        shipping_cost REAL DEFAULT 0,
                        other_cost REAL DEFAULT 0,
                        total_cost REAL DEFAULT 0,
                        selling_price REAL DEFAULT 0,
                        discount_rate REAL DEFAULT 100,
                        total_profit REAL DEFAULT 0,
                        purchaser TEXT,
                        selling_link TEXT,
                        image_path TEXT,
                        remarks TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                )

        # 更新当前数据库路径
        CURRENT_DATABASE = db_path
        os.environ["DB_PATH"] = db_path
        logging.info(f"切换到数据库: {db_path}")

        # 验证数据库连接并更新表结构
        with get_connection() as conn:
            cursor = conn.cursor()

            # 检查是否需要添加新列
            cursor.execute("PRAGMA table_info(products)")
            existing_columns = {col[1] for col in cursor.fetchall()}

            # 需要添加的列
            new_columns = {
                "unit": "TEXT DEFAULT '个'",
                "status": "TEXT DEFAULT '在售'",
                "shipping_cost": "REAL DEFAULT 0",
                "other_cost": "REAL DEFAULT 0",
                "total_cost": "REAL DEFAULT 0",
                "selling_price": "REAL DEFAULT 0",
                "discount_rate": "REAL DEFAULT 100",
                "total_profit": "REAL DEFAULT 0",
                "image_path": "TEXT",
                "remarks": "TEXT",
                "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
            }

            # 添加缺失的列
            for col_name, col_type in new_columns.items():
                if col_name not in existing_columns:
                    try:
                        cursor.execute(
                            f"ALTER TABLE products ADD COLUMN {col_name} {col_type}"
                        )
                        logging.info(f"添加新列: {col_name}")
                    except Exception as e:
                        logging.warning(f"添加列 {col_name} 失败: {str(e)}")

            conn.commit()
            logging.info("数据库表结构已更新")

        return True

    except Exception as e:
        logging.exception(f"切换数据库失败: {str(e)}")
        raise e


def delete_database(db_name):
    """删除指定的数据库"""
    if not db_name.endswith(".db"):
        db_name += ".db"
    db_path = os.path.join(DATABASE_DIR, db_name)
    if not os.path.exists(db_path):
        raise ValueError(f"数据库 '{db_name}' 不存在")

    if db_path == CURRENT_DATABASE:
        raise ValueError("无法删除当前正在使用的数据库")

    # 删除数据库文件
    os.remove(db_path)
    logging.info(f"数据库已删除: {db_name}")


def backup_database(db_name, backup_name=None):
    """备份数据库"""
    if not db_name.endswith(".db"):
        db_name += ".db"
    source_path = os.path.join(DATABASE_DIR, db_name)
    if not os.path.exists(source_path):
        raise ValueError(f"数据库 '{db_name}' 不存在")

    if backup_name is None:
        # 自动生成备份名称，格式：原名称_backup_序号.db
        base_name = db_name[:-3]  # 移除.db后缀
        i = 1
        while True:
            backup_name = f"{base_name}_backup_{i}.db"
            if not os.path.exists(os.path.join(DATABASE_DIR, backup_name)):
                break
            i += 1

    if not backup_name.endswith(".db"):
        backup_name += ".db"

    backup_path = os.path.join(DATABASE_DIR, backup_name)
    shutil.copy2(source_path, backup_path)
    return backup_name


def get_current_database():
    """获取当前数据库路径"""
    return CURRENT_DATABASE


def create_tables():
    """创建数据库表"""
    try:
        # 确保数据库目录存在
        if not os.path.exists(DATABASE_DIR):
            os.makedirs(DATABASE_DIR)
            logging.info(f"创建数据库目录: {DATABASE_DIR}")

        # 如果没有设置数据库路径，使用默认路径
        global CURRENT_DATABASE
        if not CURRENT_DATABASE:
            CURRENT_DATABASE = DEFAULT_DATABASE
            os.environ["DB_PATH"] = DEFAULT_DATABASE

        # 确保数据库目录存在
        db_dir = os.path.dirname(CURRENT_DATABASE)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)

        with sqlite3.connect(CURRENT_DATABASE) as conn:
            cursor = conn.cursor()

            # 创建商品表
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS products (
                    product_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    category TEXT NOT NULL,
                    quantity INTEGER DEFAULT 1,
                    unit TEXT DEFAULT '个',
                    status TEXT DEFAULT '在售',
                    location TEXT,
                    supplier TEXT,
                    supplier_link TEXT,
                    purchase_link TEXT,
                    purchase_price REAL DEFAULT 0,
                    shipping_cost REAL DEFAULT 0,
                    other_cost REAL DEFAULT 0,
                    total_cost REAL DEFAULT 0,
                    selling_price REAL DEFAULT 0,
                    discount_rate REAL DEFAULT 100,
                    total_profit REAL DEFAULT 0,
                    purchaser TEXT,
                    selling_link TEXT,
                    image_path TEXT,
                    remarks TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """
            )

            # 检查是否需要添加新列
            cursor.execute("PRAGMA table_info(products)")
            existing_columns = {col[1] for col in cursor.fetchall()}

            # 需要添加的列
            new_columns = {
                "image_path": "TEXT",
                "total_cost": "REAL DEFAULT 0",
                "total_profit": "REAL DEFAULT 0",
                "remarks": "TEXT",
                "created_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
            }

            # 添加缺失的列
            for col_name, col_type in new_columns.items():
                if col_name not in existing_columns:
                    try:
                        cursor.execute(
                            f"ALTER TABLE products ADD COLUMN {col_name} {col_type}"
                        )
                        logging.info(f"添加新列: {col_name}")
                    except Exception as e:
                        logging.warning(f"添加列 {col_name} 失败: {str(e)}")

            conn.commit()
            logging.info("数据库表结构已更新")

    except Exception as e:
        logging.exception("创建/更新数据库表失败")
        raise e


def get_connection():
    """获取数据库连接"""
    try:
        # 确保数据库目录存在
        db_dir = os.path.dirname(CURRENT_DATABASE)
        os.makedirs(db_dir, exist_ok=True)

        # 创建连接
        conn = sqlite3.connect(CURRENT_DATABASE)

        # 设置基本配置
        conn.row_factory = sqlite3.Row
        conn.execute("PRAGMA foreign_keys = ON")
        conn.isolation_level = None

        return conn
    except Exception as e:
        logging.exception("获取数据库连接失败")
        raise e


def execute_transaction(func):
    """装饰器：确保在函数执行期间的所有数据库操作都在一个事务中完成"""

    def wrapper(*args, **kwargs):
        conn = get_connection()
        try:
            conn.execute("BEGIN")
            result = func(*args, **kwargs)
            conn.execute("COMMIT")
            return result
        except Exception as e:
            conn.execute("ROLLBACK")
            raise e
        finally:
            conn.close()

    return wrapper


def get_next_type_number(cursor, category):
    """获取指定分类的下一个可用类型编号"""
    if not category:
        raise ValueError("分类不能为空")

    # 确保category不包含#号前缀
    category = (
        category.replace("#", "")
        if isinstance(category, str) and category.startswith("#")
        else category
    )

    # 查找当前分类下最后一个商品ID
    cursor.execute(
        """
        SELECT product_id 
        FROM products 
        WHERE product_id LIKE ? 
        ORDER BY product_id DESC 
        LIMIT 1
        """,
        (f"#{category}-%",),
    )
    result = cursor.fetchone()

    if result and result[0]:
        try:
            # 从最后一个ID中提取类型编号并加1
            parts = result[0].split("-")
            if len(parts) >= 2:
                type_str = parts[1].replace("#", "")
                type_number = int(type_str)
                return f"{type_number + 1:04d}"
        except (IndexError, ValueError) as e:
            logging.warning(f"解析商品ID失败: {result[0]}, 错误: {e}")

    # 如果没有现有商品或解析失败，返回初始编号
    return "0001"


def get_next_product_number(cursor, category, type_number):
    """获取指定分类和类型的下一个可用商品编号"""
    # 确保category不包含#号前缀
    category = category.replace("#", "") if category.startswith("#") else category
    # 确保type_number不包含#号前缀
    type_num = (
        type_number.replace("#", "") if type_number.startswith("#") else type_number
    )

    # 查找当前类型下最后一个商品ID
    cursor.execute(
        """
        SELECT product_id 
        FROM products 
        WHERE product_id LIKE ? 
        ORDER BY product_id DESC 
        LIMIT 1
        """,
        (f"#{category}-#{type_num}-%",),
    )
    result = cursor.fetchone()

    if result and result[0]:
        try:
            # 从最后一个ID中提取商品编号并加1
            parts = result[0].split("-")
            if len(parts) >= 3:
                product_number = int(parts[2])
                return f"{product_number + 1:05d}"
        except (IndexError, ValueError) as e:
            logging.warning(f"解析商品ID失败: {result[0]}, 错误: {e}")

    # 如果没有现有商品或解析失败，返回初始编号
    return "00001"


def parse_product_id(product_id):
    """
    解析商品ID，返回分类和序号
    商品ID格式: #分类-#序号-批次号
    例如: #食品-#0001-00001
    """
    try:
        if not product_id:
            raise ValueError("商品ID不能为空")

        if not isinstance(product_id, str):
            raise ValueError("商品ID必须是字符串类型")

        # 检查基本格式
        if not product_id.startswith("#"):
            raise ValueError("商品ID必须以#开头")

        # 分割ID
        parts = product_id.split("-")
        if len(parts) != 3:
            raise ValueError("商品ID格式错误，应为: #分类-#序号-批次号")

        # 解析分类
        category = parts[0].strip()
        if not category.startswith("#"):
            raise ValueError("分类必须以#开头")
        category = category[1:]  # 去掉#
        if not category:
            raise ValueError("分类不能为空")

        # 解析序号
        type_num = parts[1].strip()
        if not type_num.startswith("#"):
            raise ValueError("序号必须以#开头")
        type_num = type_num[1:]  # 去掉#
        try:
            type_num = int(type_num)
            if len(str(type_num).zfill(4)) != 4:
                raise ValueError("序号必须是4位数字")
        except ValueError:
            raise ValueError("序号必须是数字")

        # 解析批次号
        batch_num = parts[2].strip()
        try:
            batch_num = int(batch_num)
            if len(str(batch_num).zfill(5)) != 5:
                raise ValueError("批次号必须是5位数字")
        except ValueError:
            raise ValueError("批次号必须是数字")

        return category, type_num, batch_num

    except Exception as e:
        logging.error(f"解析商品ID失败: {product_id}, 错误: {str(e)}")
        raise ValueError(f"无效的商品ID格式: {str(e)}")


def generate_product_id(category, type_num=None, batch_num=None, cursor=None):
    """
    生成商品ID
    :param category: 分类
    :param type_num: 序号，如果为None则自动生成
    :param batch_num: 批次号，如果为None则自动生成
    :param cursor: 数据库游标，如果为None则创建新连接
    :return: 商品ID
    """
    try:
        # 验证分类
        if not category:
            raise ValueError("分类不能为空")

        # 确保分类不包含#
        category = category.replace("#", "").strip()
        if not category:
            raise ValueError("分类不能为空")

        # 处理数据库连接
        conn = None
        if cursor is None:
            conn = get_connection()
            cursor = conn.cursor()

        try:
            if type_num is None:
                # 获取新的类型编号
                type_num = get_next_type_number(cursor, category)
            else:
                # 确保type_num是4位数字格式
                type_num = f"{int(str(type_num).replace('#', '')):04d}"

            if batch_num is None:
                # 获取新的商品编号
                batch_num = get_next_product_number(cursor, category, type_num)
            else:
                # 确保batch_num是5位数字格式
                batch_num = f"{int(str(batch_num)):05d}"

            # 生成商品ID
            product_id = f"#{category}-#{type_num}-{batch_num}"

            # 检查生成的ID是否已存在
            while True:
                cursor.execute(
                    "SELECT COUNT(*) FROM products WHERE product_id = ?",
                    (product_id,),
                )
                if cursor.fetchone()[0] == 0:
                    break
                # 如果ID已存在，增加批次号并重新生成ID
                batch_num = f"{int(batch_num) + 1:05d}"
                product_id = f"#{category}-#{type_num}-{batch_num}"

            # 验证生成的ID
            try:
                parse_product_id(product_id)
            except ValueError as e:
                raise ValueError(f"生成的商品ID格式无效: {str(e)}")

            return product_id

        finally:
            # 如果是我们创建的连接，需要关闭它
            if conn is not None:
                conn.close()

    except Exception as e:
        logging.error(f"生成商品ID失败: {str(e)}")
        raise ValueError(f"生成商品ID失败: {str(e)}")


def add_product(product_data):
    """添加商品"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 如果没有提供商品ID，则自动生成
            if not product_data.get("product_id"):
                if not product_data.get("category"):
                    raise ValueError("添加商品时必须提供category字段")
                product_data["product_id"] = generate_product_id(
                    product_data["category"]
                )

            # 验证商品ID格式
            category, type_number, product_number = parse_product_id(
                product_data["product_id"]
            )
            if not all([category, type_number, product_number]):
                raise ValueError(f"无效的商品ID格式: {product_data['product_id']}")

            # 确保所有必需字段都存在，并设置正确的默认值
            required_fields = {
                "product_id": None,
                "name": "",
                "category": category,  # 使用从ID中解析出的分类
                "quantity": 1,
                "unit": "个",
                "status": "在售",
                "location": "",
                "supplier": "",
                "supplier_link": "",
                "purchase_link": "",
                "purchase_price": 0.0,
                "shipping_cost": 0.0,
                "other_cost": 0.0,
                "total_cost": 0.0,
                "selling_price": 0.0,
                "discount_rate": 100.0,
                "total_profit": 0.0,
                "purchaser": "",
                "selling_link": "",
                "image_path": "",
                "remarks": "",
            }

            # 使用默认值填充缺失字段，并确保数值类型正确
            for field, default_value in required_fields.items():
                if field not in product_data or product_data[field] is None:
                    product_data[field] = default_value
                else:
                    # 确保数值类型字段为正确的类型
                    if field in ["quantity"]:
                        try:
                            product_data[field] = int(float(str(product_data[field])))
                        except (ValueError, TypeError):
                            product_data[field] = default_value
                    elif field in [
                        "purchase_price",
                        "shipping_cost",
                        "other_cost",
                        "total_cost",
                        "selling_price",
                        "discount_rate",
                        "total_profit",
                    ]:
                        try:
                            product_data[field] = float(str(product_data[field]))
                        except (ValueError, TypeError):
                            product_data[field] = default_value
                    else:
                        product_data[field] = str(product_data[field])

            # 准备SQL语句和参数
            fields = ", ".join(required_fields.keys())
            placeholders = ", ".join("?" * len(required_fields))
            values = [product_data[field] for field in required_fields.keys()]

            # 插入商品数据
            cursor.execute(
                f"""
                INSERT INTO products ({fields})
                VALUES ({placeholders})
                """,
                values,
            )

            conn.commit()
            logging.info(f"商品添加成功: {product_data['product_id']}")

            return product_data["product_id"]

    except Exception as e:
        logging.exception(f"添加商品失败: {str(e)}")
        raise e


def update_product(product_id, product_data):
    """更新商品"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取现有商品数据
            cursor.execute(
                """
                SELECT *
                FROM products
                WHERE product_id = ?
                """,
                (product_id,),
            )
            columns = [description[0] for description in cursor.description]
            row = cursor.fetchone()

            if not row:
                raise ValueError(f"找不到商品: {product_id}")

            # 将现有数据转换为字典
            existing_data = dict(zip(columns, row))

            # 只更新提供的字段
            update_data = {}
            for key, value in product_data.items():
                if key in existing_data and value is not None:
                    update_data[key] = value

            if not update_data:
                return  # 没有需要更新的字段

            # 准备SQL语句和参数
            set_clause = ", ".join(f"{k} = ?" for k in update_data.keys())
            values = tuple(update_data.values()) + (product_id,)

            # 更新商品数据
            cursor.execute(
                f"""
                UPDATE products
                SET {set_clause}, updated_at = CURRENT_TIMESTAMP
                WHERE product_id = ?
                """,
                values,
            )

            conn.commit()
            logging.info(f"商品更新成功: {product_id}")

    except Exception as e:
        logging.exception("更新商品失败")
        raise e


def delete_product(product_id):
    """删除商品"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 先删除批次关联
            cursor.execute(
                "DELETE FROM product_batches WHERE product_id = ?",
                (product_id,),
            )

            # 删除商品
            cursor.execute(
                "DELETE FROM products WHERE product_id = ?",
                (product_id,),
            )

            conn.commit()
            logging.info(f"商品删除成功: {product_id}")

    except Exception as e:
        logging.exception("删除商品失败")
        raise e


def get_product(product_id):
    """获取商品信息"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取商品数据
            cursor.execute(
                """
                SELECT *
                FROM products
                WHERE product_id = ?
                """,
                (product_id,),
            )

            columns = [description[0] for description in cursor.description]
            row = cursor.fetchone()

            if row:
                return dict(zip(columns, row))
            return None

    except Exception as e:
        logging.exception("获取商品信息失败")
        raise e


def get_product_batches(product_id):
    """获取商品所属的批次"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取商品的批次信息
            cursor.execute(
                """
                SELECT b.*
                FROM batches b
                JOIN product_batches pb ON b.batch_id = pb.batch_id
                WHERE pb.product_id = ?
                ORDER BY b.created_at DESC
                """,
                (product_id,),
            )

            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

    except Exception as e:
        logging.exception("获取商品批次信息失败")
        raise e


def get_batch(batch_id):
    """获取批次信息"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取批次数据
            cursor.execute(
                """
                SELECT *
                FROM batches
                WHERE batch_id = ?
                """,
                (batch_id,),
            )

            columns = [description[0] for description in cursor.description]
            row = cursor.fetchone()

            if row:
                return dict(zip(columns, row))
            return None

    except Exception as e:
        logging.exception("获取批次信息失败")
        raise e


def update_batch(batch_id, batch_data):
    """更新批次信息"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 准备SQL语句和参数
            set_clause = ", ".join(f"{k} = ?" for k in batch_data.keys())
            values = tuple(batch_data.values()) + (batch_id,)

            # 更新批次数据
            cursor.execute(
                f"""
                UPDATE batches
                SET {set_clause}
                WHERE batch_id = ?
                """,
                values,
            )

            conn.commit()
            logging.info(f"批次更新成功: {batch_id}")

    except Exception as e:
        logging.exception("更新批次信息失败")
        raise e


def get_financial_stats():
    """获取财务统计信息"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取总体统计
            cursor.execute(
                """
                SELECT 
                    COUNT(*) as total_products,
                    SUM(total_cost) as total_cost,
                    SUM(total_profit) as total_profit,
                    SUM(total_shipping_cost) as total_shipping_cost,
                    SUM(total_other_cost) as total_other_cost
                FROM products
                """
            )

            overall_stats = dict(
                zip(
                    [
                        "total_products",
                        "total_cost",
                        "total_profit",
                        "total_shipping_cost",
                        "total_other_cost",
                    ],
                    cursor.fetchone(),
                )
            )

            # 获取各类别统计
            cursor.execute(
                """
                SELECT 
                    category,
                    COUNT(*) as product_count,
                    SUM(total_cost) as category_cost,
                    SUM(total_profit) as category_profit
                FROM products
                GROUP BY category
                ORDER BY category_cost DESC
                """
            )

            category_stats = [
                dict(
                    zip(
                        [
                            "category",
                            "product_count",
                            "category_cost",
                            "category_profit",
                        ],
                        row,
                    )
                )
                for row in cursor.fetchall()
            ]

            return {"overall": overall_stats, "by_category": category_stats}

    except Exception as e:
        logging.exception("获取财务统计信息失败")
        raise e


def export_to_excel(filename):
    """导出数据到Excel"""
    try:
        import pandas as pd

        with get_connection() as conn:
            # 读取商品数据
            products_df = pd.read_sql_query(
                """
                SELECT p.*, GROUP_CONCAT(b.batch_name) as batches
                FROM products p
                LEFT JOIN product_batches pb ON p.product_id = pb.product_id
                LEFT JOIN batches b ON pb.batch_id = b.batch_id
                GROUP BY p.product_id
                """,
                conn,
            )

            # 导出到Excel
            products_df.to_excel(filename, index=False, sheet_name="商品列表")

            logging.info(f"数据已导出到Excel文件: {filename}")

    except Exception as e:
        logging.exception("导出Excel失败")
        raise e


def export_to_csv(filename):
    """导出数据到CSV"""
    try:
        import pandas as pd

        with get_connection() as conn:
            # 读取商品数据
            products_df = pd.read_sql_query(
                """
                SELECT p.*, GROUP_CONCAT(b.batch_name) as batches
                FROM products p
                LEFT JOIN product_batches pb ON p.product_id = pb.product_id
                LEFT JOIN batches b ON pb.batch_id = b.batch_id
                GROUP BY p.product_id
                """,
                conn,
            )

            # 导出到CSV
            products_df.to_csv(filename, index=False, encoding="utf-8-sig")

            logging.info(f"数据已导出到CSV文件: {filename}")

    except Exception as e:
        logging.exception("导出CSV失败")
        raise e


def get_categories():
    """获取所有商品分类"""
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT DISTINCT category 
            FROM products 
            ORDER BY category
        """
        )
        return [row[0] for row in cursor.fetchall()]


def get_product_ids():
    """获取所有商品ID

    Returns:
        list: 商品ID列表
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT product_id, name
                FROM products
                ORDER BY product_id
                """
            )
            return [(row[0], row[1]) for row in cursor.fetchall()]
    except Exception as e:
        logging.exception("获取商品ID列表失败")
        raise e


def get_product_summary(product_id):
    """获取商品汇总信息"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            # 获取基本类型编号（如 #food-#0001）
            base_type = "-".join(product_id.split("-")[:2])

            cursor.execute(
                """
                SELECT 
                    name,
                    SUM(quantity) as total_quantity,
                    category,
                    location,
                    supplier,
                    supplier_link,
                    purchase_link,
                    selling_price,
                    purchaser,
                    selling_link,
                    image_path
                FROM products 
                WHERE product_id LIKE ?
                GROUP BY name, category, location, supplier, supplier_link, 
                        purchase_link, selling_price, purchaser, selling_link, image_path
                """,
                (f"{base_type}-%",),
            )

            return cursor.fetchone()
    except Exception as e:
        logging.exception("获取商品汇总信息失败")
        raise


def init_database():
    """初始化数据库，创建必要的表"""
    logging.info("开始初始化数据库")
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 创建商品表
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS products (
                    product_id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    quantity INTEGER DEFAULT 0,
                    category TEXT,
                    location TEXT,
                    supplier TEXT,
                    supplier_link TEXT,
                    purchase_link TEXT,
                    selling_price REAL,
                    purchaser TEXT,
                    selling_link TEXT,
                    image_path TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )
            logging.info("商品表已创建")

            # 创建批次表
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS batches (
                    batch_id TEXT PRIMARY KEY,
                    batch_name TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )
            logging.info("批次表已创建")

            # 创建商品批次关联表
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS product_batches (
                    product_id TEXT,
                    batch_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    PRIMARY KEY (product_id, batch_id),
                    FOREIGN KEY (product_id) REFERENCES products (product_id),
                    FOREIGN KEY (batch_id) REFERENCES batches (batch_id)
                )
            """
            )
            logging.info("商品批次关联表已创建")

            conn.commit()
            logging.info("数据库初始化完成")
    except Exception as e:
        logging.exception("数据库初始化失败")
        raise e


@execute_transaction
def add_batch(batch_name, remarks=None):
    """添加新批次"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # 生成批次ID
        batch_id = f"B{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 插入批次
        cursor.execute(
            """
            INSERT INTO batches (batch_id, batch_name, remarks)
            VALUES (?, ?, ?)
            """,
            (batch_id, batch_name, remarks),
        )

        return batch_id
    except Exception as e:
        logging.exception("添加批次失败")
        raise e


@execute_transaction
def update_batch(batch_id, batch_name=None, remarks=None):
    """更新批次信息"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # 构建更新语句
        update_fields = []
        params = []
        if batch_name is not None:
            update_fields.append("batch_name = ?")
            params.append(batch_name)
        if remarks is not None:
            update_fields.append("remarks = ?")
            params.append(remarks)

        if not update_fields:
            return

        params.append(batch_id)

        # 执行更新
        cursor.execute(
            f"""
            UPDATE batches
            SET {", ".join(update_fields)}, updated_at = CURRENT_TIMESTAMP
            WHERE batch_id = ?
            """,
            params,
        )

    except Exception as e:
        logging.exception("更新批次失败")
        raise e


@execute_transaction
def delete_batch(batch_id):
    """删除批次"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # 删除批次关联
        cursor.execute("DELETE FROM product_batches WHERE batch_id = ?", (batch_id,))

        # 删除批次
        cursor.execute("DELETE FROM batches WHERE batch_id = ?", (batch_id,))

    except Exception as e:
        logging.exception("删除批次失败")
        raise e


@execute_transaction
def add_products_to_batch(batch_id, product_ids):
    """将商品添加到批次"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # 验证商品是否存在
        for product_id in product_ids:
            cursor.execute("SELECT 1 FROM products WHERE product_id = ?", (product_id,))
            if not cursor.fetchone():
                raise ValueError(f"商品不存在: {product_id}")

        # 添加商品到批次
        for product_id in product_ids:
            cursor.execute(
                """
                INSERT INTO product_batches (batch_id, product_id)
                VALUES (?, ?)
                """,
                (batch_id, product_id),
            )

    except Exception as e:
        logging.exception("添加商品到批次失败")
        raise e


@execute_transaction
def remove_products_from_batch(batch_id, product_ids):
    """从批次中移除商品"""
    try:
        conn = get_connection()
        cursor = conn.cursor()

        # 移除商品
        for product_id in product_ids:
            cursor.execute(
                """
                DELETE FROM product_batches
                WHERE batch_id = ? AND product_id = ?
                """,
                (batch_id, product_id),
            )

    except Exception as e:
        logging.exception("从批次中移除商品失败")
        raise e


def get_product_type(product_id):
    """
    获取商品类型信息
    :param product_id: 商品ID
    :return: (分类, 序号)
    """
    try:
        category, type_num, _ = parse_product_id(product_id)
        return category, type_num
    except ValueError:
        return None, None
