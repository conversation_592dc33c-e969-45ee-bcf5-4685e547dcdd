# 图片工具模块 (image_utils.py)

## 功能概述
`image_utils.py` 提供了完整的图片处理功能，包括图片验证、保存、缩略图生成、图片库管理等功能。该模块处理商品相关的所有图片操作，支持多种图片格式，并实现了图片的多尺寸存储。

## 常量定义

```python
IMAGE_ROOT = os.path.join(APP_DATA, "images")  # 图片存储根目录

SUPPORTED_FORMATS = {
    "JPEG": [".jpg", ".jpeg"],
    "PNG": [".png"],
    "GIF": [".gif"],
    "BMP": [".bmp"],
}
```

## 核心功能

### 1. 目录管理
```python
def init_image_dir():
```
- 功能：初始化图片存储目录
- 操作：创建必要的目录结构

```python
def get_product_image_dir(product_id):
```
- 功能：获取商品图片目录
- 参数：product_id - 商品ID
- 返回：商品专属图片目录路径

### 2. 图片验证
```python
def validate_image(file_path):
```
- 功能：验证图片文件的有效性
- 验证项目：
  - 文件大小（限制5MB）
  - 图片格式支持
  - 图片完整性
- 异常处理：验证失败时抛出 ValueError

### 3. 图片处理

#### 缩略图生成
```python
def create_thumbnail(source_path, target_path, max_size=(80, 80)):
```
- 功能：创建缩略图
- 参数：
  - source_path: 源图片路径
  - target_path: 目标路径
  - max_size: 最大尺寸（默认80x80）
- 特点：
  - 保持宽高比
  - 自动转换为RGB模式
  - JPEG格式保存，优化压缩

#### 预览图生成
```python
def create_preview(source_path, target_path, max_size=(1000, 1000)):
```
- 功能：创建预览图
- 参数：
  - source_path: 源图片路径
  - target_path: 目标路径
  - max_size: 最大尺寸（默认1000x1000）
- 特点：
  - 保持最小边为1000像素
  - 自动计算等比例尺寸
  - 高质量JPEG保存

### 4. 商品图片管理

#### 保存商品图片
```python
def save_product_image(product_id, source_path, is_primary=False):
```
- 功能：保存商品图片
- 操作流程：
  1. 验证图片
  2. 创建目录结构
  3. 生成唯一ID
  4. 保存原图、缩略图和预览图
  5. 更新数据库记录
- 特点：
  - 自动处理主图设置
  - 保存完整的图片信息
  - 返回图片ID

#### 获取图片路径
```python
def get_image_path(product_id, image_id, thumbnail=False, preview=False):
```
- 功能：获取图片文件路径
- 参数：
  - product_id: 商品ID
  - image_id: 图片ID
  - thumbnail: 是否获取缩略图
  - preview: 是否获取预览图

#### 获取商品图片
```python
def get_product_images(product_id, cursor=None):
```
- 功能：获取商品的所有图片信息
- 返回：图片信息列表（包含ID、路径、是否主图等）

### 5. 图片库管理

#### 初始化图片库
```python
def init_image_library():
```
- 功能：初始化图片库系统
- 操作：创建必要的数据库表

#### 添加到图片库
```python
def add_to_library(image_path, tags=None):
```
- 功能：将图片添加到公共图片库
- 特点：
  - 支持标签管理
  - 自动生成多尺寸版本
  - 重复检测

#### 使用库中图片
```python
def use_library_image(image_id, product_id):
```
- 功能：将图片库中的图片应用到商品
- 操作：
  - 复制图片到商品目录
  - 创建数据库关联

## 错误处理
- 所有函数都包含完整的错误处理
- 使用 logging 模块记录错误
- 提供详细的错误信息
- 保证数据一致性

## 性能优化
1. 图片压缩
   - 缩略图：85%质量，优化压缩
   - 预览图：90%质量，优化压缩
2. 存储优化
   - 多尺寸存储
   - 自动格式转换
3. 数据库优化
   - 使用事务确保一致性
   - 索引优化

## 使用示例
```python
# 初始化
init_image_dir()
init_image_library()

# 保存商品图片
image_id = save_product_image("PROD001", "path/to/image.jpg", is_primary=True)

# 获取图片信息
images = get_product_images("PROD001")

# 获取不同尺寸
thumb_path = get_image_path("PROD001", image_id, thumbnail=True)
preview_path = get_image_path("PROD001", image_id, preview=True)
```

## 注意事项
1. 图片大小限制为5MB
2. 支持的格式：JPG、JPEG、PNG、GIF、BMP
3. 自动创建三种尺寸：
   - 原图：原始尺寸
   - 预览图：最长边1000像素
   - 缩略图：最长边80像素
4. 所有操作都有日志记录
5. 图片存储使用UUID避免冲突 