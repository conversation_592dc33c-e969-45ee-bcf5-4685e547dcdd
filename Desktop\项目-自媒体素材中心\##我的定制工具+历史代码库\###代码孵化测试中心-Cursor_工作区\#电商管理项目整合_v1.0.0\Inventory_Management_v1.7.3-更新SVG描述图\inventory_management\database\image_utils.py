import os
import logging
import shutil
import uuid
import mimetypes
from datetime import datetime
from PIL import Image
from .db_utils import get_connection, APP_DATA

# 图片存储根目录
IMAGE_ROOT = os.path.join(APP_DATA, "images")

# 支持的图片格式
SUPPORTED_FORMATS = {
    "JPEG": [".jpg", ".jpeg"],
    "PNG": [".png"],
    "GIF": [".gif"],
    "BMP": [".bmp"],
}


def get_image_type(file_path):
    """获取图片类型，替代已移除的imghdr.what()"""
    try:
        # 首先尝试通过文件扩展名判断
        ext = os.path.splitext(file_path)[1].lower()
        if ext in [".jpg", ".jpeg"]:
            return "jpeg"
        elif ext == ".png":
            return "png"
        elif ext == ".gif":
            return "gif"
        elif ext == ".bmp":
            return "bmp"
        elif ext == ".webp":
            return "webp"

        # 如果扩展名不能确定，尝试使用PIL
        try:
            with Image.open(file_path) as img:
                return img.format.lower() if img.format else None
        except Exception:
            pass

        # 最后尝试使用mimetypes
        mime_type, _ = mimetypes.guess_type(file_path)
        if mime_type and mime_type.startswith("image/"):
            return mime_type.split("/")[-1]

        return None
    except Exception as e:
        logging.warning(f"无法确定图片类型: {file_path}, 错误: {e}")
        return None


def init_image_dir():
    """初始化图片存储目录"""
    if not os.path.exists(IMAGE_ROOT):
        os.makedirs(IMAGE_ROOT)
        logging.info(f"创建图片存储目录: {IMAGE_ROOT}")


def get_product_image_dir(product_id):
    """获取商品图片目录"""
    return os.path.join(IMAGE_ROOT, product_id)


def validate_image(file_path):
    """验证图片文件"""
    try:
        # 检查文件大小（5MB限制）
        max_size = 5 * 1024 * 1024
        if os.path.getsize(file_path) > max_size:
            raise ValueError("图片大小超过限制(5MB)")

        # 验证图片类型
        img = Image.open(file_path)
        ext = os.path.splitext(file_path)[1].lower()

        # 检查文件扩展名是否在支持的格式中
        format_supported = False
        for format_exts in SUPPORTED_FORMATS.values():
            if ext in format_exts:
                format_supported = True
                break

        if not format_supported:
            raise ValueError(f"不支持的图片格式: {ext}")

        # 验证图片是否可以打开和读取
        img.verify()

        return True
    except Exception as e:
        logging.error(f"图片验证失败: {str(e)}")
        raise ValueError(f"图片验证失败: {str(e)}")


def create_thumbnail(source_path, target_path, max_size=(80, 80)):
    """创建缩略图"""
    try:
        with Image.open(source_path) as img:
            if img.mode in ("RGBA", "P"):
                img = img.convert("RGB")
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            img.save(target_path, "JPEG", quality=85, optimize=True)
    except Exception as e:
        logging.exception(f"创建缩略图失败: {str(e)}")
        raise


def create_preview(source_path, target_path, max_size=(1000, 1000)):
    """创建预览图"""
    try:
        with Image.open(source_path) as img:
            if img.mode in ("RGBA", "P"):
                img = img.convert("RGB")

            # 计算新尺寸，保持最小边为1000
            width, height = img.size
            if width < height:
                new_width = 1000
                new_height = int(height * (1000 / width))
            else:
                new_height = 1000
                new_width = int(width * (1000 / height))

            # 调整图片大小
            img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            img.save(target_path, "JPEG", quality=90, optimize=True)
    except Exception as e:
        logging.exception(f"创建预览图失败: {str(e)}")
        raise


def save_product_image(product_id, source_path, is_primary=False):
    """保存商品图片"""
    try:
        validate_image(source_path)

        # 创建目录结构
        product_dir = get_product_image_dir(product_id)
        thumbnails_dir = os.path.join(product_dir, "thumbnails")
        previews_dir = os.path.join(product_dir, "previews")
        os.makedirs(product_dir, exist_ok=True)
        os.makedirs(thumbnails_dir, exist_ok=True)
        os.makedirs(previews_dir, exist_ok=True)

        # 生成文件名
        image_id = str(uuid.uuid4())
        original_name = os.path.basename(source_path)
        ext = os.path.splitext(original_name)[1].lower()
        image_name = f"{image_id}{ext}"

        # 保存图片
        target_path = os.path.join(product_dir, image_name)
        thumbnail_path = os.path.join(thumbnails_dir, f"thumb_{image_name}")
        preview_path = os.path.join(previews_dir, f"preview_{image_name}")

        shutil.copy2(source_path, target_path)
        create_thumbnail(source_path, thumbnail_path)
        create_preview(source_path, preview_path)

        # 保存到数据库
        with get_connection() as conn:
            cursor = conn.cursor()

            # 检查是否是第一张图片
            cursor.execute(
                "SELECT COUNT(*) FROM product_images WHERE product_id = ?",
                (product_id,),
            )
            is_first_image = cursor.fetchone()[0] == 0
            is_primary = is_primary or is_first_image

            # 如果是主图，重置其他图片状态
            if is_primary:
                cursor.execute(
                    "UPDATE product_images SET is_primary = 0 WHERE product_id = ?",
                    (product_id,),
                )

            # 插入图片记录
            cursor.execute(
                """
                INSERT INTO product_images (
                    image_id, product_id, image_name, image_path,
                    is_primary, file_size, image_type
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    image_id,
                    product_id,
                    original_name,
                    image_name,
                    1 if is_primary else 0,
                    os.path.getsize(target_path),
                    get_image_type(target_path),
                ),
            )

            # 更新商品主图
            if is_primary:
                cursor.execute(
                    "UPDATE products SET image_path = ? WHERE product_id = ?",
                    (image_name, product_id),
                )

            conn.commit()

        return image_id
    except Exception as e:
        logging.exception(f"保存商品图片失败: {str(e)}")
        raise


def get_image_path(product_id, image_id, thumbnail=False, preview=False):
    """获取图片路径"""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT image_path FROM product_images WHERE image_id = ?", (image_id,)
            )
            result = cursor.fetchone()
            if not result:
                return None

            image_path = result[0]
            product_dir = get_product_image_dir(product_id)

            if thumbnail:
                return os.path.join(product_dir, "thumbnails", f"thumb_{image_path}")
            elif preview:
                return os.path.join(product_dir, "previews", f"preview_{image_path}")
            return os.path.join(product_dir, image_path)
    except Exception as e:
        logging.exception(f"获取图片路径失败: {str(e)}")
        return None


def get_product_images(product_id, cursor=None):
    """获取商品的所有图片

    Args:
        product_id: 商品ID
        cursor: 数据库游标，如果为None则创建新的连接

    Returns:
        list: 包含图片信息的列表，每个元素是一个字典，包含image_id、image_path、is_primary等信息
    """
    try:
        # 如果没有提供cursor，创建新的数据库连接
        if cursor is None:
            with get_connection() as conn:
                cursor = conn.cursor()
                return get_product_images(product_id, cursor)

        # 查询商品的所有图片
        cursor.execute(
            """
            SELECT 
                image_id,
                image_path,
                is_primary,
                created_at,
                updated_at
            FROM product_images 
            WHERE product_id = ?
            ORDER BY is_primary DESC, created_at DESC
            """,
            (product_id,),
        )

        # 获取结果
        images = []
        for row in cursor.fetchall():
            images.append(
                {
                    "image_id": row[0],
                    "image_path": row[1],
                    "is_primary": bool(row[2]),
                    "created_at": row[3],
                    "updated_at": row[4],
                }
            )

        return images

    except Exception as e:
        logging.exception(f"获取商品图片失败: {str(e)}")
        raise


def delete_product_image(product_id, image_id):
    """删除商品图片

    Args:
        product_id: 商品ID
        image_id: 图片ID
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取图片信息
            cursor.execute(
                "SELECT image_path, is_primary FROM product_images WHERE image_id = ?",
                (image_id,),
            )
            result = cursor.fetchone()
            if not result:
                raise ValueError(f"图片不存在: {image_id}")

            image_path, is_primary = result

            # 删除物理文件
            product_dir = get_product_image_dir(product_id)
            file_paths = [
                os.path.join(product_dir, image_path),  # 原图
                os.path.join(
                    product_dir, "thumbnails", f"thumb_{image_path}"
                ),  # 缩略图
                os.path.join(
                    product_dir, "previews", f"preview_{image_path}"
                ),  # 预览图
            ]

            for path in file_paths:
                if os.path.exists(path):
                    os.remove(path)
                    logging.info(f"删除图片文件: {path}")

            # 从数据库删除记录
            cursor.execute("DELETE FROM product_images WHERE image_id = ?", (image_id,))

            # 如果是主图，需要更新商品的主图
            if is_primary:
                # 查找该商品的其他图片
                cursor.execute(
                    """
                    SELECT image_path 
                    FROM product_images 
                    WHERE product_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT 1
                    """,
                    (product_id,),
                )
                new_primary = cursor.fetchone()

                # 更新商品主图
                cursor.execute(
                    "UPDATE products SET image_path = ? WHERE product_id = ?",
                    (new_primary[0] if new_primary else None, product_id),
                )

                # 如果还有其他图片，将第一张设为主图
                if new_primary:
                    cursor.execute(
                        """
                        UPDATE product_images 
                        SET is_primary = 1 
                        WHERE product_id = ? AND image_path = ?
                        """,
                        (product_id, new_primary[0]),
                    )

            conn.commit()
            logging.info(f"成功删除商品图片: {image_id}")

    except Exception as e:
        logging.exception(f"删除商品图片失败: {str(e)}")
        raise


def set_primary_image(product_id, image_id):
    """设置商品主图

    Args:
        product_id: 商品ID
        image_id: 要设为主图的图片ID
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取图片信息
            cursor.execute(
                "SELECT image_path FROM product_images WHERE image_id = ?", (image_id,)
            )
            result = cursor.fetchone()
            if not result:
                raise ValueError(f"图片不存在: {image_id}")

            image_path = result[0]

            # 重置所有图片的主图状态
            cursor.execute(
                "UPDATE product_images SET is_primary = 0 WHERE product_id = ?",
                (product_id,),
            )

            # 设置新的主图
            cursor.execute(
                "UPDATE product_images SET is_primary = 1 WHERE image_id = ?",
                (image_id,),
            )

            # 更新商品表中的主图路径
            cursor.execute(
                "UPDATE products SET image_path = ? WHERE product_id = ?",
                (image_path, product_id),
            )

            conn.commit()
            logging.info(f"成功设置主图: {image_id}")

    except Exception as e:
        logging.exception(f"设置主图失败: {str(e)}")
        raise


def init_image_library():
    """初始化图片库"""
    with get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS image_library (
                image_id TEXT PRIMARY KEY,
                original_name TEXT NOT NULL,
                image_path TEXT NOT NULL,
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                usage_count INTEGER DEFAULT 0,
                tags TEXT
            )
        """
        )
        conn.commit()


def add_to_library(image_path, tags=None):
    """添加图片到图片库"""
    try:
        # 生成新的图片ID
        image_id = str(uuid.uuid4())
        original_name = os.path.basename(image_path)

        # 创建图片库目录
        library_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "images", "library"
        )
        os.makedirs(library_dir, exist_ok=True)
        os.makedirs(os.path.join(library_dir, "thumbnails"), exist_ok=True)
        os.makedirs(os.path.join(library_dir, "previews"), exist_ok=True)

        # 复制并处理图片
        new_path = os.path.join(
            library_dir, f"{image_id}{os.path.splitext(original_name)[1]}"
        )
        img = Image.open(image_path)

        # 确保图片格式正确
        if img.mode in ("RGBA", "LA") or (
            img.mode == "P" and "transparency" in img.info
        ):
            bg = Image.new("RGB", img.size, (255, 255, 255))
            if img.mode == "P":
                img = img.convert("RGBA")
            bg.paste(img, mask=img.split()[3] if img.mode == "RGBA" else None)
            img = bg
        elif img.mode != "RGB":
            img = img.convert("RGB")

        # 保存原图
        img.save(new_path, "JPEG", quality=95)

        # 创建缩略图
        thumb_path = os.path.join(library_dir, "thumbnails", f"{image_id}.jpg")
        thumb_img = img.copy()
        thumb_img.thumbnail((80, 80), Image.Resampling.LANCZOS)
        thumb_img.save(thumb_path, "JPEG", quality=85)

        # 创建预览图
        preview_path = os.path.join(library_dir, "previews", f"{image_id}.jpg")
        preview_img = img.copy()
        preview_img.thumbnail((1000, 1000), Image.Resampling.LANCZOS)
        preview_img.save(preview_path, "JPEG", quality=90)

        # 添加到数据库
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(
                """
                INSERT INTO image_library (image_id, original_name, image_path, tags)
                VALUES (?, ?, ?, ?)
            """,
                (image_id, original_name, new_path, tags),
            )
            conn.commit()

        return image_id

    except Exception as e:
        logging.exception("添加图片到图片库失败")
        raise


def get_library_images(search_text=None):
    """获取图片库中的图片

    Args:
        search_text: 搜索文本，用于匹配图片名称或标签
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            if search_text:
                cursor.execute(
                    """
                    SELECT image_id, original_name, image_path, upload_time, usage_count, tags
                    FROM image_library
                    WHERE original_name LIKE ? OR tags LIKE ?
                    ORDER BY usage_count DESC, upload_time DESC
                """,
                    (f"%{search_text}%", f"%{search_text}%"),
                )
            else:
                cursor.execute(
                    """
                    SELECT image_id, original_name, image_path, upload_time, usage_count, tags
                    FROM image_library
                    ORDER BY usage_count DESC, upload_time DESC
                """
                )
            return cursor.fetchall()
    except Exception as e:
        logging.exception("获取图片库图片失败")
        return []


def use_library_image(image_id, product_id):
    """使用图片库中的图片

    Args:
        image_id: 图片库中的图片ID
        product_id: 目标商品ID
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            # 获取图片信息
            cursor.execute(
                """
                SELECT image_path, original_name FROM image_library
                WHERE image_id = ?
                """,
                (image_id,),
            )
            result = cursor.fetchone()

            if not result:
                raise ValueError(f"图片库中不存在ID为 {image_id} 的图片")

            src_path, original_name = result
            if not os.path.exists(src_path):
                raise ValueError(f"图片文件不存在: {src_path}")

            # 更新使用次数
            cursor.execute(
                """
                UPDATE image_library
                SET usage_count = usage_count + 1
                WHERE image_id = ?
                """,
                (image_id,),
            )

            # 复制到商品目录
            product_image_dir = get_product_image_dir(product_id)
            new_image_id = str(uuid.uuid4())
            new_filename = f"{new_image_id}.jpg"  # 统一使用jpg格式
            dst_path = os.path.join(product_image_dir, new_filename)

            # 确保目标目录存在
            os.makedirs(product_image_dir, exist_ok=True)
            os.makedirs(os.path.join(product_image_dir, "thumbnails"), exist_ok=True)
            os.makedirs(os.path.join(product_image_dir, "previews"), exist_ok=True)

            # 复制图片文件
            shutil.copy2(src_path, dst_path)
            shutil.copy2(
                os.path.join(
                    os.path.dirname(src_path), "thumbnails", f"{image_id}.jpg"
                ),
                os.path.join(product_image_dir, "thumbnails", new_filename),
            )
            shutil.copy2(
                os.path.join(os.path.dirname(src_path), "previews", f"{image_id}.jpg"),
                os.path.join(product_image_dir, "previews", new_filename),
            )

            # 添加图片记录
            cursor.execute(
                """
                INSERT INTO product_images (
                    image_id, product_id, image_name, image_path, is_primary,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """,
                (
                    new_image_id,
                    product_id,
                    original_name,  # 使用原始文件名
                    new_filename,  # 只保存文件名
                    0,
                ),
            )

            conn.commit()
            return new_image_id

    except Exception as e:
        logging.exception("使用图片库图片失败")
        raise


def add_product_image(product_id, source_path, is_primary=False):
    """添加商品图片

    Args:
        product_id: 商品ID
        source_path: 源图片路径
        is_primary: 是否设为主图
    """
    try:
        validate_image(source_path)

        # 创建目录结构
        product_dir = get_product_image_dir(product_id)
        thumbnails_dir = os.path.join(product_dir, "thumbnails")
        previews_dir = os.path.join(product_dir, "previews")
        os.makedirs(product_dir, exist_ok=True)
        os.makedirs(thumbnails_dir, exist_ok=True)
        os.makedirs(previews_dir, exist_ok=True)

        # 生成文件名
        image_id = str(uuid.uuid4())
        original_name = os.path.basename(source_path)
        file_ext = os.path.splitext(original_name)[1].lower()
        new_filename = f"{image_id}{file_ext}"

        # 复制并处理原图
        dst_path = os.path.join(product_dir, new_filename)
        img = Image.open(source_path)

        # 确保图片格式正确
        if img.mode in ("RGBA", "LA") or (
            img.mode == "P" and "transparency" in img.info
        ):
            bg = Image.new("RGB", img.size, (255, 255, 255))
            if img.mode == "P":
                img = img.convert("RGBA")
            bg.paste(img, mask=img.split()[3] if img.mode == "RGBA" else None)
            img = bg
        elif img.mode != "RGB":
            img = img.convert("RGB")

        # 统一使用jpg格式保存
        new_filename = f"{image_id}.jpg"
        dst_path = os.path.join(product_dir, new_filename)
        thumb_path = os.path.join(thumbnails_dir, new_filename)
        preview_path = os.path.join(previews_dir, new_filename)

        # 保存原图
        img.save(dst_path, "JPEG", quality=95)

        # 创建缩略图
        thumb_img = img.copy()
        thumb_img.thumbnail((80, 80), Image.Resampling.LANCZOS)
        thumb_img.save(thumb_path, "JPEG", quality=85)

        # 创建预览图
        preview_img = img.copy()
        preview_img.thumbnail((1000, 1000), Image.Resampling.LANCZOS)
        preview_img.save(preview_path, "JPEG", quality=90)

        # 保存到数据库
        with get_connection() as conn:
            cursor = conn.cursor()

            # 检查是否是第一张图片
            cursor.execute(
                "SELECT COUNT(*) FROM product_images WHERE product_id = ?",
                (product_id,),
            )
            is_first_image = cursor.fetchone()[0] == 0
            is_primary = is_primary or is_first_image

            # 如果是主图，重置其他图片状态
            if is_primary:
                cursor.execute(
                    "UPDATE product_images SET is_primary = 0 WHERE product_id = ?",
                    (product_id,),
                )

            # 插入图片记录
            cursor.execute(
                """
                INSERT INTO product_images (
                    image_id, product_id, image_name, image_path, is_primary,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """,
                (
                    image_id,
                    product_id,
                    original_name,
                    new_filename,  # 只保存文件名，不保存完整路径
                    1 if is_primary else 0,
                ),
            )

            # 更新商品主图
            if is_primary:
                cursor.execute(
                    "UPDATE products SET image_path = ? WHERE product_id = ?",
                    (new_filename, product_id),  # 同样只保存文件名
                )

            conn.commit()

        return image_id
    except Exception as e:
        logging.exception(f"添加商品图片失败: {str(e)}")
        raise


def delete_library_image(image_id):
    """从图片库中删除图片

    Args:
        image_id: 图片ID
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取图片信息
            cursor.execute(
                """
                SELECT image_path FROM image_library
                WHERE image_id = ?
                """,
                (image_id,),
            )
            result = cursor.fetchone()

            if not result:
                raise ValueError(f"图片不存在: {image_id}")

            image_path = result[0]
            library_dir = os.path.dirname(image_path)

            # 删除物理文件
            file_paths = [
                image_path,  # 原图
                os.path.join(library_dir, "thumbnails", f"{image_id}.jpg"),  # 缩略图
                os.path.join(library_dir, "previews", f"{image_id}.jpg"),  # 预览图
            ]

            for path in file_paths:
                if os.path.exists(path):
                    os.remove(path)
                    logging.info(f"删除图片文件: {path}")

            # 从数据库删除记录
            cursor.execute("DELETE FROM image_library WHERE image_id = ?", (image_id,))
            conn.commit()

            logging.info(f"成功从图片库删除图片: {image_id}")

    except Exception as e:
        logging.exception("删除图片库图片失败")
        raise
