"""
现代化数据库模型定义
使用SQLAlchemy 2.0的新语法
"""
from __future__ import annotations
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from pathlib import Path

from sqlalchemy import (
    String, Integer, DateTime, Text, Numeric, 
    ForeignKey, Boolean, create_engine
)
from sqlalchemy.orm import (
    DeclarativeBase, Mapped, mapped_column, 
    relationship, Session, sessionmaker
)
from sqlalchemy.sql import func


class Base(DeclarativeBase):
    """数据库基类"""
    pass


class ProductModel(Base):
    """商品数据库模型"""
    __tablename__ = "products"
    
    # 主键
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 基本信息
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    quantity: Mapped[int] = mapped_column(Integer, default=1)
    category: Mapped[Optional[str]] = mapped_column(String(100), index=True)
    unit: Mapped[str] = mapped_column(String(20), default="个")
    status: Mapped[str] = mapped_column(String(20), default="在库", index=True)
    location: Mapped[Optional[str]] = mapped_column(String(255))
    
    # 供应商信息
    supplier: Mapped[Optional[str]] = mapped_column(String(255))
    supplier_link: Mapped[Optional[str]] = mapped_column(Text)
    purchase_link: Mapped[Optional[str]] = mapped_column(Text)
    purchaser: Mapped[Optional[str]] = mapped_column(String(100))
    
    # 价格信息 (使用Numeric确保精度)
    purchase_price: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), default=Decimal('0')
    )
    shipping_cost: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), default=Decimal('0')
    )
    other_cost: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), default=Decimal('0')
    )
    total_cost: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), default=Decimal('0')
    )
    selling_price: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), default=Decimal('0')
    )
    discount_rate: Mapped[Decimal] = mapped_column(
        Numeric(precision=5, scale=2), default=Decimal('100')
    )
    total_profit: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), default=Decimal('0')
    )
    
    # 其他信息
    selling_link: Mapped[Optional[str]] = mapped_column(Text)
    image_path: Mapped[Optional[str]] = mapped_column(String(500))
    remarks: Mapped[Optional[str]] = mapped_column(Text)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    
    # 关系
    batches: Mapped[List["BatchModel"]] = relationship(
        "BatchModel", secondary="product_batches", back_populates="products"
    )
    images: Mapped[List["ProductImageModel"]] = relationship(
        "ProductImageModel", back_populates="product", cascade="all, delete-orphan"
    )
    transactions: Mapped[List["TransactionModel"]] = relationship(
        "TransactionModel", back_populates="product", cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Product(id={self.id}, name='{self.name}', category='{self.category}')>"


class BatchModel(Base):
    """批次数据库模型"""
    __tablename__ = "batches"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    code: Mapped[str] = mapped_column(String(50), unique=True, nullable=False, index=True)
    name: Mapped[Optional[str]] = mapped_column(String(255))
    status: Mapped[str] = mapped_column(String(20), default="活跃", index=True)
    description: Mapped[Optional[str]] = mapped_column(Text)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    
    # 关系
    products: Mapped[List["ProductModel"]] = relationship(
        "ProductModel", secondary="product_batches", back_populates="batches"
    )
    
    def __repr__(self) -> str:
        return f"<Batch(id={self.id}, code='{self.code}', name='{self.name}')>"


class ProductBatchModel(Base):
    """商品批次关联表"""
    __tablename__ = "product_batches"
    
    product_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("products.id"), primary_key=True
    )
    batch_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("batches.id"), primary_key=True
    )
    quantity: Mapped[int] = mapped_column(Integer, default=1)
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )


class ProductImageModel(Base):
    """商品图片模型"""
    __tablename__ = "product_images"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    product_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("products.id"), nullable=False
    )
    image_path: Mapped[str] = mapped_column(String(500), nullable=False)
    is_primary: Mapped[bool] = mapped_column(Boolean, default=False)
    description: Mapped[Optional[str]] = mapped_column(String(255))
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    
    # 关系
    product: Mapped["ProductModel"] = relationship(
        "ProductModel", back_populates="images"
    )
    
    def __repr__(self) -> str:
        return f"<ProductImage(id={self.id}, product_id={self.product_id}, path='{self.image_path}')>"


class TransactionModel(Base):
    """交易记录模型"""
    __tablename__ = "transactions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    product_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("products.id"), nullable=False
    )
    
    # 交易信息
    transaction_type: Mapped[str] = mapped_column(String(20), nullable=False)  # 'purchase', 'sale', 'adjustment'
    quantity: Mapped[int] = mapped_column(Integer, nullable=False)
    unit_price: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), nullable=False
    )
    total_amount: Mapped[Decimal] = mapped_column(
        Numeric(precision=10, scale=2), nullable=False
    )
    
    # 其他信息
    description: Mapped[Optional[str]] = mapped_column(Text)
    reference_number: Mapped[Optional[str]] = mapped_column(String(100))
    
    # 时间戳
    transaction_date: Mapped[datetime] = mapped_column(DateTime(timezone=True))
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now()
    )
    
    # 关系
    product: Mapped["ProductModel"] = relationship(
        "ProductModel", back_populates="transactions"
    )
    
    def __repr__(self) -> str:
        return f"<Transaction(id={self.id}, type='{self.transaction_type}', amount={self.total_amount})>"


# 数据库引擎和会话配置
class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str = "sqlite:///inventory.db"):
        self.engine = create_engine(
            database_url,
            echo=False,  # 设置为True可以看到SQL语句
            pool_pre_ping=True,
        )
        self.SessionLocal = sessionmaker(
            autocommit=False, autoflush=False, bind=self.engine
        )
    
    def create_tables(self) -> None:
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def drop_tables(self) -> None:
        """删除所有表（仅用于测试）"""
        Base.metadata.drop_all(bind=self.engine)


# 全局数据库管理器实例
db_manager = DatabaseManager()
