# 财务处理模块 (finances.py)

## 功能概述
`finances.py` 提供了完整的财务数据处理和分析功能，包括财务统计、日常报表生成、商品业绩分析等功能。该模块基于 SQLite 数据库，提供精确的财务计算和全面的数据分析能力。

## 核心功能

### 1. 财务统计
```python
def get_financial_stats(start_date=None, end_date=None, category=None):
```
- 功能：获取财务统计信息
- 参数：
  - start_date: 开始日期（可选）
  - end_date: 结束日期（可选）
  - category: 商品类别（可选）
- 返回数据：
  ```python
  {
      "categories": [  # 按类别统计
          {
              "category": "类别名称",
              "product_count": 商品数量,
              "total_cost": 总成本,
              "total_profit": 总利润,
              "total_shipping_cost": 总运费,
              "total_other_cost": 其他成本,
              "transaction_count": 交易次数,
              "total_quantity": 总数量,
              "total_revenue": 总收入,
              "transaction_profit": 交易利润,
              "profit_margin": 利润率,
              "avg_cost_per_product": 平均成本,
              "avg_profit_per_product": 平均利润
          }
      ],
      "total": {  # 总体统计
          # 与类别统计相同的字段
      },
      "period": {
          "start_date": 开始日期,
          "end_date": 结束日期
      }
  }
  ```

### 2. 每日统计
```python
def get_daily_stats(days=30):
```
- 功能：获取每日统计数据
- 参数：
  - days: 统计天数（默认30天）
- 返回数据：
  ```python
  [
      {
          "date": "日期",
          "transaction_count": 交易次数,
          "total_quantity": 总数量,
          "total_revenue": 总收入,
          "total_profit": 总利润,
          "profit_margin": 利润率
      }
  ]
  ```

### 3. 商品业绩分析
```python
def get_product_performance(product_id):
```
- 功能：获取单个商品的业绩统计
- 参数：
  - product_id: 商品ID
- 返回数据：
  ```python
  {
      "product_info": {  # 商品基本信息
          "product_id": "商品ID",
          "name": "商品名称",
          # 其他商品属性
      },
      "performance": {  # 业绩统计
          "transaction_count": 交易次数,
          "total_quantity": 总销量,
          "total_revenue": 总收入,
          "total_profit": 总利润,
          "avg_unit_price": 平均单价
      }
  }
  ```

## 数据计算

### 1. 成本计算
- 总成本 = 采购成本 + 运输成本 + 其他成本
- 平均成本 = 总成本 / 商品数量

### 2. 利润计算
- 交易利润 = 销售收入 - 总成本
- 利润率 = (交易利润 / 销售收入) * 100%
- 平均利润 = 总利润 / 商品数量

### 3. 统计指标
- 交易频率
- 销量分析
- 收入趋势
- 利润变化

## 数据库交互

### 1. 查询优化
- 使用索引加速查询
- 优化 SQL 语句
- 使用事务保证一致性

### 2. 数据聚合
- 按类别分组
- 按时间聚合
- 多维度统计

## 使用示例

### 1. 获取财务概览
```python
# 获取所有类别的财务统计
stats = get_financial_stats()

# 获取指定时间段的统计
stats = get_financial_stats(
    start_date="2024-01-01",
    end_date="2024-01-31"
)

# 获取特定类别的统计
stats = get_financial_stats(category="电子产品")
```

### 2. 分析每日数据
```python
# 获取最近30天的统计
daily_stats = get_daily_stats()

# 获取最近7天的统计
weekly_stats = get_daily_stats(days=7)
```

### 3. 分析商品业绩
```python
# 获取单个商品的详细业绩
performance = get_product_performance("PROD001")
```

## 错误处理
- 数据库连接异常处理
- 查询执行错误处理
- 数据验证和清理
- 完整的日志记录

## 性能优化
1. 查询优化：
   - 使用适当的索引
   - 优化 SQL 语句
   - 减少数据库访问

2. 数据缓存：
   - 缓存常用数据
   - 定期更新缓存
   - 合理的缓存策略

3. 批量处理：
   - 批量数据更新
   - 事务管理
   - 内存优化

## 注意事项
1. 数据精度：
   - 使用 Decimal 类型
   - 保留适当小数位
   - 注意舍入规则

2. 时间处理：
   - 统一时区处理
   - 日期格式转换
   - 时间范围验证

3. 数据一致性：
   - 事务完整性
   - 数据验证
   - 异常回滚

## 扩展建议
1. 添加更多财务指标
2. 实现数据导出功能
3. 添加图表生成功能
4. 支持自定义报表
5. 添加预测分析功能 