import logging
from datetime import datetime, timedelta
from database.db_utils import get_connection


def get_financial_stats(start_date=None, end_date=None, category=None):
    """获取财务统计信息

    Args:
        start_date: 开始日期,默认为None(不限制)
        end_date: 结束日期,默认为None(不限制)
        category: 商品类别,默认为None(所有类别)

    Returns:
        包含统计信息的字典
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 构建基础查询
            query = """
                SELECT 
                    p.category,
                    COUNT(DISTINCT p.product_id) as product_count,
                    SUM(p.total_cost) as total_cost,
                    SUM(p.total_profit) as total_profit,
                    SUM(p.total_shipping_cost) as total_shipping_cost,
                    SUM(p.total_other_cost) as total_other_cost,
                    COUNT(DISTINCT t.transaction_id) as transaction_count,
                    SUM(t.quantity) as total_quantity,
                    SUM(t.total_price) as total_revenue,
                    SUM(t.profit) as transaction_profit
                FROM products p
                LEFT JOIN transactions t ON p.product_id = t.product_id
                WHERE 1=1
            """

            params = []

            # 添加日期过滤
            if start_date:
                query += " AND t.transaction_time >= ?"
                params.append(start_date)
            if end_date:
                query += " AND t.transaction_time <= ?"
                params.append(end_date)

            # 添加类别过滤
            if category:
                query += " AND p.category = ?"
                params.append(category)

            # 按类别分组
            query += " GROUP BY p.category"

            # 执行查询
            cursor.execute(query, params)
            rows = cursor.fetchall()

            # 处理结果
            categories = []
            total_stats = {
                "product_count": 0,
                "total_cost": 0,
                "total_profit": 0,
                "total_shipping_cost": 0,
                "total_other_cost": 0,
                "transaction_count": 0,
                "total_quantity": 0,
                "total_revenue": 0,
                "transaction_profit": 0,
            }

            for row in rows:
                category_stats = {
                    "category": row[0],
                    "product_count": row[1] or 0,
                    "total_cost": row[2] or 0,
                    "total_profit": row[3] or 0,
                    "total_shipping_cost": row[4] or 0,
                    "total_other_cost": row[5] or 0,
                    "transaction_count": row[6] or 0,
                    "total_quantity": row[7] or 0,
                    "total_revenue": row[8] or 0,
                    "transaction_profit": row[9] or 0,
                }

                # 计算其他指标
                if category_stats["total_revenue"] > 0:
                    category_stats["profit_margin"] = (
                        category_stats["transaction_profit"]
                        / category_stats["total_revenue"]
                        * 100
                    )
                else:
                    category_stats["profit_margin"] = 0

                if category_stats["product_count"] > 0:
                    category_stats["avg_cost_per_product"] = (
                        category_stats["total_cost"] / category_stats["product_count"]
                    )
                    category_stats["avg_profit_per_product"] = (
                        category_stats["total_profit"] / category_stats["product_count"]
                    )
                else:
                    category_stats["avg_cost_per_product"] = 0
                    category_stats["avg_profit_per_product"] = 0

                categories.append(category_stats)

                # 累加总计
                for key in total_stats:
                    if key in category_stats:
                        total_stats[key] += category_stats[key]

            # 计算总体指标
            if total_stats["total_revenue"] > 0:
                total_stats["profit_margin"] = (
                    total_stats["transaction_profit"]
                    / total_stats["total_revenue"]
                    * 100
                )
            else:
                total_stats["profit_margin"] = 0

            if total_stats["product_count"] > 0:
                total_stats["avg_cost_per_product"] = (
                    total_stats["total_cost"] / total_stats["product_count"]
                )
                total_stats["avg_profit_per_product"] = (
                    total_stats["total_profit"] / total_stats["product_count"]
                )
            else:
                total_stats["avg_cost_per_product"] = 0
                total_stats["avg_profit_per_product"] = 0

            return {
                "categories": categories,
                "total": total_stats,
                "period": {
                    "start_date": start_date,
                    "end_date": end_date,
                },
            }

    except Exception as e:
        logging.exception("获取财务统计信息失败")
        raise e


def get_daily_stats(days=30):
    """获取每日统计数据

    Args:
        days: 统计天数,默认30天

    Returns:
        每日统计数据列表
    """
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        with get_connection() as conn:
            cursor = conn.cursor()

            # 查询每日交易数据
            cursor.execute(
                """
                SELECT 
                    date(transaction_time) as date,
                    COUNT(transaction_id) as transaction_count,
                    SUM(quantity) as total_quantity,
                    SUM(total_price) as total_revenue,
                    SUM(profit) as total_profit
                FROM transactions
                WHERE transaction_time BETWEEN ? AND ?
                GROUP BY date(transaction_time)
                ORDER BY date
                """,
                (start_date, end_date),
            )

            daily_stats = []
            for row in cursor.fetchall():
                stats = {
                    "date": row[0],
                    "transaction_count": row[1],
                    "total_quantity": row[2],
                    "total_revenue": row[3],
                    "total_profit": row[4],
                }

                # 计算利润率
                if stats["total_revenue"] > 0:
                    stats["profit_margin"] = (
                        stats["total_profit"] / stats["total_revenue"] * 100
                    )
                else:
                    stats["profit_margin"] = 0

                daily_stats.append(stats)

            return daily_stats

    except Exception as e:
        logging.exception("获取每日统计数据失败")
        raise e


def get_product_performance(product_id):
    """获取单个商品的业绩统计

    Args:
        product_id: 商品ID

    Returns:
        商品业绩统计信息
    """
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # 获取商品基本信息
            cursor.execute(
                """
                SELECT *
                FROM products
                WHERE product_id = ?
                """,
                (product_id,),
            )

            columns = [description[0] for description in cursor.description]
            product_row = cursor.fetchone()
            if not product_row:
                raise ValueError(f"商品不存在: {product_id}")

            product_info = dict(zip(columns, product_row))

            # 获取交易统计
            cursor.execute(
                """
                SELECT 
                    COUNT(*) as transaction_count,
                    SUM(quantity) as total_quantity,
                    SUM(total_price) as total_revenue,
                    SUM(profit) as total_profit,
                    AVG(unit_price) as avg_unit_price,
                    MAX(unit_price) as max_unit_price,
                    MIN(unit_price) as min_unit_price
                FROM transactions
                WHERE product_id = ?
                """,
                (product_id,),
            )

            transaction_stats = dict(
                zip(
                    [
                        "transaction_count",
                        "total_quantity",
                        "total_revenue",
                        "total_profit",
                        "avg_unit_price",
                        "max_unit_price",
                        "min_unit_price",
                    ],
                    cursor.fetchone(),
                )
            )

            # 计算其他指标
            if transaction_stats["total_revenue"] > 0:
                transaction_stats["profit_margin"] = (
                    transaction_stats["total_profit"]
                    / transaction_stats["total_revenue"]
                    * 100
                )
            else:
                transaction_stats["profit_margin"] = 0

            return {
                "product": product_info,
                "transactions": transaction_stats,
            }

    except Exception as e:
        logging.exception(f"获取商品业绩统计失败: {product_id}")
        raise e
