# GUI 模块

## 概述
GUI 模块实现了库存管理系统的图形用户界面，基于 PyQt6 框架开发。

## 主要组件

### 1. MainWindow
主窗口类，是应用程序的核心界面。
- 使用 PyQt6 的 QMainWindow 作为基类
- 实现了商品管理和批次管理两个主要功能标签页
- 支持数据的导入导出
- 提供实时搜索和过滤功能

### 2. 对话框
所有对话框均基于 PyQt6.QtWidgets.QDialog：
- ProductDialog: 商品编辑对话框
- BatchDialog: 批次管理对话框
- FinanceDialog: 财务统计对话框
- DatabaseDialog: 数据库管理对话框
- ImageDialog: 图片管理对话框

### 3. 自定义控件
基于 PyQt6 的各种自定义控件：
- BatchTable: 批次表格控件
- BatchToolBar: 批次工具栏
- ProductTable: 商品表格控件
- ProductToolBar: 商品工具栏
- ImagePathDelegate: 图片路径代理
- PreviewDialog: 图片预览对话框

## 技术特点

### 1. PyQt6 特性使用
- 使用 Qt6 的新信号槽语法
- 采用 Qt6 的新样式表系统
- 使用 Qt6 的新图标主题
- 支持高 DPI 显示

### 2. 性能优化
- 使用延迟加载机制
- 实现数据缓存
- 优化大量数据的显示
- 异步处理耗时操作

### 3. 用户体验
- 响应式界面设计
- 实时搜索和过滤
- 批量操作支持
- 自动刷新机制

### 4. 错误处理
- 统一的异常处理机制
- 用户友好的错误提示
- 详细的日志记录
- 自动恢复机制

## 依赖关系
- PyQt6 >= 6.0.0
- Python >= 3.8
- 其他依赖见 requirements.txt

## 使用说明

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python -m gui.main_window
```

### 3. 开发注意事项
- 所有 UI 代码必须使用 PyQt6
- 遵循 Qt 的信号槽机制
- 使用统一的错误处理
- 保持代码风格一致

## 更新日志

### v1.6.5
- 升级到 PyQt6
- 优化用户界面
- 改进错误处理
- 增强数据管理功能

### v1.6.4
- 完善批次管理
- 增加图片管理
- 优化性能
- 修复已知问题

## 目录结构
```
gui/
├── main_window.py      # 主窗口实现
├── dialogs/           # 对话框组件
│   ├── product_dialog.py    # 商品编辑对话框
│   ├── batch_dialog.py     # 批次管理对话框
│   ├── image_dialog.py     # 图片管理对话框
│   └── transaction_dialog.py # 交易记录对话框
└── widgets/           # 自定义控件
    ├── product_table.py    # 商品表格控件
    ├── batch_list.py      # 批次列表控件
    └── image_viewer.py    # 图片查看器控件
```

## 核心组件

### 1. 主窗口 (main_window.py)
- **功能特性**
  - 商品列表显示和管理
  - 批次管理功能
  - 工具栏和菜单系统
  - 状态栏信息显示
  - 搜索和筛选功能
  - 数据导入导出

- **界面布局**
  - 顶部：菜单栏和工具栏
  - 中部：商品列表/数据表格
  - 左侧：批次/分类树形视图
  - 右侧：详细信息面板
  - 底部：状态栏

### 2. 对话框组件 (dialogs/)

#### 商品对话框 (product_dialog.py)
- 商品信息编辑
- 图片管理集成
- 价格和成本设置
- 批次关联管理
- 数据验证功能

#### 批次对话框 (batch_dialog.py)
- 批次信息管理
- 商品关联操作
- 批量处理功能
- 状态管理

#### 图片对话框 (image_dialog.py)
- 图片上传和预览
- 缩略图生成
- 图片编辑功能
- 多图片管理

#### 交易对话框 (transaction_dialog.py)
- 交易记录管理
- 收支明细录入
- 数据统计显示
- 打印功能

### 3. 自定义控件 (widgets/)

#### 商品表格 (product_table.py)
- 自定义表格视图
- 排序和筛选
- 右键菜单
- 批量操作

#### 批次列表 (batch_list.py)
- 树形结构显示
- 拖拽支持
- 状态显示
- 快速操作

#### 图片查看器 (image_viewer.py)
- 图片预览
- 缩放功能
- 旋转功能
- 基本编辑

## 界面特性

### 1. 响应式设计
- 窗口大小自适应
- 控件布局灵活
- 分辨率适配
- DPI缩放支持

### 2. 用户交互
- 键盘快捷键
- 右键菜单
- 拖拽操作
- 工具提示

### 3. 主题系统
- 明暗主题切换
- 自定义颜色方案
- 图标主题
- 字体设置

### 4. 性能优化
- 延迟加载
- 分页显示
- 异步加载
- 缓存机制

## 使用说明

### 1. 主窗口操作
- 使用工具栏快速访问常用功能
- 通过菜单访问完整功能集
- 使用快捷键提高操作效率
- 状态栏查看系统信息

### 2. 数据管理
- 双击项目编辑详细信息
- 右键菜单访问上下文操作
- 拖拽实现批次关联
- 使用筛选和搜索定位数据

### 3. 图片处理
- 支持图片拖拽上传
- 预览和编辑功能
- 批量图片处理
- 自动生成缩略图

## 开发指南

### 1. 新增界面
- 遵循现有的模块结构
- 使用统一的样式系统
- 实现必要的信号和槽
- 添加适当的文档注释

### 2. 自定义控件
- 继承适当的基类
- 实现必要的事件处理
- 保持与现有控件一致的接口
- 注意性能优化

### 3. 错误处理
- 使用统一的错误处理机制
- 提供友好的错误提示
- 记录详细的错误日志
- 实现错误恢复机制

## 测试要求

### 1. 界面测试
- 功能完整性测试
- 界面响应测试
- 数据显示正确性
- 用户交互测试

### 2. 性能测试
- 大数据加载测试
- 响应时间测试
- 内存使用监控
- 资源释放验证

### 3. 兼容性测试
- 不同分辨率测试
- 不同DPI设置测试
- 不同主题测试
- 不同平台测试 
