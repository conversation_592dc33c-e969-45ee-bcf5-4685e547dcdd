from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QFormLayout,
    QLineEdit,
    QComboBox,
    QPushButton,
    QDateEdit,
    QMessageBox,
)
from PyQt6.QtCore import QDate
import logging
from database import db_utils


class BatchDialog(QDialog):
    """批次对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("新建批次")
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 表单布局
        form_layout = QFormLayout()

        # 批次编号
        self.code_input = QLineEdit()
        form_layout.addRow("批次编号:", self.code_input)

        # 批次名称
        self.name_input = QLineEdit()
        form_layout.addRow("批次名称:", self.name_input)

        # 批次状态
        self.status_combo = QComboBox()
        self.status_combo.addItems(["进行中", "已完成", "已取消"])
        form_layout.addRow("批次状态:", self.status_combo)

        # 创建日期
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        form_layout.addRow("创建日期:", self.date_input)

        # 添加表单布局
        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QVBoxLayout()
        self.save_button = QPushButton("保存")
        self.cancel_button = QPushButton("取消")
        self.save_button.clicked.connect(self.save_batch)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def save_batch(self):
        """保存批次"""
        try:
            # 验证数据
            code = self.code_input.text().strip()
            if not code:
                QMessageBox.warning(self, "警告", "批次编号不能为空")
                return

            name = self.name_input.text().strip()
            if not name:
                QMessageBox.warning(self, "警告", "批次名称不能为空")
                return

            # 收集数据
            batch_data = {
                "code": code,
                "name": name,
                "status": self.status_combo.currentText(),
                "created_at": self.date_input.date().toString("yyyy-MM-dd"),
            }

            # 保存批次
            db_utils.add_batch(batch_data)

            QMessageBox.information(self, "成功", "批次保存成功")
            self.accept()

        except Exception as e:
            logging.exception("保存批次失败")
            QMessageBox.critical(self, "错误", f"保存批次失败: {str(e)}")

    def load_batch(self, batch_id):
        """加载批次数据"""
        try:
            batch = db_utils.get_batch(batch_id)
            if batch:
                self.code_input.setText(batch["code"])
                self.name_input.setText(batch["name"])
                self.status_combo.setCurrentText(batch["status"])
                self.date_input.setDate(
                    QDate.fromString(batch["created_at"], "yyyy-MM-dd")
                )

        except Exception as e:
            logging.exception("加载批次数据失败")
            QMessageBox.critical(self, "错误", f"加载批次数据失败: {str(e)}")

    def update_batch(self, batch_id):
        """更新批次"""
        try:
            # 验证数据
            code = self.code_input.text().strip()
            if not code:
                QMessageBox.warning(self, "警告", "批次编号不能为空")
                return

            name = self.name_input.text().strip()
            if not name:
                QMessageBox.warning(self, "警告", "批次名称不能为空")
                return

            # 收集数据
            batch_data = {
                "code": code,
                "name": name,
                "status": self.status_combo.currentText(),
                "created_at": self.date_input.date().toString("yyyy-MM-dd"),
            }

            # 更新批次
            db_utils.update_batch(batch_id, batch_data)

            QMessageBox.information(self, "成功", "批次更新成功")
            self.accept()

        except Exception as e:
            logging.exception("更新批次失败")
            QMessageBox.critical(self, "错误", f"更新批次失败: {str(e)}")

    def delete_batch(self, batch_id):
        """删除批次"""
        try:
            reply = QMessageBox.question(
                self,
                "确认删除",
                "确定要删除该批次吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                db_utils.delete_batch(batch_id)
                QMessageBox.information(self, "成功", "批次删除成功")
                self.accept()

        except Exception as e:
            logging.exception("删除批次失败")
            QMessageBox.critical(self, "错误", f"删除批次失败: {str(e)}")


class StatusDialog(QDialog):
    def __init__(self, parent=None, batch_id=None):
        super().__init__(parent)
        self.batch_id = batch_id
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("更新批次状态")
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()

        # 创建输入控件
        self.status_combo = QComboBox()
        self.status_combo.addItems(["在库", "已售", "损坏", "其他"])
        self.sale_price_input = QLineEdit()
        self.sale_date_input = QDateEdit()
        self.sale_date_input.setDate(QDate.currentDate())
        self.remarks_input = QLineEdit()

        # 添加到表单布局
        form_layout.addRow("状态:", self.status_combo)
        form_layout.addRow("售价:", self.sale_price_input)
        form_layout.addRow("销售日期:", self.sale_date_input)
        form_layout.addRow("备注:", self.remarks_input)

        # 添加按钮
        self.save_btn = QPushButton("保存")
        self.cancel_btn = QPushButton("取消")
        self.save_btn.clicked.connect(self.save_status)
        self.cancel_btn.clicked.connect(self.reject)

        # 添加到主布局
        layout.addLayout(form_layout)
        layout.addWidget(self.save_btn)
        layout.addWidget(self.cancel_btn)

        # 根据状态显示/隐藏相关控件
        self.status_combo.currentTextChanged.connect(self.on_status_changed)
        self.on_status_changed(self.status_combo.currentText())

    def on_status_changed(self, status):
        """状态改变时的处理"""
        is_sold = status == "已售"
        self.sale_price_input.setEnabled(is_sold)
        self.sale_date_input.setEnabled(is_sold)

    def save_status(self):
        """保存状态更新"""
        try:
            status = self.status_combo.currentText()
            sale_info = None
            if status == "已售":
                sale_price = float(self.sale_price_input.text() or 0)
                if sale_price < 0:
                    raise ValueError("售价不能为负数")
                sale_info = {
                    "sale_price": sale_price,
                    "sale_date": self.sale_date_input.date().toString("yyyy-MM-dd"),
                }

            db_utils.update_batch_status(self.batch_id, status, sale_info)
            self.accept()

        except ValueError as e:
            QMessageBox.warning(self, "输入错误", str(e))
        except Exception as e:
            logging.exception("更新批次状态失败")
            QMessageBox.critical(self, "错误", f"更新批次状态失败: {str(e)}")
