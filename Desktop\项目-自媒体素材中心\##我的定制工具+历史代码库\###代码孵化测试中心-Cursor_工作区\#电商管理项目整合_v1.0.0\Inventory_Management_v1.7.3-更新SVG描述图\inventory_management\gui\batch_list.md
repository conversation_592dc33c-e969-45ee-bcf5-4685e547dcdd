# 批次列表控件 (batch_list.py)

## 功能概述
`batch_list.py` 实现了一个自定义的批次列表控件,继承自 `QTreeWidget`,提供了批次数据的树形显示、拖拽操作、状态管理等功能。该控件支持批次的层级显示、状态颜色标识、快速操作等特性。

## 类定义

### BatchList 类
```python
class BatchList(QTreeWidget):
    """批次列表控件类,继承自 QTreeWidget"""
```

#### 初始化
```python
def __init__(self, parent=None):
    """
    初始化批次列表
    :param parent: 父窗口
    """
    super().__init__(parent)
    self.setup_tree()
    self.setup_context_menu()
    self.setup_connections()
```

## 核心功能

### 1. 树形设置
```python
def setup_tree(self):
    """设置树形控件基本属性"""
    # 列设置
    self.setColumnCount(3)
    self.setHeaderLabels(["批次名称", "商品数量", "状态"])
    
    # 树形属性
    self.setAlternatingRowColors(True)
    self.setDragEnabled(True)
    self.setAcceptDrops(True)
    self.setDropIndicatorShown(True)
    self.setSelectionMode(QTreeWidget.ExtendedSelection)
```

### 2. 数据加载
```python
def load_data(self, batches):
    """
    加载批次数据
    :param batches: 批次对象列表
    """
    self.clear()  # 清空树形控件
    for batch in batches:
        self.add_batch_item(batch)
        
def add_batch_item(self, batch):
    """
    添加批次项
    :param batch: 批次对象
    """
    item = QTreeWidgetItem()
    item.setText(0, batch.batch_name)
    item.setText(1, str(len(batch.products)))
    item.setText(2, batch.status)
    item.setData(0, Qt.UserRole, batch.batch_id)
    
    # 设置状态颜色
    status_color = self.get_status_color(batch.status)
    item.setBackground(2, QBrush(status_color))
    
    # 添加商品子项
    for product in batch.products:
        child = QTreeWidgetItem()
        child.setText(0, product.name)
        child.setText(1, str(product.quantity))
        child.setText(2, product.status)
        child.setData(0, Qt.UserRole, product.product_id)
        item.addChild(child)
    
    self.addTopLevelItem(item)
    item.setExpanded(True)
```

### 3. 状态管理
```python
def get_status_color(self, status):
    """
    获取状态对应的颜色
    :param status: 状态文本
    :return: QColor 对象
    """
    status_colors = {
        "进行中": QColor("#e3f2fd"),
        "已完成": QColor("#c8e6c9"),
        "已取消": QColor("#ffcdd2"),
        "暂停": QColor("#fff3e0")
    }
    return status_colors.get(status, QColor("#ffffff"))

def update_batch_status(self, batch_id, status):
    """
    更新批次状态
    :param batch_id: 批次ID
    :param status: 新状态
    """
    items = self.findItems(batch_id, Qt.MatchExactly | Qt.MatchRecursive, 0)
    if items:
        item = items[0]
        item.setText(2, status)
        item.setBackground(2, QBrush(self.get_status_color(status)))
```

### 4. 拖放操作
```python
def dragEnterEvent(self, event):
    """拖入事件"""
    if event.mimeData().hasFormat("application/x-qabstractitemmodeldatalist"):
        event.acceptProposedAction()
        
def dropEvent(self, event):
    """放下事件"""
    if event.mimeData().hasFormat("application/x-qabstractitemmodeldatalist"):
        drop_item = self.itemAt(event.pos())
        if drop_item:
            batch_id = drop_item.data(0, Qt.UserRole)
            self.handle_product_drop(event.mimeData(), batch_id)
            
def handle_product_drop(self, mime_data, batch_id):
    """
    处理商品拖放
    :param mime_data: MIME数据
    :param batch_id: 目标批次ID
    """
    try:
        product_ids = self.decode_mime_data(mime_data)
        for product_id in product_ids:
            self.db.add_product_to_batch(product_id, batch_id)
        self.refresh()
    except Exception as e:
        ErrorHandler.handle_error(e, self)
```

### 5. 右键菜单
```python
def setup_context_menu(self):
    """设置右键菜单"""
    self.setContextMenuPolicy(Qt.CustomContextMenu)
    self.customContextMenuRequested.connect(self.show_context_menu)
    
def show_context_menu(self, pos):
    """
    显示右键菜单
    :param pos: 鼠标位置
    """
    item = self.itemAt(pos)
    if item:
        menu = QMenu(self)
        
        if self.is_batch_item(item):
            # 批次菜单
            edit_action = menu.addAction("编辑批次")
            delete_action = menu.addAction("删除批次")
            menu.addSeparator()
            status_menu = menu.addMenu("设置状态")
            for status in ["进行中", "已完成", "已取消", "暂停"]:
                status_action = status_menu.addAction(status)
                status_action.triggered.connect(
                    lambda s=status: self.set_batch_status(item, s)
                )
        else:
            # 商品菜单
            remove_action = menu.addAction("从批次移除")
            view_action = menu.addAction("查看商品")
            
        menu.exec_(self.mapToGlobal(pos))
```

### 6. 批次操作
```python
def is_batch_item(self, item):
    """
    判断是否为批次项
    :param item: 树形项
    :return: bool
    """
    return item.parent() is None

def set_batch_status(self, item, status):
    """
    设置批次状态
    :param item: 树形项
    :param status: 状态文本
    """
    try:
        batch_id = item.data(0, Qt.UserRole)
        self.db.update_batch_status(batch_id, status)
        self.update_batch_status(batch_id, status)
    except Exception as e:
        ErrorHandler.handle_error(e, self)

def remove_product_from_batch(self, item):
    """
    从批次中移除商品
    :param item: 树形项
    """
    try:
        product_id = item.data(0, Qt.UserRole)
        batch_id = item.parent().data(0, Qt.UserRole)
        self.db.remove_product_from_batch(product_id, batch_id)
        item.parent().removeChild(item)
    except Exception as e:
        ErrorHandler.handle_error(e, self)
```

## 信号定义
```python
# 自定义信号
batch_selected = pyqtSignal(str)  # 批次选中信号
batch_status_changed = pyqtSignal(str, str)  # 批次状态变更信号
product_removed = pyqtSignal(str, str)  # 商品移除信号
```

## 事件处理

### 1. 选择事件
```python
def selectionChanged(self, selected, deselected):
    """选择变更事件"""
    super().selectionChanged(selected, deselected)
    current = self.currentItem()
    if current and self.is_batch_item(current):
        batch_id = current.data(0, Qt.UserRole)
        self.batch_selected.emit(batch_id)
```

### 2. 双击事件
```python
def mouseDoubleClickEvent(self, event):
    """鼠标双击事件"""
    super().mouseDoubleClickEvent(event)
    item = self.itemAt(event.pos())
    if item:
        if self.is_batch_item(item):
            self.edit_batch(item)
        else:
            self.view_product(item)
```

## 样式设置
```python
def setup_style(self):
    """设置树形控件样式"""
    self.setStyleSheet("""
        QTreeWidget {
            border: 1px solid #d0d0d0;
            background: white;
        }
        QTreeWidget::item {
            height: 25px;
            padding: 2px;
        }
        QTreeWidget::item:selected {
            background: #e3f2fd;
        }
        QTreeWidget::item:hover {
            background: #f5f5f5;
        }
    """)
```

## 依赖关系

### 1. PyQt5 组件
- QTreeWidget
- QTreeWidgetItem
- QMenu
- QColor
- QBrush
- pyqtSignal

### 2. 自定义组件
- ErrorHandler
- DatabaseManager

## 使用示例
```python
# 创建列表
batch_list = BatchList(parent_window)

# 加载数据
batches = db.get_batches()
batch_list.load_data(batches)

# 连接信号
batch_list.batch_selected.connect(on_batch_selected)
batch_list.batch_status_changed.connect(on_status_changed)
batch_list.product_removed.connect(on_product_removed)
```

## 注意事项
1. 树形结构的正确维护
2. 拖放操作的数据一致性
3. 状态更新的实时性
4. 性能优化考虑
5. 错误处理机制 