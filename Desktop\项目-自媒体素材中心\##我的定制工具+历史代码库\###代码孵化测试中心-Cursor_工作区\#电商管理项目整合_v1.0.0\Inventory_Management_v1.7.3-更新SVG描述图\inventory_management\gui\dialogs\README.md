# 对话框模块

## 概述
对话框模块包含了系统中所有的对话框界面，基于 PyQt6 开发，提供了丰富的用户交互功能。

## 对话框列表

### 1. 产品管理
#### ProductDialog
产品编辑对话框
- 继承自 QDialog
- 支持产品信息的编辑
- 实现图片管理
- 提供批次关联

### 2. 批次管理
#### BatchDialog
批次编辑对话框
- 继承自 QDialog
- 支持批次信息的编辑
- 实现产品关联
- 提供统计信息

### 3. 财务管理
#### FinanceDialog
财务统计对话框
- 继承自 QDialog
- 支持多维度统计
- 实现图表展示
- 提供数据导出

### 4. 数据库管理
#### DatabaseDialog
数据库管理对话框
- 继承自 QDialog
- 支持数据库切换
- 实现备份还原
- 提供性能优化

### 5. 图片管理
#### ImageDialog
图片管理对话框
- 继承自 QDialog
- 支持图片预览
- 实现批量操作
- 提供图片信息

## 技术特点

### 1. PyQt6 新特性
- 使用 Qt6 的新对话框基类
- 采用新的布局管理器
- 支持高 DPI 显示
- 现代化的界面风格

### 2. 性能优化
- 模态对话框优化
- 数据加载优化
- 内存使用优化
- 响应速度提升

### 3. 用户体验
- 统一的界面风格
- 直观的操作方式
- 友好的提示信息
- 合理的默认值

### 4. 可扩展性
- 模块化设计
- 标准化接口
- 易于继承
- 代码复用

## 开发指南

### 1. 创建新对话框
```python
from PyQt6.QtWidgets import QDialog

class CustomDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        # UI 初始化代码
        pass
```

### 2. 数据处理
```python
def load_data(self):
    try:
        # 加载数据
        pass
    except Exception as e:
        ErrorHandler.log_error(e)
        
def save_data(self):
    try:
        # 保存数据
        pass
    except Exception as e:
        ErrorHandler.log_error(e)
```

### 3. 事件处理
```python
def closeEvent(self, event):
    if self.check_modified():
        reply = QMessageBox.question(self, '确认', '是否保存修改？')
        if reply == QMessageBox.Yes:
            self.save_data()
    event.accept()
```

## 使用示例

### 1. 产品对话框
```python
dialog = ProductDialog(parent)
if dialog.exec() == QDialog.Accepted:
    # 处理结果
    pass
```

### 2. 批次对话框
```python
dialog = BatchDialog(parent, batch_id)
dialog.data_saved.connect(on_data_saved)
dialog.show()
```

### 3. 财务对话框
```python
dialog = FinanceDialog(parent)
dialog.set_date_range(start_date, end_date)
dialog.update_statistics()
```

## 注意事项
1. 正确处理模态性
2. 实现必要的信号
3. 处理窗口关闭
4. 保存用户设置
5. 提供帮助信息
6. 处理异常情况
7. 优化加载性能
8. 保持代码整洁 
