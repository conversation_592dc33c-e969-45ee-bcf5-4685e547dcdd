# 批次对话框模块 (batch_dialog.py)

## 类定义

### BatchDialog 类
```python
class BatchDialog(QDialog):
    """批次管理对话框类，继承自 QDialog"""
```

#### 初始化
```python
def __init__(self, parent=None, batch=None):
    """
    初始化批次对话框
    :param parent: 父窗口
    :param batch: 要编辑的批次对象，如果为None则为新建模式
    """
    super().__init__(parent)
    self.batch = batch
    self.setWindowTitle("编辑批次" if batch else "新建批次")
    self.resize(900, 700)
    self.setup_ui()
```

## 界面组件

### 1. 基本信息区域
```python
self.basic_info_group = QGroupBox("基本信息")
self.batch_name_edit = QLineEdit()        # 批次名称
self.status_combo = QComboBox()           # 批次状态
self.created_date = QDateEdit()           # 创建日期
self.remarks_edit = QTextEdit()           # 备注信息
```

### 2. 商品管理区域
```python
self.products_group = QGroupBox("商品管理")
self.products_table = QTableWidget()      # 商品列表
self.search_box = QLineEdit()             # 搜索框
self.add_product_btn = QPushButton("添加商品")
self.remove_product_btn = QPushButton("移除商品")
self.batch_operation_btn = QPushButton("批量操作")
```

### 3. 统计信息区域
```python
self.stats_group = QGroupBox("统计信息")
self.product_count_label = QLabel()       # 商品数量
self.total_cost_label = QLabel()          # 总成本
self.total_price_label = QLabel()         # 总价值
self.profit_label = QLabel()              # 预计利润
```

### 4. 按钮区域
```python
self.button_box = QDialogButtonBox(
    QDialogButtonBox.Ok | QDialogButtonBox.Cancel
)
```

## 功能方法

### 1. 界面设置
```python
def setup_ui(self):
    """设置用户界面"""
    self.setup_basic_info()
    self.setup_products_table()
    self.setup_stats_info()
    self.setup_buttons()
    self.setup_layout()
    self.setup_connections()
```

### 2. 数据加载
```python
def load_data(self):
    """加载批次数据"""
    if self.batch:
        self.batch_name_edit.setText(self.batch.batch_name)
        self.status_combo.setCurrentText(self.batch.status)
        self.created_date.setDate(self.batch.created_at)
        self.remarks_edit.setText(self.batch.remarks)
        self.load_products()
        self.update_stats()
```

### 3. 商品管理
```python
def load_products(self):
    """加载批次商品"""
    try:
        products = self.db.get_batch_products(self.batch.batch_id)
        self.products_table.setRowCount(len(products))
        for row, product in enumerate(products):
            self.set_product_row(row, product)
    except Exception as e:
        ErrorHandler.handle_error(e, self)

def add_products(self):
    """添加商品到批次"""
    dialog = ProductSelectDialog(self)
    if dialog.exec_() == QDialog.Accepted:
        selected_products = dialog.get_selected_products()
        try:
            for product in selected_products:
                self.db.add_product_to_batch(
                    product.product_id,
                    self.batch.batch_id
                )
            self.load_products()
            self.update_stats()
        except Exception as e:
            ErrorHandler.handle_error(e, self)

def remove_products(self):
    """从批次移除商品"""
    selected_rows = self.get_selected_rows()
    if selected_rows:
        if ErrorHandler.confirm_action(
            self, "确认移除", f"确定要移除选中的 {len(selected_rows)} 个商品吗？"
        ):
            try:
                for row in selected_rows:
                    product_id = self.products_table.item(row, 0).text()
                    self.db.remove_product_from_batch(
                        product_id,
                        self.batch.batch_id
                    )
                self.load_products()
                self.update_stats()
            except Exception as e:
                ErrorHandler.handle_error(e, self)
```

### 4. 统计计算
```python
def update_stats(self):
    """更新统计信息"""
    try:
        stats = self.db.get_batch_stats(self.batch.batch_id)
        self.product_count_label.setText(f"商品数量：{stats['product_count']}")
        self.total_cost_label.setText(f"总成本：¥{stats['total_cost']:.2f}")
        self.total_price_label.setText(f"总价值：¥{stats['total_price']:.2f}")
        self.profit_label.setText(f"预计利润：¥{stats['total_profit']:.2f}")
    except Exception as e:
        ErrorHandler.handle_error(e, self)
```

### 5. 数据保存
```python
def save_data(self):
    """保存批次数据"""
    try:
        batch_data = {
            "batch_name": self.batch_name_edit.text(),
            "status": self.status_combo.currentText(),
            "remarks": self.remarks_edit.toPlainText()
        }
        
        if self.batch:
            self.db.update_batch(self.batch.batch_id, batch_data)
        else:
            self.db.add_batch(batch_data)
            
        return True
    except Exception as e:
        ErrorHandler.handle_error(e, self)
        return False
```

## 数据验证

### 1. 基本验证
```python
def validate_data(self):
    """验证数据有效性"""
    if not self.batch_name_edit.text():
        ErrorHandler.show_warning(self, "验证错误", "批次名称不能为空！")
        return False
        
    if not self.status_combo.currentText():
        ErrorHandler.show_warning(self, "验证错误", "请选择批次状态！")
        return False
        
    return True
```

## 搜索和筛选

### 1. 搜索功能
```python
def setup_search(self):
    """设置搜索功能"""
    self.search_box.setPlaceholderText("输入关键字搜索商品...")
    self.search_box.textChanged.connect(self.on_search_text_changed)

def on_search_text_changed(self, text):
    """搜索文本变更处理"""
    for row in range(self.products_table.rowCount()):
        show = False
        for col in range(self.products_table.columnCount()):
            item = self.products_table.item(row, col)
            if item and text.lower() in item.text().lower():
                show = True
                break
        self.products_table.setRowHidden(row, not show)
```

## 批量操作

### 1. 批量更新
```python
def batch_update(self):
    """批量更新商品"""
    selected_rows = self.get_selected_rows()
    if selected_rows:
        dialog = BatchUpdateDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            update_data = dialog.get_update_data()
            try:
                for row in selected_rows:
                    product_id = self.products_table.item(row, 0).text()
                    self.db.update_product(product_id, update_data)
                self.load_products()
                self.update_stats()
            except Exception as e:
                ErrorHandler.handle_error(e, self)
```

## 信号和槽

### 1. 按钮信号
```python
self.button_box.accepted.connect(self.accept)
self.button_box.rejected.connect(self.reject)
self.add_product_btn.clicked.connect(self.add_products)
self.remove_product_btn.clicked.connect(self.remove_products)
self.batch_operation_btn.clicked.connect(self.batch_update)
```

### 2. 表格信号
```python
self.products_table.itemSelectionChanged.connect(self.on_selection_changed)
self.products_table.itemDoubleClicked.connect(self.on_item_double_clicked)
```

## 事件处理

### 1. 接受事件
```python
def accept(self):
    """确认按钮事件"""
    if self.validate_data():
        if self.save_data():
            super().accept()
```

### 2. 关闭事件
```python
def closeEvent(self, event):
    """窗口关闭事件"""
    if self.is_data_modified():
        reply = ErrorHandler.confirm_action(
            self, "确认关闭", "数据已修改，是否保存？"
        )
        if reply:
            if self.validate_data() and self.save_data():
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
    else:
        event.accept()
```

## 依赖关系

### 1. PyQt5 组件
- QDialog
- QGroupBox
- QLineEdit
- QComboBox
- QDateEdit
- QTextEdit
- QTableWidget
- QPushButton
- QLabel
- QDialogButtonBox
- QVBoxLayout
- QHBoxLayout

### 2. 自定义组件
- ProductSelectDialog
- BatchUpdateDialog
- ErrorHandler

### 3. 数据库
- DatabaseManager

## 使用示例
```python
# 新建批次
dialog = BatchDialog(parent_window)
if dialog.exec_() == QDialog.Accepted:
    # 处理新建批次的后续操作
    pass

# 编辑批次
dialog = BatchDialog(parent_window, existing_batch)
if dialog.exec_() == QDialog.Accepted:
    # 处理编辑批次的后续操作
    pass
``` 