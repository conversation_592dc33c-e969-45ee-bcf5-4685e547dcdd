import logging
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QMessageBox,
    QGroupBox,
    QAbstractItemView,
    QHeaderView,
)
from PyQt6.QtCore import Qt, pyqtSignal
from database.db_utils import get_connection
from datetime import datetime
from utils.error_handler import ErrorHandler


class BatchDialog(QDialog):
    # 定义信号
    batch_updated = pyqtSignal()

    def __init__(self, parent=None, batch_id=None):
        super().__init__(parent)
        self.batch_id = batch_id
        self.selected_product = None

        # 设置窗口标题
        self.setWindowTitle("编辑批次" if batch_id else "添加批次")

        # 设置对话框大小为主窗口的80%
        if parent:
            parent_size = parent.size()
            self.resize(int(parent_size.width() * 0.8), int(parent_size.height() * 0.8))

        try:
            # 初始化UI
            self.init_ui()

            # 加载商品数据
            self.load_products()

            # 如果是编辑模式，加载批次数据
            if batch_id:
                self.load_batch_data()

        except Exception as e:
            ErrorHandler.log_error("批次对话框初始化失败")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")
            self.reject()  # 关闭对话框

    def init_ui(self):
        """初始化UI"""
        try:
            layout = QVBoxLayout(self)
            layout.setSpacing(10)  # 减小控件间距

            # 商品选择区域 - 设置为占据85%的空间
            product_group = QGroupBox("选择商品")
            product_layout = QVBoxLayout()
            product_layout.setSpacing(5)  # 减小内部控件间距

            # 商品搜索
            search_layout = QHBoxLayout()
            self.search_input = QLineEdit()
            self.search_input.setPlaceholderText("输入商品ID或名称搜索")
            self.search_input.textChanged.connect(self.search_products)  # 实时搜索
            search_layout.addWidget(self.search_input)
            product_layout.addLayout(search_layout)

            # 商品列表
            self.product_table = QTableWidget()
            self.product_table.setColumnCount(5)
            self.product_table.setHorizontalHeaderLabels(
                ["商品ID", "名称", "类别", "状态", "所属批次"]
            )
            # 设置选择模式为扩展选择（允许多选）
            self.product_table.setSelectionMode(
                QAbstractItemView.SelectionMode.ExtendedSelection
            )
            # 设置选择行为为选择整行
            self.product_table.setSelectionBehavior(
                QAbstractItemView.SelectionBehavior.SelectRows
            )
            # 启用鼠标跟踪
            self.product_table.setMouseTracking(True)
            # 允许排序
            self.product_table.setSortingEnabled(True)
            # 设置交替行颜色
            self.product_table.setAlternatingRowColors(True)
            # 设置表头自适应
            self.product_table.horizontalHeader().setSectionResizeMode(
                QHeaderView.ResizeMode.ResizeToContents
            )
            # 设置表头样式
            self.product_table.horizontalHeader().setStyleSheet(
                "QHeaderView::section { background-color: #f0f0f0; padding: 4px; }"
            )
            product_layout.addWidget(self.product_table)

            # 添加选择提示标签
            selection_hint = QLabel(
                "提示：可以使用以下方式选择多个商品：\n"
                "1. 按住Ctrl键点击选择多个商品\n"
                "2. 按住Shift键选择连续的商品\n"
                "3. 按住鼠标左键拖动选择多个商品"
            )
            selection_hint.setStyleSheet("color: gray; font-size: 10pt;")
            product_layout.addWidget(selection_hint)

            product_group.setLayout(product_layout)
            layout.addWidget(product_group, stretch=85)

            # 批次信息和按钮区域 - 设置为占据15%的空间，使用水平布局
            bottom_layout = QHBoxLayout()
            bottom_layout.setSpacing(10)

            # 批次名称
            name_layout = QHBoxLayout()
            name_layout.addWidget(QLabel("批次名称:"))
            self.batch_name_input = QLineEdit()
            self.batch_name_input.setPlaceholderText("输入批次名称")
            name_layout.addWidget(self.batch_name_input)

            # 批次备注
            remarks_layout = QHBoxLayout()
            remarks_layout.addWidget(QLabel("备注:"))
            self.remarks_input = QLineEdit()
            self.remarks_input.setPlaceholderText("输入批次备注")
            remarks_layout.addWidget(self.remarks_input)

            # 按钮
            button_layout = QHBoxLayout()
            self.save_btn = QPushButton("保存")
            self.save_btn.setDefault(True)  # 设置为默认按钮
            self.cancel_btn = QPushButton("取消")
            self.save_btn.clicked.connect(self.save_batch)
            self.cancel_btn.clicked.connect(self.reject)
            button_layout.addWidget(self.save_btn)
            button_layout.addWidget(self.cancel_btn)

            # 将所有元素添加到底部布局
            bottom_layout.addLayout(name_layout, stretch=4)
            bottom_layout.addLayout(remarks_layout, stretch=4)
            button_layout.addStretch()  # 添加弹性空间
            bottom_layout.addLayout(button_layout, stretch=2)

            layout.addLayout(bottom_layout, stretch=15)

        except Exception as e:
            ErrorHandler.log_error("初始化批次对话框UI失败")
            raise e

    def load_products(self):
        """加载所有商品"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT p.product_id, p.name, p.category, p.status,
                           GROUP_CONCAT(b.batch_name) as batch_names
                    FROM products p
                    LEFT JOIN product_batches pb ON p.product_id = pb.product_id
                    LEFT JOIN batches b ON pb.batch_id = b.batch_id
                    GROUP BY p.product_id
                    ORDER BY p.product_id
                    """
                )
                products = cursor.fetchall()
                self.product_table.setRowCount(len(products))
                for row, product in enumerate(products):
                    for col, value in enumerate(product):
                        if col == 4 and value:  # 批次列
                            value = ", ".join(
                                sorted(set(value.split(",")))
                            )  # 去重并排序
                        item = QTableWidgetItem(str(value) if value is not None else "")
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)  # 居中对齐
                        if col == 3:  # 状态列
                            item.setForeground(
                                self.get_status_color(str(value))
                            )  # 设置状态颜色
                        self.product_table.setItem(row, col, item)
                self.product_table.resizeColumnsToContents()
                self.product_table.resizeRowsToContents()
        except Exception as e:
            ErrorHandler.log_error("加载商品列表失败")
            QMessageBox.critical(self, "错误", f"加载商品列表失败: {str(e)}")

    def get_status_color(self, status):
        """获取状态对应的颜色"""
        from PyQt6.QtGui import QColor

        status_colors = {
            "正常": QColor("#28a745"),  # 绿色
            "缺货": QColor("#dc3545"),  # 红色
            "待补货": QColor("#ffc107"),  # 黄色
            "停售": QColor("#6c757d"),  # 灰色
        }
        return status_colors.get(status, QColor("#000000"))  # 默认黑色

    def search_products(self):
        """搜索商品"""
        search_text = self.search_input.text().strip().lower()
        for row in range(self.product_table.rowCount()):
            show_row = False
            for col in range(self.product_table.columnCount()):
                item = self.product_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            self.product_table.setRowHidden(row, not show_row)

    def load_batch_data(self):
        """加载批次数据"""
        try:
            if not self.batch_id:
                return

            with get_connection() as conn:
                cursor = conn.cursor()
                # 获取批次基本信息
                cursor.execute(
                    """
                    SELECT batch_name, remarks
                    FROM batches
                    WHERE batch_id = ?
                    """,
                    (self.batch_id,),
                )
                batch_data = cursor.fetchone()
                if batch_data:
                    self.batch_name_input.setText(batch_data[0])
                    self.remarks_input.setText(batch_data[1] or "")

                # 获取批次中的商品
                cursor.execute(
                    """
                    SELECT p.product_id
                    FROM products p
                    JOIN product_batches pb ON p.product_id = pb.product_id
                    WHERE pb.batch_id = ?
                    """,
                    (self.batch_id,),
                )
                product_ids = [row[0] for row in cursor.fetchall()]

                # 在表格中选中这些商品
                for row in range(self.product_table.rowCount()):
                    product_id = self.product_table.item(row, 0).text()
                    if product_id in product_ids:
                        self.product_table.selectRow(row)

        except Exception as e:
            ErrorHandler.log_error("加载批次数据失败")
            QMessageBox.critical(self, "错误", f"加载批次数据失败: {str(e)}")

    def save_batch(self):
        """保存批次数据"""
        try:
            # 获取输入数据
            batch_name = self.batch_name_input.text().strip()
            remarks = self.remarks_input.text().strip()

            # 验证输入
            if not batch_name:
                QMessageBox.warning(self, "警告", "请输入批次名称")
                self.batch_name_input.setFocus()
                return

            # 获取选中的商品
            selected_rows = self.product_table.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "警告", "请选择至少一个商品")
                return

            # 获取选中的商品ID
            selected_products = set()
            for item in selected_rows:
                if item.column() == 0:  # 商品ID列
                    selected_products.add(item.text())

            with get_connection() as conn:
                cursor = conn.cursor()

                if self.batch_id:  # 编辑模式
                    # 更新批次基本信息
                    cursor.execute(
                        """
                        UPDATE batches
                        SET batch_name = ?, remarks = ?, updated_at = ?
                        WHERE batch_id = ?
                        """,
                        (batch_name, remarks, datetime.now(), self.batch_id),
                    )

                    # 删除原有的商品关联
                    cursor.execute(
                        """
                        DELETE FROM product_batches
                        WHERE batch_id = ?
                        """,
                        (self.batch_id,),
                    )

                    # 添加新的商品关联
                    for product_id in selected_products:
                        cursor.execute(
                            """
                            INSERT INTO product_batches (product_id, batch_id)
                            VALUES (?, ?)
                            """,
                            (product_id, self.batch_id),
                        )

                    message = "批次更新成功！"
                else:  # 新建模式
                    # 生成新的批次ID
                    cursor.execute("SELECT MAX(batch_id) FROM batches")
                    max_id = cursor.fetchone()[0]
                    next_id = 1 if max_id is None else int(max_id) + 1
                    self.batch_id = str(next_id)

                    # 插入新批次
                    cursor.execute(
                        """
                        INSERT INTO batches (batch_id, batch_name, remarks, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                        """,
                        (
                            self.batch_id,
                            batch_name,
                            remarks,
                            datetime.now(),
                            datetime.now(),
                        ),
                    )

                    # 添加商品关联
                    for product_id in selected_products:
                        cursor.execute(
                            """
                            INSERT INTO product_batches (product_id, batch_id)
                            VALUES (?, ?)
                            """,
                            (product_id, self.batch_id),
                        )

                    message = "批次创建成功！"

                conn.commit()
                QMessageBox.information(self, "成功", message)
                self.batch_updated.emit()  # 发送批次更新信号
                self.accept()

        except Exception as e:
            ErrorHandler.log_error("保存批次失败")
            QMessageBox.critical(self, "错误", f"保存批次失败: {str(e)}")
