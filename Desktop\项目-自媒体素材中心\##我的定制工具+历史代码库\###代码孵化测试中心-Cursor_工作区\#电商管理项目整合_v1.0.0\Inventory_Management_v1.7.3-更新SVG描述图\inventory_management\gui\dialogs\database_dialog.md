# 数据库对话框模块 (database_dialog.py)

## 功能概述
`database_dialog.py` 实现了数据库管理对话框,提供数据库备份、恢复、优化和维护等功能。该模块基于 PyQt5 开发,支持数据库文件的导入导出和基本维护操作。

## 类定义

### DatabaseDialog 类
```python
class DatabaseDialog(QDialog):
    """数据库管理对话框类,继承自 QDialog"""
```

#### 初始化
```python
def __init__(self, parent=None):
    """
    初始化数据库对话框
    :param parent: 父窗口
    """
    super().__init__(parent)
    self.setup_ui()
    self.setup_connections()
```

## 界面组件

### 1. 基本布局
```python
def setup_ui(self):
    """设置界面布局"""
    self.setWindowTitle("数据库管理")
    self.setMinimumWidth(400)
    
    layout = QVBoxLayout()
    
    # 添加操作按钮组
    button_group = QGroupBox("数据库操作")
    button_layout = QVBoxLayout()
    
    # 备份按钮
    self.backup_btn = QPushButton("备份数据库")
    self.backup_btn.setIcon(QIcon(":/icons/backup.png"))
    button_layout.addWidget(self.backup_btn)
    
    # 恢复按钮
    self.restore_btn = QPushButton("恢复数据库")
    self.restore_btn.setIcon(QIcon(":/icons/restore.png"))
    button_layout.addWidget(self.restore_btn)
    
    # 优化按钮
    self.optimize_btn = QPushButton("优化数据库")
    self.optimize_btn.setIcon(QIcon(":/icons/optimize.png"))
    button_layout.addWidget(self.optimize_btn)
    
    button_group.setLayout(button_layout)
    layout.addWidget(button_group)
    
    # 添加状态信息区域
    self.status_group = QGroupBox("状态信息")
    status_layout = QFormLayout()
    
    # 数据库大小
    self.size_label = QLabel("0 MB")
    status_layout.addRow("数据库大小:", self.size_label)
    
    # 最后备份时间
    self.last_backup_label = QLabel("从未备份")
    status_layout.addRow("最后备份:", self.last_backup_label)
    
    # 表数量
    self.tables_label = QLabel("0")
    status_layout.addRow("数据表数量:", self.tables_label)
    
    self.status_group.setLayout(status_layout)
    layout.addWidget(self.status_group)
    
    # 添加进度条
    self.progress_bar = QProgressBar()
    self.progress_bar.setVisible(False)
    layout.addWidget(self.progress_bar)
    
    self.setLayout(layout)
```

## 核心功能

### 1. 数据库备份
```python
def backup_database(self):
    """备份数据库"""
    try:
        # 选择备份保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择备份位置",
            "",
            "SQLite数据库 (*.db);;所有文件 (*.*)"
        )
        
        if not file_path:
            return
            
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 执行备份
        self.backup_worker = DatabaseBackupWorker(file_path)
        self.backup_worker.progress_updated.connect(self.update_progress)
        self.backup_worker.finished.connect(self.on_backup_finished)
        self.backup_worker.start()
        
    except Exception as e:
        ErrorHandler.show_error(self, "备份失败", str(e))
```

### 2. 数据库恢复
```python
def restore_database(self):
    """恢复数据库"""
    try:
        # 选择备份文件
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择备份文件",
            "",
            "SQLite数据库 (*.db);;所有文件 (*.*)"
        )
        
        if not file_path:
            return
            
        # 确认恢复操作
        reply = QMessageBox.warning(
            self,
            "确认恢复",
            "恢复数据库将覆盖当前数据,是否继续?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.No:
            return
            
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 执行恢复
        self.restore_worker = DatabaseRestoreWorker(file_path)
        self.restore_worker.progress_updated.connect(self.update_progress)
        self.restore_worker.finished.connect(self.on_restore_finished)
        self.restore_worker.start()
        
    except Exception as e:
        ErrorHandler.show_error(self, "恢复失败", str(e))
```

### 3. 数据库优化
```python
def optimize_database(self):
    """优化数据库"""
    try:
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 执行优化
        self.optimize_worker = DatabaseOptimizeWorker()
        self.optimize_worker.progress_updated.connect(self.update_progress)
        self.optimize_worker.finished.connect(self.on_optimize_finished)
        self.optimize_worker.start()
        
    except Exception as e:
        ErrorHandler.show_error(self, "优化失败", str(e))
```

## 工作线程

### 1. 备份工作线程
```python
class DatabaseBackupWorker(QThread):
    """数据库备份工作线程"""
    progress_updated = pyqtSignal(int)  # 进度更新信号
    finished = pyqtSignal(bool, str)    # 完成信号
    
    def __init__(self, target_path):
        super().__init__()
        self.target_path = target_path
        
    def run(self):
        try:
            # 执行备份操作
            self.progress_updated.emit(50)
            # ... 备份逻辑 ...
            self.progress_updated.emit(100)
            self.finished.emit(True, "备份完成")
        except Exception as e:
            self.finished.emit(False, str(e))
```

### 2. 恢复工作线程
```python
class DatabaseRestoreWorker(QThread):
    """数据库恢复工作线程"""
    progress_updated = pyqtSignal(int)  # 进度更新信号
    finished = pyqtSignal(bool, str)    # 完成信号
    
    def __init__(self, source_path):
        super().__init__()
        self.source_path = source_path
        
    def run(self):
        try:
            # 执行恢复操作
            self.progress_updated.emit(50)
            # ... 恢复逻辑 ...
            self.progress_updated.emit(100)
            self.finished.emit(True, "恢复完成")
        except Exception as e:
            self.finished.emit(False, str(e))
```

## 信号和槽

### 1. 按钮信号
```python
def setup_connections(self):
    """设置信号连接"""
    self.backup_btn.clicked.connect(self.backup_database)
    self.restore_btn.clicked.connect(self.restore_database)
    self.optimize_btn.clicked.connect(self.optimize_database)
```

### 2. 进度更新
```python
def update_progress(self, value):
    """更新进度条"""
    self.progress_bar.setValue(value)
```

### 3. 操作完成处理
```python
def on_backup_finished(self, success, message):
    """备份完成处理"""
    self.progress_bar.setVisible(False)
    if success:
        self.update_status_info()
        QMessageBox.information(self, "备份完成", message)
    else:
        QMessageBox.critical(self, "备份失败", message)

def on_restore_finished(self, success, message):
    """恢复完成处理"""
    self.progress_bar.setVisible(False)
    if success:
        self.update_status_info()
        QMessageBox.information(self, "恢复完成", message)
    else:
        QMessageBox.critical(self, "恢复失败", message)
```

## 状态管理

### 1. 状态信息更新
```python
def update_status_info(self):
    """更新状态信息"""
    try:
        # 获取数据库大小
        db_size = os.path.getsize(DATABASE_PATH)
        self.size_label.setText(f"{db_size / 1024 / 1024:.2f} MB")
        
        # 获取最后备份时间
        if os.path.exists(BACKUP_INFO_PATH):
            with open(BACKUP_INFO_PATH, 'r') as f:
                last_backup = datetime.fromisoformat(f.read().strip())
                self.last_backup_label.setText(
                    last_backup.strftime("%Y-%m-%d %H:%M:%S")
                )
        
        # 获取表数量
        with DatabaseManager() as db:
            cursor = db.cursor()
            cursor.execute(
                "SELECT COUNT(*) FROM sqlite_master WHERE type='table'"
            )
            table_count = cursor.fetchone()[0]
            self.tables_label.setText(str(table_count))
            
    except Exception as e:
        ErrorHandler.show_error(self, "状态更新失败", str(e))
```

## 依赖关系

### 1. PyQt5 组件
- QDialog
- QVBoxLayout
- QFormLayout
- QGroupBox
- QPushButton
- QLabel
- QProgressBar
- QThread
- QIcon
- QMessageBox
- QFileDialog

### 2. 自定义组件
- ErrorHandler
- DatabaseManager

### 3. 系统组件
- os
- datetime

## 使用示例
```python
# 创建对话框
dialog = DatabaseDialog(parent_window)

# 显示对话框
dialog.exec_()
```

## 注意事项
1. 数据库操作的安全性
2. 备份文件的完整性验证
3. 大文件操作的性能优化
4. 用户操作的确认机制
5. 错误处理和日志记录 