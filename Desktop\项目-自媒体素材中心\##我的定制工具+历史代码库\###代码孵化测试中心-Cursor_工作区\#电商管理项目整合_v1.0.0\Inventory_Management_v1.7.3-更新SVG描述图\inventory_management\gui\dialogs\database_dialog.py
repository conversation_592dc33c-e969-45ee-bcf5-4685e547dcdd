import logging
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QListWidget,
    QMessageBox,
    QInputDialog,
    QLineEdit,
    QProgressDialog,
    QGroupBox,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QFont
from database.db_utils import (
    get_database_list,
    create_new_database,
    switch_database,
    delete_database,
    get_current_database,
)
from utils.error_handler import ErrorHandler
import os


class DatabaseDialog(QDialog):
    """数据库管理对话框"""

    database_changed = pyqtSignal()  # 数据库切换信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("数据库管理")
        self.resize(500, 400)
        self.init_ui()
        self.load_databases()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # 当前数据库信息组
        current_db_group = QGroupBox("当前数据库信息")
        current_db_layout = QVBoxLayout()

        # 当前数据库路径
        current_db_path_layout = QHBoxLayout()
        current_db_label = QLabel("数据库路径：")
        current_db_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.current_db_path = QLabel()
        self.current_db_path.setStyleSheet(
            "padding: 5px; background-color: #f0f0f0; border-radius: 3px;"
        )
        current_db_path_layout.addWidget(current_db_label)
        current_db_path_layout.addWidget(self.current_db_path)
        current_db_layout.addLayout(current_db_path_layout)

        # 数据库大小
        size_layout = QHBoxLayout()
        size_label = QLabel("数据库大小：")
        self.size_info = QLabel()
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.size_info)
        current_db_layout.addLayout(size_layout)

        current_db_group.setLayout(current_db_layout)
        layout.addWidget(current_db_group)

        # 数据库列表组
        db_list_group = QGroupBox("可用数据库")
        db_list_layout = QVBoxLayout()

        # 数据库列表
        self.db_list = QListWidget()
        self.db_list.setAlternatingRowColors(True)  # 设置交替行颜色
        self.db_list.itemDoubleClicked.connect(self.switch_to_database)
        db_list_layout.addWidget(self.db_list)

        db_list_group.setLayout(db_list_layout)
        layout.addWidget(db_list_group)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)

        # 添加数据库按钮
        self.add_btn = QPushButton("添加数据库")
        self.add_btn.setIcon(QIcon(":/icons/add.png"))
        self.add_btn.clicked.connect(self.add_database)
        button_layout.addWidget(self.add_btn)

        # 切换数据库按钮
        self.switch_btn = QPushButton("切换到选中数据库")
        self.switch_btn.setIcon(QIcon(":/icons/switch.png"))
        self.switch_btn.clicked.connect(self.switch_to_database)
        button_layout.addWidget(self.switch_btn)

        # 删除数据库按钮
        self.delete_btn = QPushButton("删除选中数据库")
        self.delete_btn.setIcon(QIcon(":/icons/delete.png"))
        self.delete_btn.clicked.connect(self.delete_selected_database)
        button_layout.addWidget(self.delete_btn)

        # 关闭按钮
        self.close_btn = QPushButton("关闭")
        self.close_btn.setIcon(QIcon(":/icons/close.png"))
        self.close_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def load_databases(self):
        """加载数据库列表"""
        try:
            self.db_list.clear()
            databases = get_database_list()
            current_db = os.path.basename(get_current_database())

            # 更新当前数据库显示
            self.current_db_path.setText(current_db)
            self.update_size_info()

            # 添加数据库列表
            for db in databases:
                self.db_list.addItem(db)
                # 如果是当前数据库，选中它
                if db == current_db:
                    self.db_list.setCurrentRow(self.db_list.count() - 1)

        except Exception as e:
            ErrorHandler.log_error("加载数据库列表失败")
            QMessageBox.critical(self, "错误", f"加载数据库列表失败: {str(e)}")

    def update_size_info(self):
        """更新数据库大小信息"""
        try:
            current_db = get_current_database()
            size = os.path.getsize(current_db)
            if size < 1024:
                size_str = f"{size} B"
            elif size < 1024 * 1024:
                size_str = f"{size/1024:.2f} KB"
            else:
                size_str = f"{size/1024/1024:.2f} MB"
            self.size_info.setText(size_str)
        except Exception as e:
            ErrorHandler.log_error("获取数据库大小失败")
            self.size_info.setText("未知")

    def add_database(self):
        """添加新数据库"""
        try:
            name, ok = QInputDialog.getText(
                self,
                "新建数据库",
                "请输入数据库名称：",
                QLineEdit.EchoMode.Normal,
            )

            if ok and name:
                if not name.endswith(".db"):
                    name += ".db"

                # 创建进度对话框
                progress = QProgressDialog("正在创建数据库...", "取消", 0, 100, self)
                progress.setWindowModality(Qt.WindowModality.WindowModal)
                progress.show()

                try:
                    # 创建新数据库
                    create_new_database(name)
                    progress.setValue(100)
                    self.load_databases()
                    QMessageBox.information(self, "成功", f"数据库 '{name}' 创建成功！")
                finally:
                    progress.close()

        except Exception as e:
            ErrorHandler.log_error("创建数据库失败")
            QMessageBox.critical(self, "错误", f"创建数据库失败: {str(e)}")

    def switch_to_database(self):
        """切换到选中的数据库"""
        try:
            current_item = self.db_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "警告", "请先选择一个数据库！")
                return

            db_name = current_item.text()
            if db_name == os.path.basename(get_current_database()):
                QMessageBox.information(self, "提示", "当前已经是这个数据库了！")
                return

            # 确认切换
            reply = QMessageBox.question(
                self,
                "确认切换",
                f"确定要切换到数据库 '{db_name}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 创建进度对话框
                progress = QProgressDialog("正在切换数据库...", "取消", 0, 100, self)
                progress.setWindowModality(Qt.WindowModality.WindowModal)
                progress.show()

                try:
                    # 构建完整的数据库路径
                    db_dir = os.path.dirname(get_current_database())
                    db_path = os.path.join(db_dir, db_name)

                    # 切换数据库
                    if switch_database(db_path):
                        progress.setValue(100)
                        self.load_databases()
                        self.database_changed.emit()  # 发送数据库改变信号
                        QMessageBox.information(
                            self, "成功", f"已切换到数据库 '{db_name}'"
                        )
                    else:
                        QMessageBox.critical(self, "错误", "切换数据库失败！")
                finally:
                    progress.close()

        except Exception as e:
            ErrorHandler.log_error("切换数据库失败")
            QMessageBox.critical(self, "错误", f"切换数据库失败: {str(e)}")

    def delete_selected_database(self):
        """删除选中的数据库"""
        try:
            current_item = self.db_list.currentItem()
            if not current_item:
                QMessageBox.warning(self, "警告", "请先选择一个数据库！")
                return

            db_name = current_item.text()

            # 检查是否是当前数据库
            if db_name == os.path.basename(get_current_database()):
                QMessageBox.warning(self, "警告", "无法删除当前正在使用的数据库！")
                return

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除数据库 '{db_name}' 吗？此操作不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 创建进度对话框
                progress = QProgressDialog("正在删除数据库...", "取消", 0, 100, self)
                progress.setWindowModality(Qt.WindowModality.WindowModal)
                progress.show()

                try:
                    delete_database(db_name)
                    progress.setValue(100)
                    self.load_databases()
                    QMessageBox.information(
                        self, "成功", f"数据库 '{db_name}' 已删除！"
                    )
                finally:
                    progress.close()

        except Exception as e:
            ErrorHandler.log_error("删除数据库失败")
            QMessageBox.critical(self, "错误", f"删除数据库失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 保存当前状态或执行清理操作
            event.accept()
        except Exception as e:
            ErrorHandler.log_error("关闭数据库对话框失败")
            event.accept()
