# 财务对话框模块 (finance_dialog.py)

## 功能概述
`finance_dialog.py` 实现了财务管理对话框,提供销售统计、成本分析、利润计算和财务报表等功能。该模块基于 PyQt5 开发,支持多维度的财务数据分析和可视化展示。

## 类定义

### FinanceDialog 类
```python
class FinanceDialog(QDialog):
    """财务管理对话框类,继承自 QDialog"""
```

#### 初始化
```python
def __init__(self, parent=None):
    """
    初始化财务对话框
    :param parent: 父窗口
    """
    super().__init__(parent)
    self.setup_ui()
    self.setup_connections()
    self.load_data()
```

## 界面组件

### 1. 基本布局
```python
def setup_ui(self):
    """设置界面布局"""
    self.setWindowTitle("财务管理")
    self.setMinimumSize(800, 600)
    
    layout = QVBoxLayout()
    
    # 添加工具栏
    self.setup_toolbar()
    
    # 添加选项卡
    self.tab_widget = QTabWidget()
    self.setup_overview_tab()      # 概览选项卡
    self.setup_sales_tab()        # 销售统计选项卡
    self.setup_cost_tab()         # 成本分析选项卡
    self.setup_profit_tab()       # 利润分析选项卡
    self.setup_report_tab()       # 财务报表选项卡
    
    layout.addWidget(self.tab_widget)
    self.setLayout(layout)
```

### 2. 工具栏设置
```python
def setup_toolbar(self):
    """设置工具栏"""
    self.toolbar = QToolBar()
    
    # 时间范围选择
    self.date_range_combo = QComboBox()
    self.date_range_combo.addItems([
        "今日", "本周", "本月", "本季度", "本年", "自定义"
    ])
    self.toolbar.addWidget(QLabel("时间范围:"))
    self.toolbar.addWidget(self.date_range_combo)
    
    # 自定义日期选择
    self.start_date = QDateEdit()
    self.end_date = QDateEdit()
    self.start_date.setDate(QDate.currentDate())
    self.end_date.setDate(QDate.currentDate())
    self.toolbar.addWidget(self.start_date)
    self.toolbar.addWidget(QLabel("至"))
    self.toolbar.addWidget(self.end_date)
    
    # 刷新按钮
    self.refresh_btn = QToolButton()
    self.refresh_btn.setIcon(QIcon(":/icons/refresh.png"))
    self.refresh_btn.setToolTip("刷新数据")
    self.toolbar.addWidget(self.refresh_btn)
    
    # 导出按钮
    self.export_btn = QToolButton()
    self.export_btn.setIcon(QIcon(":/icons/export.png"))
    self.export_btn.setToolTip("导出报表")
    self.toolbar.addWidget(self.export_btn)
```

### 3. 概览选项卡
```python
def setup_overview_tab(self):
    """设置概览选项卡"""
    overview_widget = QWidget()
    layout = QVBoxLayout()
    
    # 关键指标卡片
    metrics_group = QGroupBox("关键指标")
    metrics_layout = QHBoxLayout()
    
    # 销售总额
    self.total_sales_card = MetricCard("销售总额")
    metrics_layout.addWidget(self.total_sales_card)
    
    # 总成本
    self.total_cost_card = MetricCard("总成本")
    metrics_layout.addWidget(self.total_cost_card)
    
    # 净利润
    self.net_profit_card = MetricCard("净利润")
    metrics_layout.addWidget(self.net_profit_card)
    
    # 利润率
    self.profit_margin_card = MetricCard("利润率")
    metrics_layout.addWidget(self.profit_margin_card)
    
    metrics_group.setLayout(metrics_layout)
    layout.addWidget(metrics_group)
    
    # 趋势图
    trend_group = QGroupBox("趋势分析")
    trend_layout = QVBoxLayout()
    self.trend_chart = TrendChart()
    trend_layout.addWidget(self.trend_chart)
    trend_group.setLayout(trend_layout)
    layout.addWidget(trend_group)
    
    overview_widget.setLayout(layout)
    self.tab_widget.addTab(overview_widget, "概览")
```

### 4. 销售统计选项卡
```python
def setup_sales_tab(self):
    """设置销售统计选项卡"""
    sales_widget = QWidget()
    layout = QVBoxLayout()
    
    # 销售数据表格
    self.sales_table = QTableWidget()
    self.sales_table.setColumnCount(6)
    self.sales_table.setHorizontalHeaderLabels([
        "商品名称", "销售数量", "单价", "折扣", "销售额", "日期"
    ])
    layout.addWidget(self.sales_table)
    
    # 销售分析图表
    chart_group = QGroupBox("销售分析")
    chart_layout = QHBoxLayout()
    
    # 品类销售占比
    self.category_pie = PieChart("品类销售占比")
    chart_layout.addWidget(self.category_pie)
    
    # 销售趋势
    self.sales_line = LineChart("销售趋势")
    chart_layout.addWidget(self.sales_line)
    
    chart_group.setLayout(chart_layout)
    layout.addWidget(chart_group)
    
    sales_widget.setLayout(layout)
    self.tab_widget.addTab(sales_widget, "销售统计")
```

## 数据处理

### 1. 数据加载
```python
def load_data(self):
    """加载财务数据"""
    try:
        # 获取时间范围
        start_date = self.start_date.date().toPyDate()
        end_date = self.end_date.date().toPyDate()
        
        # 加载概览数据
        self.load_overview_data(start_date, end_date)
        
        # 加载销售数据
        self.load_sales_data(start_date, end_date)
        
        # 加载成本数据
        self.load_cost_data(start_date, end_date)
        
        # 加载利润数据
        self.load_profit_data(start_date, end_date)
        
    except Exception as e:
        ErrorHandler.show_error(self, "数据加载失败", str(e))
```

### 2. 数据计算
```python
def calculate_metrics(self, data):
    """计算关键指标"""
    try:
        # 计算销售总额
        total_sales = sum(item["amount"] for item in data)
        self.total_sales_card.setValue(f"¥{total_sales:,.2f}")
        
        # 计算总成本
        total_cost = sum(item["cost"] for item in data)
        self.total_cost_card.setValue(f"¥{total_cost:,.2f}")
        
        # 计算净利润
        net_profit = total_sales - total_cost
        self.net_profit_card.setValue(f"¥{net_profit:,.2f}")
        
        # 计算利润率
        profit_margin = (net_profit / total_sales * 100) if total_sales > 0 else 0
        self.profit_margin_card.setValue(f"{profit_margin:.1f}%")
        
    except Exception as e:
        ErrorHandler.show_error(self, "指标计算失败", str(e))
```

### 3. 图表更新
```python
def update_charts(self, data):
    """更新图表数据"""
    try:
        # 更新趋势图
        self.trend_chart.update_data(data)
        
        # 更新品类占比图
        self.category_pie.update_data(data)
        
        # 更新销售趋势图
        self.sales_line.update_data(data)
        
    except Exception as e:
        ErrorHandler.show_error(self, "图表更新失败", str(e))
```

## 报表导出

### 1. Excel导出
```python
def export_to_excel(self):
    """导出Excel报表"""
    try:
        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出报表",
            "",
            "Excel文件 (*.xlsx);;所有文件 (*.*)"
        )
        
        if not file_path:
            return
            
        # 创建Excel工作簿
        workbook = Workbook()
        
        # 添加概览sheet
        self.export_overview_sheet(workbook)
        
        # 添加销售统计sheet
        self.export_sales_sheet(workbook)
        
        # 添加成本分析sheet
        self.export_cost_sheet(workbook)
        
        # 添加利润分析sheet
        self.export_profit_sheet(workbook)
        
        # 保存文件
        workbook.save(file_path)
        
        QMessageBox.information(self, "导出成功", "报表导出完成!")
        
    except Exception as e:
        ErrorHandler.show_error(self, "导出失败", str(e))
```

## 信号和槽

### 1. 工具栏信号
```python
def setup_connections(self):
    """设置信号连接"""
    # 时间范围变更
    self.date_range_combo.currentIndexChanged.connect(
        self.on_date_range_changed
    )
    
    # 日期选择变更
    self.start_date.dateChanged.connect(self.on_date_changed)
    self.end_date.dateChanged.connect(self.on_date_changed)
    
    # 刷新按钮
    self.refresh_btn.clicked.connect(self.load_data)
    
    # 导出按钮
    self.export_btn.clicked.connect(self.export_to_excel)
```

### 2. 表格信号
```python
def setup_table_connections(self):
    """设置表格信号连接"""
    # 表格排序
    self.sales_table.horizontalHeader().sectionClicked.connect(
        self.on_table_header_clicked
    )
    
    # 表格选择
    self.sales_table.itemSelectionChanged.connect(
        self.on_table_selection_changed
    )
```

## 依赖关系

### 1. PyQt5 组件
- QDialog
- QVBoxLayout
- QHBoxLayout
- QTabWidget
- QToolBar
- QComboBox
- QDateEdit
- QToolButton
- QGroupBox
- QTableWidget
- QLabel

### 2. 自定义组件
- MetricCard
- TrendChart
- PieChart
- LineChart
- ErrorHandler

### 3. 第三方库
- openpyxl (Excel处理)
- pandas (数据处理)
- matplotlib (图表绘制)

## 使用示例
```python
# 创建对话框
dialog = FinanceDialog(parent_window)

# 设置时间范围
dialog.start_date.setDate(QDate(2024, 1, 1))
dialog.end_date.setDate(QDate(2024, 3, 31))

# 显示对话框
dialog.exec_()
```

## 注意事项
1. 数据计算的准确性
2. 图表展示的清晰度
3. 导出报表的完整性
4. 大数据量的性能优化
5. 用户操作的响应速度 