import logging
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QMessageBox,
    QGroupBox,
    QComboBox,
    QTabWidget,
    QWidget,
    QHeaderView,
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from database.db_utils import get_connection
from utils.error_handler import ErrorHandler
from datetime import datetime, timedelta
import pandas as pd


class FinanceDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("财务统计")
        self.resize(800, 600)

        try:
            self.init_ui()
            self.load_categories()
            self.load_product_ids()
            self.load_batches()
            self.update_statistics()
        except Exception as e:
            ErrorHandler.log_error("初始化财务统计对话框失败")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")
            self.reject()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 创建选项卡
        self.tab_widget = QTabWidget()

        # 总体统计选项卡
        self.overall_tab = QWidget()
        self.setup_overall_tab()
        self.tab_widget.addTab(self.overall_tab, "总体统计")

        # 每日统计选项卡
        self.daily_tab = QWidget()
        self.setup_daily_tab()
        self.tab_widget.addTab(self.daily_tab, "每日统计")

        # 商品业绩选项卡
        self.product_tab = QWidget()
        self.setup_product_tab()
        self.tab_widget.addTab(self.product_tab, "商品业绩")

        # 批次统计选项卡
        self.batch_tab = QWidget()
        self.setup_batch_tab()
        self.tab_widget.addTab(self.batch_tab, "批次统计")

        layout.addWidget(self.tab_widget)

        # 底部按钮
        button_layout = QHBoxLayout()
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.reject)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        layout.addLayout(button_layout)

    def setup_overall_tab(self):
        """设置总体统计选项卡"""
        layout = QVBoxLayout(self.overall_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 时间范围选择
        self.period_combo = QComboBox()
        self.period_combo.addItems(["今日", "本周", "本月", "本年", "全部"])
        self.period_combo.currentIndexChanged.connect(self.update_overall_statistics)

        # 分类选择
        self.category_combo = QComboBox()
        self.category_combo.currentIndexChanged.connect(self.update_overall_statistics)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_overall_statistics)

        filter_layout.addWidget(QLabel("时间范围:"))
        filter_layout.addWidget(self.period_combo)
        filter_layout.addWidget(QLabel("商品分类:"))
        filter_layout.addWidget(self.category_combo)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 统计表格
        self.overall_table = QTableWidget()
        self.overall_table.setColumnCount(4)
        self.overall_table.setHorizontalHeaderLabels(
            ["统计项目", "数值", "同比", "环比"]
        )
        self.overall_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.ResizeToContents
        )
        self.overall_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        layout.addWidget(self.overall_table)

    def setup_daily_tab(self):
        """设置每日统计选项卡"""
        layout = QVBoxLayout(self.daily_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 时间范围选择
        self.daily_period_combo = QComboBox()
        self.daily_period_combo.addItems(["最近7天", "最近30天", "最近90天"])
        self.daily_period_combo.currentIndexChanged.connect(
            self.update_daily_statistics
        )

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_daily_statistics)

        filter_layout.addWidget(QLabel("时间范围:"))
        filter_layout.addWidget(self.daily_period_combo)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 统计表格
        self.daily_table = QTableWidget()
        self.daily_table.setColumnCount(5)
        self.daily_table.setHorizontalHeaderLabels(
            ["日期", "销售额", "成本", "利润", "利润率"]
        )
        self.daily_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.ResizeToContents
        )
        self.daily_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        layout.addWidget(self.daily_table)

    def setup_product_tab(self):
        """设置商品业绩选项卡"""
        layout = QVBoxLayout(self.product_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 商品选择
        self.product_combo = QComboBox()
        self.product_combo.currentIndexChanged.connect(self.update_product_statistics)

        # 时间范围选择
        self.product_period_combo = QComboBox()
        self.product_period_combo.addItems(
            ["最近30天", "最近90天", "最近180天", "全部"]
        )
        self.product_period_combo.currentIndexChanged.connect(
            self.update_product_statistics
        )

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_product_statistics)

        filter_layout.addWidget(QLabel("选择商品:"))
        filter_layout.addWidget(self.product_combo)
        filter_layout.addWidget(QLabel("时间范围:"))
        filter_layout.addWidget(self.product_period_combo)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 统计表格
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(6)
        self.product_table.setHorizontalHeaderLabels(
            ["统计项目", "数值", "销售额", "成本", "利润", "利润率"]
        )
        self.product_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.ResizeToContents
        )
        self.product_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        layout.addWidget(self.product_table)

    def setup_batch_tab(self):
        """设置批次统计选项卡"""
        layout = QVBoxLayout(self.batch_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 批次选择下拉框
        self.batch_combo = QComboBox()
        self.batch_combo.currentIndexChanged.connect(self.update_batch_statistics)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_batch_statistics)

        filter_layout.addWidget(QLabel("选择批次:"))
        filter_layout.addWidget(self.batch_combo)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 批次统计表格
        self.batch_table = QTableWidget()
        self.batch_table.setColumnCount(7)
        self.batch_table.setHorizontalHeaderLabels(
            ["统计项目", "数值", "商品数", "总成本", "总销售额", "总利润", "备注"]
        )
        self.batch_table.horizontalHeader().setSectionResizeMode(
            QHeaderView.ResizeMode.ResizeToContents
        )
        self.batch_table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        layout.addWidget(self.batch_table)

    def load_categories(self):
        """加载商品分类"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT DISTINCT category
                    FROM products
                    ORDER BY category
                    """
                )
                categories = [row[0] for row in cursor.fetchall()]
                self.category_combo.clear()
                self.category_combo.addItem("全部分类")
                self.category_combo.addItems(categories)
        except Exception as e:
            ErrorHandler.log_error("加载分类失败")
            QMessageBox.critical(self, "错误", f"加载分类失败: {str(e)}")

    def load_product_ids(self):
        """加载商品ID列表"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT product_id, name
                    FROM products
                    ORDER BY product_id
                    """
                )
                products = cursor.fetchall()
                self.product_combo.clear()
                self.product_combo.addItem("全部商品")
                for product_id, name in products:
                    self.product_combo.addItem(f"{product_id} - {name}", product_id)
        except Exception as e:
            ErrorHandler.log_error("加载商品ID失败")
            QMessageBox.critical(self, "错误", f"加载商品ID失败: {str(e)}")

    def load_batches(self):
        """加载批次列表"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT batch_id, batch_name
                    FROM batches
                    ORDER BY created_at DESC
                    """
                )
                batches = cursor.fetchall()
                self.batch_combo.clear()
                for batch_id, batch_name in batches:
                    self.batch_combo.addItem(f"{batch_id} - {batch_name}", batch_id)
        except Exception as e:
            ErrorHandler.log_error("加载批次列表失败")
            QMessageBox.critical(self, "错误", f"加载批次列表失败: {str(e)}")

    def update_statistics(self):
        """更新所有统计数据"""
        self.update_overall_statistics()
        self.update_daily_statistics()
        self.update_product_statistics()
        self.update_batch_statistics()

    def update_overall_statistics(self):
        """更新总体统计数据"""
        try:
            period = self.period_combo.currentText()
            category = self.category_combo.currentText()

            # 构建日期条件
            date_condition = ""
            if period == "今日":
                date_condition = "DATE(created_at) = DATE('now')"
            elif period == "本周":
                date_condition = (
                    "DATE(created_at) >= DATE('now', 'weekday 0', '-7 days')"
                )
            elif period == "本月":
                date_condition = "DATE(created_at) >= DATE('now', 'start of month')"
            elif period == "本年":
                date_condition = "DATE(created_at) >= DATE('now', 'start of year')"

            # 构建分类条件
            category_condition = "AND category = ?" if category != "全部分类" else ""

            with get_connection() as conn:
                cursor = conn.cursor()
                query = f"""
                SELECT 
                    COUNT(DISTINCT product_id) as product_count,
                    SUM(quantity) as total_quantity,
                    SUM(total_cost) as total_cost,
                    SUM(selling_price) as total_revenue,
                    SUM(selling_price - total_cost) as total_profit,
                    AVG((selling_price - total_cost) / total_cost * 100) as avg_profit_rate
                FROM products
                WHERE {date_condition if date_condition else "1=1"}
                {category_condition}
                """

                params = [category] if category != "全部分类" else []
                cursor.execute(query, params)
                result = cursor.fetchone()

                # 更新表格
                self.overall_table.setRowCount(6)
                rows = [
                    ["商品数量", str(result[0]), "-", "-"],
                    ["总库存", str(result[1]), "-", "-"],
                    [
                        "总成本",
                        f"¥{result[2]:.2f}" if result[2] else "¥0.00",
                        "-",
                        "-",
                    ],
                    [
                        "总销售额",
                        f"¥{result[3]:.2f}" if result[3] else "¥0.00",
                        "-",
                        "-",
                    ],
                    [
                        "总利润",
                        f"¥{result[4]:.2f}" if result[4] else "¥0.00",
                        "-",
                        "-",
                    ],
                    [
                        "平均利润率",
                        f"{result[5]:.2f}%" if result[5] else "0.00%",
                        "-",
                        "-",
                    ],
                ]

                for row, items in enumerate(rows):
                    for col, item in enumerate(items):
                        table_item = QTableWidgetItem(item)
                        table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        if col == 1 and row > 1:  # 金额列使用不同颜色
                            if "-" not in item:
                                value = float(item.replace("¥", "").replace("%", ""))
                                if value > 0:
                                    table_item.setForeground(QColor("#28a745"))
                                elif value < 0:
                                    table_item.setForeground(QColor("#dc3545"))
                        self.overall_table.setItem(row, col, table_item)

        except Exception as e:
            ErrorHandler.log_error("更新总体统计失败")
            QMessageBox.critical(self, "错误", f"更新总体统计失败: {str(e)}")

    def update_daily_statistics(self):
        """更新每日统计数据"""
        try:
            period = self.daily_period_combo.currentText()
            days = int(period.split("天")[0].replace("最近", ""))

            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT 
                        DATE(created_at) as date,
                        SUM(selling_price) as revenue,
                        SUM(total_cost) as cost,
                        SUM(selling_price - total_cost) as profit,
                        AVG((selling_price - total_cost) / total_cost * 100) as profit_rate
                    FROM products
                    WHERE DATE(created_at) >= DATE('now', ?)
                    GROUP BY DATE(created_at)
                    ORDER BY date DESC
                    """,
                    (f"-{days} days",),
                )
                results = cursor.fetchall()

                self.daily_table.setRowCount(len(results))
                for row, result in enumerate(results):
                    items = [
                        result[0],
                        f"¥{result[1]:.2f}" if result[1] else "¥0.00",
                        f"¥{result[2]:.2f}" if result[2] else "¥0.00",
                        f"¥{result[3]:.2f}" if result[3] else "¥0.00",
                        f"{result[4]:.2f}%" if result[4] else "0.00%",
                    ]
                    for col, item in enumerate(items):
                        table_item = QTableWidgetItem(item)
                        table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        if col in [1, 2, 3]:  # 金额列使用不同颜色
                            if "¥" in item:
                                value = float(item.replace("¥", ""))
                                if value > 0:
                                    table_item.setForeground(QColor("#28a745"))
                                elif value < 0:
                                    table_item.setForeground(QColor("#dc3545"))
                        self.daily_table.setItem(row, col, table_item)

        except Exception as e:
            ErrorHandler.log_error("更新每日统计失败")
            QMessageBox.critical(self, "错误", f"更新每日统计失败: {str(e)}")

    def update_product_statistics(self):
        """更新商品统计数据"""
        try:
            product_id = self.product_combo.currentData()
            period = self.product_period_combo.currentText()

            # 构建日期条件
            date_condition = ""
            if period != "全部":
                days = int(period.split("天")[0].replace("最近", ""))
                date_condition = f"AND DATE(created_at) >= DATE('now', '-{days} days')"

            # 构建商品条件
            product_condition = "AND product_id = ?" if product_id else ""

            with get_connection() as conn:
                cursor = conn.cursor()
                query = f"""
                SELECT 
                    COUNT(*) as count,
                    SUM(quantity) as quantity,
                    SUM(selling_price) as revenue,
                    SUM(total_cost) as cost,
                    SUM(selling_price - total_cost) as profit,
                    AVG((selling_price - total_cost) / total_cost * 100) as profit_rate
                FROM products
                WHERE 1=1 {date_condition} {product_condition}
                """

                params = [product_id] if product_id else []
                cursor.execute(query, params)
                result = cursor.fetchone()

                # 更新表格
                self.product_table.setRowCount(2)
                rows = [
                    [
                        "销售数量",
                        str(result[1]),
                        f"¥{result[2]:.2f}" if result[2] else "¥0.00",
                        f"¥{result[3]:.2f}" if result[3] else "¥0.00",
                        f"¥{result[4]:.2f}" if result[4] else "¥0.00",
                        f"{result[5]:.2f}%" if result[5] else "0.00%",
                    ],
                    [
                        "平均单价",
                        "-",
                        (
                            f"¥{result[2]/result[1]:.2f}"
                            if result[1] and result[2]
                            else "¥0.00"
                        ),
                        (
                            f"¥{result[3]/result[1]:.2f}"
                            if result[1] and result[3]
                            else "¥0.00"
                        ),
                        (
                            f"¥{result[4]/result[1]:.2f}"
                            if result[1] and result[4]
                            else "¥0.00"
                        ),
                        "-",
                    ],
                ]

                for row, items in enumerate(rows):
                    for col, item in enumerate(items):
                        table_item = QTableWidgetItem(item)
                        table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        if col in [2, 3, 4]:  # 金额列使用不同颜色
                            if "¥" in item:
                                value = float(item.replace("¥", ""))
                                if value > 0:
                                    table_item.setForeground(QColor("#28a745"))
                                elif value < 0:
                                    table_item.setForeground(QColor("#dc3545"))
                        self.product_table.setItem(row, col, table_item)

        except Exception as e:
            ErrorHandler.log_error("更新商品统计失败")
            QMessageBox.critical(self, "错误", f"更新商品统计失败: {str(e)}")

    def update_batch_statistics(self):
        """更新批次统计数据"""
        try:
            if self.batch_combo.currentData() is None:
                return

            batch_id = self.batch_combo.currentData()

            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT 
                        b.batch_name,
                        COUNT(p.product_id) as product_count,
                        SUM(p.total_cost) as total_cost,
                        SUM(p.selling_price) as total_revenue,
                        SUM(p.selling_price - p.total_cost) as total_profit,
                        AVG((p.selling_price - p.total_cost) / p.total_cost * 100) as avg_profit_rate
                    FROM batches b
                    LEFT JOIN product_batches pb ON b.batch_id = pb.batch_id
                    LEFT JOIN products p ON pb.product_id = p.product_id
                    WHERE b.batch_id = ?
                    GROUP BY b.batch_id
                    """,
                    (batch_id,),
                )
                result = cursor.fetchone()

                # 更新表格
                self.batch_table.setRowCount(6)
                rows = [
                    ["批次名称", result[0], str(result[1]), "-", "-", "-", "-"],
                    [
                        "总成本",
                        f"¥{result[2]:.2f}" if result[2] else "¥0.00",
                        "-",
                        "-",
                        "-",
                        "-",
                        "-",
                    ],
                    [
                        "总销售额",
                        f"¥{result[3]:.2f}" if result[3] else "¥0.00",
                        "-",
                        "-",
                        "-",
                        "-",
                        "-",
                    ],
                    [
                        "总利润",
                        f"¥{result[4]:.2f}" if result[4] else "¥0.00",
                        "-",
                        "-",
                        "-",
                        "-",
                        "-",
                    ],
                    [
                        "平均利润率",
                        f"{result[5]:.2f}%" if result[5] else "0.00%",
                        "-",
                        "-",
                        "-",
                        "-",
                        "-",
                    ],
                    ["备注", "-", "-", "-", "-", "-", "-"],
                ]

                for row, items in enumerate(rows):
                    for col, item in enumerate(items):
                        table_item = QTableWidgetItem(str(item))
                        table_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        if col == 1 and row in [1, 2, 3]:  # 金额列使用不同颜色
                            if "¥" in item:
                                value = float(item.replace("¥", ""))
                                if value > 0:
                                    table_item.setForeground(QColor("#28a745"))
                                elif value < 0:
                                    table_item.setForeground(QColor("#dc3545"))
                        self.batch_table.setItem(row, col, table_item)

        except Exception as e:
            ErrorHandler.log_error("更新批次统计失败")
            QMessageBox.critical(self, "错误", f"更新批次统计失败: {str(e)}")
