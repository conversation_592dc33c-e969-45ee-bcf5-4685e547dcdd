import logging
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QComboBox,
    QPushButton,
    QTabWidget,
    QWidget,
    QTableWidget,
    QTableWidgetItem,
    QMessageBox,
    QDateEdit,
    QSpinBox,
    QGroupBox,
)
from PyQt5.QtCore import Qt, QDate
from database.db_utils import get_connection


class FinanceDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("财务统计")
        self.resize(800, 600)

        try:
            self.init_ui()
            self.load_categories()
            self.load_product_ids()
            self.load_batches()
            self.update_statistics()
        except Exception as e:
            logging.exception("初始化财务统计对话框失败")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")
            self.reject()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)

        # 创建选项卡
        self.tab_widget = QTabWidget()

        # 总体统计选项卡
        self.overall_tab = QWidget()
        self.setup_overall_tab()
        self.tab_widget.addTab(self.overall_tab, "总体统计")

        # 每日统计选项卡
        self.daily_tab = QWidget()
        self.setup_daily_tab()
        self.tab_widget.addTab(self.daily_tab, "每日统计")

        # 商品业绩选项卡
        self.product_tab = QWidget()
        self.setup_product_tab()
        self.tab_widget.addTab(self.product_tab, "商品业绩")

        # 批次统计选项卡
        self.batch_tab = QWidget()
        self.setup_batch_tab()
        self.tab_widget.addTab(self.batch_tab, "批次统计")

        layout.addWidget(self.tab_widget)

        # 底部按钮
        button_layout = QHBoxLayout()
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.reject)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        layout.addLayout(button_layout)

    def setup_overall_tab(self):
        """设置总体统计选项卡"""
        layout = QVBoxLayout(self.overall_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 日期范围
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        self.start_date.setCalendarPopup(True)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)

        # 分类筛选
        self.category_combo = QComboBox()
        self.category_combo.addItem("全部分类")

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.update_statistics)

        filter_layout.addWidget(QLabel("开始日期:"))
        filter_layout.addWidget(self.start_date)
        filter_layout.addWidget(QLabel("结束日期:"))
        filter_layout.addWidget(self.end_date)
        filter_layout.addWidget(QLabel("分类:"))
        filter_layout.addWidget(self.category_combo)
        filter_layout.addWidget(self.refresh_btn)
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 统计结果表格
        self.overall_table = QTableWidget()
        self.overall_table.setColumnCount(7)
        self.overall_table.setHorizontalHeaderLabels(
            [
                "统计项目",
                "商品数量",
                "总成本",
                "总销售额",
                "总利润",
                "平均利润率",
                "备注",
            ]
        )
        self.overall_table.setEditTriggers(QTableWidget.NoEditTriggers)
        layout.addWidget(self.overall_table)

    def setup_daily_tab(self):
        """设置每日统计选项卡"""
        layout = QVBoxLayout(self.daily_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 天数选择
        self.days_spin = QSpinBox()
        self.days_spin.setRange(1, 365)
        self.days_spin.setValue(30)
        self.days_spin.valueChanged.connect(self.update_daily_stats)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_daily_stats)

        filter_layout.addWidget(QLabel("显示最近天数:"))
        filter_layout.addWidget(self.days_spin)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 每日统计表格
        self.daily_table = QTableWidget()
        self.daily_table.setColumnCount(6)
        self.daily_table.setHorizontalHeaderLabels(
            ["日期", "交易数量", "总成本", "总销售额", "总利润", "利润率"]
        )
        self.daily_table.setEditTriggers(QTableWidget.NoEditTriggers)
        layout.addWidget(self.daily_table)

    def setup_product_tab(self):
        """设置商品业绩选项卡"""
        layout = QVBoxLayout(self.product_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 商品选择
        self.product_combo = QComboBox()
        self.product_combo.currentIndexChanged.connect(self.update_product_stats)

        # 日期范围
        self.product_start_date = QDateEdit()
        self.product_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.product_start_date.setCalendarPopup(True)
        self.product_end_date = QDateEdit()
        self.product_end_date.setDate(QDate.currentDate())
        self.product_end_date.setCalendarPopup(True)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_product_stats)

        filter_layout.addWidget(QLabel("商品:"))
        filter_layout.addWidget(self.product_combo)
        filter_layout.addWidget(QLabel("开始日期:"))
        filter_layout.addWidget(self.product_start_date)
        filter_layout.addWidget(QLabel("结束日期:"))
        filter_layout.addWidget(self.product_end_date)
        filter_layout.addWidget(refresh_btn)
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 商品统计表格
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(8)
        self.product_table.setHorizontalHeaderLabels(
            ["日期", "状态", "成本", "原价", "折扣率", "实际售价", "利润", "利润率"]
        )
        self.product_table.setEditTriggers(QTableWidget.NoEditTriggers)
        layout.addWidget(self.product_table)

    def setup_batch_tab(self):
        """设置批次统计选项卡"""
        layout = QVBoxLayout(self.batch_tab)

        # 筛选条件
        filter_group = QGroupBox("筛选条件")
        filter_layout = QHBoxLayout()

        # 批次选择下拉框
        self.batch_combo = QComboBox()
        self.batch_combo.currentIndexChanged.connect(self.update_batch_statistics)

        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_batch_statistics)

        filter_layout.addWidget(QLabel("选择批次:"))
        filter_layout.addWidget(self.batch_combo)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # 批次统计表格
        self.batch_table = QTableWidget()
        self.batch_table.setColumnCount(7)
        self.batch_table.setHorizontalHeaderLabels(
            ["统计项目", "数值", "商品数", "总成本", "总销售额", "总利润", "备注"]
        )
        self.batch_table.setEditTriggers(QTableWidget.NoEditTriggers)
        layout.addWidget(self.batch_table)

    def load_categories(self):
        """加载商品分类"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT DISTINCT category
                    FROM products
                    ORDER BY category
                    """
                )
                categories = [row[0] for row in cursor.fetchall()]
                self.category_combo.clear()
                self.category_combo.addItem("全部分类")
                self.category_combo.addItems(categories)
        except Exception as e:
            logging.exception("加载商品分类失败")
            QMessageBox.critical(self, "错误", f"加载分类失败: {str(e)}")

    def load_product_ids(self):
        """加载商品ID列表"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT product_id, name
                    FROM products
                    ORDER BY product_id
                    """
                )
                products = cursor.fetchall()
                self.product_combo.clear()
                for product_id, name in products:
                    self.product_combo.addItem(f"{product_id} - {name}", product_id)
        except Exception as e:
            logging.exception("加载商品ID列表失败")
            QMessageBox.critical(self, "错误", f"加载商品列表失败: {str(e)}")

    def load_batches(self):
        """加载批次列表"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT batch_id, batch_name
                    FROM batches
                    ORDER BY created_at DESC
                    """
                )
                batches = cursor.fetchall()

                self.batch_combo.clear()
                self.batch_combo.addItem("全部批次", None)
                for batch_id, batch_name in batches:
                    self.batch_combo.addItem(f"{batch_name} ({batch_id})", batch_id)
        except Exception as e:
            logging.exception("加载批次列表失败")
            QMessageBox.warning(self, "警告", f"加载批次列表失败: {str(e)}")

    def update_statistics(self):
        """更新总体统计"""
        try:
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")
            category = self.category_combo.currentText()

            with get_connection() as conn:
                cursor = conn.cursor()

                # 构建查询条件
                where_clause = "WHERE 1=1"
                params = []
                if category != "全部分类":
                    where_clause += " AND category = ?"
                    params.append(category)
                if start_date:
                    where_clause += " AND date(updated_at) >= ?"
                    params.append(start_date)
                if end_date:
                    where_clause += " AND date(updated_at) <= ?"
                    params.append(end_date)

                # 执行查询
                cursor.execute(
                    f"""
                    SELECT 
                        COUNT(*) as count,
                        SUM(total_cost) as total_cost,
                        SUM(CASE WHEN status = '已售出' THEN selling_price * (discount_rate/100) ELSE 0 END) as total_sales,
                        SUM(CASE WHEN status = '已售出' THEN total_profit ELSE 0 END) as total_profit,
                        AVG(CASE WHEN status = '已售出' THEN (total_profit / (selling_price * (discount_rate/100))) * 100 ELSE NULL END) as avg_profit_rate
                    FROM products
                    {where_clause}
                    """,
                    params,
                )
                stats = cursor.fetchone()

                # 更新表格
                self.overall_table.setRowCount(5)
                items = [
                    ("商品总数", str(stats[0]), "包括所有状态的商品"),
                    (
                        "总成本",
                        f"¥{stats[1]:.2f}" if stats[1] else "¥0.00",
                        "所有商品的成本总和",
                    ),
                    (
                        "总销售额",
                        f"¥{stats[2]:.2f}" if stats[2] else "¥0.00",
                        "已售出商品的实际销售额",
                    ),
                    (
                        "总利润",
                        f"¥{stats[3]:.2f}" if stats[3] else "¥0.00",
                        "已售出商品的实际利润",
                    ),
                    (
                        "平均利润率",
                        f"{stats[4]:.2f}%" if stats[4] else "0.00%",
                        "已售出商品的平均利润率",
                    ),
                ]

                for row, (name, value, note) in enumerate(items):
                    self.overall_table.setItem(row, 0, QTableWidgetItem(name))
                    self.overall_table.setItem(row, 1, QTableWidgetItem(value))
                    self.overall_table.setItem(row, 6, QTableWidgetItem(note))

                self.overall_table.resizeColumnsToContents()

        except Exception as e:
            logging.exception("更新总体统计失败")
            QMessageBox.critical(self, "错误", f"更新统计失败: {str(e)}")

    def update_daily_stats(self):
        """更新每日统计"""
        try:
            days = self.days_spin.value()
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT 
                        date(updated_at) as date,
                        COUNT(*) as count,
                        SUM(total_cost) as total_cost,
                        SUM(selling_price * (discount_rate/100)) as total_sales,
                        SUM(total_profit) as total_profit
                    FROM products
                    WHERE status = '已售出'
                    AND date(updated_at) BETWEEN ? AND ?
                    GROUP BY date(updated_at)
                    ORDER BY date DESC
                    """,
                    (start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")),
                )
                stats = cursor.fetchall()

                # 更新表格
                self.daily_table.setRowCount(len(stats))
                for row, stat in enumerate(stats):
                    date, count, cost, sales, profit = stat
                    profit_rate = (profit / sales * 100) if sales else 0

                    self.daily_table.setItem(row, 0, QTableWidgetItem(date))
                    self.daily_table.setItem(row, 1, QTableWidgetItem(str(count)))
                    self.daily_table.setItem(row, 2, QTableWidgetItem(f"¥{cost:.2f}"))
                    self.daily_table.setItem(row, 3, QTableWidgetItem(f"¥{sales:.2f}"))
                    self.daily_table.setItem(row, 4, QTableWidgetItem(f"¥{profit:.2f}"))
                    self.daily_table.setItem(
                        row, 5, QTableWidgetItem(f"{profit_rate:.2f}%")
                    )

                self.daily_table.resizeColumnsToContents()

        except Exception as e:
            logging.exception("更新每日统计失败")
            QMessageBox.critical(self, "错误", f"更新每日统计失败: {str(e)}")

    def update_product_stats(self):
        """更新商品业绩统计"""
        try:
            product_id = self.product_combo.currentData()
            if not product_id:
                return

            start_date = self.product_start_date.date().toString("yyyy-MM-dd")
            end_date = self.product_end_date.date().toString("yyyy-MM-dd")

            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT 
                        date(updated_at) as date,
                        status,
                        total_cost,
                        selling_price,
                        discount_rate,
                        selling_price * (discount_rate/100) as actual_price,
                        total_profit,
                        (total_profit / (selling_price * (discount_rate/100))) * 100 as profit_rate
                    FROM products
                    WHERE product_id = ?
                    AND date(updated_at) BETWEEN ? AND ?
                    ORDER BY updated_at DESC
                    """,
                    (product_id, start_date, end_date),
                )
                stats = cursor.fetchall()

                # 更新表格
                self.product_table.setRowCount(len(stats))
                for row, stat in enumerate(stats):
                    date, status, cost, price, discount, actual, profit, rate = stat
                    self.product_table.setItem(row, 0, QTableWidgetItem(date))
                    self.product_table.setItem(row, 1, QTableWidgetItem(status))
                    self.product_table.setItem(row, 2, QTableWidgetItem(f"¥{cost:.2f}"))
                    self.product_table.setItem(
                        row, 3, QTableWidgetItem(f"¥{price:.2f}")
                    )
                    self.product_table.setItem(
                        row, 4, QTableWidgetItem(f"{discount:.1f}%")
                    )
                    self.product_table.setItem(
                        row, 5, QTableWidgetItem(f"¥{actual:.2f}")
                    )
                    self.product_table.setItem(
                        row, 6, QTableWidgetItem(f"¥{profit:.2f}")
                    )
                    self.product_table.setItem(row, 7, QTableWidgetItem(f"{rate:.2f}%"))

                self.product_table.resizeColumnsToContents()

        except Exception as e:
            logging.exception("更新商品业绩统计失败")
            QMessageBox.critical(self, "错误", f"更新商品业绩统计失败: {str(e)}")

    def update_batch_statistics(self):
        """更新批次统计"""
        try:
            batch_id = self.batch_combo.currentData()

            with get_connection() as conn:
                cursor = conn.cursor()

                if batch_id:
                    # 查询特定批次的统计数据
                    cursor.execute(
                        """
                        SELECT 
                            b.batch_name,
                            COUNT(DISTINCT pb.product_id) as product_count,
                            SUM(p.total_cost) as total_cost,
                            SUM(CASE WHEN p.status = '已售出' THEN p.selling_price * (p.discount_rate/100) ELSE 0 END) as total_sales,
                            SUM(CASE WHEN p.status = '已售出' THEN p.total_profit ELSE 0 END) as total_profit,
                            b.remarks
                        FROM batches b
                        LEFT JOIN product_batches pb ON b.batch_id = pb.batch_id
                        LEFT JOIN products p ON pb.product_id = p.product_id
                        WHERE b.batch_id = ?
                        GROUP BY b.batch_id
                        """,
                        (batch_id,),
                    )
                else:
                    # 查询所有批次的汇总统计数据
                    cursor.execute(
                        """
                        SELECT 
                            '所有批次' as batch_name,
                            COUNT(DISTINCT pb.product_id) as product_count,
                            SUM(p.total_cost) as total_cost,
                            SUM(CASE WHEN p.status = '已售出' THEN p.selling_price * (p.discount_rate/100) ELSE 0 END) as total_sales,
                            SUM(CASE WHEN p.status = '已售出' THEN p.total_profit ELSE 0 END) as total_profit,
                            NULL as remarks
                        FROM batches b
                        LEFT JOIN product_batches pb ON b.batch_id = pb.batch_id
                        LEFT JOIN products p ON pb.product_id = p.product_id
                        """
                    )

                stats = cursor.fetchone()
                if not stats:
                    return

                # 更新表格
                self.batch_table.setRowCount(6)
                items = [
                    (
                        "批次名称",
                        stats[0],
                        str(stats[1]),
                        f"¥{stats[2]:.2f}" if stats[2] else "¥0.00",
                        f"¥{stats[3]:.2f}" if stats[3] else "¥0.00",
                        f"¥{stats[4]:.2f}" if stats[4] else "¥0.00",
                        stats[5] if stats[5] else "",
                    ),
                    ("商品数量", str(stats[1]), "-", "-", "-", "-", "批次中的商品数量"),
                    (
                        "总成本",
                        f"¥{stats[2]:.2f}" if stats[2] else "¥0.00",
                        "-",
                        "-",
                        "-",
                        "-",
                        "所有商品的成本总和",
                    ),
                    (
                        "总销售额",
                        f"¥{stats[3]:.2f}" if stats[3] else "¥0.00",
                        "-",
                        "-",
                        "-",
                        "-",
                        "已售出商品的实际销售额",
                    ),
                    (
                        "总利润",
                        f"¥{stats[4]:.2f}" if stats[4] else "¥0.00",
                        "-",
                        "-",
                        "-",
                        "-",
                        "已售出商品的实际利润",
                    ),
                    (
                        "备注",
                        stats[5] if stats[5] else "",
                        "-",
                        "-",
                        "-",
                        "-",
                        "批次备注信息",
                    ),
                ]

                for row, item in enumerate(items):
                    for col, value in enumerate(item):
                        self.batch_table.setItem(row, col, QTableWidgetItem(str(value)))

                # 调整列宽
                self.batch_table.resizeColumnsToContents()

        except Exception as e:
            logging.exception("更新批次统计失败")
            QMessageBox.warning(self, "警告", f"更新批次统计失败: {str(e)}")
