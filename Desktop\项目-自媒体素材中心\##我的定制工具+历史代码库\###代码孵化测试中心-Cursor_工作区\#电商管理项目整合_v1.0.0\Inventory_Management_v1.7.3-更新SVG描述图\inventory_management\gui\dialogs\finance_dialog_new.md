# 新版财务对话框模块 (finance_dialog_new.py)

## 功能概述
`finance_dialog_new.py` 是财务管理对话框的新版实现,提供了更现代化的界面和更丰富的数据分析功能。该模块基于 PyQt5 开发,支持实时数据更新、多维度分析和数据可视化。

## 类定义

### FinanceDialogNew 类
```python
class FinanceDialogNew(QDialog):
    """新版财务管理对话框类,继承自 QDialog"""
```

#### 初始化
```python
def __init__(self, parent=None):
    """
    初始化财务对话框
    :param parent: 父窗口
    """
    super().__init__(parent)
    self.setup_ui()
    self.setup_connections()
    self.load_initial_data()
```

## 界面组件

### 1. 基本布局
```python
def setup_ui(self):
    """设置界面布局"""
    self.setWindowTitle("财务管理")
    self.setMinimumSize(1000, 700)
    
    # 主布局
    main_layout = QHBoxLayout()
    
    # 左侧导航栏
    self.setup_navigation()
    main_layout.addWidget(self.nav_widget, 1)
    
    # 右侧内容区
    self.setup_content_area()
    main_layout.addWidget(self.content_widget, 4)
    
    self.setLayout(main_layout)
```

### 2. 导航栏
```python
def setup_navigation(self):
    """设置导航栏"""
    self.nav_widget = QWidget()
    nav_layout = QVBoxLayout()
    
    # 时间范围选择
    self.date_group = QGroupBox("时间范围")
    date_layout = QVBoxLayout()
    
    self.date_range_combo = QComboBox()
    self.date_range_combo.addItems([
        "今日", "本周", "本月", "本季度", "本年", "自定义"
    ])
    date_layout.addWidget(self.date_range_combo)
    
    self.date_range_stack = QStackedWidget()
    self.setup_date_range_pages()
    date_layout.addWidget(self.date_range_stack)
    
    self.date_group.setLayout(date_layout)
    nav_layout.addWidget(self.date_group)
    
    # 数据维度选择
    self.dimension_group = QGroupBox("数据维度")
    dimension_layout = QVBoxLayout()
    
    self.dimension_list = QListWidget()
    self.dimension_list.addItems([
        "商品维度", "类别维度", "批次维度", "时间维度"
    ])
    dimension_layout.addWidget(self.dimension_list)
    
    self.dimension_group.setLayout(dimension_layout)
    nav_layout.addWidget(self.dimension_group)
    
    # 数据筛选器
    self.filter_group = QGroupBox("数据筛选")
    filter_layout = QVBoxLayout()
    
    self.setup_filters()
    filter_layout.addWidget(self.filter_widget)
    
    self.filter_group.setLayout(filter_layout)
    nav_layout.addWidget(self.filter_group)
    
    self.nav_widget.setLayout(nav_layout)
```

### 3. 内容区域
```python
def setup_content_area(self):
    """设置内容区域"""
    self.content_widget = QWidget()
    content_layout = QVBoxLayout()
    
    # 工具栏
    self.setup_toolbar()
    content_layout.addWidget(self.toolbar)
    
    # 数据展示区
    self.data_stack = QStackedWidget()
    self.setup_data_pages()
    content_layout.addWidget(self.data_stack)
    
    self.content_widget.setLayout(content_layout)
```

### 4. 数据页面
```python
def setup_data_pages(self):
    """设置数据页面"""
    # 概览页面
    self.overview_page = OverviewPage()
    self.data_stack.addWidget(self.overview_page)
    
    # 商品分析页面
    self.product_page = ProductAnalysisPage()
    self.data_stack.addWidget(self.product_page)
    
    # 类别分析页面
    self.category_page = CategoryAnalysisPage()
    self.data_stack.addWidget(self.category_page)
    
    # 批次分析页面
    self.batch_page = BatchAnalysisPage()
    self.data_stack.addWidget(self.batch_page)
    
    # 时间分析页面
    self.time_page = TimeAnalysisPage()
    self.data_stack.addWidget(self.time_page)
```

## 数据处理

### 1. 数据加载
```python
def load_initial_data(self):
    """加载初始数据"""
    try:
        # 设置默认时间范围
        self.set_default_date_range()
        
        # 加载筛选器选项
        self.load_filter_options()
        
        # 加载概览数据
        self.load_overview_data()
        
    except Exception as e:
        ErrorHandler.show_error(self, "数据加载失败", str(e))

def load_filtered_data(self):
    """加载筛选后的数据"""
    try:
        # 获取筛选条件
        filters = self.get_current_filters()
        
        # 获取时间范围
        date_range = self.get_date_range()
        
        # 获取当前维度
        dimension = self.get_current_dimension()
        
        # 加载数据
        self.load_dimension_data(dimension, date_range, filters)
        
    except Exception as e:
        ErrorHandler.show_error(self, "数据加载失败", str(e))
```

### 2. 数据分析
```python
def analyze_data(self, data, dimension):
    """
    分析数据
    :param data: 原始数据
    :param dimension: 分析维度
    :return: 分析结果
    """
    try:
        analyzer = DataAnalyzer()
        
        if dimension == "商品维度":
            return analyzer.analyze_by_product(data)
        elif dimension == "类别维度":
            return analyzer.analyze_by_category(data)
        elif dimension == "批次维度":
            return analyzer.analyze_by_batch(data)
        elif dimension == "时间维度":
            return analyzer.analyze_by_time(data)
            
    except Exception as e:
        ErrorHandler.show_error(self, "数据分析失败", str(e))
```

### 3. 数据导出
```python
def export_data(self):
    """导出数据"""
    try:
        # 获取当前数据
        data = self.get_current_data()
        
        # 选择保存路径
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出数据",
            "",
            "Excel文件 (*.xlsx);;CSV文件 (*.csv);;所有文件 (*.*)"
        )
        
        if not file_path:
            return
            
        # 导出数据
        exporter = DataExporter()
        exporter.export(data, file_path)
        
        QMessageBox.information(self, "导出成功", "数据导出完成!")
        
    except Exception as e:
        ErrorHandler.show_error(self, "导出失败", str(e))
```

## 图表组件

### 1. 趋势图
```python
class TrendChart(QChartView):
    """趋势图组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_chart()
    
    def update_data(self, data):
        """更新图表数据"""
        series = QLineSeries()
        
        for point in data:
            series.append(point["x"], point["y"])
            
        self.chart().removeAllSeries()
        self.chart().addSeries(series)
        self.chart().createDefaultAxes()
```

### 2. 饼图
```python
class PieChart(QChartView):
    """饼图组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_chart()
    
    def update_data(self, data):
        """更新图表数据"""
        series = QPieSeries()
        
        for item in data:
            slice = series.append(item["name"], item["value"])
            slice.setLabelVisible(True)
            
        self.chart().removeAllSeries()
        self.chart().addSeries(series)
```

## 信号和槽

### 1. 导航信号
```python
def setup_nav_connections(self):
    """设置导航信号连接"""
    # 时间范围变更
    self.date_range_combo.currentIndexChanged.connect(
        self.on_date_range_changed
    )
    
    # 维度选择变更
    self.dimension_list.currentItemChanged.connect(
        self.on_dimension_changed
    )
    
    # 筛选条件变更
    self.filter_widget.filter_changed.connect(
        self.on_filter_changed
    )
```

### 2. 工具栏信号
```python
def setup_toolbar_connections(self):
    """设置工具栏信号连接"""
    # 刷新按钮
    self.refresh_btn.clicked.connect(self.load_filtered_data)
    
    # 导出按钮
    self.export_btn.clicked.connect(self.export_data)
    
    # 打印按钮
    self.print_btn.clicked.connect(self.print_report)
```

## 依赖关系

### 1. PyQt5 组件
- QDialog
- QHBoxLayout
- QVBoxLayout
- QStackedWidget
- QGroupBox
- QComboBox
- QListWidget
- QChartView
- QLineSeries
- QPieSeries

### 2. 自定义组件
- OverviewPage
- ProductAnalysisPage
- CategoryAnalysisPage
- BatchAnalysisPage
- TimeAnalysisPage
- DataAnalyzer
- DataExporter
- ErrorHandler

### 3. 第三方库
- pandas (数据处理)
- numpy (数据计算)
- matplotlib (图表绘制)

## 使用示例
```python
# 创建对话框
dialog = FinanceDialogNew(parent_window)

# 设置初始维度
dialog.dimension_list.setCurrentRow(0)  # 选择商品维度

# 设置时间范围
dialog.date_range_combo.setCurrentText("本月")

# 显示对话框
dialog.exec_()
```

## 注意事项
1. 数据加载性能优化
2. 图表渲染效率
3. 内存占用控制
4. 用户交互响应
5. 数据计算准确性 