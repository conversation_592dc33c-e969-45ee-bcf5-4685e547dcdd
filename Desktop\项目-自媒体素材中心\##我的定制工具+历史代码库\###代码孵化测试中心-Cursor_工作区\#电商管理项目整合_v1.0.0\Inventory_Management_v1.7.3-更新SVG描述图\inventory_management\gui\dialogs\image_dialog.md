# 图片对话框模块 (image_dialog.py)

## 类定义

### ImageDialog 类
```python
class ImageDialog(QDialog):
    """图片管理对话框类，继承自 QDialog"""
```

#### 初始化
```python
def __init__(self, parent=None, product_id=None):
    """
    初始化图片对话框
    :param parent: 父窗口
    :param product_id: 商品ID
    """
    super().__init__(parent)
    self.product_id = product_id
    self.setWindowTitle("图片管理")
    self.resize(800, 600)
    self.setup_ui()
```

## 界面组件

### 1. 图片列表区域
```python
self.image_list = QListWidget()           # 图片列表
self.image_list.setViewMode(QListWidget.IconMode)
self.image_list.setIconSize(QSize(100, 100))
self.image_list.setSpacing(10)
self.image_list.setResizeMode(QListWidget.Adjust)
self.image_list.setMovement(QListWidget.Static)
```

### 2. 预览区域
```python
self.preview_label = QLabel()             # 预览标签
self.preview_label.setMinimumSize(400, 400)
self.preview_label.setAlignment(Qt.AlignCenter)
self.preview_label.setStyleSheet(
    "border: 1px solid #ccc; background: #f0f0f0;"
)
```

### 3. 工具栏
```python
self.toolbar = QToolBar()
self.add_image_action = QAction(QIcon(":/icons/add.png"), "添加图片", self)
self.delete_image_action = QAction(QIcon(":/icons/delete.png"), "删除图片", self)
self.set_primary_action = QAction(QIcon(":/icons/star.png"), "设为主图", self)
self.rotate_left_action = QAction(QIcon(":/icons/rotate_left.png"), "向左旋转", self)
self.rotate_right_action = QAction(QIcon(":/icons/rotate_right.png"), "向右旋转", self)
```

### 4. 图片信息区域
```python
self.info_group = QGroupBox("图片信息")
self.file_name_label = QLabel()           # 文件名
self.file_size_label = QLabel()           # 文件大小
self.dimensions_label = QLabel()          # 尺寸
self.create_time_label = QLabel()         # 创建时间
```

## 功能方法

### 1. 界面设置
```python
def setup_ui(self):
    """设置用户界面"""
    self.setup_toolbar()
    self.setup_image_list()
    self.setup_preview()
    self.setup_info()
    self.setup_layout()
    self.setup_connections()
```

### 2. 图片加载
```python
def load_images(self):
    """加载商品图片"""
    try:
        images = self.db.get_product_images(self.product_id)
        self.image_list.clear()
        for image in images:
            self.add_image_item(image)
    except Exception as e:
        ErrorHandler.handle_error(e, self)

def add_image_item(self, image_data):
    """添加图片项到列表"""
    item = QListWidgetItem()
    item.setData(Qt.UserRole, image_data)
    pixmap = QPixmap(image_data["thumbnail_path"])
    item.setIcon(QIcon(pixmap))
    item.setText(image_data["image_name"])
    if image_data["is_primary"]:
        item.setBackground(QColor("#e3f2fd"))
    self.image_list.addItem(item)
```

### 3. 图片操作
```python
def add_images(self):
    """添加图片"""
    file_paths, _ = QFileDialog.getOpenFileNames(
        self, "选择图片", "", "Images (*.png *.jpg *.jpeg)"
    )
    if file_paths:
        try:
            for path in file_paths:
                image_data = self.process_image(path)
                self.db.add_product_image(self.product_id, image_data)
            self.load_images()
        except Exception as e:
            ErrorHandler.handle_error(e, self)

def delete_image(self):
    """删除图片"""
    current_item = self.image_list.currentItem()
    if current_item:
        image_data = current_item.data(Qt.UserRole)
        if ErrorHandler.confirm_action(
            self, "确认删除", "确定要删除选中的图片吗？"
        ):
            try:
                self.db.delete_product_image(image_data["image_id"])
                self.load_images()
            except Exception as e:
                ErrorHandler.handle_error(e, self)

def set_primary_image(self):
    """设置主图"""
    current_item = self.image_list.currentItem()
    if current_item:
        image_data = current_item.data(Qt.UserRole)
        try:
            self.db.set_primary_image(
                self.product_id,
                image_data["image_id"]
            )
            self.load_images()
        except Exception as e:
            ErrorHandler.handle_error(e, self)
```

### 4. 图片处理
```python
def process_image(self, image_path):
    """处理图片"""
    try:
        # 读取原图
        image = Image.open(image_path)
        
        # 生成缩略图
        thumbnail = image.copy()
        thumbnail.thumbnail((100, 100))
        
        # 生成预览图
        preview = image.copy()
        preview.thumbnail((800, 800))
        
        # 保存图片
        file_name = os.path.basename(image_path)
        base_path = f"images/{self.product_id}"
        os.makedirs(base_path, exist_ok=True)
        
        # 保存各种尺寸的图片
        image_path = f"{base_path}/{file_name}"
        thumbnail_path = f"{base_path}/thumb_{file_name}"
        preview_path = f"{base_path}/preview_{file_name}"
        
        image.save(image_path)
        thumbnail.save(thumbnail_path)
        preview.save(preview_path)
        
        return {
            "image_name": file_name,
            "image_path": image_path,
            "thumbnail_path": thumbnail_path,
            "preview_path": preview_path,
            "file_size": os.path.getsize(image_path),
            "dimensions": f"{image.width}x{image.height}",
            "created_at": datetime.now()
        }
    except Exception as e:
        raise Exception(f"图片处理失败：{str(e)}")
```

### 5. 图片预览
```python
def show_preview(self, item):
    """显示图片预览"""
    if item:
        image_data = item.data(Qt.UserRole)
        pixmap = QPixmap(image_data["preview_path"])
        scaled_pixmap = pixmap.scaled(
            self.preview_label.size(),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        self.preview_label.setPixmap(scaled_pixmap)
        self.update_image_info(image_data)
```

### 6. 图片旋转
```python
def rotate_image(self, direction):
    """旋转图片"""
    current_item = self.image_list.currentItem()
    if current_item:
        image_data = current_item.data(Qt.UserRole)
        try:
            # 读取图片
            image = Image.open(image_data["image_path"])
            
            # 旋转图片
            angle = 90 if direction == "right" else -90
            rotated = image.rotate(angle, expand=True)
            
            # 保存旋转后的图片
            rotated.save(image_data["image_path"])
            
            # 更新缩略图和预览图
            thumbnail = rotated.copy()
            thumbnail.thumbnail((100, 100))
            thumbnail.save(image_data["thumbnail_path"])
            
            preview = rotated.copy()
            preview.thumbnail((800, 800))
            preview.save(image_data["preview_path"])
            
            # 刷新显示
            self.load_images()
            self.show_preview(current_item)
        except Exception as e:
            ErrorHandler.handle_error(e, self)
```

## 信号和槽

### 1. 工具栏信号
```python
self.add_image_action.triggered.connect(self.add_images)
self.delete_image_action.triggered.connect(self.delete_image)
self.set_primary_action.triggered.connect(self.set_primary_image)
self.rotate_left_action.triggered.connect(lambda: self.rotate_image("left"))
self.rotate_right_action.triggered.connect(lambda: self.rotate_image("right"))
```

### 2. 列表信号
```python
self.image_list.currentItemChanged.connect(self.show_preview)
self.image_list.itemDoubleClicked.connect(self.show_full_image)
```

## 事件处理

### 1. 大小调整事件
```python
def resizeEvent(self, event):
    """窗口大小调整事件"""
    super().resizeEvent(event)
    if self.image_list.currentItem():
        self.show_preview(self.image_list.currentItem())
```

### 2. 拖放事件
```python
def dragEnterEvent(self, event):
    """拖入事件"""
    if event.mimeData().hasUrls():
        event.acceptProposedAction()

def dropEvent(self, event):
    """放下事件"""
    urls = event.mimeData().urls()
    file_paths = [url.toLocalFile() for url in urls]
    try:
        for path in file_paths:
            if path.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_data = self.process_image(path)
                self.db.add_product_image(self.product_id, image_data)
        self.load_images()
    except Exception as e:
        ErrorHandler.handle_error(e, self)
```

## 依赖关系

### 1. PyQt5 组件
- QDialog
- QListWidget
- QLabel
- QToolBar
- QAction
- QGroupBox
- QVBoxLayout
- QHBoxLayout
- QFileDialog

### 2. 图片处理
- PIL (Python Imaging Library)
- os
- datetime

### 3. 自定义组件
- ErrorHandler
- DatabaseManager

## 使用示例
```python
# 打开图片管理对话框
dialog = ImageDialog(parent_window, product_id="PROD001")
dialog.exec_() 