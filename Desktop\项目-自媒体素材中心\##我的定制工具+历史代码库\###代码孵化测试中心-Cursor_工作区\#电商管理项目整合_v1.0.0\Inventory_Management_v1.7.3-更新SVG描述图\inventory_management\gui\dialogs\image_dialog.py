import os
from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
    QWidget,
    QGridLayout,
    QMessageBox,
    QFileDialog,
    QListWidget,
    QListWidgetItem,
    QTabWidget,
    QLineEdit,
    QInputDialog,
    QMenu,
    QToolBar,
    QProgressDialog,
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QPixmap, QImage, QIcon, QAction
from database.db_utils import get_connection
from database.image_utils import (
    save_product_image,
    get_product_images,
    delete_product_image,
    set_primary_image,
    get_image_path,
    get_product_image_dir,
    add_product_image,
    add_to_library,
    get_library_images,
    use_library_image,
    init_image_library,
    delete_library_image,
)
import logging
from PIL import Image
from utils.error_handler import ErrorHandler
import shutil


class ImageListWidget(QListWidget):
    """自定义图片列表控件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setViewMode(QListWidget.ViewMode.IconMode)
        self.setIconSize(QSize(100, 100))
        self.setSpacing(10)
        self.setResizeMode(QListWidget.ResizeMode.Adjust)
        self.setSelectionMode(QListWidget.SelectionMode.SingleSelection)
        self.setWrapping(True)

    def mouseDoubleClickEvent(self, event):
        """双击事件处理"""
        item = self.itemAt(event.position().toPoint())
        if item:
            # 获取图片信息
            image_data = item.data(Qt.ItemDataRole.UserRole)
            if image_data:
                # 打开预览窗口
                preview_dialog = ImagePreviewDialog(
                    self.parent(), image_data["product_id"], image_data["image_id"]
                )
                preview_dialog.exec()


class ImageDialog(QDialog):
    """图片管理对话框"""

    def __init__(self, parent=None, product_id=None):
        super().__init__(parent)
        self.product_id = product_id
        self.setWindowTitle("图片管理")
        self.resize(800, 600)
        self.init_ui()
        self.load_images()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # 工具栏
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))

        # 添加图片
        add_action = QAction(QIcon(":/icons/add.png"), "添加图片", self)
        add_action.setStatusTip("添加新图片")
        add_action.triggered.connect(self.add_images)
        toolbar.addAction(add_action)

        # 删除图片
        delete_action = QAction(QIcon(":/icons/delete.png"), "删除图片", self)
        delete_action.setStatusTip("删除选中的图片")
        delete_action.triggered.connect(self.delete_images)
        toolbar.addAction(delete_action)

        # 设置主图
        set_primary_action = QAction(QIcon(":/icons/star.png"), "设为主图", self)
        set_primary_action.setStatusTip("将选中的图片设为主图")
        set_primary_action.triggered.connect(self.set_primary_image)
        toolbar.addAction(set_primary_action)

        layout.addWidget(toolbar)

        # 主布局
        main_layout = QHBoxLayout()

        # 图片列表
        self.image_list = QListWidget()
        self.image_list.setViewMode(QListWidget.ViewMode.IconMode)
        self.image_list.setIconSize(QSize(100, 100))
        self.image_list.setSpacing(10)
        self.image_list.setResizeMode(QListWidget.ResizeMode.Adjust)
        self.image_list.setMovement(QListWidget.Movement.Static)
        self.image_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        self.image_list.itemSelectionChanged.connect(self.on_selection_changed)
        self.image_list.itemDoubleClicked.connect(self.show_image_preview)
        main_layout.addWidget(self.image_list, stretch=1)

        # 预览区域
        preview_layout = QVBoxLayout()
        preview_label = QLabel("预览")
        preview_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(preview_label)

        self.preview_image = QLabel()
        self.preview_image.setMinimumSize(300, 300)
        self.preview_image.setMaximumSize(400, 400)
        self.preview_image.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.preview_image.setStyleSheet(
            "QLabel { background-color: #f0f0f0; border: 1px solid #ccc; border-radius: 5px; }"
        )
        preview_layout.addWidget(self.preview_image)

        # 图片信息
        self.image_info = QLabel()
        self.image_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        preview_layout.addWidget(self.image_info)

        main_layout.addLayout(preview_layout)
        layout.addLayout(main_layout)

        # 底部按钮
        button_layout = QHBoxLayout()
        self.close_btn = QPushButton("关闭")
        self.close_btn.setIcon(QIcon(":/icons/close.png"))
        self.close_btn.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        layout.addLayout(button_layout)

    def load_images(self):
        """加载商品图片"""
        try:
            self.image_list.clear()
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT image_id, image_path, is_primary
                    FROM product_images
                    WHERE product_id = ?
                    ORDER BY is_primary DESC, image_id
                    """,
                    (self.product_id,),
                )
                images = cursor.fetchall()

                for image_id, image_path, is_primary in images:
                    if os.path.exists(image_path):
                        pixmap = QPixmap(image_path)
                        if not pixmap.isNull():
                            scaled_pixmap = pixmap.scaled(
                                100,
                                100,
                                Qt.AspectRatioMode.KeepAspectRatio,
                                Qt.TransformationMode.SmoothTransformation,
                            )
                            item = QListWidgetItem(QIcon(scaled_pixmap), "")
                            item.setData(
                                Qt.ItemDataRole.UserRole, (image_id, image_path)
                            )
                            if is_primary:
                                item.setToolTip("主图")
                                item.setBackground(Qt.GlobalColor.yellow)
                            self.image_list.addItem(item)

        except Exception as e:
            ErrorHandler.log_error("加载图片失败")
            QMessageBox.critical(self, "错误", f"加载图片失败: {str(e)}")

    def add_images(self):
        """添加图片"""
        try:
            files, _ = QFileDialog.getOpenFileNames(
                self,
                "选择图片",
                "",
                "图片文件 (*.jpg *.jpeg *.png *.gif);;所有文件 (*.*)",
            )

            if not files:
                return

            # 创建进度对话框
            progress = QProgressDialog("正在添加图片...", "取消", 0, len(files), self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)

            try:
                for i, file_path in enumerate(files):
                    if progress.wasCanceled():
                        break

                    progress.setValue(i)
                    self.add_single_image(file_path)

                progress.setValue(len(files))
                self.load_images()

            finally:
                progress.close()

        except Exception as e:
            ErrorHandler.log_error("添加图片失败")
            QMessageBox.critical(self, "错误", f"添加图片失败: {str(e)}")

    def add_single_image(self, file_path):
        """添加单个图片"""
        try:
            # 检查图片是否有效
            image = Image.open(file_path)
            image.verify()

            # 生成目标路径
            filename = os.path.basename(file_path)
            target_dir = os.path.join("images", self.product_id)
            os.makedirs(target_dir, exist_ok=True)
            target_path = os.path.join(target_dir, filename)

            # 复制图片文件
            shutil.copy2(file_path, target_path)

            # 添加数据库记录
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO product_images (product_id, image_path)
                    VALUES (?, ?)
                    """,
                    (self.product_id, target_path),
                )
                conn.commit()

        except Exception as e:
            ErrorHandler.log_error(f"添加图片 {file_path} 失败")
            raise

    def delete_images(self):
        """删除选中的图片"""
        try:
            items = self.image_list.selectedItems()
            if not items:
                QMessageBox.warning(self, "警告", "请先选择要删除的图片！")
                return

            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {len(items)} 张图片吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 创建进度对话框
                progress = QProgressDialog(
                    "正在删除图片...", "取消", 0, len(items), self
                )
                progress.setWindowModality(Qt.WindowModality.WindowModal)

                try:
                    for i, item in enumerate(items):
                        if progress.wasCanceled():
                            break

                        progress.setValue(i)
                        image_id, image_path = item.data(Qt.ItemDataRole.UserRole)

                        # 删除数据库记录
                        with get_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute(
                                "DELETE FROM product_images WHERE image_id = ?",
                                (image_id,),
                            )
                            conn.commit()

                        # 删除文件
                        if os.path.exists(image_path):
                            os.remove(image_path)

                    progress.setValue(len(items))
                    self.load_images()
                    self.clear_preview()

                finally:
                    progress.close()

        except Exception as e:
            ErrorHandler.log_error("删除图片失败")
            QMessageBox.critical(self, "错误", f"删除图片失败: {str(e)}")

    def set_primary_image(self):
        """设置主图"""
        try:
            items = self.image_list.selectedItems()
            if not items:
                QMessageBox.warning(self, "警告", "请先选择要设为主图的图片！")
                return

            if len(items) > 1:
                QMessageBox.warning(self, "警告", "只能选择一张图片设为主图！")
                return

            image_id, _ = items[0].data(Qt.ItemDataRole.UserRole)

            with get_connection() as conn:
                cursor = conn.cursor()
                # 先取消所有主图标记
                cursor.execute(
                    """
                    UPDATE product_images
                    SET is_primary = 0
                    WHERE product_id = ?
                    """,
                    (self.product_id,),
                )
                # 设置新的主图
                cursor.execute(
                    """
                    UPDATE product_images
                    SET is_primary = 1
                    WHERE image_id = ?
                    """,
                    (image_id,),
                )
                conn.commit()

            self.load_images()

        except Exception as e:
            ErrorHandler.log_error("设置主图失败")
            QMessageBox.critical(self, "错误", f"设置主图失败: {str(e)}")

    def on_selection_changed(self):
        """选中图片变化处理"""
        items = self.image_list.selectedItems()
        if items:
            _, image_path = items[0].data(Qt.ItemDataRole.UserRole)
            self.show_preview(image_path)
        else:
            self.clear_preview()

    def show_preview(self, image_path):
        """显示图片预览"""
        try:
            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    scaled_pixmap = pixmap.scaled(
                        400,
                        400,
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )
                    self.preview_image.setPixmap(scaled_pixmap)

                    # 更新图片信息
                    image = Image.open(image_path)
                    size = os.path.getsize(image_path)
                    size_str = (
                        f"{size/1024/1024:.2f} MB"
                        if size > 1024 * 1024
                        else f"{size/1024:.2f} KB"
                    )
                    info = f"尺寸: {image.size[0]}x{image.size[1]}\n大小: {size_str}"
                    self.image_info.setText(info)

        except Exception as e:
            ErrorHandler.log_error("显示图片预览失败")
            self.clear_preview()

    def clear_preview(self):
        """清除预览"""
        self.preview_image.clear()
        self.preview_image.setText("无预览")
        self.image_info.clear()

    def show_image_preview(self, item):
        """显示图片预览对话框"""
        try:
            _, image_path = item.data(Qt.ItemDataRole.UserRole)
            if os.path.exists(image_path):
                from .preview_dialog import PreviewDialog

                dialog = PreviewDialog(self, image_path)
                dialog.exec()

        except Exception as e:
            ErrorHandler.log_error("显示图片预览对话框失败")
            QMessageBox.critical(self, "错误", f"显示图片预览失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            # 保存当前状态或执行清理操作
            event.accept()
        except Exception as e:
            ErrorHandler.log_error("关闭图片对话框失败")
            event.accept()


class ImagePreviewDialog(QDialog):
    """图片预览对话框"""

    def __init__(self, parent, product_id, image_id):
        super().__init__(parent)
        self.product_id = product_id
        self.image_id = image_id
        self.setWindowTitle("图片预览")
        self.resize(800, 600)
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()

        # 图片显示区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 图片标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        scroll_area.setWidget(self.image_label)

        layout.addWidget(scroll_area)

        # 按钮
        button_layout = QHBoxLayout()
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # 加载图片
        try:
            image_path = get_image_path(self.product_id, self.image_id)
            if image_path and os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 调整图片大小以适应窗口
                    screen_size = self.screen().geometry()
                    max_width = screen_size.width() * 0.8
                    max_height = screen_size.height() * 0.8
                    scaled_pixmap = pixmap.scaled(
                        int(max_width),
                        int(max_height),
                        Qt.AspectRatioMode.KeepAspectRatio,
                        Qt.TransformationMode.SmoothTransformation,
                    )
                    self.image_label.setPixmap(scaled_pixmap)
                    # 调整窗口大小
                    self.resize(scaled_pixmap.size())
                else:
                    self.image_label.setText("无法加载图片")
            else:
                self.image_label.setText("图片不存在")
        except Exception as e:
            logging.exception("加载预览图片失败")
            self.image_label.setText(f"加载失败: {str(e)}")

    def keyPressEvent(self, event):
        """按键事件处理"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)
