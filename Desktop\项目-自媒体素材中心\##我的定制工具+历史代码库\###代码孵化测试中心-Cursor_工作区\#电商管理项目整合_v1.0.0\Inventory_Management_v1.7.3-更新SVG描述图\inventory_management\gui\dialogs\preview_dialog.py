from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QPixmap, QIcon
from utils.error_handler import ErrorHandler
import os


class PreviewDialog(QDialog):
    """图片预览对话框"""

    def __init__(self, parent=None, image_path=None):
        super().__init__(parent)
        self.image_path = image_path
        self.setWindowTitle("图片预览")
        self.resize(800, 600)
        self.init_ui()
        self.load_image()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 图片标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        scroll_area.setWidget(self.image_label)
        layout.addWidget(scroll_area)

        # 图片信息
        self.info_label = QLabel()
        self.info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.info_label)

        # 底部按钮
        button_layout = QHBoxLayout()
        self.close_btn = QPushButton("关闭")
        self.close_btn.setIcon(QIcon(":/icons/close.png"))
        self.close_btn.clicked.connect(self.accept)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        layout.addLayout(button_layout)

    def load_image(self):
        """加载图片"""
        try:
            if not self.image_path or not os.path.exists(self.image_path):
                self.image_label.setText("图片不存在")
                return

            pixmap = QPixmap(self.image_path)
            if pixmap.isNull():
                self.image_label.setText("无法加载图片")
                return

            # 获取窗口大小和图片大小
            window_size = self.size()
            image_size = pixmap.size()

            # 计算缩放比例
            scale = min(
                window_size.width() / image_size.width(),
                window_size.height() / image_size.height(),
            )

            # 如果图片比窗口小，不进行缩放
            if scale >= 1:
                scaled_pixmap = pixmap
            else:
                # 缩放图片
                scaled_pixmap = pixmap.scaled(
                    image_size.width() * scale,
                    image_size.height() * scale,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation,
                )

            # 显示图片
            self.image_label.setPixmap(scaled_pixmap)

            # 更新图片信息
            size = os.path.getsize(self.image_path)
            size_str = (
                f"{size/1024/1024:.2f} MB"
                if size > 1024 * 1024
                else f"{size/1024:.2f} KB"
            )
            info = f"原始尺寸: {image_size.width()}x{image_size.height()}\n"
            info += f"显示尺寸: {scaled_pixmap.width()}x{scaled_pixmap.height()}\n"
            info += f"文件大小: {size_str}"
            self.info_label.setText(info)

        except Exception as e:
            ErrorHandler.log_error("加载预览图片失败")
            self.image_label.setText(f"加载图片失败: {str(e)}")

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        self.load_image()  # 重新加载图片以适应新的窗口大小
