# 商品对话框模块 (product_dialog.py)

## 类定义

### ProductDialog 类
```python
class ProductDialog(QDialog):
    """商品编辑对话框类，继承自 QDialog"""
```

#### 初始化
```python
def __init__(self, parent=None, product=None):
    """
    初始化商品对话框
    :param parent: 父窗口
    :param product: 要编辑的商品对象，如果为None则为新建模式
    """
    super().__init__(parent)
    self.product = product
    self.setWindowTitle("编辑商品" if product else "新建商品")
    self.resize(800, 600)
    self.setup_ui()
```

## 界面组件

### 1. 基本信息区域
```python
self.basic_info_group = QGroupBox("基本信息")
self.name_edit = QLineEdit()              # 商品名称
self.category_combo = QComboBox()         # 商品类别
self.status_combo = QComboBox()           # 商品状态
self.quantity_spin = QSpinBox()           # 数量
self.unit_edit = QLineEdit()              # 单位
```

### 2. 价格信息区域
```python
self.price_info_group = QGroupBox("价格信息")
self.purchase_price_spin = QDoubleSpinBox()  # 采购价
self.selling_price_spin = QDoubleSpinBox()   # 售价
self.shipping_cost_spin = QDoubleSpinBox()   # 运费
self.other_cost_spin = QDoubleSpinBox()      # 其他成本
self.discount_spin = QSpinBox()              # 折扣率
```

### 3. 供应商信息区域
```python
self.supplier_info_group = QGroupBox("供应商信息")
self.supplier_edit = QLineEdit()          # 供应商
self.supplier_link_edit = QLineEdit()     # 供应商链接
self.purchase_link_edit = QLineEdit()     # 采购链接
self.purchaser_edit = QLineEdit()         # 采购人
```

### 4. 图片管理区域
```python
self.image_group = QGroupBox("图片管理")
self.image_list = ImageList()             # 图片列表
self.add_image_btn = QPushButton("添加图片")
self.delete_image_btn = QPushButton("删除图片")
self.set_primary_btn = QPushButton("设为主图")
```

### 5. 备注区域
```python
self.remarks_group = QGroupBox("备注")
self.remarks_edit = QTextEdit()           # 备注文本框
```

### 6. 按钮区域
```python
self.button_box = QDialogButtonBox(
    QDialogButtonBox.Ok | QDialogButtonBox.Cancel
)
```

## 功能方法

### 1. 界面设置
```python
def setup_ui(self):
    """设置用户界面"""
    self.setup_basic_info()
    self.setup_price_info()
    self.setup_supplier_info()
    self.setup_image_management()
    self.setup_remarks()
    self.setup_buttons()
    self.setup_layout()
    self.setup_connections()
```

### 2. 数据加载
```python
def load_data(self):
    """加载商品数据"""
    if self.product:
        self.name_edit.setText(self.product.name)
        self.category_combo.setCurrentText(self.product.category)
        self.status_combo.setCurrentText(self.product.status)
        self.quantity_spin.setValue(self.product.quantity)
        self.unit_edit.setText(self.product.unit)
        self.purchase_price_spin.setValue(self.product.purchase_price)
        self.selling_price_spin.setValue(self.product.selling_price)
        self.shipping_cost_spin.setValue(self.product.shipping_cost)
        self.other_cost_spin.setValue(self.product.other_cost)
        self.discount_spin.setValue(self.product.discount_rate)
        self.supplier_edit.setText(self.product.supplier)
        self.supplier_link_edit.setText(self.product.supplier_link)
        self.purchase_link_edit.setText(self.product.purchase_link)
        self.purchaser_edit.setText(self.product.purchaser)
        self.remarks_edit.setText(self.product.remarks)
        self.image_list.load_images(self.product.product_id)
```

### 3. 数据保存
```python
def save_data(self):
    """保存商品数据"""
    try:
        product_data = {
            "name": self.name_edit.text(),
            "category": self.category_combo.currentText(),
            "status": self.status_combo.currentText(),
            "quantity": self.quantity_spin.value(),
            "unit": self.unit_edit.text(),
            "purchase_price": self.purchase_price_spin.value(),
            "selling_price": self.selling_price_spin.value(),
            "shipping_cost": self.shipping_cost_spin.value(),
            "other_cost": self.other_cost_spin.value(),
            "discount_rate": self.discount_spin.value(),
            "supplier": self.supplier_edit.text(),
            "supplier_link": self.supplier_link_edit.text(),
            "purchase_link": self.purchase_link_edit.text(),
            "purchaser": self.purchaser_edit.text(),
            "remarks": self.remarks_edit.toPlainText()
        }
        
        if self.product:
            self.db.update_product(self.product.product_id, product_data)
        else:
            self.db.add_product(product_data)
            
        return True
    except Exception as e:
        ErrorHandler.handle_error(e, self)
        return False
```

### 4. 图片管理
```python
def add_image(self):
    """添加图片"""
    file_paths, _ = QFileDialog.getOpenFileNames(
        self, "选择图片", "", "Images (*.png *.jpg *.jpeg)"
    )
    if file_paths:
        try:
            for path in file_paths:
                self.image_list.add_image(path)
        except Exception as e:
            ErrorHandler.handle_error(e, self)

def delete_image(self):
    """删除图片"""
    if self.image_list.current_item:
        try:
            self.image_list.delete_current_image()
        except Exception as e:
            ErrorHandler.handle_error(e, self)

def set_primary_image(self):
    """设置主图"""
    if self.image_list.current_item:
        try:
            self.image_list.set_primary_image()
        except Exception as e:
            ErrorHandler.handle_error(e, self)
```

## 数据验证

### 1. 基本验证
```python
def validate_data(self):
    """验证数据有效性"""
    if not self.name_edit.text():
        ErrorHandler.show_warning(self, "验证错误", "商品名称不能为空！")
        return False
        
    if not self.category_combo.currentText():
        ErrorHandler.show_warning(self, "验证错误", "请选择商品类别！")
        return False
        
    if self.purchase_price_spin.value() < 0:
        ErrorHandler.show_warning(self, "验证错误", "采购价不能为负数！")
        return False
        
    if self.selling_price_spin.value() < 0:
        ErrorHandler.show_warning(self, "验证错误", "售价不能为负数！")
        return False
        
    return True
```

### 2. 自动计算
```python
def calculate_total_cost(self):
    """计算总成本"""
    purchase_price = self.purchase_price_spin.value()
    shipping_cost = self.shipping_cost_spin.value()
    other_cost = self.other_cost_spin.value()
    return purchase_price + shipping_cost + other_cost

def calculate_profit(self):
    """计算预估利润"""
    selling_price = self.selling_price_spin.value()
    discount_rate = self.discount_spin.value() / 100
    total_cost = self.calculate_total_cost()
    return selling_price * discount_rate - total_cost
```

## 信号和槽

### 1. 按钮信号
```python
self.button_box.accepted.connect(self.accept)
self.button_box.rejected.connect(self.reject)
self.add_image_btn.clicked.connect(self.add_image)
self.delete_image_btn.clicked.connect(self.delete_image)
self.set_primary_btn.clicked.connect(self.set_primary_image)
```

### 2. 数据变更信号
```python
self.purchase_price_spin.valueChanged.connect(self.on_cost_changed)
self.shipping_cost_spin.valueChanged.connect(self.on_cost_changed)
self.other_cost_spin.valueChanged.connect(self.on_cost_changed)
self.selling_price_spin.valueChanged.connect(self.on_price_changed)
self.discount_spin.valueChanged.connect(self.on_price_changed)
```

## 事件处理

### 1. 接受事件
```python
def accept(self):
    """确认按钮事件"""
    if self.validate_data():
        if self.save_data():
            super().accept()
```

### 2. 关闭事件
```python
def closeEvent(self, event):
    """窗口关闭事件"""
    if self.is_data_modified():
        reply = ErrorHandler.confirm_action(
            self, "确认关闭", "数据已修改，是否保存？"
        )
        if reply:
            if self.validate_data() and self.save_data():
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
    else:
        event.accept()
```

## 依赖关系

### 1. PyQt5 组件
- QDialog
- QGroupBox
- QLineEdit
- QComboBox
- QSpinBox
- QDoubleSpinBox
- QTextEdit
- QPushButton
- QDialogButtonBox
- QVBoxLayout
- QHBoxLayout
- QFileDialog

### 2. 自定义组件
- ImageList
- ErrorHandler

### 3. 数据库
- DatabaseManager

## 使用示例
```python
# 新建商品
dialog = ProductDialog(parent_window)
if dialog.exec_() == QDialog.Accepted:
    # 处理新建商品的后续操作
    pass

# 编辑商品
dialog = ProductDialog(parent_window, existing_product)
if dialog.exec_() == QDialog.Accepted:
    # 处理编辑商品的后续操作
    pass
``` 