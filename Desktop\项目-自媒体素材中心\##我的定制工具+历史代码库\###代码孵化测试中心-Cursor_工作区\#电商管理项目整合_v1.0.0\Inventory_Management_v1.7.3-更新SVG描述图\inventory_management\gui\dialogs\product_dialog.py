from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QComboBox,
    QSpinBox,
    QDoubleSpinBox,
    QTextEdit,
    QMessageBox,
    QGroupBox,
    QCheckBox,
    QFormLayout,
    QFileDialog,
    QScrollArea,
    QWidget,
    QListWidget,
    QListWidgetItem,
)
from PyQt6.QtGui import QPixmap
from PyQt6.QtCore import Qt, pyqtSignal
from database.db_utils import (
    get_connection,
    get_categories,
    get_next_type_number,
    generate_product_id,
    parse_product_id,
)
from database.image_utils import get_product_image_dir
import logging
import os
import shutil
from datetime import datetime


class ProductDialog(QDialog):
    """商品编辑对话框"""

    product_saved = pyqtSignal(str)  # 商品保存成功信号

    def __init__(self, parent=None, product_id=None, is_merged_view=False):
        """初始化商品编辑对话框"""
        super().__init__(parent)
        logging.info(
            f"初始化商品编辑对话框: product_id={product_id}, is_merged_view={is_merged_view}"
        )

        self.product_id = product_id
        self.image_path = None
        self.current_type_number = None  # 当前类型编号
        self.is_merged_view = is_merged_view  # 是否合并视图

        # 设置窗口属性
        self.setWindowTitle("添加商品" if product_id is None else "编辑商品")
        self.resize(800, 600)  # 调整窗口大小

        # 初始化UI
        self.init_ui()
        logging.info("UI初始化完成")

        # 连接信号
        self.connect_signals()
        logging.info("信号连接完成")

        # 如果是编辑模式，加载现有商品数据
        if product_id:
            logging.info(f"编辑模式，开始加载商品数据: {product_id}")
            self.load_product_data()
        else:
            # 新建模式，触发分类改变事件以生成预览ID
            logging.info("新建模式，触发分类改变事件")
            self.on_category_changed(self.category_input.currentText())

            # 设置合并复选框的初始状态
            self.merge_checkbox.setChecked(self.is_merged_view)
            logging.info(f"设置合并复选框状态: {self.is_merged_view}")

    def connect_signals(self):
        """连接所有信号"""
        try:
            # 分类改变时更新商品ID
            if not self.product_id:
                self.category_input.currentTextChanged.connect(self.on_category_changed)

            # 价格相关的信号
            self.purchase_price_input.valueChanged.connect(self.calculate_prices)
            self.shipping_cost_input.valueChanged.connect(self.calculate_prices)
            self.other_cost_input.valueChanged.connect(self.calculate_prices)
            self.selling_price_input.valueChanged.connect(self.calculate_prices)
            self.discount_rate_input.valueChanged.connect(self.calculate_prices)

            logging.info("所有信号连接完成")
        except Exception as e:
            logging.exception("连接信号失败")
            raise

    def init_ui(self):
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 创建水平布局来放置左右两侧的内容
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)  # 增加左右两侧的间距

        # 左侧内容区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # 基本信息区域
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout()
        basic_layout.setSpacing(10)

        # 商品ID（仅编辑时显示）
        self.product_id_input = QLineEdit()
        self.product_id_input.setEnabled(False)
        basic_layout.addRow("商品ID:", self.product_id_input)

        # 商品名称
        self.name_input = QLineEdit()
        basic_layout.addRow("商品名称:", self.name_input)

        # 商品分类
        self.category_input = QComboBox()
        self.category_input.setEditable(True)
        self.load_categories()
        basic_layout.addRow("商品分类:", self.category_input)

        # 商品数量和合并选项的水平布局
        quantity_layout = QHBoxLayout()
        self.quantity_input = QSpinBox()
        self.quantity_input.setRange(0, 999999)
        self.merge_checkbox = QCheckBox("合并为一个商品")
        quantity_layout.addWidget(self.quantity_input)
        quantity_layout.addWidget(self.merge_checkbox)
        quantity_layout.addStretch()
        basic_layout.addRow("商品数量:", quantity_layout)

        # 商品单位
        self.unit_input = QLineEdit()
        self.unit_input.setText("个")
        basic_layout.addRow("商品单位:", self.unit_input)

        # 存放位置
        self.location_input = QLineEdit()
        basic_layout.addRow("存放位置:", self.location_input)

        basic_group.setLayout(basic_layout)
        left_layout.addWidget(basic_group)

        # 价格信息区域
        price_group = QGroupBox("价格信息")
        price_layout = QFormLayout()
        price_layout.setSpacing(10)

        # 进货价
        self.purchase_price_input = QDoubleSpinBox()
        self.purchase_price_input.setRange(0, 999999.99)
        self.purchase_price_input.setDecimals(2)
        self.purchase_price_input.setPrefix("¥")
        price_layout.addRow("进货价:", self.purchase_price_input)

        # 运费
        self.shipping_cost_input = QDoubleSpinBox()
        self.shipping_cost_input.setRange(0, 999999.99)
        self.shipping_cost_input.setDecimals(2)
        self.shipping_cost_input.setPrefix("¥")
        price_layout.addRow("运费:", self.shipping_cost_input)

        # 其他成本
        self.other_cost_input = QDoubleSpinBox()
        self.other_cost_input.setRange(0, 999999.99)
        self.other_cost_input.setDecimals(2)
        self.other_cost_input.setPrefix("¥")
        price_layout.addRow("其他成本:", self.other_cost_input)

        # 总成本（自动计算）
        self.total_cost_label = QLabel("¥0.00")
        price_layout.addRow("总成本:", self.total_cost_label)

        # 零售价
        self.selling_price_input = QDoubleSpinBox()
        self.selling_price_input.setRange(0, 999999.99)
        self.selling_price_input.setDecimals(2)
        self.selling_price_input.setPrefix("¥")
        price_layout.addRow("零售价:", self.selling_price_input)

        # 折扣率
        self.discount_rate_input = QSpinBox()
        self.discount_rate_input.setRange(0, 100)
        self.discount_rate_input.setSuffix("%")
        self.discount_rate_input.setValue(100)
        price_layout.addRow("折扣率:", self.discount_rate_input)

        # 预估利润（自动计算）
        self.profit_label = QLabel("¥0.00")
        price_layout.addRow("预估利润:", self.profit_label)

        price_group.setLayout(price_layout)
        left_layout.addWidget(price_group)

        # 其他信息区域
        other_group = QGroupBox("其他信息")
        other_layout = QFormLayout()
        other_layout.setSpacing(10)

        # 供应商
        self.supplier_input = QLineEdit()
        other_layout.addRow("供应商:", self.supplier_input)

        # 供应商链接
        self.supplier_link_input = QLineEdit()
        other_layout.addRow("供应商链接:", self.supplier_link_input)

        # 采购链接
        self.purchase_link_input = QLineEdit()
        other_layout.addRow("采购链接:", self.purchase_link_input)

        # 采购人员
        self.purchaser_input = QLineEdit()
        other_layout.addRow("采购人员:", self.purchaser_input)

        # 销售链接
        self.selling_link_input = QLineEdit()
        other_layout.addRow("销售链接:", self.selling_link_input)

        # 商品状态
        self.status_input = QComboBox()
        self.status_input.addItems(["在售", "缺货", "停售"])
        other_layout.addRow("商品状态:", self.status_input)

        # 备注
        self.remarks_input = QTextEdit()
        self.remarks_input.setMaximumHeight(100)
        other_layout.addRow("备注:", self.remarks_input)

        other_group.setLayout(other_layout)
        left_layout.addWidget(other_group)

        # 创建左侧的滚动区域
        left_scroll = QScrollArea()
        left_scroll.setWidget(left_widget)
        left_scroll.setWidgetResizable(True)
        left_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        left_scroll.setMinimumWidth(500)  # 设置最小宽度
        content_layout.addWidget(left_scroll, stretch=2)

        # 右侧图片区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(10)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_widget.setMinimumWidth(320)  # 设置最小宽度，确保图片区域可见

        # 图片管理区域
        image_group = QGroupBox("图片管理")
        image_layout = QVBoxLayout()
        image_layout.setSpacing(10)
        image_layout.setContentsMargins(10, 10, 10, 10)

        # 图片预览
        self.image_label = QLabel()
        self.image_label.setFixedSize(300, 300)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet(
            """
            QLabel {
                border: 1px solid #ccc;
                background-color: white;
                padding: 5px;
            }
            """
        )
        self.image_label.setText("无图片")
        image_layout.addWidget(
            self.image_label, alignment=Qt.AlignmentFlag.AlignHCenter
        )

        # 图片操作按钮
        image_buttons_layout = QHBoxLayout()
        image_buttons_layout.setSpacing(10)
        self.select_image_btn = QPushButton("选择图片")
        self.select_image_btn.setMinimumWidth(100)
        self.select_image_btn.clicked.connect(self.select_image)
        self.clear_image_btn = QPushButton("清除图片")
        self.clear_image_btn.setMinimumWidth(100)
        self.clear_image_btn.clicked.connect(self.clear_image)
        image_buttons_layout.addWidget(self.select_image_btn)
        image_buttons_layout.addWidget(self.clear_image_btn)
        image_layout.addLayout(image_buttons_layout)

        image_group.setLayout(image_layout)
        right_layout.addWidget(image_group)
        right_layout.addStretch()
        content_layout.addWidget(right_widget, stretch=1)

        main_layout.addLayout(content_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        save_btn = QPushButton("保存")
        save_btn.setMinimumWidth(100)
        save_btn.clicked.connect(self.save_product)
        cancel_btn = QPushButton("取消")
        cancel_btn.setMinimumWidth(100)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addStretch()
        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)

        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

    def load_categories(self):
        """加载商品分类"""
        try:
            categories = get_categories()
            self.category_input.clear()
            self.category_input.addItems(categories)
        except Exception as e:
            logging.exception("加载商品分类失败")
            QMessageBox.warning(self, "警告", f"加载商品分类失败: {str(e)}")

    def load_product_data(self):
        """加载商品数据"""
        try:
            if not self.product_id:
                raise ValueError("商品ID不能为空")

            logging.info(f"开始加载商品数据: {self.product_id}")

            # 使用with语句管理数据库连接
            with get_connection() as conn:
                cursor = conn.cursor()

                # 执行查询
                try:
                    cursor.execute(
                        """
                        SELECT *
                        FROM products 
                        WHERE product_id = ?
                        """,
                        (self.product_id,),
                    )
                except Exception as e:
                    raise Exception(f"执行SQL查询失败: {str(e)}")

                # 获取结果
                try:
                    product = cursor.fetchone()
                except Exception as e:
                    raise Exception(f"获取查询结果失败: {str(e)}")

                if not product:
                    logging.warning(f"未找到商品数据: {self.product_id}")
                    QMessageBox.warning(
                        self, "警告", f"未找到商品数据: {self.product_id}"
                    )
                    return

                # 设置UI数据
                try:
                    # 设置商品ID
                    try:
                        self.product_id_input.setText(str(product[0]))
                    except Exception as e:
                        raise Exception(f"设置商品ID失败: {str(e)}")

                    # 设置基本信息
                    try:
                        self.name_input.setText(str(product[1] or ""))
                        self.category_input.setCurrentText(str(product[2] or ""))
                        self.quantity_input.setValue(int(product[3] or 0))
                        self.unit_input.setText(str(product[4] or "个"))
                        self.status_input.setCurrentText(str(product[5] or "在售"))
                    except Exception as e:
                        raise Exception(f"设置基本信息失败: {str(e)}")

                    # 设置位置和供应商信息
                    try:
                        self.location_input.setText(str(product[6] or ""))
                        self.supplier_input.setText(str(product[7] or ""))
                        self.supplier_link_input.setText(str(product[8] or ""))
                        self.purchase_link_input.setText(str(product[9] or ""))
                    except Exception as e:
                        raise Exception(f"设置位置和供应商信息失败: {str(e)}")

                    # 设置价格信息
                    try:

                        def safe_float(value, default=0.0):
                            try:
                                return (
                                    float(str(value)) if value is not None else default
                                )
                            except (ValueError, TypeError):
                                return default

                        self.purchase_price_input.setValue(safe_float(product[10]))
                        self.shipping_cost_input.setValue(safe_float(product[11]))
                        self.other_cost_input.setValue(safe_float(product[12]))
                        self.total_cost_label.setText(f"¥{safe_float(product[13]):.2f}")
                        self.selling_price_input.setValue(safe_float(product[14]))

                        try:
                            discount = (
                                int(float(str(product[15]))) if product[15] else 100
                            )
                            discount = max(0, min(100, discount))  # 确保在0-100之间
                            self.discount_rate_input.setValue(discount)
                        except (ValueError, TypeError):
                            self.discount_rate_input.setValue(100)
                            logging.warning(
                                f"折扣率转换失败，使用默认值100: {product[15]}"
                            )
                    except Exception as e:
                        raise Exception(f"设置价格信息失败: {str(e)}")

                    # 设置其他信息
                    try:
                        self.purchaser_input.setText(str(product[17] or ""))
                        self.selling_link_input.setText(str(product[18] or ""))
                    except Exception as e:
                        raise Exception(f"设置其他信息失败: {str(e)}")

                    # 设置图片
                    try:
                        self.image_path = str(product[19] or "")
                        if self.image_path and os.path.exists(self.image_path):
                            pixmap = QPixmap(self.image_path)
                            if not pixmap.isNull():
                                scaled_pixmap = pixmap.scaled(
                                    self.image_label.size(),
                                    Qt.TransformationMode.KeepAspectRatio,
                                    Qt.TransformationMode.SmoothTransformation,
                                )
                                self.image_label.setPixmap(scaled_pixmap)
                            else:
                                self.image_label.setText("图片加载失败")
                                logging.warning(f"图片加载失败: {self.image_path}")
                        else:
                            self.image_label.setText("无图片")
                    except Exception as e:
                        self.image_label.setText("图片加载失败")
                        logging.error(f"设置图片失败: {str(e)}")

                    # 设置备注
                    try:
                        self.remarks_input.setText(str(product[20] or ""))
                    except Exception as e:
                        raise Exception(f"设置备注失败: {str(e)}")

                    # 触发价格计算
                    try:
                        self.calculate_prices()
                    except Exception as e:
                        raise Exception(f"计算价格失败: {str(e)}")

                    logging.info("商品数据加载完成")

                except Exception as e:
                    error_msg = f"设置UI数据失败: {str(e)}"
                    logging.exception(error_msg)
                    QMessageBox.warning(self, "警告", error_msg)
                    raise

        except Exception as e:
            error_msg = f"加载商品数据失败: {str(e)}"
            logging.exception(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
            raise

    def calculate_prices(self):
        """计算总成本和预估利润"""
        try:
            # 计算总成本
            purchase_price = self.purchase_price_input.value()
            shipping_cost = self.shipping_cost_input.value()
            other_cost = self.other_cost_input.value()
            total_cost = purchase_price + shipping_cost + other_cost
            self.total_cost_label.setText(f"¥{total_cost:.2f}")

            # 计算预估利润
            selling_price = self.selling_price_input.value()
            discount_rate = self.discount_rate_input.value() / 100.0
            discounted_price = selling_price * discount_rate
            profit = discounted_price - total_cost
            self.profit_label.setText(f"¥{profit:.2f}")

        except Exception as e:
            logging.exception("计算价格失败")
            self.total_cost_label.setText("计算错误")
            self.profit_label.setText("计算错误")

    def select_image(self):
        """选择商品图片"""
        try:
            if self.product_id:
                # 编辑模式：打开图片管理对话框
                from gui.dialogs.image_dialog import ImageDialog

                if self.is_merged_view:
                    # 合并视图：先询问是否要为所有同类商品设置相同的图片
                    reply = QMessageBox.question(
                        self,
                        "图片管理",
                        '是否要为所有同类商品设置相同的图片？\n选择"否"将只修改当前商品的图片',
                        QMessageBox.StandardButton.Yes
                        | QMessageBox.StandardButton.No
                        | QMessageBox.StandardButton.Cancel,
                    )

                    if reply == QMessageBox.StandardButton.Cancel:
                        return

                    if reply == QMessageBox.StandardButton.Yes:
                        # 获取所有同类商品ID
                        type_id = self.product_id.rsplit("-", 1)[0]
                        with get_connection() as conn:
                            cursor = conn.cursor()
                            cursor.execute(
                                """
                                SELECT product_id 
                                FROM products 
                                WHERE product_id LIKE ?
                                ORDER BY product_id
                                """,
                                (type_id + "%",),
                            )
                            product_ids = [row[0] for row in cursor.fetchall()]

                        # 打开图片管理对话框
                        dialog = ImageDialog(self, self.product_id)
                        if dialog.exec_() == QDialog.DialogCode.Accepted:
                            # 同步图片到所有同类商品
                            with get_connection() as conn:
                                cursor = conn.cursor()
                                # 获取主图信息
                                cursor.execute(
                                    """
                                    SELECT image_id, image_path 
                                    FROM product_images 
                                    WHERE product_id = ? AND is_primary = 1
                                    """,
                                    (self.product_id,),
                                )
                                primary_image = cursor.fetchone()

                                if primary_image:
                                    # 为所有同类商品设置相同的主图
                                    for pid in product_ids:
                                        if pid != self.product_id:  # 跳过当前商品
                                            cursor.execute(
                                                """
                                                UPDATE product_images
                                                SET is_primary = 0
                                                WHERE product_id = ?
                                                """,
                                                (pid,),
                                            )
                                            cursor.execute(
                                                """
                                                INSERT OR REPLACE INTO product_images
                                                (image_id, product_id, image_path, is_primary)
                                                VALUES (?, ?, ?, 1)
                                                """,
                                                (
                                                    primary_image[0],
                                                    pid,
                                                    primary_image[1],
                                                ),
                                            )
                                conn.commit()
                    else:
                        # 只修改当前商品
                        dialog = ImageDialog(self, self.product_id)
                        dialog.exec_()
                else:
                    # 单个商品编辑
                    dialog = ImageDialog(self, self.product_id)
                    dialog.exec_()

                # 刷新图片显示
                self.load_product_images()
            else:
                # 新建模式：直接选择图片文件
                file_name, _ = QFileDialog.getOpenFileName(
                    self,
                    "选择图片",
                    "",
                    "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif);;所有文件 (*.*)",
                )
                if file_name:
                    # 加载并显示图片
                    pixmap = QPixmap(file_name)
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(
                            self.image_label.size(),
                            Qt.TransformationMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation,
                        )
                        self.image_label.setPixmap(scaled_pixmap)
                        self.image_path = file_name
                    else:
                        QMessageBox.warning(self, "警告", "无法加载所选图片")

        except Exception as e:
            logging.exception("选择图片失败")
            QMessageBox.critical(self, "错误", f"选择图片失败: {str(e)}")

    def load_product_images(self):
        """加载商品图片"""
        try:
            if not self.product_id:
                return

            # 获取商品的主图
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT image_path 
                    FROM product_images 
                    WHERE product_id = ? AND is_primary = 1
                    """,
                    (self.product_id,),
                )
                result = cursor.fetchone()

            if result and result[0]:
                # 获取预览图路径
                preview_path = os.path.join(
                    get_product_image_dir(self.product_id),
                    "previews",
                    f"preview_{result[0]}",
                )

                if os.path.exists(preview_path):
                    pixmap = QPixmap(preview_path)
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(
                            self.image_label.size(),
                            Qt.TransformationMode.KeepAspectRatio,
                            Qt.TransformationMode.SmoothTransformation,
                        )
                        self.image_label.setPixmap(scaled_pixmap)
                        return

            # 如果没有图片或加载失败，显示默认文本
            self.image_label.clear()
            self.image_label.setText("无图片")

        except Exception as e:
            logging.exception("加载商品图片失败")
            self.image_label.setText("图片加载失败")

    def clear_image(self):
        """清除商品图片"""
        if self.product_id:
            # 编辑模式：打开图片管理
            self.select_image()
        else:
            # 新建模式：清除图片
            self.image_label.clear()
            self.image_label.setText("无图片")
            self.image_path = None

    def on_category_changed(self, category):
        """当分类改变时，自动更新商品ID"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                # 生成新的商品ID预览
                if category:  # 添加分类检查
                    preview_id = generate_product_id(category, cursor=cursor)
                    self.product_id_input.setText(preview_id)
                    logging.debug(f"生成的商品ID预览: {preview_id}")
                else:
                    self.product_id_input.clear()
        except Exception as e:
            logging.exception("生成商品ID预览失败")
            QMessageBox.warning(self, "Error", f"生成商品ID预览失败: {e}")

    def save_product(self):
        """保存商品数据"""
        try:
            # 获取输入数据
            name = self.name_input.text().strip()
            if not name:
                QMessageBox.warning(self, "提示", "请输入商品名称")
                return

            # 获取要添加的商品数量
            quantity = self.quantity_input.value()
            if quantity < 1:
                QMessageBox.warning(self, "提示", "商品数量必须大于0")
                return

            # 收集基本输入数据
            base_data = {
                "name": name,
                "category": self.category_input.currentText().strip(),
                "unit": self.unit_input.text().strip(),
                "status": self.status_input.currentText(),
                "location": self.location_input.text().strip(),
                "supplier": self.supplier_input.text().strip(),
                "supplier_link": self.supplier_link_input.text().strip(),
                "purchase_link": self.purchase_link_input.text().strip(),
                "purchase_price": self.purchase_price_input.value(),
                "shipping_cost": self.shipping_cost_input.value(),
                "other_cost": self.other_cost_input.value(),
                "total_cost": float(self.total_cost_label.text().replace("¥", "")),
                "selling_price": self.selling_price_input.value(),
                "discount_rate": self.discount_rate_input.value(),
                "total_profit": float(self.profit_label.text().replace("¥", "")),
                "purchaser": self.purchaser_input.text().strip(),
                "selling_link": self.selling_link_input.text().strip(),
                "image_path": self.image_path if self.image_path else "",
                "remarks": self.remarks_input.toPlainText().strip(),
            }

            with get_connection() as conn:
                cursor = conn.cursor()

                if self.product_id:  # 编辑模式
                    if self.is_merged_view:
                        # 在合并视图下，更新所有同类商品
                        type_id = self.product_id.rsplit("-", 1)[
                            0
                        ]  # 获取类型ID（去掉最后的序号）

                        # 更新所有同类商品
                        set_clause = ", ".join(f"{k} = ?" for k in base_data.keys())
                        values = list(base_data.values())
                        values.append(type_id + "%")  # 添加WHERE条件的值

                        cursor.execute(
                            f"""
                            UPDATE products
                            SET {set_clause}
                            WHERE product_id LIKE ?
                            """,
                            values,
                        )

                        # 如果有图片更新，同步更新所有同类商品的图片记录
                        if base_data["image_path"]:
                            cursor.execute(
                                """
                                SELECT product_id 
                                FROM products 
                                WHERE product_id LIKE ?
                                """,
                                (type_id + "%",),
                            )
                            product_ids = [row[0] for row in cursor.fetchall()]

                            # 获取主图信息
                            cursor.execute(
                                """
                                SELECT image_id, image_path 
                                FROM product_images 
                                WHERE product_id = ? AND is_primary = 1
                                """,
                                (self.product_id,),
                            )
                            primary_image = cursor.fetchone()

                            if primary_image:
                                # 为所有同类商品设置相同的主图
                                for pid in product_ids:
                                    cursor.execute(
                                        """
                                        UPDATE product_images
                                        SET is_primary = 0
                                        WHERE product_id = ?
                                        """,
                                        (pid,),
                                    )
                                    cursor.execute(
                                        """
                                        INSERT OR REPLACE INTO product_images
                                        (image_id, product_id, image_path, is_primary)
                                        VALUES (?, ?, ?, 1)
                                        """,
                                        (primary_image[0], pid, primary_image[1]),
                                    )

                    else:
                        # 普通编辑模式，只更新当前商品
                        set_clause = ", ".join(f"{k} = ?" for k in base_data.keys())
                        values = list(base_data.values())
                        values.append(self.product_id)  # 添加WHERE条件的值

                        cursor.execute(
                            f"""
                            UPDATE products
                            SET {set_clause}
                            WHERE product_id = ?
                            """,
                            values,
                        )

                    logging.info(f"商品更新成功: {self.product_id}")
                    QMessageBox.information(self, "成功", "商品更新成功！")

                else:  # 新建模式
                    success_count = 0
                    first_product_id = None

                    # 获取或生成类型编号
                    type_number = None
                    if "-" in self.product_id_input.text():
                        parts = self.product_id_input.text().split("-")
                        if len(parts) >= 2:
                            type_number = parts[1].replace("#", "")

                    if not type_number:
                        type_number = get_next_type_number(
                            cursor, base_data["category"]
                        )

                    # 批量添加商品
                    for i in range(quantity):
                        try:
                            # 为每个商品生成唯一ID
                            product_data = base_data.copy()
                            product_data["quantity"] = 1  # 每个商品数量为1
                            product_data["product_id"] = generate_product_id(
                                base_data["category"], type_number, cursor=cursor
                            )

                            if not first_product_id:
                                first_product_id = product_data["product_id"]

                            # 插入新商品
                            fields = ", ".join(product_data.keys())
                            placeholders = ", ".join("?" * len(product_data))
                            values = list(product_data.values())

                            cursor.execute(
                                f"""
                                INSERT INTO products ({fields})
                                VALUES ({placeholders})
                                """,
                                values,
                            )
                            success_count += 1
                            logging.info(
                                f"成功添加第 {i+1} 个商品: {product_data['product_id']}"
                            )

                        except Exception as e:
                            logging.error(f"添加第 {i+1} 个商品失败: {str(e)}")
                            continue

                    if success_count > 0:
                        logging.info(f"成功添加 {success_count}/{quantity} 个商品")
                        QMessageBox.information(
                            self,
                            "成功",
                            f"成功添加 {success_count}/{quantity} 个商品！",
                        )
                        self.product_saved.emit(first_product_id)
                    else:
                        QMessageBox.critical(self, "错误", "所有商品添加都失败了！")

                conn.commit()
                self.accept()

        except Exception as e:
            logging.exception("保存商品失败")
            QMessageBox.critical(self, "错误", f"保存商品失败: {str(e)}")
