# 主窗口模块 (main_window.py)

## 功能概述
`main_window.py` 实现了应用程序的主窗口界面，管理所有核心功能的集成和交互。主窗口采用选项卡式布局，包含商品管理和批次管理两个主要功能区域。

## 类定义
```python
class MainWindow(QMainWindow):
    """主窗口类，继承自 QMainWindow"""
```

### 核心属性
- `product_table`: 商品表格控件
- `product_toolbar`: 商品工具栏控件
- `batch_table`: 批次表格控件
- `batch_toolbar`: 批次工具栏控件
- `refresh_timer`: 自动刷新定时器
- `last_refresh_time`: 最后刷新时间

### 初始化方法
1. **__init__()**
   - 初始化窗口基本属性
   - 设置错误处理和日志
   - 配置图片存储目录
   - 初始化数据库
   - 设置UI界面
   - 启动自动刷新

2. **init_ui()**
   - 创建中心部件
   - 设置主布局
   - 创建选项卡
   - 初始化工具栏和状态栏
   - 加载样式表

### 商品管理功能
1. **setup_product_tab()**
   - 创建商品工具栏
   - 创建商品表格
   - 连接信号和槽
   - 加载初始数据

2. **load_products()**
   - 从数据库加载商品数据
   - 支持合并/展开显示
   - 处理过滤条件
   - 更新表格显示

3. **filter_products()**
   - 根据搜索文本过滤
   - 支持分类过滤
   - 支持批次过滤
   - 更新状态信息

4. **商品操作方法**
   - `show_add_dialog()`: 显示添加商品对话框
   - `edit_product()`: 编辑商品
   - `delete_product()`: 删除商品
   - `manage_images()`: 管理商品图片

### 批次管理功能
1. **setup_batch_tab()**
   - 创建批次工具栏
   - 创建批次表格
   - 连接信号和槽

2. **批次操作方法**
   - `show_batch_dialog()`: 显示批次对话框
   - `edit_batch()`: 编辑批次
   - `delete_batch()`: 删除批次
   - `filter_batches()`: 过滤批次列表

### 数据刷新机制
1. **refresh_data()**
   - 刷新商品数据
   - 更新分类和批次列表
   - 更新状态栏信息
   - 记录刷新时间

2. **自动刷新**
   - `start_auto_refresh()`: 启动自动刷新
   - `toggle_auto_refresh()`: 切换自动刷新状态
   - `set_refresh_interval()`: 设置刷新间隔

### 状态栏管理
1. **setup_status_bar()**
   - 创建状态栏标签
   - 设置数据库状态
   - 显示商品计数
   - 显示刷新时间

2. **update_status_info()**
   - 更新数据库状态
   - 更新商品数量统计
   - 更新最后刷新时间

## 依赖关系
- PyQt5 组件
- 自定义控件 (product_widgets, batch_widgets)
- 数据库工具 (db_utils)
- 错误处理 (error_handler)
- 对话框组件 (dialogs)

## 事件处理
1. **closeEvent()**
   - 停止定时器
   - 关闭控制台窗口
   - 记录日志

2. **信号连接**
   - 商品相关信号
   - 批次相关信号
   - 工具栏信号
   - 自动刷新信号

## 注意事项
1. 数据一致性
   - 操作后及时刷新
   - 保持状态栏同步
   - 确保数据完整性

2. 性能优化
   - 合理使用定时刷新
   - 优化数据加载
   - 避免重复操作

3. 错误处理
   - 完整的异常捕获
   - 用户友好的错误提示
   - 详细的日志记录

4. 用户体验
   - 响应式界面
   - 清晰的状态反馈
   - 合理的操作流程