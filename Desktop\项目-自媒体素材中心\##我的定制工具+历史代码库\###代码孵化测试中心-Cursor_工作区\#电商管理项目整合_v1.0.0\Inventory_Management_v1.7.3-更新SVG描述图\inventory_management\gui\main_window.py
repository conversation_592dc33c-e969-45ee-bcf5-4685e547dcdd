from PyQt6.QtWidgets import (
    QMainWindow,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QComboBox,
    QLineEdit,
    QTableWidget,
    QMessageBox,
    QAbstractItemView,
    QTableWidgetItem,
    QMenu,
    QFileDialog,
    QInputDialog,
    QProgressDialog,
    QTabWidget,
    QDialog,
    QApplication,
    QFormLayout,
    QCheckBox,
    QStyledItemDelegate,
    QStyle,
    QHeaderView,
)
from PyQt6.QtGui import QIcon, QPixmap, QPainter, QAction
from PyQt6.QtCore import Qt, QTimer, QSize, QSettings, QRect
import logging
import pandas as pd
import os
from datetime import datetime
from database.db_utils import (
    get_connection,
    get_product,
    update_product,
    delete_product,
    parse_product_id,
)
from gui.dialogs.product_dialog import ProductDialog
from gui.dialogs.batch_dialog import BatchDialog
from gui.dialogs.finance_dialog import FinanceDialog
from gui.dialogs.database_dialog import DatabaseDialog
from icons import Icons
from utils.error_handler import ErrorHandler
import win32gui
import win32con
from gui.product_edit_dialog import ProductEditDialog
from PIL import Image
import shutil
from database.image_utils import save_product_image, get_image_path
from .widgets.image_widgets import ImagePathDelegate, PreviewDialog
from .widgets.batch_widgets import BatchTable, BatchToolBar
from gui.widgets.product_widgets import ProductTable, ProductToolBar


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("库存管理系统")

        # 设置窗口标志，支持最大化和最小化
        self.setWindowFlags(
            Qt.WindowType.Window
            | Qt.WindowType.WindowMinimizeButtonHint
            | Qt.WindowType.WindowMaximizeButtonHint
            | Qt.WindowType.WindowCloseButtonHint
        )

        # 允许调整窗口大小
        self.setWindowFlag(Qt.WindowType.WindowMaximizeButtonHint, True)

        self.resize(1720, 1200)

        # 初始化错误处理和日志
        ErrorHandler.init_logger()
        ErrorHandler.log_info("启动库存管理系统")

        # 设置图片存储根目录
        self.images_root = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "..", "images")
        )
        os.makedirs(self.images_root, exist_ok=True)
        os.environ["IMAGES_DIR"] = self.images_root

        # 初始化显示模式
        self.display_mode = "展开显示"

        # 初始化定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.auto_refresh)
        self.refresh_interval = 300  # 默认5分钟
        self.last_refresh_time = datetime.now()

        try:
            # 确保数据库和表已创建
            from database.db_utils import create_tables

            create_tables()

            # 初始化UI
            self.init_ui()

            # 加载数据
            self.load_categories()
            self.load_batches()
            self.load_products()

            # 设置状态栏
            self.setup_status_bar()

            # 启动自动刷新
            self.start_auto_refresh()

            # 关闭控制台窗口
            self.close_console_window()

            ErrorHandler.log_info("系统初始化完成")

        except Exception as e:
            ErrorHandler.handle_error(e, self)

    def init_ui(self):
        """初始化UI"""
        try:
            # 创建中心部件
            self.central_widget = QWidget()
            self.setCentralWidget(self.central_widget)

            # 创建主布局
            layout = QVBoxLayout()
            self.central_widget.setLayout(layout)

            # 创建选项卡部件
            self.tab_widget = QTabWidget()
            layout.addWidget(self.tab_widget)

            # 创建商品和批次选项卡
            self.product_tab = QWidget()
            self.batch_tab = QWidget()
            self.tab_widget.addTab(self.product_tab, "商品管理")
            self.tab_widget.addTab(self.batch_tab, "批次管理")

            # 设置商品管理选项卡
            self.setup_product_tab()

            # 设置批次管理选项卡
            self.setup_batch_tab()

            # 设置菜单栏
            self.setup_menu()

            # 加载样式表
            self.load_stylesheet()

            ErrorHandler.log_info("UI初始化完成")

        except Exception as e:
            ErrorHandler.log_error(f"UI初始化失败: {str(e)}")
            raise

    def setup_toolbar(self):
        """设置工具栏"""
        try:
            # 创建工具栏
            toolbar = self.addToolBar("工具栏")
            toolbar.setMovable(False)  # 禁止移动工具栏
            toolbar.setFloatable(False)  # 禁止浮动工具栏
            toolbar.setIconSize(QSize(24, 24))  # 设置图标大小
            toolbar.setToolButtonStyle(
                Qt.ToolButtonStyle.ToolButtonTextUnderIcon
            )  # 图标下方显示文字

            # 获取图标实例
            icons = Icons.instance()

            # 添加商品
            add_product_action = QAction(
                icons.get_icon("add_product"), "添加商品", self
            )
            add_product_action.setStatusTip("添加新商品")
            add_product_action.triggered.connect(self.show_add_dialog)
            toolbar.addAction(add_product_action)

            # 添加批次
            add_batch_action = QAction(icons.get_icon("add_batch"), "添加批次", self)
            add_batch_action.setStatusTip("添加新批次")
            add_batch_action.triggered.connect(self.show_batch_dialog)
            toolbar.addAction(add_batch_action)

            toolbar.addSeparator()

            # 导入数据
            import_action = QAction(icons.get_icon("import"), "导入数据", self)
            import_action.setStatusTip("导入商品或批次数据")
            import_menu = QMenu(self)
            import_menu.addAction("导入商品数据", lambda: self.import_data("products"))
            import_menu.addAction("导入批次数据", lambda: self.import_data("batches"))
            import_action.setMenu(import_menu)
            toolbar.addAction(import_action)

            # 导出数据
            export_action = QAction(icons.get_icon("export"), "导出数据", self)
            export_action.setStatusTip("导出商品或批次数据")
            export_menu = QMenu(self)
            export_menu.addAction("导出商品数据", lambda: self.export_data("products"))
            export_menu.addAction("导出批次数据", lambda: self.export_data("batches"))
            export_action.setMenu(export_menu)
            toolbar.addAction(export_action)

            toolbar.addSeparator()

            # 数据库管理
            db_action = QAction(icons.get_icon("database"), "数据库管理", self)
            db_action.setStatusTip("管理数据库")
            db_action.triggered.connect(self.show_database_dialog)
            toolbar.addAction(db_action)

            toolbar.addSeparator()

            # 财务统计
            finance_action = QAction(icons.get_icon("finance"), "财务统计", self)
            finance_action.setStatusTip("查看财务统计信息")
            finance_action.triggered.connect(self.show_finance_dialog)
            toolbar.addAction(finance_action)

            # 刷新数据
            refresh_action = QAction(icons.get_icon("refresh"), "刷新数据", self)
            refresh_action.setStatusTip("刷新所有数据")
            refresh_action.triggered.connect(self.refresh_data)
            toolbar.addAction(refresh_action)

        except Exception as e:
            ErrorHandler.log_error(f"工具栏设置失败: {str(e)}")
            raise

    def setup_product_tab(self):
        """设置商品管理选项卡"""
        try:
            layout = QVBoxLayout()
            self.product_tab.setLayout(layout)

            # 创建商品工具栏
            self.product_toolbar = ProductToolBar(self)
            self.product_toolbar.add_clicked.connect(self.show_add_dialog)
            self.product_toolbar.edit_clicked.connect(self.edit_product)
            self.product_toolbar.delete_clicked.connect(self.delete_product)
            self.product_toolbar.image_clicked.connect(self.manage_images)
            self.product_toolbar.search_changed.connect(self.filter_products)
            self.product_toolbar.category_changed.connect(self.filter_products)
            self.product_toolbar.batch_changed.connect(self.filter_products)
            self.product_toolbar.refresh_clicked.connect(self.refresh_data)
            self.product_toolbar.merge_changed.connect(self.on_merge_state_changed)
            self.product_toolbar.thumbnail_changed.connect(
                self.on_thumbnail_state_changed
            )
            layout.addWidget(self.product_toolbar)

            # 创建商品表格
            self.product_table = ProductTable(self)
            self.product_table.product_selected.connect(self.on_product_selected)
            self.product_table.product_double_clicked.connect(
                self.on_product_double_clicked
            )

            # 设置表格列宽和显示模式
            header = self.product_table.horizontalHeader()
            self.product_table.setColumnWidth(0, 120)  # 商品ID
            self.product_table.setColumnWidth(1, 150)  # 商品名称
            self.product_table.setColumnWidth(2, 80)  # 数量
            self.product_table.setColumnWidth(3, 80)  # 分类
            self.product_table.setColumnWidth(4, 100)  # 位置
            self.product_table.setColumnWidth(5, 100)  # 供应商
            self.product_table.setColumnWidth(6, 150)  # 供应商链接
            self.product_table.setColumnWidth(7, 150)  # 采购链接
            self.product_table.setColumnWidth(8, 80)  # 售价
            self.product_table.setColumnWidth(9, 80)  # 采购人
            self.product_table.setColumnWidth(10, 150)  # 销售链接
            self.product_table.setColumnWidth(11, 80)  # 进货价
            self.product_table.setColumnWidth(12, 80)  # 运费
            self.product_table.setColumnWidth(13, 80)  # 其他成本
            self.product_table.setColumnWidth(14, 80)  # 总成本
            self.product_table.setColumnWidth(15, 80)  # 折扣率
            self.product_table.setColumnWidth(16, 80)  # 预估利润
            self.product_table.setColumnWidth(17, 80)  # 采购人员
            self.product_table.setColumnWidth(18, 80)  # 状态
            self.product_table.setColumnWidth(19, 80)  # 单位
            self.product_table.setColumnWidth(20, 200)  # 图片列

            # 设置图片列为可调整大小
            header.setSectionResizeMode(20, QHeaderView.ResizeMode.Interactive)

            # 设置图片代理
            self.image_delegate = ImagePathDelegate(self.product_table)
            self.image_delegate.set_show_thumbnails(
                self.product_toolbar.is_showing_thumbnails()
            )
            self.product_table.setItemDelegateForColumn(20, self.image_delegate)

            layout.addWidget(self.product_table)

            # 加载初始数据
            self.load_categories()
            self.load_batches()
            self.refresh_data()

            ErrorHandler.log_info("商品管理选项卡设置完成")

        except Exception as e:
            ErrorHandler.handle_error(e, self)

    def filter_products(self):
        """过滤商品列表"""
        try:
            search_text = self.product_toolbar.search_edit.text().strip()
            category = self.product_toolbar.category_combo.currentText()
            batch = self.product_toolbar.batch_combo.currentText()

            self.product_table.filter_products(search_text, category, batch)
            self.update_status_info()

        except Exception as e:
            ErrorHandler.handle_error(e, self)

    def load_categories(self):
        """加载商品分类"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT DISTINCT category FROM products WHERE category IS NOT NULL"
                )
                categories = [row[0] for row in cursor.fetchall()]
                self.product_toolbar.update_categories(categories)
        except Exception as e:
            ErrorHandler.handle_error(e, self)

    def load_batches(self):
        """加载批次列表"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT batch_name FROM batches")
                batches = [row[0] for row in cursor.fetchall()]
                self.product_toolbar.update_batches(batches)
        except Exception as e:
            ErrorHandler.handle_error(e, self)

    def refresh_data(self):
        """刷新数据"""
        try:
            # 先加载商品数据
            self.load_products()
            # 更新分类和批次列表
            self.load_categories()
            self.load_batches()
            # 更新状态栏
            self.update_status_info()
            # 更新最后刷新时间
            self.last_refresh_time = datetime.now()
        except Exception as e:
            ErrorHandler.handle_error(e, self)

    def on_product_selected(self, product_id):
        """处理商品选中事件"""
        self.current_product = product_id
        self.update_status_info()

    def on_merge_state_changed(self, is_merged):
        """合并状态改变时的处理"""
        try:
            self.load_products()  # 重新加载商品列表
        except Exception as e:
            ErrorHandler.log_error("切换合并状态失败", e)
            QMessageBox.critical(self, "错误", f"切换合并状态失败: {str(e)}")

    def load_products(self):
        """加载商品列表"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()

                # 获取合并状态
                merge = self.product_toolbar.is_merged()

                # 构建SQL查询
                conditions = []
                params = []

                # 添加过滤条件
                category = self.product_toolbar.category_combo.currentText()
                if category and category != "所有分类":
                    conditions.append("p.category = ?")
                    params.append(category)

                batch = self.product_toolbar.batch_combo.currentText()
                if batch and batch != "所有批次":
                    conditions.append("b.batch_name = ?")
                    params.append(batch)

                # 构建基础SQL
                if merge:
                    # 合并视图的SQL查询
                    base_sql = """
                        SELECT 
                        substr(p.product_id, 1, length(p.product_id) - 6) as base_id,
                        p.name,
                        SUM(p.quantity) as total_quantity,
                        p.category,
                        GROUP_CONCAT(DISTINCT b.batch_name) as batch_names,
                        p.location,
                        p.supplier,
                        p.supplier_link,
                        p.purchase_link,
                        AVG(p.purchase_price) as avg_purchase_price,
                        AVG(p.shipping_cost) as avg_shipping_cost,
                        AVG(p.other_cost) as avg_other_cost,
                        AVG(p.total_cost) as avg_total_cost,
                        p.selling_price,
                        p.discount_rate,
                        SUM(p.total_profit) as total_profit,
                        p.purchaser,
                        p.selling_link,
                        GROUP_CONCAT(DISTINCT p.image_path) as image_paths,
                        GROUP_CONCAT(DISTINCT p.status) as statuses,
                        p.unit
                    FROM products p
                    LEFT JOIN product_batches pb ON p.product_id = pb.product_id
                    LEFT JOIN batches b ON pb.batch_id = b.batch_id
                    """
                else:
                    # 展开视图的SQL查询
                    base_sql = """
                    SELECT 
                        p.product_id,
                        p.name,
                        p.quantity,
                        p.category,
                        GROUP_CONCAT(DISTINCT b.batch_name) as batch_names,
                        p.location,
                        p.supplier,
                        p.supplier_link,
                        p.purchase_link,
                        p.purchase_price,
                        p.shipping_cost,
                        p.other_cost,
                        p.total_cost,
                        p.selling_price,
                        p.discount_rate,
                        p.total_profit,
                        p.purchaser,
                        p.selling_link,
                        p.image_path,
                        p.status,
                        p.unit
                    FROM products p
                    LEFT JOIN product_batches pb ON p.product_id = pb.product_id
                    LEFT JOIN batches b ON pb.batch_id = b.batch_id
                    """

                # 添加条件
                if conditions:
                    base_sql += " WHERE " + " AND ".join(conditions)

                # 添加分组
                if merge:
                    base_sql += (
                        " GROUP BY substr(p.product_id, 1, length(p.product_id) - 6)"
                    )
                else:
                    base_sql += " GROUP BY p.product_id"

                # 添加排序
                base_sql += " ORDER BY p.product_id"

                # 执行查询
                cursor.execute(base_sql, params)
                products = cursor.fetchall()

                # 更新表格
                self.product_table.load_products(products)

        except Exception as e:
            ErrorHandler.log_error(f"加载商品列表失败: {str(e)}")
            raise

    def show_add_dialog(self):
        """显示添加商品对话框"""
        try:
            # 获取当前合并状态
            is_merged = self.product_toolbar.is_merged()
            dialog = ProductDialog(self, product_id=None, is_merged_view=is_merged)
            dialog.product_saved.connect(self.on_product_saved)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()
                self.statusBar().showMessage("商品添加成功")
        except Exception as e:
            ErrorHandler.log_error("显示添加商品对话框失败", e)
            QMessageBox.critical(self, "错误", f"显示添加商品对话框失败: {str(e)}")

    def show_batch_dialog(self):
        """显示批次管理对话框"""
        try:
            dialog = BatchDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()
                self.load_batches()  # 重新加载批次列表
        except Exception as e:
            ErrorHandler.log_error(f"显示批次管理对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"显示批次管理对话框失败: {str(e)}")

    def show_context_menu(self, pos):
        """显示右键菜单"""
        try:
            # 获取选中的行
            selected_rows = set(
                item.row() for item in self.product_table.selectedItems()
            )
            if not selected_rows:
                return

            # 创建菜单
            menu = QMenu(self)

            if len(selected_rows) > 1:
                # 多选模式
                edit_action = QAction(f"批量编辑 ({len(selected_rows)} 个商品)", self)
                edit_action.triggered.connect(
                    lambda: self.edit_multiple_products(selected_rows)
                )
                menu.addAction(edit_action)

                delete_action = QAction(f"批量删除 ({len(selected_rows)} 个商品)", self)
                delete_action.triggered.connect(lambda: self.delete_selected_products())
                menu.addAction(delete_action)
            else:
                # 单选模式
                edit_action = QAction("编辑商品", self)
                edit_action.triggered.connect(self.edit_product)
                menu.addAction(edit_action)

                delete_action = QAction("删除商品", self)
                delete_action.triggered.connect(self.delete_product)
                menu.addAction(delete_action)

            # 添加分隔线
            menu.addSeparator()

            # 图片管理操作
            image_action = QAction("管理图片", self)
            image_action.triggered.connect(self.manage_images)
            menu.addAction(image_action)

            # 显示菜单
            menu.exec(self.product_table.viewport().mapToGlobal(pos))

        except Exception as e:
            logging.exception("显示右键菜单失败")
            QMessageBox.critical(self, "错误", f"显示右键菜单失败: {str(e)}")

    def manage_images(self):
        """管理商品图片"""
        try:
            # 获取选中的商品
            current_row = self.product_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "提示", "请先选择一个商品")
                return

            # 获取商品ID
            product_id = self.product_table.item(current_row, 0).text()

            # 打开图片管理对话框
            from gui.dialogs.image_dialog import ImageDialog

            dialog = ImageDialog(self, product_id)
            dialog.exec()

            # 刷新商品列表
            self.refresh_data()

        except Exception as e:
            logging.exception("打开图片管理失败")
            QMessageBox.critical(self, "错误", f"打开图片管理失败: {str(e)}")

    def edit_multiple_products(self, selected_rows):
        """批量编辑商品"""
        try:
            # 获取选中的商品数据
            products_to_edit = []
            for row in selected_rows:
                product_id = self.product_table.item(row, 0).text()
                product = get_product(product_id)
                if product:
                    products_to_edit.append(product)

            if not products_to_edit:
                QMessageBox.warning(self, "警告", "未找到要编辑的商品")
                return

            # 创建批量编辑对话框
            dialog = ProductEditDialog(self, products_to_edit)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                # 获取更新的数据
                updated_data = dialog.get_updated_data()
                if not updated_data:
                    return

                # 更新所有选中的商品
                success_count = 0
                error_count = 0
                for product in products_to_edit:
                    try:
                        # 更新商品
                        update_product(product["product_id"], updated_data)
                        success_count += 1
                        logging.info(f"更新商品: {product['product_id']}")
                    except Exception as e:
                        error_count += 1
                        logging.error(
                            f"更新商品失败: {product['product_id']}, 错误: {str(e)}"
                        )

                # 刷新表格
                self.refresh_data()

                # 显示更新结果
                message = f"更新完成\n成功: {success_count} 个商品"
                if error_count > 0:
                    message += f"\n失败: {error_count} 个商品"
                QMessageBox.information(self, "更新结果", message)

        except Exception as e:
            logging.exception("批量编辑商品失败")
            QMessageBox.critical(self, "错误", f"批量编辑失败: {str(e)}")

    def edit_product(self):
        """编辑商品"""
        try:
            current_row = self.product_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要编辑的商品")
                return

            product_id = self.product_table.item(current_row, 0).text()

            # 如果是合并视图，获取所有同类商品
            if self.product_toolbar.is_merged():
                type_id = product_id.rsplit("-", 1)[0]  # 获取类型ID（去掉最后的序号）
                with get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        """
                        SELECT * 
                        FROM products 
                        WHERE product_id LIKE ?
                        ORDER BY product_id
                        """,
                        (type_id + "%",),
                    )
                    products = cursor.fetchall()
                    if products:
                        # 转换为字典列表
                        products_to_edit = []
                        for product in products:
                            product_dict = {}
                            for i, col in enumerate(cursor.description):
                                product_dict[col[0]] = product[i]
                            products_to_edit.append(product_dict)

                        # 使用批量编辑对话框
                        dialog = ProductEditDialog(self, products_to_edit)
                        if dialog.exec() == QDialog.DialogCode.Accepted:
                            self.refresh_data()
                    else:
                        QMessageBox.warning(self, "警告", "未找到要编辑的商品")
            else:
                # 单个商品编辑
                dialog = ProductDialog(self, product_id)
                if dialog.exec() == QDialog.DialogCode.Accepted:
                    self.refresh_data()

        except Exception as e:
            logging.exception("编辑商品失败")
            QMessageBox.critical(self, "错误", f"编辑商品失败: {str(e)}")

    def on_product_saved(self, product_id):
        """当商品保存成功时的处理"""
        try:
            self.refresh_data()
            self.statusBar().showMessage(f"商品 {product_id} 保存成功")
        except Exception as e:
            logging.exception("保存商品后刷新失败")
            QMessageBox.critical(self, "错误", f"保存商品后刷新失败: {str(e)}")

    @ErrorHandler.exception_handler
    def delete_product(self, row):
        """删除商品"""
        product_id = self.product_table.item(row, 0).text()
        product_name = self.product_table.item(row, 1).text()

        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除商品 {product_name} 吗？此操作不可恢复！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )

        if reply == QMessageBox.StandardButton.Yes:
            with get_connection() as conn:
                cursor = conn.cursor()
                # 删除商品批次关联
                cursor.execute(
                    "DELETE FROM product_batches WHERE product_id = ?",
                    (product_id,),
                )
                # 删除商品图片关联
                cursor.execute(
                    "DELETE FROM product_images WHERE product_id = ?",
                    (product_id,),
                )
                # 删除商品
                cursor.execute(
                    "DELETE FROM products WHERE product_id = ?",
                    (product_id,),
                )
                conn.commit()

            self.refresh_data()
            self.statusBar().showMessage(f"商品 {product_name} 已删除")
            logging.info(f"删除商品: {product_id} - {product_name}")

    def show_import_export_menu(self):
        """显示导入导出菜单"""
        try:
            menu = QMenu(self)

            # 导入商品
            import_action = QAction("导入商品", self)
            import_action.triggered.connect(self.import_products)
            menu.addAction(import_action)

            # 导出商品
            export_action = QAction("导出商品", self)
            export_action.triggered.connect(self.export_products)
            menu.addAction(export_action)

            # 显示菜单
            menu.exec(
                self.import_export_btn.mapToGlobal(
                    self.import_export_btn.rect().bottomLeft()
                )
            )

        except Exception as e:
            logging.exception("显示导入导出菜单失败")
            QMessageBox.critical(self, "错误", f"显示导入导出菜单失败: {str(e)}")

    def import_products(self, file_path, progress):
        """导入商品数据"""
        try:
            df = pd.read_excel(file_path)
            total_rows = len(df)

            # 检查必要的列是否存在
            required_columns = ["商品ID", "名称", "类别", "状态"]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(
                    f"Excel文件缺少以下必要列: {', '.join(missing_columns)}"
                )

            success_count = 0
            error_count = 0

            with get_connection() as conn:
                cursor = conn.cursor()
                for index, row in df.iterrows():
                    if progress.wasCanceled():
                        break

                    try:
                        # 更新进度
                        progress.setValue(int((index + 1) / total_rows * 100))

                        # 检查商品ID是否已存在
                        cursor.execute(
                            "SELECT 1 FROM products WHERE product_id = ?",
                            (row["商品ID"],),
                        )
                        if cursor.fetchone():
                            # 更新现有商品
                            cursor.execute(
                                """
                                UPDATE products
                                SET name = ?, category = ?, status = ?, 
                                    quantity = ?, location = ?, supplier = ?,
                                    purchase_price = ?, selling_price = ?,
                                    updated_at = CURRENT_TIMESTAMP
                                WHERE product_id = ?
                                """,
                                (
                                    row["名称"],
                                    row["类别"],
                                    row["状态"],
                                    row.get("数量", 0),
                                    row.get("位置", ""),
                                    row.get("供应商", ""),
                                    row.get("进价", 0.0),
                                    row.get("售价", 0.0),
                                    row["商品ID"],
                                ),
                            )
                        else:
                            # 插入新商品
                            cursor.execute(
                                """
                                INSERT INTO products (
                                    product_id, name, category, status,
                                    quantity, location, supplier,
                                    purchase_price, selling_price
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """,
                                (
                                    row["商品ID"],
                                    row["名称"],
                                    row["类别"],
                                    row["状态"],
                                    row.get("数量", 0),
                                    row.get("位置", ""),
                                    row.get("供应商", ""),
                                    row.get("进价", 0.0),
                                    row.get("售价", 0.0),
                                ),
                            )
                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        ErrorHandler.log_error(
                            f"导入商品失败: {str(e)}, 行号: {index + 2}"
                        )

                conn.commit()

            QMessageBox.information(
                self,
                "导入完成",
                f"成功导入 {success_count} 个商品\n失败 {error_count} 个商品",
            )
            self.refresh_data()

        except Exception as e:
            raise Exception(f"导入商品数据失败: {str(e)}")

    def import_batches(self, file_path, progress):
        """导入批次数据"""
        try:
            df = pd.read_excel(file_path)
            total_rows = len(df)

            # 检查必要的列是否存在
            required_columns = ["批次名称", "创建日期", "状态"]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(
                    f"Excel文件缺少以下必要列: {', '.join(missing_columns)}"
                )

            success_count = 0
            error_count = 0

            with get_connection() as conn:
                cursor = conn.cursor()
                for index, row in df.iterrows():
                    if progress.wasCanceled():
                        break

                    try:
                        # 更新进度
                        progress.setValue(int((index + 1) / total_rows * 100))

                        # 检查批次是否已存在
                        cursor.execute(
                            "SELECT batch_id FROM batches WHERE batch_name = ?",
                            (row["批次名称"],),
                        )
                        result = cursor.fetchone()

                        if result:
                            # 更新现有批次
                            cursor.execute(
                                """
                                UPDATE batches
                                SET status = ?, description = ?, updated_at = CURRENT_TIMESTAMP
                                WHERE batch_id = ?
                                """,
                                (row["状态"], row.get("描述", ""), result[0]),
                            )
                        else:
                            # 插入新批次
                            cursor.execute(
                                """
                                INSERT INTO batches (
                                    batch_name, created_at, status, description
                                ) VALUES (?, ?, ?, ?)
                                """,
                                (
                                    row["批次名称"],
                                    row["创建日期"],
                                    row["状态"],
                                    row.get("描述", ""),
                                ),
                            )
                        success_count += 1
                    except Exception as e:
                        error_count += 1
                        ErrorHandler.log_error(
                            f"导入批次失败: {str(e)}, 行号: {index + 2}"
                        )

                conn.commit()

            QMessageBox.information(
                self,
                "导入完成",
                f"成功导入 {success_count} 个批次\n失败 {error_count} 个批次",
            )
            self.refresh_data()

        except Exception as e:
            raise Exception(f"导入批次数据失败: {str(e)}")

    def export_products(self, file_path, progress):
        """导出商品数据"""
        try:
            # 获取所有可见的商品数据
            data = []
            headers = [
                self.product_table.horizontalHeaderItem(i).text()
                for i in range(self.product_table.columnCount())
            ]

            total_rows = self.product_table.rowCount()
            for row in range(total_rows):
                if progress.wasCanceled():
                    break

                # 更新进度
                progress.setValue(int((row + 1) / total_rows * 100))

                if not self.product_table.isRowHidden(row):
                    row_data = []
                    for col in range(self.product_table.columnCount()):
                        item = self.product_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

            # 创建DataFrame并导出到Excel
            df = pd.DataFrame(data, columns=headers)
            df.to_excel(file_path, index=False)

            QMessageBox.information(self, "导出完成", f"已成功导出 {len(data)} 个商品")

        except Exception as e:
            raise Exception(f"导出商品数据失败: {str(e)}")

    def export_batches(self, file_path, progress):
        """导出批次数据"""
        try:
            # 获取所有可见的批次数据
            data = []
            headers = [
                self.batch_table.horizontalHeaderItem(i).text()
                for i in range(self.batch_table.columnCount())
            ]

            total_rows = self.batch_table.rowCount()
            for row in range(total_rows):
                if progress.wasCanceled():
                    break

                # 更新进度
                progress.setValue(int((row + 1) / total_rows * 100))

                if not self.batch_table.isRowHidden(row):
                    row_data = []
                    for col in range(self.batch_table.columnCount()):
                        item = self.batch_table.item(row, col)
                        row_data.append(item.text() if item else "")
                    data.append(row_data)

            # 创建DataFrame并导出到Excel
            df = pd.DataFrame(data, columns=headers)
            df.to_excel(file_path, index=False)

            QMessageBox.information(self, "导出完成", f"已成功导出 {len(data)} 个批次")

        except Exception as e:
            raise Exception(f"导出批次数据失败: {str(e)}")

    def close_console_window(self):
        """关闭控制台窗口"""
        try:
            # 查找控制台窗口
            def callback(hwnd, pid):
                if win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    if "python" in title.lower():
                        win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                return True

            win32gui.EnumWindows(callback, None)
        except Exception as e:
            logging.warning(f"关闭控制台窗口失败: {str(e)}")

    def closeEvent(self, event):
        """关闭窗口时的处理"""
        try:
            # 停止定时器
            self.refresh_timer.stop()

            # 关闭控制台窗口
            self.close_console_window()

            ErrorHandler.log_info("系统正常关闭")
            event.accept()
        except Exception as e:
            ErrorHandler.handle_error(e, self, show_message=False)
            event.accept()

    def setup_menu(self):
        """设置菜单栏"""
        # 创建菜单栏
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        # 数据库管理
        db_action = QAction("数据库管理", self)
        db_action.triggered.connect(self.show_database_dialog)
        file_menu.addAction(db_action)

        file_menu.addSeparator()

        # 设置子菜单
        settings_menu = QMenu("设置", self)
        file_menu.addMenu(settings_menu)

        # 自动刷新设置
        self.auto_refresh_action = QAction("自动刷新", self)
        self.auto_refresh_action.setCheckable(True)
        self.auto_refresh_action.setChecked(True)
        self.auto_refresh_action.triggered.connect(self.toggle_auto_refresh)
        settings_menu.addAction(self.auto_refresh_action)

        # 刷新间隔设置
        refresh_interval_action = QAction("刷新间隔...", self)
        refresh_interval_action.triggered.connect(self.set_refresh_interval)
        settings_menu.addAction(refresh_interval_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 数据菜单
        data_menu = menubar.addMenu("数据")

        # 导出数据
        export_menu = QMenu("导出数据", self)
        data_menu.addMenu(export_menu)

        # 导出商品数据
        export_products_action = QAction("导出商品数据", self)
        export_products_action.triggered.connect(lambda: self.export_data("products"))
        export_menu.addAction(export_products_action)

        # 导出批次数据
        export_batches_action = QAction("导出批次数据", self)
        export_batches_action.triggered.connect(lambda: self.export_data("batches"))
        export_menu.addAction(export_batches_action)

        # 导入数据
        import_menu = QMenu("导入数据", self)
        data_menu.addMenu(import_menu)

        # 导入商品数据
        import_products_action = QAction("导入商品数据", self)
        import_products_action.triggered.connect(lambda: self.import_data("products"))
        import_menu.addAction(import_products_action)

        # 导入批次数据
        import_batches_action = QAction("导入批次数据", self)
        import_batches_action.triggered.connect(lambda: self.import_data("batches"))
        import_menu.addAction(import_batches_action)

        # 统计菜单
        stats_menu = menubar.addMenu("统计")

        # 财务统计
        finance_action = QAction("财务统计", self)
        finance_action.triggered.connect(self.show_finance_dialog)
        stats_menu.addAction(finance_action)

    def show_database_dialog(self):
        """显示数据库管理对话框"""
        try:
            dialog = DatabaseDialog(self)
            dialog.database_changed.connect(self.on_database_changed)
            dialog.exec()
        except Exception as e:
            logging.exception("打开数据库管理对话框失败")
            QMessageBox.critical(self, "错误", f"打开数据库管理对话框失败: {str(e)}")

    def on_database_changed(self):
        """数据库切换后的处理"""
        try:
            # 刷新所有数据
            self.refresh_data()
            # 更新状态栏信息
            self.update_status_info()
            # 重新加载分类和批次
            self.load_categories()
            self.load_batches()
        except Exception as e:
            logging.exception("数据库切换后更新失败")
            QMessageBox.critical(self, "错误", f"数据库切换后更新失败: {str(e)}")

    def show_finance_dialog(self):
        """显示财务统计对话框"""
        try:
            dialog = FinanceDialog(self)
            dialog.exec()
        except Exception as e:
            logging.exception("打开财务统计对话框失败")
            QMessageBox.critical(self, "错误", f"打开财务统计对话框失败: {str(e)}")

    def setup_status_bar(self):
        """设置状态栏"""
        # 如果标签已经存在，直接返回
        if hasattr(self, "db_label"):
            return

        # 创建状态栏标签
        self.db_label = QLabel()
        self.count_label = QLabel()
        self.time_label = QLabel()

        # 添加到状态栏
        status_bar = self.statusBar()
        status_bar.addWidget(self.db_label)  # 左侧显示数据库状态
        status_bar.addPermanentWidget(self.count_label)  # 右侧显示数据统计
        status_bar.addPermanentWidget(self.time_label)  # 最右侧显示时间

        # 初始化标签内容
        self.update_status_info()

    def update_status_info(self):
        """更新状态栏信息"""
        try:
            # 如果状态栏标签还未创建，直接返回
            if not hasattr(self, "db_label"):
                return

            # 更新数据库状态
            self.db_label.setText("数据库已连接")

            # 更新商品数量
            if hasattr(self, "product_table"):
                total_count = self.product_table.rowCount()
                visible_count = sum(
                    1
                    for row in range(total_count)
                    if not self.product_table.isRowHidden(row)
                )
                self.count_label.setText(f"显示: {visible_count}/{total_count}")

            # 更新时间信息
            current_time = datetime.now().strftime("%H:%M:%S")
            self.time_label.setText(f"上次刷新: {current_time}")

        except Exception as e:
            ErrorHandler.log_error(f"更新状态栏信息失败: {str(e)}")

    def toggle_auto_refresh(self, checked):
        """切换自动刷新状态"""
        if checked:
            self.start_auto_refresh()
            self.statusBar().showMessage("自动刷新已开启")
        else:
            self.refresh_timer.stop()
            self.statusBar().showMessage("自动刷新已关闭")

    def start_auto_refresh(self):
        """启动自动刷新"""
        if self.auto_refresh_action.isChecked():
            self.refresh_timer.start(self.refresh_interval * 1000)  # 转换为毫秒

    def set_refresh_interval(self):
        """设置刷新间隔"""
        try:
            current = self.refresh_interval // 60  # 转换为分钟
            interval, ok = QInputDialog.getInt(
                self, "设置刷新间隔", "请输入刷新间隔（分钟）:", current, 1, 60, 1
            )
            if ok:
                self.refresh_interval = interval * 60  # 转换为秒
                if self.auto_refresh_action.isChecked():
                    self.start_auto_refresh()  # 重启定时器
                self.statusBar().showMessage(f"刷新间隔已设置为 {interval} 分钟")
        except Exception as e:
            logging.exception("设置刷新间隔失败")
            QMessageBox.critical(self, "错误", f"设置刷新间隔失败: {str(e)}")

    def auto_refresh(self):
        """自动刷新处理"""
        try:
            # 检查是否有未保存的更改
            if self.has_unsaved_changes():
                return  # 如果有未保存的更改，跳过本次刷新

            # 执行刷新
            self.refresh_data()

            # 更新最后刷新时间
            self.last_refresh_time = datetime.now()

        except Exception as e:
            logging.exception("自动刷新失败")
            self.statusBar().showMessage(f"自动刷新失败: {str(e)}")

    def has_unsaved_changes(self):
        """检查是否有未保存的更改"""
        # TODO: 实现未保存更改检查
        return False

    def delete_selected_products(self):
        """删除选中的商品"""
        try:
            # 获取所有选中的行
            selected_rows = set(
                item.row() for item in self.product_table.selectedItems()
            )
            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要删除的商品")
                return

            # 获取所有选中商品的ID和名称
            products_to_delete = []
            for row in selected_rows:
                product_id = self.product_table.item(row, 0).text()
                name = self.product_table.item(row, 1).text()
                products_to_delete.append((product_id, name))

            # 构建确认消息
            if len(products_to_delete) == 1:
                message = f"确定要删除商品 {products_to_delete[0][1]} (ID: {products_to_delete[0][0]}) 吗？"
            else:
                message = f"确定要删除以下 {len(products_to_delete)} 个商品吗？\n\n"
                # 最多显示前5个商品
                for i, (pid, name) in enumerate(products_to_delete[:5]):
                    message += f"{i+1}. {name} (ID: {pid})\n"
                if len(products_to_delete) > 5:
                    message += f"... 等共 {len(products_to_delete)} 个商品"

            reply = QMessageBox.question(
                self,
                "确认删除",
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                for product_id, name in products_to_delete:
                    # 删除商品
                    delete_product(product_id)
                    logging.info(f"删除商品: {product_id} - {name}")

                # 刷新表格
                self.refresh_data()
                QMessageBox.information(
                    self, "成功", f"已删除 {len(products_to_delete)} 个商品"
                )

        except Exception as e:
            logging.exception("删除商品失败")
            QMessageBox.critical(self, "错误", f"删除商品失败: {str(e)}")

    def setup_batch_tab(self):
        """设置批次管理选项卡"""
        try:
            layout = QVBoxLayout()
            self.batch_tab.setLayout(layout)

            # 创建工具栏
            self.batch_toolbar = BatchToolBar()
            self.batch_toolbar.add_batch.connect(self.show_batch_dialog)
            self.batch_toolbar.edit_batch.connect(self.edit_batch)
            self.batch_toolbar.delete_batch.connect(self.delete_batch)
            self.batch_toolbar.refresh_data.connect(self.refresh_data)
            self.batch_toolbar.search_text_changed.connect(self.filter_batches)
            layout.addWidget(self.batch_toolbar)

            # 创建批次表格
            self.batch_table = BatchTable()
            self.batch_table.batch_selected.connect(self.on_batch_selected)
            self.batch_table.batch_edited.connect(self.edit_batch)
            self.batch_table.batch_deleted.connect(self.delete_batch)
            layout.addWidget(self.batch_table)

            ErrorHandler.log_info("批次管理选项卡设置完成")
        except Exception as e:
            ErrorHandler.log_error(f"批次管理选项卡设置失败: {str(e)}")
            raise

    def edit_batch(self):
        """编辑选中的批次"""
        try:
            current_row = self.batch_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要编辑的批次")
                return

            batch_id = self.batch_table.item(current_row, 0).text()
            dialog = BatchDialog(self, batch_id=batch_id)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()
                self.load_batches()
        except Exception as e:
            ErrorHandler.log_error(f"编辑批次失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"编辑批次失败: {str(e)}")

    def delete_batch(self):
        """删除选中的批次"""
        try:
            current_row = self.batch_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请先选择要删除的批次")
                return

            batch_id = self.batch_table.item(current_row, 0).text()
            batch_name = self.batch_table.item(current_row, 1).text()

            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除批次 {batch_name} 吗？\n此操作不可恢复！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                with get_connection() as conn:
                    cursor = conn.cursor()
                    # 首先删除批次关联
                    cursor.execute(
                        "DELETE FROM product_batches WHERE batch_id = ?", (batch_id,)
                    )
                    # 然后删除批次
                    cursor.execute(
                        "DELETE FROM batches WHERE batch_id = ?", (batch_id,)
                    )
                    conn.commit()

                self.refresh_data()
                self.load_batches()
                QMessageBox.information(self, "成功", f"批次 {batch_name} 已删除")
        except Exception as e:
            ErrorHandler.log_error(f"删除批次失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除批次失败: {str(e)}")

    def filter_batches(self, text):
        """过滤批次列表"""
        try:
            self.batch_table.filter_batches(text)
        except Exception as e:
            logging.exception("过滤批次失败")
            QMessageBox.critical(self, "错误", f"过滤批次失败: {str(e)}")

    def on_batch_selected(self, batch_name):
        """批次选中处理"""
        try:
            # 切换到商品标签页
            self.tab_widget.setCurrentIndex(0)

            # 在批次过滤器中选择当前批次
            index = self.batch_filter.findText(batch_name)
            if index >= 0:
                self.batch_filter.setCurrentIndex(index)
                self.filter_products()
        except Exception as e:
            ErrorHandler.log_error(f"查看批次相关商品失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"查看批次相关商品失败: {str(e)}")

    def toggle_merge_display(self):
        """切换合并显示状态"""
        try:
            self.display_mode = (
                "合并显示" if self.display_mode == "展开显示" else "展开显示"
            )
            self.merge_button.setText(f"切换到{self.display_mode}")
            self.load_products()
        except Exception as e:
            logging.exception("切换合并状态失败")
            QMessageBox.critical(self, "错误", f"切换合并状态失败: {str(e)}")

    def get_selected_product(self):
        """获取选中的商品信息"""
        try:
            current_row = self.product_table.currentRow()
            if current_row < 0:
                return None

            # 获取商品ID
            product_id = self.product_table.item(current_row, 0).text()
            if not product_id:
                return None

            # 如果是合并显示模式，需要获取该类型的所有商品
            if self.product_toolbar.is_merged():
                try:
                    # 解析商品ID获取分类和序号
                    category, type_num, _ = parse_product_id(product_id)
                    if category is None or type_num is None:
                        return None

                    # 查询该类型的所有商品
                    with get_connection() as conn:
                        cursor = conn.cursor()
                        cursor.execute(
                            """
                            SELECT *
                            FROM products
                            WHERE product_id LIKE '#' || ? || '-#' || ? || '-%'
                            ORDER BY product_id
                            """,
                            (category, type_num),
                        )
                        products = cursor.fetchall()
                        return products[0] if products else None
                except ValueError:
                    return None
            else:
                # 展开显示模式，直接查询单个商品
                with get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        """
                        SELECT *
                        FROM products
                        WHERE product_id = ?
                        """,
                        (product_id,),
                    )
                    return cursor.fetchone()

        except Exception as e:
            logging.exception("获取选中商品失败")
            QMessageBox.critical(self, "错误", f"获取选中商品失败: {str(e)}")
            return None

    def on_product_double_clicked(self, row, column):
        """处理商品表格的双击事件"""
        try:
            product_id = self.product_table.item(row, 0).text()
            dialog = ProductDialog(
                self,
                product_id=product_id,
                is_merged_view=self.product_toolbar.is_merged(),
            )
            dialog.product_saved.connect(self.on_product_saved)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.refresh_data()
                self.statusBar().showMessage("商品更新成功")
        except Exception as e:
            ErrorHandler.log_error("打开商品编辑对话框失败", e)
            QMessageBox.critical(self, "错误", f"打开商品编辑对话框失败: {str(e)}")

    def load_stylesheet(self):
        """加载样式表"""
        try:
            # 获取样式表文件路径
            qss_file = os.path.join(
                os.path.dirname(__file__), "..", "resources", "style.qss"
            )

            # 检查文件是否存在
            if not os.path.exists(qss_file):
                ErrorHandler.log_warning("样式表文件不存在")
                return

            # 读取并应用样式表
            with open(qss_file, "r", encoding="utf-8") as f:
                style = f.read()
                self.setStyleSheet(style)
                ErrorHandler.log_info("QSS file successfully loaded.")

        except Exception as e:
            ErrorHandler.log_error(f"加载样式表失败: {str(e)}")
            # 不抛出异常，使用默认样式继续运行

    def import_data(self, data_type):
        """导入数据"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Excel文件",
                "",
                "Excel Files (*.xlsx *.xls);;All Files (*)",
            )
            if not file_path:
                return

            # 创建进度对话框
            progress = QProgressDialog("正在导入数据...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            try:
                if data_type == "products":
                    self.import_products(file_path, progress)
                elif data_type == "batches":
                    self.import_batches(file_path, progress)
            finally:
                progress.close()

        except Exception as e:
            ErrorHandler.log_error(f"导入数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导入数据失败: {str(e)}")

    def export_data(self, data_type):
        """导出数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "保存Excel文件",
                f"{data_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx);;All Files (*)",
            )
            if not file_path:
                return

            # 创建进度对话框
            progress = QProgressDialog("正在导出数据...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModality.WindowModal)
            progress.show()

            try:
                if data_type == "products":
                    self.export_products(file_path, progress)
                elif data_type == "batches":
                    self.export_batches(file_path, progress)
            finally:
                progress.close()

        except Exception as e:
            ErrorHandler.log_error(f"导出数据失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出数据失败: {str(e)}")

    def on_thumbnail_state_changed(self, show_thumbnails):
        """处理缩略图显示状态变化"""
        try:
            self.image_delegate.set_show_thumbnails(show_thumbnails)
            # 调整表格行高
            self.product_table.adjust_row_heights(show_thumbnails)
            # 刷新表格显示
            self.product_table.viewport().update()
        except Exception as e:
            ErrorHandler.handle_error(e, self)
