# 商品编辑对话框模块 (product_edit_dialog.py)

## 功能概述
`product_edit_dialog.py` 实现了商品编辑对话框,提供商品信息的编辑、图片管理和批次关联等功能。该模块基于 PyQt5 开发,整合了商品表单和图片管理功能。

## 类定义

### ProductEditDialog 类
```python
class ProductEditDialog(QDialog):
    """商品编辑对话框类,继承自 QDialog"""
```

#### 初始化
```python
def __init__(self, parent=None, product_id=None):
    """
    初始化商品编辑对话框
    :param parent: 父窗口
    :param product_id: 商品ID,为None时表示新建商品
    """
    super().__init__(parent)
    self.product_id = product_id
    self.setup_ui()
    self.setup_connections()
    if product_id:
        self.load_product_data()
```

## 界面组件

### 1. 基本布局
```python
def setup_ui(self):
    """设置界面布局"""
    self.setWindowTitle("编辑商品" if self.product_id else "新建商品")
    self.setMinimumWidth(600)
    
    layout = QVBoxLayout()
    
    # 添加商品表单
    self.product_form = ProductForm(self)
    layout.addWidget(self.product_form)
    
    # 添加图片管理区域
    self.setup_image_area()
    layout.addWidget(self.image_group)
    
    # 添加批次关联区域
    self.setup_batch_area()
    layout.addWidget(self.batch_group)
    
    # 添加按钮区域
    self.setup_buttons()
    layout.addLayout(self.button_layout)
    
    self.setLayout(layout)
```

### 2. 图片管理区域
```python
def setup_image_area(self):
    """设置图片管理区域"""
    self.image_group = QGroupBox("商品图片")
    layout = QHBoxLayout()
    
    # 图片列表
    self.image_list = ImageList()
    layout.addWidget(self.image_list)
    
    # 图片操作按钮
    button_layout = QVBoxLayout()
    
    self.add_image_btn = QPushButton("添加图片")
    self.add_image_btn.setIcon(QIcon(":/icons/add_image.png"))
    button_layout.addWidget(self.add_image_btn)
    
    self.delete_image_btn = QPushButton("删除图片")
    self.delete_image_btn.setIcon(QIcon(":/icons/delete_image.png"))
    button_layout.addWidget(self.delete_image_btn)
    
    self.set_primary_btn = QPushButton("设为主图")
    self.set_primary_btn.setIcon(QIcon(":/icons/primary_image.png"))
    button_layout.addWidget(self.set_primary_btn)
    
    layout.addLayout(button_layout)
    self.image_group.setLayout(layout)
```

### 3. 批次关联区域
```python
def setup_batch_area(self):
    """设置批次关联区域"""
    self.batch_group = QGroupBox("关联批次")
    layout = QVBoxLayout()
    
    # 批次列表
    self.batch_list = QListWidget()
    layout.addWidget(self.batch_list)
    
    # 批次操作按钮
    button_layout = QHBoxLayout()
    
    self.add_batch_btn = QPushButton("添加批次")
    self.add_batch_btn.setIcon(QIcon(":/icons/add_batch.png"))
    button_layout.addWidget(self.add_batch_btn)
    
    self.remove_batch_btn = QPushButton("移除批次")
    self.remove_batch_btn.setIcon(QIcon(":/icons/remove_batch.png"))
    button_layout.addWidget(self.remove_batch_btn)
    
    layout.addLayout(button_layout)
    self.batch_group.setLayout(layout)
```

## 数据处理

### 1. 商品数据加载
```python
def load_product_data(self):
    """加载商品数据"""
    try:
        with DatabaseManager() as db:
            # 查询商品基本信息
            product = db.get_product(self.product_id)
            if not product:
                raise ValueError(f"商品不存在: {self.product_id}")
                
            # 设置表单数据
            self.product_form.set_form_data(product)
            
            # 加载商品图片
            images = db.get_product_images(self.product_id)
            self.image_list.load_images(images)
            
            # 加载关联批次
            batches = db.get_product_batches(self.product_id)
            self.load_batch_list(batches)
            
    except Exception as e:
        ErrorHandler.show_error(self, "数据加载失败", str(e))
```

### 2. 数据保存
```python
def save_product_data(self):
    """保存商品数据"""
    try:
        # 验证表单数据
        if not self.product_form.validate_data():
            return False
            
        # 获取表单数据
        data = self.product_form.get_form_data()
        
        with DatabaseManager() as db:
            if self.product_id:
                # 更新商品
                db.update_product(self.product_id, data)
            else:
                # 创建商品
                self.product_id = db.add_product(data)
                
            # 保存图片关联
            self.save_product_images()
            
            # 保存批次关联
            self.save_product_batches()
            
        return True
        
    except Exception as e:
        ErrorHandler.show_error(self, "保存失败", str(e))
        return False
```

### 3. 图片管理
```python
def add_product_image(self):
    """添加商品图片"""
    try:
        # 选择图片文件
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "选择图片",
            "",
            "图片文件 (*.jpg *.jpeg *.png *.gif);;所有文件 (*.*)"
        )
        
        if not file_paths:
            return
            
        # 添加图片到列表
        for path in file_paths:
            self.image_list.add_image(path)
            
    except Exception as e:
        ErrorHandler.show_error(self, "添加图片失败", str(e))

def save_product_images(self):
    """保存商品图片"""
    try:
        with DatabaseManager() as db:
            # 获取图片列表数据
            images = self.image_list.get_image_data()
            
            # 保存到数据库
            db.update_product_images(self.product_id, images)
            
    except Exception as e:
        ErrorHandler.show_error(self, "图片保存失败", str(e))
```

## 信号和槽

### 1. 按钮信号
```python
def setup_connections(self):
    """设置信号连接"""
    # 图片操作信号
    self.add_image_btn.clicked.connect(self.add_product_image)
    self.delete_image_btn.clicked.connect(self.image_list.delete_selected)
    self.set_primary_btn.clicked.connect(self.image_list.set_primary_image)
    
    # 批次操作信号
    self.add_batch_btn.clicked.connect(self.add_product_batch)
    self.remove_batch_btn.clicked.connect(self.remove_product_batch)
    
    # 确认和取消按钮
    self.accept_btn.clicked.connect(self.accept_dialog)
    self.cancel_btn.clicked.connect(self.reject)
```

### 2. 数据变更信号
```python
# 自定义信号
product_saved = pyqtSignal(int)  # 商品保存信号,参数为商品ID
```

## 依赖关系

### 1. PyQt5 组件
- QDialog
- QVBoxLayout
- QHBoxLayout
- QGroupBox
- QPushButton
- QListWidget
- QFileDialog
- QIcon

### 2. 自定义组件
- ProductForm
- ImageList
- ErrorHandler
- DatabaseManager

## 使用示例
```python
# 创建新商品
dialog = ProductEditDialog(parent_window)
if dialog.exec_() == QDialog.Accepted:
    product_id = dialog.product_id
    # 处理新建商品...

# 编辑商品
dialog = ProductEditDialog(parent_window, product_id=1)
if dialog.exec_() == QDialog.Accepted:
    # 处理编辑完成...
```

## 注意事项
1. 数据验证的完整性
2. 图片上传的限制
3. 批次关联的合理性
4. 数据保存的事务性
5. 用户操作的友好提示 