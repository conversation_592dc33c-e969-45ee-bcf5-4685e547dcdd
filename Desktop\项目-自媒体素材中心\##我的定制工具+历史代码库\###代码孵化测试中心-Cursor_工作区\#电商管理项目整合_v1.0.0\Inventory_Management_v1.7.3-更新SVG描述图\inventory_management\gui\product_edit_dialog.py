from PyQt6.QtWidgets import (
    QDialog,
    QVBoxLayout,
    QHBoxLayout,
    QFormLayout,
    QLabel,
    QLineEdit,
    QPushButton,
    QComboBox,
    QCheckBox,
    QMessageBox,
    QScrollArea,
    QGroupBox,
    QListWidget,
    QListWidgetItem,
    QWidget,
    QDoubleSpinBox,
    QSpinBox,
    QProgressDialog,
    QFileDialog,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap
from database.db_utils import get_connection, get_categories
from database.image_utils import (
    get_product_image_dir,
    get_product_images,
    validate_image,
    save_product_image,
)
import logging
import os
import uuid
from PIL import Image
import shutil


class ProductEditDialog(QDialog):
    """商品编辑对话框"""

    # 信号定义
    product_saved = pyqtSignal(dict)  # 商品保存信号

    def __init__(self, parent=None, product_data=None):
        super().__init__(parent)
        self.product_data = product_data or {}
        self.setWindowTitle("编辑商品")
        self.setMinimumWidth(400)
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 基本信息
        form_layout = QVBoxLayout()

        # 商品名称
        name_layout = QHBoxLayout()
        name_label = QLabel("商品名称:")
        self.name_edit = QLineEdit()
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_edit)
        form_layout.addLayout(name_layout)

        # 商品编码
        code_layout = QHBoxLayout()
        code_label = QLabel("商品编码:")
        self.code_edit = QLineEdit()
        code_layout.addWidget(code_label)
        code_layout.addWidget(self.code_edit)
        form_layout.addLayout(code_layout)

        # 商品类别
        category_layout = QHBoxLayout()
        category_label = QLabel("商品类别:")
        self.category_combo = QComboBox()
        self.category_combo.addItems(["水果", "蔬菜", "肉类", "海鲜", "其他"])
        self.category_combo.setEditable(True)
        category_layout.addWidget(category_label)
        category_layout.addWidget(self.category_combo)
        form_layout.addLayout(category_layout)

        # 数量
        quantity_layout = QHBoxLayout()
        quantity_label = QLabel("数量:")
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setRange(0, 999999)
        quantity_layout.addWidget(quantity_label)
        quantity_layout.addWidget(self.quantity_spin)
        form_layout.addLayout(quantity_layout)

        # 单价
        price_layout = QHBoxLayout()
        price_label = QLabel("单价:")
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setRange(0, 999999.99)
        self.price_spin.setDecimals(2)
        price_layout.addWidget(price_label)
        price_layout.addWidget(self.price_spin)
        form_layout.addLayout(price_layout)

        # 图片
        image_layout = QHBoxLayout()
        image_label = QLabel("图片:")
        self.image_edit = QLineEdit()
        self.image_button = QPushButton("选择...")
        self.image_button.clicked.connect(self.select_image)
        image_layout.addWidget(image_label)
        image_layout.addWidget(self.image_edit)
        image_layout.addWidget(self.image_button)
        form_layout.addLayout(image_layout)

        # 添加表单布局
        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("保存")
        self.cancel_button = QPushButton("取消")
        self.save_button.clicked.connect(self.save_product)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)

        self.setLayout(layout)

    def load_data(self):
        """加载商品数据"""
        if self.product_data:
            self.name_edit.setText(self.product_data.get("name", ""))
            self.code_edit.setText(self.product_data.get("code", ""))
            self.category_combo.setCurrentText(self.product_data.get("category", ""))
            self.quantity_spin.setValue(self.product_data.get("quantity", 0))
            self.price_spin.setValue(self.product_data.get("price", 0.0))
            self.image_edit.setText(self.product_data.get("image_path", ""))

    def select_image(self):
        """选择图片"""
        file_name, _ = QFileDialog.getOpenFileName(
            self, "选择图片", "", "图片文件 (*.jpg *.jpeg *.png *.bmp)"
        )
        if file_name:
            try:
                # 验证图片
                validate_image(file_name)
                self.image_edit.setText(file_name)
            except Exception as e:
                QMessageBox.warning(self, "警告", f"图片无效: {str(e)}")

    def save_product(self):
        """保存商品"""
        try:
            # 验证数据
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "警告", "商品名称不能为空")
                return

            code = self.code_edit.text().strip()
            if not code:
                QMessageBox.warning(self, "警告", "商品编码不能为空")
                return

            # 收集数据
            product_data = {
                "name": name,
                "code": code,
                "category": self.category_combo.currentText(),
                "quantity": self.quantity_spin.value(),
                "price": self.price_spin.value(),
                "image_path": self.image_edit.text(),
            }

            # 处理图片
            if product_data["image_path"]:
                try:
                    # 保存图片到系统目录
                    product_data["image_path"] = save_product_image(
                        product_data["image_path"], product_data["code"]
                    )
                except Exception as e:
                    QMessageBox.warning(self, "警告", f"保存图片失败: {str(e)}")
                    return

            # 发送信号
            self.product_saved.emit(product_data)
            self.accept()

        except Exception as e:
            logging.exception("保存商品失败")
            QMessageBox.critical(self, "错误", f"保存商品失败: {str(e)}")

    def closeEvent(self, event):
        """关闭事件处理"""
        event.accept()
