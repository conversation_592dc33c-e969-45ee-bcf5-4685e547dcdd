# 商品表单模块 (product_form.py)

## 功能概述
`product_form.py` 实现了商品信息的表单界面,提供了商品信息的输入、验证和数据处理功能。该模块基于 PyQt5 的表单控件开发,支持完整的数据验证和实时计算功能。

## 类定义

### ProductForm 类
```python
class ProductForm(QWidget):
    """商品表单类,继承自 QWidget"""
```

#### 初始化
```python
def __init__(self, parent=None):
    """
    初始化商品表单
    :param parent: 父窗口
    """
    super().__init__(parent)
    self.setup_ui()
    self.setup_validators()
    self.setup_connections()
```

## 界面组件

### 1. 基本信息区域
```python
def setup_basic_info(self):
    """设置基本信息区域"""
    self.basic_group = QGroupBox("基本信息")
    layout = QFormLayout()
    
    # 商品名称
    self.name_edit = QLineEdit()
    self.name_edit.setPlaceholderText("请输入商品名称")
    layout.addRow("商品名称:", self.name_edit)
    
    # 商品类别
    self.category_combo = QComboBox()
    self.category_combo.setEditable(True)
    self.category_combo.addItems(self.get_categories())
    layout.addRow("商品类别:", self.category_combo)
    
    # 商品状态
    self.status_combo = QComboBox()
    self.status_combo.addItems(["在库", "在售", "已售", "缺货"])
    layout.addRow("商品状态:", self.status_combo)
    
    # 商品数量
    self.quantity_spin = QSpinBox()
    self.quantity_spin.setRange(0, 99999)
    layout.addRow("商品数量:", self.quantity_spin)
    
    # 商品单位
    self.unit_edit = QLineEdit()
    self.unit_edit.setPlaceholderText("件/个/套等")
    layout.addRow("商品单位:", self.unit_edit)
    
    self.basic_group.setLayout(layout)
```

### 2. 价格信息区域
```python
def setup_price_info(self):
    """设置价格信息区域"""
    self.price_group = QGroupBox("价格信息")
    layout = QFormLayout()
    
    # 采购价
    self.purchase_price_spin = QDoubleSpinBox()
    self.purchase_price_spin.setRange(0, 999999.99)
    self.purchase_price_spin.setDecimals(2)
    self.purchase_price_spin.setPrefix("¥")
    layout.addRow("采购价:", self.purchase_price_spin)
    
    # 运费
    self.shipping_cost_spin = QDoubleSpinBox()
    self.shipping_cost_spin.setRange(0, 9999.99)
    self.shipping_cost_spin.setDecimals(2)
    self.shipping_cost_spin.setPrefix("¥")
    layout.addRow("运费:", self.shipping_cost_spin)
    
    # 其他成本
    self.other_cost_spin = QDoubleSpinBox()
    self.other_cost_spin.setRange(0, 9999.99)
    self.other_cost_spin.setDecimals(2)
    self.other_cost_spin.setPrefix("¥")
    layout.addRow("其他成本:", self.other_cost_spin)
    
    # 售价
    self.selling_price_spin = QDoubleSpinBox()
    self.selling_price_spin.setRange(0, 999999.99)
    self.selling_price_spin.setDecimals(2)
    self.selling_price_spin.setPrefix("¥")
    layout.addRow("售价:", self.selling_price_spin)
    
    # 折扣率
    self.discount_spin = QSpinBox()
    self.discount_spin.setRange(0, 100)
    self.discount_spin.setSuffix("%")
    self.discount_spin.setValue(100)
    layout.addRow("折扣率:", self.discount_spin)
    
    self.price_group.setLayout(layout)
```

### 3. 供应商信息区域
```python
def setup_supplier_info(self):
    """设置供应商信息区域"""
    self.supplier_group = QGroupBox("供应商信息")
    layout = QFormLayout()
    
    # 供应商
    self.supplier_edit = QLineEdit()
    layout.addRow("供应商:", self.supplier_edit)
    
    # 供应商链接
    self.supplier_link_edit = QLineEdit()
    layout.addRow("供应商链接:", self.supplier_link_edit)
    
    # 采购链接
    self.purchase_link_edit = QLineEdit()
    layout.addRow("采购链接:", self.purchase_link_edit)
    
    # 采购人
    self.purchaser_edit = QLineEdit()
    layout.addRow("采购人:", self.purchaser_edit)
    
    self.supplier_group.setLayout(layout)
```

### 4. 计算结果区域
```python
def setup_result_info(self):
    """设置计算结果区域"""
    self.result_group = QGroupBox("计算结果")
    layout = QFormLayout()
    
    # 总成本
    self.total_cost_label = QLabel("¥0.00")
    layout.addRow("总成本:", self.total_cost_label)
    
    # 折后价
    self.discounted_price_label = QLabel("¥0.00")
    layout.addRow("折后价:", self.discounted_price_label)
    
    # 预计利润
    self.profit_label = QLabel("¥0.00")
    layout.addRow("预计利润:", self.profit_label)
    
    # 利润率
    self.profit_margin_label = QLabel("0.0%")
    layout.addRow("利润率:", self.profit_margin_label)
    
    self.result_group.setLayout(layout)
```

## 数据验证

### 1. 验证器设置
```python
def setup_validators(self):
    """设置输入验证器"""
    # 商品名称验证
    self.name_edit.setValidator(
        QRegExpValidator(QRegExp(".{1,50}"))
    )
    
    # 单位验证
    self.unit_edit.setValidator(
        QRegExpValidator(QRegExp("[\\u4e00-\\u9fa5a-zA-Z]{1,10}"))
    )
    
    # 链接验证
    url_validator = QRegExpValidator(
        QRegExp("(https?:\\/\\/)?[\\w-]+\\.+[\\w-]+(\\/[\\w- ./?%&=]*)?")
    )
    self.supplier_link_edit.setValidator(url_validator)
    self.purchase_link_edit.setValidator(url_validator)
```

### 2. 数据验证
```python
def validate_data(self):
    """验证表单数据"""
    if not self.name_edit.text():
        ErrorHandler.show_warning(self, "验证错误", "商品名称不能为空！")
        return False
        
    if not self.category_combo.currentText():
        ErrorHandler.show_warning(self, "验证错误", "请选择商品类别！")
        return False
        
    if not self.unit_edit.text():
        ErrorHandler.show_warning(self, "验证错误", "请输入商品单位！")
        return False
        
    if self.purchase_price_spin.value() <= 0:
        ErrorHandler.show_warning(self, "验证错误", "采购价必须大于0！")
        return False
        
    if self.selling_price_spin.value() <= 0:
        ErrorHandler.show_warning(self, "验证错误", "售价必须大于0！")
        return False
        
    return True
```

## 数据处理

### 1. 价格计算
```python
def calculate_total_cost(self):
    """计算总成本"""
    purchase_price = self.purchase_price_spin.value()
    shipping_cost = self.shipping_cost_spin.value()
    other_cost = self.other_cost_spin.value()
    total = purchase_price + shipping_cost + other_cost
    self.total_cost_label.setText(f"¥{total:.2f}")
    return total

def calculate_discounted_price(self):
    """计算折后价"""
    selling_price = self.selling_price_spin.value()
    discount_rate = self.discount_spin.value() / 100
    discounted = selling_price * discount_rate
    self.discounted_price_label.setText(f"¥{discounted:.2f}")
    return discounted

def calculate_profit(self):
    """计算利润"""
    total_cost = self.calculate_total_cost()
    discounted_price = self.calculate_discounted_price()
    profit = discounted_price - total_cost
    profit_margin = (profit / discounted_price * 100) if discounted_price > 0 else 0
    
    self.profit_label.setText(f"¥{profit:.2f}")
    self.profit_margin_label.setText(f"{profit_margin:.1f}%")
```

### 2. 数据获取
```python
def get_form_data(self):
    """获取表单数据"""
    return {
        "name": self.name_edit.text(),
        "category": self.category_combo.currentText(),
        "status": self.status_combo.currentText(),
        "quantity": self.quantity_spin.value(),
        "unit": self.unit_edit.text(),
        "purchase_price": self.purchase_price_spin.value(),
        "shipping_cost": self.shipping_cost_spin.value(),
        "other_cost": self.other_cost_spin.value(),
        "selling_price": self.selling_price_spin.value(),
        "discount_rate": self.discount_spin.value(),
        "supplier": self.supplier_edit.text(),
        "supplier_link": self.supplier_link_edit.text(),
        "purchase_link": self.purchase_link_edit.text(),
        "purchaser": self.purchaser_edit.text()
    }
```

### 3. 数据设置
```python
def set_form_data(self, data):
    """
    设置表单数据
    :param data: 商品数据字典
    """
    self.name_edit.setText(data.get("name", ""))
    self.category_combo.setCurrentText(data.get("category", ""))
    self.status_combo.setCurrentText(data.get("status", "在库"))
    self.quantity_spin.setValue(data.get("quantity", 0))
    self.unit_edit.setText(data.get("unit", ""))
    self.purchase_price_spin.setValue(data.get("purchase_price", 0))
    self.shipping_cost_spin.setValue(data.get("shipping_cost", 0))
    self.other_cost_spin.setValue(data.get("other_cost", 0))
    self.selling_price_spin.setValue(data.get("selling_price", 0))
    self.discount_spin.setValue(data.get("discount_rate", 100))
    self.supplier_edit.setText(data.get("supplier", ""))
    self.supplier_link_edit.setText(data.get("supplier_link", ""))
    self.purchase_link_edit.setText(data.get("purchase_link", ""))
    self.purchaser_edit.setText(data.get("purchaser", ""))
```

## 信号和槽

### 1. 价格变更信号
```python
def setup_connections(self):
    """设置信号连接"""
    # 价格相关信号
    self.purchase_price_spin.valueChanged.connect(self.on_price_changed)
    self.shipping_cost_spin.valueChanged.connect(self.on_price_changed)
    self.other_cost_spin.valueChanged.connect(self.on_price_changed)
    self.selling_price_spin.valueChanged.connect(self.on_price_changed)
    self.discount_spin.valueChanged.connect(self.on_price_changed)
```

### 2. 数据变更信号
```python
# 自定义信号
form_data_changed = pyqtSignal()  # 表单数据变更信号
```

## 依赖关系

### 1. PyQt5 组件
- QWidget
- QGroupBox
- QFormLayout
- QLineEdit
- QComboBox
- QSpinBox
- QDoubleSpinBox
- QLabel
- QRegExpValidator

### 2. 自定义组件
- ErrorHandler

## 使用示例
```python
# 创建表单
form = ProductForm(parent_window)

# 设置数据
product_data = {
    "name": "测试商品",
    "category": "电子产品",
    "price": 199.99
}
form.set_form_data(product_data)

# 获取数据
if form.validate_data():
    data = form.get_form_data()
    # 处理数据...

# 连接信号
form.form_data_changed.connect(on_data_changed)
```

## 注意事项
1. 输入验证的完整性
2. 价格计算的准确性
3. 数据一致性维护
4. 实时计算的性能
5. 用户输入的友好提示 