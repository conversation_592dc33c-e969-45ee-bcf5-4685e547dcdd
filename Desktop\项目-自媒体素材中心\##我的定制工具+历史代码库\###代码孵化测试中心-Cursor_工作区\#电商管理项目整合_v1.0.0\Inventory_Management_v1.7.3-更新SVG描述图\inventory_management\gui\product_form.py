from PyQt6.QtWidgets import (
    QWidget,
    QLabel,
    QLineEdit,
    QPushButton,
    QVBoxLayout,
    QHBoxLayout,
    QMessageBox,
    QComboBox,
    QTableWidget,
    QTableWidgetItem,
    QAbstractItemView,
    QFormLayout,
    QMenu,
    QApplication,
    QDialog,
    QCheckBox,
    QHeaderView,
    QSizePolicy,
    QFileDialog,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QPixmap, QPainter
from database.db_utils import (
    add_product,
    get_product,
    update_product,
    delete_product,
    get_transactions_by_product,
    get_connection,
    get_next_type_number,
    generate_product_id,
)
from models.product import Product
from utils.scanner import scan_qr_code
import logging
import os


class ProductForm(QWidget):
    # 定义信号
    products_updated = pyqtSignal()
    product_added = pyqtSignal()
    product_saved = pyqtSignal(dict)  # 商品保存信号
    product_deleted = pyqtSignal(str)  # 商品删除信号

    def __init__(self):
        super().__init__()
        logging.debug("ProductForm init start")

        # 设置大小策略
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 添加图片预览设置
        self.show_thumbnails = True  # 默认显示缩略图
        self.thumbnail_size = 80  # 增大缩略图大小

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)  # 减小边距
        main_layout.setSpacing(5)  # 减小间距

        # 顶部控制区域
        top_control_layout = QHBoxLayout()
        top_control_layout.setSpacing(10)
        top_control_layout.setContentsMargins(0, 0, 0, 0)  # 移除边距

        # 视图控制
        view_control_layout = QHBoxLayout()
        view_control_layout.setSpacing(10)

        # 创建并设置视图模式按钮
        self.view_mode_button = QPushButton("切换到合并视图")
        self.view_mode_button.setFixedWidth(120)

        # 创建并设置图片预览复选框
        self.show_thumbnails_check = QCheckBox("显示图片预览")
        self.show_thumbnails_check.setChecked(self.show_thumbnails)
        self.show_thumbnails_check.setFixedWidth(100)

        view_control_layout.addWidget(self.view_mode_button)
        view_control_layout.addWidget(self.show_thumbnails_check)
        view_control_layout.addStretch()  # 添加弹性空间

        top_control_layout.addLayout(view_control_layout)
        top_control_layout.addStretch()  # 添加弹性空间

        main_layout.addLayout(top_control_layout)

        # 商品信息区域
        product_layout = QFormLayout()
        product_layout.addRow(QLabel("商品信息"))
        logging.debug("商品信息区域已创建")

        self.product_id_input = QLineEdit()
        self.product_id_input.setReadOnly(True)  # 设置为只读
        self.product_name_input = QLineEdit()
        self.product_quantity_input = QLineEdit()
        self.product_category_input = QComboBox()
        self.product_category_input.addItems(["食品", "饮料", "日用品", "电器", "其他"])
        self.product_category_input.currentTextChanged.connect(
            self.on_category_changed
        )  # 添加分类改变事件
        self.product_location_input = QLineEdit()
        self.product_supplier_input = QLineEdit()
        self.product_supplier_link_input = QLineEdit()
        self.product_purchase_link_input = QLineEdit()
        self.product_selling_price_input = QLineEdit()
        self.product_purchaser_input = QLineEdit()
        self.product_selling_link_input = QLineEdit()

        # 当前使用的类型编号和完整商品ID
        self.current_type_number = None
        self.full_product_id = None

        logging.debug("商品信息输入框已创建")

        product_layout.addRow("商品ID:", self.product_id_input)
        product_layout.addRow("商品名称:", self.product_name_input)
        product_layout.addRow("库存数量:", self.product_quantity_input)
        product_layout.addRow("商品分类:", self.product_category_input)
        product_layout.addRow("存放位置:", self.product_location_input)
        product_layout.addRow("进货渠道:", self.product_supplier_input)
        product_layout.addRow("进货链接:", self.product_supplier_link_input)
        product_layout.addRow("采购链接:", self.product_purchase_link_input)
        product_layout.addRow("零售价:", self.product_selling_price_input)
        product_layout.addRow("采购人员:", self.product_purchaser_input)
        product_layout.addRow("出售链接:", self.product_selling_link_input)
        logging.debug("商品信息输入框已添加到布局")

        # 商品操作按钮
        product_buttons_layout = QHBoxLayout()
        self.add_product_button = QPushButton("添加商品")
        self.search_product_button = QPushButton("搜索商品")
        self.update_product_button = QPushButton("更新商品")
        self.delete_product_button = QPushButton("删除商品")
        self.scan_qr_button = QPushButton("扫描二维码")

        product_buttons_layout.addWidget(self.add_product_button)
        product_buttons_layout.addWidget(self.search_product_button)
        product_buttons_layout.addWidget(self.update_product_button)
        product_buttons_layout.addWidget(self.delete_product_button)
        product_buttons_layout.addWidget(self.scan_qr_button)
        product_layout.addRow(product_buttons_layout)
        logging.debug("商品操作按钮已创建并添加到布局")

        main_layout.addLayout(product_layout)

        # 表格区域
        self.product_table = QTableWidget()
        self.product_table.setColumnCount(12)
        self.product_table.setHorizontalHeaderLabels(
            [
                "商品ID",
                "商品名称",
                "库存数量",
                "商品分类",
                "存放位置",
                "进货渠道",
                "进货链接",
                "采购链接",
                "零售价",
                "采购人员",
                "出售链接",
                "图片",
            ]
        )

        # 设置表格属性
        self.product_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.product_table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        self.product_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # 设置列宽
        header = self.product_table.horizontalHeader()
        header.setMinimumSectionSize(80)

        column_widths = {
            0: 120,  # 商品ID
            1: 150,  # 商品名称
            2: 80,  # 库存数量
            3: 80,  # 商品分类
            4: 100,  # 存放位置
            5: 100,  # 进货渠道
            6: 150,  # 进货链接
            7: 150,  # 采购链接
            8: 80,  # 零售价
            9: 80,  # 采购人员
            10: 150,  # 出售链接
            11: 200,  # 图片列
        }

        for col, width in column_widths.items():
            self.product_table.setColumnWidth(col, width)
            if col == 11:  # 图片列使用 Interactive 模式，允许用户调整
                header.setSectionResizeMode(col, QHeaderView.ResizeMode.Interactive)
            else:
                header.setSectionResizeMode(col, QHeaderView.ResizeMode.Fixed)

        # 设置表格大小策略
        self.product_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 设置右键菜单
        self.product_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.product_table.customContextMenuRequested.connect(self.show_context_menu)

        # 添加表格选择事件
        self.product_table.itemSelectionChanged.connect(self.on_table_selection_changed)
        # 添加表格双击事件
        self.product_table.doubleClicked.connect(self.on_table_double_clicked)

        # 设置表格列宽自适应
        self.product_table.horizontalHeader().setStretchLastSection(
            False
        )  # 修改为False

        main_layout.addWidget(self.product_table)

        # 连接信号
        self.show_thumbnails_check.stateChanged.connect(self.toggle_thumbnails)
        self.view_mode_button.clicked.connect(self.toggle_view_mode)
        self.add_product_button.clicked.connect(self.add_product)
        self.search_product_button.clicked.connect(self.search_product)
        self.update_product_button.clicked.connect(self.update_product)
        self.delete_product_button.clicked.connect(self.delete_product)
        self.scan_qr_button.clicked.connect(self.scan_qr)
        self.product_category_input.currentTextChanged.connect(self.on_category_changed)

        # 初始加载商品列表
        self.load_products()

        logging.debug("ProductForm init end")

    def on_category_changed(self, category):
        """当分类改变时，自动更新商品ID"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                # 获取新的类型编号
                type_number = get_next_type_number(cursor, category)
                # 生成新的商品ID预览（显示分类和类型编号部分）
                self.current_type_number = type_number
                preview_id = f"#{category}-#{type_number}"
                self.product_id_input.setText(preview_id)
                logging.debug(f"生成的商品ID预览: {preview_id}")
        except Exception as e:
            logging.exception("生成商品ID预览失败")
            QMessageBox.warning(self, "Error", f"生成商品ID预览失败: {e}")

    def add_product(self):
        """添加商品"""
        logging.debug("add_product 开始")

        # 获取商品信息
        display_id = self.product_id_input.text()
        if not display_id:
            QMessageBox.warning(self, "Error", "商品ID不能为空，请先选择商品分类")
            return

        name = self.product_name_input.text()
        if not name:
            QMessageBox.warning(self, "Error", "商品名称不能为空")
            return

        try:
            quantity = int(self.product_quantity_input.text())
            if quantity < 1:
                QMessageBox.warning(self, "Error", "库存数量必须大于0")
                return
        except ValueError:
            QMessageBox.warning(self, "Error", "库存数量必须是整数")
            return

        try:
            selling_price = float(self.product_selling_price_input.text())
        except ValueError:
            QMessageBox.warning(self, "Error", "零售价必须是数字")
            return

        category = self.product_category_input.currentText()
        location = self.product_location_input.text()
        supplier = self.product_supplier_input.text()
        supplier_link = self.product_supplier_link_input.text()
        purchase_link = self.product_purchase_link_input.text()
        purchaser = self.product_purchaser_input.text()
        selling_link = self.product_selling_link_input.text()
        image_path = ""

        logging.debug(f"准备添加 {quantity} 个商品")
        try:
            with get_connection() as conn:
                cursor = conn.cursor()

                # 使用当前的类型编号，如果没有则获取新的
                type_number = None
                if "-" in display_id:
                    parts = display_id.split("-")
                    if len(parts) >= 2:
                        type_number = parts[1].replace(
                            "#", ""
                        )  # 使用预览ID中的类型编号

                # 批量添加商品
                success_count = 0
                first_product_id = None  # 记录第一个添加的商品ID

                for i in range(quantity):
                    try:
                        # 生成商品ID，使用相同的类型编号
                        product_id = generate_product_id(
                            category, type_number, cursor=cursor
                        )
                        if not first_product_id:
                            first_product_id = product_id

                        # 创建 Product 对象
                        product = Product(
                            product_id,
                            name,
                            1,  # 每个商品的数量为1
                            category,
                            location,
                            supplier,
                            supplier_link,
                            purchase_link,
                            selling_price,
                            purchaser,
                            selling_link,
                            image_path,
                        )

                        # 添加到数据库
                        add_product(product)
                        success_count += 1
                        logging.debug(f"成功添加第 {i+1} 个商品: {product_id}")

                    except Exception as e:
                        logging.error(f"添加第 {i+1} 个商品失败: {e}")
                        continue

                if success_count > 0:
                    QMessageBox.information(
                        self,
                        "成功",
                        f"成功添加 {success_count} 个商品\n第一个商品ID: {first_product_id}",
                    )
                    # 发送信号通知主窗口刷新商品列表
                    self.product_added.emit()
                    # 清空输入框
                    self.clear_inputs()
                else:
                    QMessageBox.warning(self, "失败", "没有成功添加任何商品")

        except Exception as e:
            logging.error(f"添加商品失败: {e}")
            QMessageBox.warning(self, "Error", f"添加商品失败: {e}")
            return

    def search_product(self):
        """搜索商品"""
        logging.debug("search_product 开始")
        display_id = self.product_id_input.text()
        if not display_id:
            QMessageBox.warning(self, "Error", "请输入商品ID!")
            logging.warning("未输入商品ID")
            return

        # 如果有完整ID，使用完整ID搜索
        product_id = self.full_product_id if self.full_product_id else display_id
        product = get_product(product_id)
        if product:
            self.product_table.setRowCount(1)
            # 在表格中显示简化ID
            display_id = (
                f"-{product.product_id.split('-')[-1]}"
                if "-" in product.product_id
                else product.product_id
            )
            self.product_table.setItem(0, 0, QTableWidgetItem(display_id))
            self.product_table.setItem(0, 1, QTableWidgetItem(product.name))
            self.product_table.setItem(0, 2, QTableWidgetItem(str(product.quantity)))
            self.product_table.setItem(0, 3, QTableWidgetItem(product.category))
            self.product_table.setItem(0, 4, QTableWidgetItem(product.location))
            self.product_table.setItem(0, 5, QTableWidgetItem(product.supplier))
            self.product_table.setItem(0, 6, QTableWidgetItem(product.supplier_link))
            self.product_table.setItem(0, 7, QTableWidgetItem(product.purchase_link))
            self.product_table.setItem(
                0, 8, QTableWidgetItem(str(product.selling_price))
            )
            self.product_table.setItem(0, 9, QTableWidgetItem(product.purchaser))
            self.product_table.setItem(0, 10, QTableWidgetItem(product.selling_link))
            self.product_table.setItem(0, 11, QTableWidgetItem(product.image_path))
            logging.debug(f"已找到商品: {product_id}")
        else:
            QMessageBox.information(self, "Not Found", "Product not found.")
            logging.info(f"未找到商品: {product_id}")

        logging.debug("search_product 完成")

    def update_product(self):
        """更新商品信息"""
        logging.debug("update_product 开始")

        # 获取商品信息
        display_id = self.product_id_input.text()
        if not display_id:
            QMessageBox.warning(self, "Error", "商品ID不能为空")
            return

        # 使用完整的商品ID
        product_id = self.full_product_id if self.full_product_id else display_id

        # 获取并验证输入数据
        try:
            product_data = {
                "product_id": product_id,
                "name": self.product_name_input.text(),
                "quantity": int(self.product_quantity_input.text()),
                "category": self.product_category_input.currentText(),
                "location": self.product_location_input.text(),
                "supplier": self.product_supplier_input.text(),
                "supplier_link": self.product_supplier_link_input.text(),
                "purchase_link": self.product_purchase_link_input.text(),
                "selling_price": float(self.product_selling_price_input.text() or 0),
                "purchaser": self.product_purchaser_input.text(),
                "selling_link": self.product_selling_link_input.text(),
            }
        except ValueError as e:
            QMessageBox.warning(self, "Error", str(e))
            return

        try:
            # 获取原有商品数据
            existing_product = get_product(product_id)
            if not existing_product:
                QMessageBox.warning(self, "Error", f"找不到商品: {product_id}")
                return

            # 合并现有数据和更新数据
            existing_product.update(product_data)

            # 更新商品
            update_product(product_id, existing_product)

            # 刷新显示
            self.products_updated.emit()
            self.clear_inputs()
            QMessageBox.information(self, "Success", "商品更新成功")

        except Exception as e:
            logging.exception("更新商品失败")
            QMessageBox.warning(self, "Error", f"更新商品失败: {str(e)}")
            return

    def delete_product(self):
        """删除商品"""
        logging.debug("delete_product 开始")
        display_id = self.product_id_input.text()
        if not display_id:
            QMessageBox.warning(self, "Error", "请输入商品ID!")
            logging.warning("未输入商品ID")
            return

        # 使用完整ID删除商品
        product_id = self.full_product_id if self.full_product_id else display_id
        try:
            delete_product(product_id)
            logging.debug("商品已从数据库中删除")
            self.products_updated.emit()  # 发射信号
            self.clear_product_inputs()
            QMessageBox.information(self, "Success", "Product deleted successfully!")
            logging.debug("已显示商品删除成功提示")
        except Exception as e:
            logging.exception("删除商品失败")
            QMessageBox.warning(self, "Error", f"Failed to delete product: {e}")

        logging.debug("delete_product 完成")

    def clear_product_inputs(self):
        """清空商品信息输入框"""
        logging.debug("clear_product_inputs 开始")
        self.product_id_input.clear()
        self.product_name_input.clear()
        self.product_quantity_input.clear()
        self.product_category_input.setCurrentIndex(0)
        self.product_location_input.clear()
        self.product_supplier_input.clear()
        self.product_supplier_link_input.clear()
        self.product_purchase_link_input.clear()
        self.product_selling_price_input.clear()
        self.product_purchaser_input.clear()
        self.product_selling_link_input.clear()
        # 清空完整商品ID
        self.full_product_id = None
        self.current_type_number = None
        logging.debug("clear_product_inputs 完成")

    def scan_qr(self):
        """扫描二维码"""
        logging.debug("scan_qr 开始")
        qr_data = scan_qr_code()
        if qr_data:
            # 将扫描到的二维码信息填入商品ID输入框
            self.product_id_input.setText(qr_data)
            logging.debug(f"已将扫描到的二维码信息填入商品ID输入框: {qr_data}")
        else:
            QMessageBox.warning(self, "Error", "Failed to scan QR code!")
            logging.warning("扫描二维码失败")

        logging.debug("scan_qr 完成")

    def view_product_transactions(self, index):
        """双击商品表格中的某一行时，显示该商品的交易记录"""
        logging.debug("view_product_transactions 开始")
        product_id = self.product_table.item(index.row(), 0).text()
        transactions = get_transactions_by_product(product_id)
        logging.debug(f"已获取商品 {product_id} 的交易记录")

        self.transaction_table.setRowCount(len(transactions))
        for row, transaction in enumerate(transactions):
            self.transaction_table.setItem(
                row, 0, QTableWidgetItem(transaction.transaction_id)
            )
            self.transaction_table.setItem(
                row, 1, QTableWidgetItem(transaction.product_id)
            )
            self.transaction_table.setItem(
                row, 2, QTableWidgetItem(transaction.transaction_type)
            )
            self.transaction_table.setItem(
                row, 3, QTableWidgetItem(str(transaction.quantity))
            )
            self.transaction_table.setItem(
                row, 4, QTableWidgetItem(str(transaction.unit_price))
            )
            self.transaction_table.setItem(
                row, 5, QTableWidgetItem(str(transaction.total_price))
            )
            self.transaction_table.setItem(
                row, 6, QTableWidgetItem(str(transaction.shipping_cost))
            )
            self.transaction_table.setItem(
                row, 7, QTableWidgetItem(transaction.transaction_time)
            )
            self.transaction_table.setItem(
                row, 8, QTableWidgetItem(str(transaction.profit))
            )
            self.transaction_table.setItem(
                row, 9, QTableWidgetItem(transaction.payment_method)
            )
            self.transaction_table.setItem(
                row, 10, QTableWidgetItem(transaction.order_id)
            )
        logging.debug("交易记录已显示在交易表格中")

        logging.debug("view_product_transactions 完成")

    def on_table_selection_changed(self):
        """当表格选择改变时，更新输入框的值"""
        selected_rows = self.product_table.selectedItems()
        if not selected_rows:
            return

        # 获取选中行的第一个单元格（商品ID）
        row = selected_rows[0].row()
        display_id = self.product_table.item(row, 0).text()
        category = self.product_table.item(row, 3).text()

        # 根据显示的ID和分类重建完整ID
        if "-" in display_id:
            parts = display_id.split("-")
            if len(parts) == 2:  # 合并视图模式
                base_type = display_id
                # 获取该类型下的第一个商品
                with get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        "SELECT product_id FROM products WHERE product_id LIKE ? ORDER BY product_id LIMIT 1",
                        (f"{base_type}-%",),
                    )
                    result = cursor.fetchone()
                    if result:
                        self.full_product_id = result[0]
                    else:
                        self.full_product_id = display_id
            else:  # 展开视图模式
                self.full_product_id = display_id
        else:
            self.full_product_id = display_id

        # 更新输入框的值
        self.product_id_input.setText(display_id)
        self.product_name_input.setText(self.product_table.item(row, 1).text())
        self.product_quantity_input.setText(self.product_table.item(row, 2).text())
        # 设置分类
        index = self.product_category_input.findText(category)
        if index >= 0:
            self.product_category_input.setCurrentIndex(index)
        self.product_location_input.setText(self.product_table.item(row, 4).text())
        self.product_supplier_input.setText(self.product_table.item(row, 5).text())
        self.product_supplier_link_input.setText(self.product_table.item(row, 6).text())
        self.product_purchase_link_input.setText(self.product_table.item(row, 7).text())
        self.product_selling_price_input.setText(self.product_table.item(row, 8).text())
        self.product_purchaser_input.setText(self.product_table.item(row, 9).text())
        self.product_selling_link_input.setText(self.product_table.item(row, 10).text())

    def toggle_view_mode(self):
        """切换视图模式（合并/展开）"""
        if self.view_mode_button.text() == "切换到合并视图":
            self.view_mode_button.setText("切换到展开视图")
            self.load_products(merged=True)
        else:
            self.view_mode_button.setText("切换到合并视图")
            self.load_products(merged=False)

    def load_products(self, merged=False):
        """加载商品列表，支持合并/展开视图，现在包含图片预览功能"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                if merged:
                    # 在合并视图中，按商品类型分组并汇总数量
                    cursor.execute(
                        """
                        WITH base_types AS (
                            SELECT DISTINCT 
                                substr(product_id, 1, instr(substr(product_id, instr(product_id, '-') + 1), '-') + instr(product_id, '-')) as base_type
                            FROM products
                        )
                        SELECT 
                            MIN(p.product_id) as product_id,
                            p.name,
                            COUNT(*) as total_quantity,
                            p.category,
                            p.location,
                            p.supplier,
                            p.supplier_link,
                            p.purchase_link,
                            p.selling_price,
                            p.purchaser,
                            p.selling_link,
                            p.image_path
                        FROM products p
                        JOIN base_types bt ON p.product_id LIKE bt.base_type || '%'
                        GROUP BY bt.base_type, p.name, p.category, p.location, p.supplier, 
                                p.supplier_link, p.purchase_link, p.selling_price, 
                                p.purchaser, p.selling_link, p.image_path
                        ORDER BY p.product_id
                    """
                    )
                else:
                    cursor.execute(
                        """
                        SELECT 
                            product_id,
                            name,
                            quantity,
                            category,
                            location,
                            supplier,
                            supplier_link,
                            purchase_link,
                            selling_price,
                            purchaser,
                            selling_link,
                            image_path
                        FROM products
                        ORDER BY product_id
                        """
                    )

                products = cursor.fetchall()

                # 更新表格
                self.product_table.setRowCount(len(products))

                # 设置行高
                row_height = (
                    max(self.thumbnail_size + 10, 24) if self.show_thumbnails else 24
                )
                for row in range(self.product_table.rowCount()):
                    self.product_table.setRowHeight(row, row_height)

                for row, product in enumerate(products):
                    # 设置其他列
                    for col in range(11):  # 除了最后一列（图片）
                        self.product_table.setItem(
                            row, col, QTableWidgetItem(str(product[col]))
                        )

                    # 处理图片列
                    image_path = product[11]
                    if self.show_thumbnails:
                        if image_path and os.path.exists(image_path):
                            thumbnail = self.create_thumbnail(
                                image_path, self.thumbnail_size
                            )
                            if thumbnail:
                                label = QLabel()
                                label.setFixedSize(
                                    self.thumbnail_size, self.thumbnail_size
                                )
                                label.setPixmap(thumbnail)
                                label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                                label.setStyleSheet(
                                    """
                                    QLabel {
                                        background-color: white;
                                        border: 1px solid #cccccc;
                                        padding: 2px;
                                    }
                                """
                                )
                                self.product_table.setCellWidget(row, 11, label)
                            else:
                                self.product_table.setItem(
                                    row, 11, QTableWidgetItem("无法加载图片")
                                )
                        else:
                            self.product_table.setItem(
                                row, 11, QTableWidgetItem("无图片")
                            )
                    else:
                        # 显示简化的图片路径
                        if image_path:
                            simplified_path = os.path.basename(image_path)
                            item = QTableWidgetItem(simplified_path)
                            item.setToolTip(image_path)  # 显示完整路径作为工具提示
                            self.product_table.setItem(row, 11, item)
                        else:
                            self.product_table.setItem(
                                row, 11, QTableWidgetItem("无图片")
                            )

                logging.debug(
                    f"已加载商品列表 (合并模式: {merged}, 显示缩略图: {self.show_thumbnails})"
                )

        except Exception as e:
            logging.exception("加载商品列表失败")
            QMessageBox.warning(self, "Error", f"Failed to load products: {e}")

    def show_context_menu(self, pos):
        """显示右键菜单，添加图片预览选项"""
        logging.debug("开始显示右键菜单")
        # 获取选中的行
        selected_rows = set(item.row() for item in self.product_table.selectedItems())
        logging.debug(f"选中的行数: {len(selected_rows)}")
        if not selected_rows:
            logging.debug("没有选中的行，不显示菜单")
            return

        menu = QMenu(self)
        logging.debug("创建菜单对象")

        # 添加菜单项
        if len(selected_rows) == 1:
            # 单选时显示编辑选项
            edit_action = menu.addAction("编辑商品")
            menu.addSeparator()
            copy_id_action = menu.addAction("复制商品ID")
            copy_name_action = menu.addAction("复制商品名称")
            menu.addSeparator()

            # 添加图片预览选项
            row = list(selected_rows)[0]
            image_path = self.product_table.item(row, 11).text()
            if image_path and os.path.exists(image_path):
                preview_image_action = menu.addAction("预览图片")
                menu.addSeparator()

            logging.debug("添加单选菜单项")

        delete_action = menu.addAction("删除商品")
        if len(selected_rows) > 1:
            delete_action.setText(f"删除选中的 {len(selected_rows)} 个商品")
        logging.debug("添加删除菜单项")

        # 计算菜单显示位置
        global_pos = self.product_table.mapToGlobal(pos)
        logging.debug(f"菜单显示位置: {global_pos.x()}, {global_pos.y()}")

        # 显示菜单并获取选择的动作
        action = menu.exec_(global_pos)
        logging.debug(f"用户选择的动作: {action.text() if action else 'None'}")

        if not action:
            logging.debug("用户未选择任何动作")
            return

        try:
            if action == delete_action:
                logging.debug("执行删除操作")
                self.delete_selected_products()
            elif len(selected_rows) == 1:
                row = list(selected_rows)[0]
                if action == edit_action:
                    logging.debug("执行编辑操作")
                    self.fill_form_for_edit(row)
                elif action == copy_id_action:
                    logging.debug("执行复制ID操作")
                    product_id = self.product_table.item(row, 0).text()
                    QApplication.clipboard().setText(product_id)
                elif action == copy_name_action:
                    logging.debug("执行复制名称操作")
                    product_name = self.product_table.item(row, 1).text()
                    QApplication.clipboard().setText(product_name)
                elif action == preview_image_action:
                    logging.debug("执行图片预览操作")
                    self.preview_image(image_path)
        except Exception as e:
            logging.exception("处理右键菜单动作失败")
            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")

    def on_table_double_clicked(self, index):
        """处理表格双击事件"""
        row = index.row()
        self.fill_form_for_edit(row)

    def fill_form_for_edit(self, row):
        """将表格中选中行的数据填充到表单中进行编辑"""
        try:
            # 获取商品ID
            product_id = self.product_table.item(row, 0).text()

            # 更新主界面输入框
            self.product_id_input.setText(product_id)
            self.product_name_input.setText(self.product_table.item(row, 1).text())
            self.product_quantity_input.setText(self.product_table.item(row, 2).text())

            # 设置分类
            category = self.product_table.item(row, 3).text()
            index = self.product_category_input.findText(category)
            if index >= 0:
                self.product_category_input.setCurrentIndex(index)

            self.product_location_input.setText(self.product_table.item(row, 4).text())
            self.product_supplier_input.setText(self.product_table.item(row, 5).text())
            self.product_supplier_link_input.setText(
                self.product_table.item(row, 6).text()
            )
            self.product_purchase_link_input.setText(
                self.product_table.item(row, 7).text()
            )
            self.product_selling_price_input.setText(
                self.product_table.item(row, 8).text()
            )
            self.product_purchaser_input.setText(self.product_table.item(row, 9).text())
            self.product_selling_link_input.setText(
                self.product_table.item(row, 10).text()
            )

            # 保存完整商品ID
            self.full_product_id = product_id

            logging.debug(f"已加载商品 {product_id} 的数据到表单")

        except Exception as e:
            logging.exception("加载商品数据到表单失败")
            QMessageBox.critical(self, "错误", f"加载商品数据失败: {str(e)}")

    def delete_selected_products(self):
        """删除选中的商品"""
        try:
            # 获取所有选中的行
            selected_rows = set(
                item.row() for item in self.product_table.selectedItems()
            )
            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要删除的商品")
                return

            # 获取所有选中商品的ID和名称
            products_to_delete = []
            for row in selected_rows:
                product_id = self.product_table.item(row, 0).text()
                name = self.product_table.item(row, 1).text()
                products_to_delete.append((product_id, name))

            # 构建确认消息
            if len(products_to_delete) == 1:
                message = f"确定要删除商品 {products_to_delete[0][1]} (ID: {products_to_delete[0][0]}) 吗？"
            else:
                message = f"确定要删除以下 {len(products_to_delete)} 个商品吗？\n\n"
                # 最多显示前5个商品
                for i, (pid, name) in enumerate(products_to_delete[:5]):
                    message += f"{i+1}. {name} (ID: {pid})\n"
                if len(products_to_delete) > 5:
                    message += f"... 等共 {len(products_to_delete)} 个商品"

            reply = QMessageBox.question(
                self,
                "确认删除",
                message,
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            )

            if reply == QMessageBox.StandardButton.Yes:
                with get_connection() as conn:
                    cursor = conn.cursor()
                    for product_id, _ in products_to_delete:
                        # 删除商品
                        delete_product(product_id)

                # 清空输入框
                self.clear_product_inputs()
                # 刷新表格
                self.products_updated.emit()

                QMessageBox.information(
                    self, "成功", f"已删除 {len(products_to_delete)} 个商品"
                )
                logging.info(f"成功删除 {len(products_to_delete)} 个商品")

        except Exception as e:
            logging.exception("删除商品失败")
            QMessageBox.critical(self, "错误", f"删除商品失败: {str(e)}")

    def toggle_thumbnails(self, state):
        """切换是否显示缩略图"""
        try:
            self.show_thumbnails = bool(state)
            self.load_products(self.view_mode_button.text() == "切换到展开视图")
            logging.debug(f"切换图片预览状态: {self.show_thumbnails}")
        except Exception as e:
            logging.exception("切换图片预览状态失败")
            QMessageBox.warning(self, "Error", f"切换图片预览状态失败: {e}")

    def create_thumbnail(self, image_path, size):
        """创建缩略图"""
        if not image_path or not os.path.exists(image_path):
            return None
        try:
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                return None

            # 创建一个正方形的缩略图
            scaled_pixmap = pixmap.scaled(
                size, size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )

            # 创建一个白色背景的正方形画布
            final_pixmap = QPixmap(size, size)
            final_pixmap.fill(Qt.white)

            # 在画布中央绘制缩略图
            painter = QPainter(final_pixmap)
            x = (size - scaled_pixmap.width()) // 2
            y = (size - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
            painter.end()

            return final_pixmap

        except Exception as e:
            logging.warning(f"创建缩略图失败: {e}")
            return None

    def preview_image(self, image_path):
        """预览图片"""
        try:
            if not image_path or not os.path.exists(image_path):
                QMessageBox.warning(self, "警告", "图片不存在")
                return

            # 创建预览对话框
            preview_dialog = QDialog(self)
            preview_dialog.setWindowTitle("图片预览")
            preview_dialog.setModal(True)

            # 创建图片标签
            label = QLabel()
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                QMessageBox.warning(self, "警告", "无法加载图片")
                return

            # 计算合适的显示大小
            screen = QApplication.primaryScreen().geometry()
            max_width = screen.width() * 0.8
            max_height = screen.height() * 0.8
            scaled_pixmap = pixmap.scaled(
                max_width, max_height, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )

            label.setPixmap(scaled_pixmap)
            label.setAlignment(Qt.AlignCenter)

            # 创建布局
            layout = QVBoxLayout()
            layout.addWidget(label)

            # 添加关闭按钮
            close_button = QPushButton("关闭")
            close_button.clicked.connect(preview_dialog.accept)
            layout.addWidget(close_button, alignment=Qt.AlignHCenter)

            preview_dialog.setLayout(layout)
            preview_dialog.resize(
                scaled_pixmap.width() + 40, scaled_pixmap.height() + 80
            )
            preview_dialog.exec_()

        except Exception as e:
            logging.exception("预览图片失败")
            QMessageBox.critical(self, "错误", f"预览图片失败: {str(e)}")
