# 自定义控件模块

## 概述
自定义控件模块提供了系统中所有的自定义 UI 控件，基于 PyQt6 开发，实现了特定的业务功能和交互需求。

## 控件列表

### 1. 表格控件
#### BatchTable
批次表格控件
- 继承自 QTableWidget
- 支持批次数据的显示和管理
- 实现自定义右键菜单
- 支持数据过滤和排序

#### ProductTable
商品表格控件
- 继承自 QTableWidget
- 支持商品数据的展示
- 实现合并视图模式
- 支持多选和批量操作

### 2. 工具栏控件
#### BatchToolBar
批次工具栏
- 继承自 QWidget
- 提供批次管理的常用操作
- 实现搜索功能
- 发送操作信号

#### ProductToolBar
商品工具栏
- 继承自 QWidget
- 提供商品管理的工具按钮
- 实现过滤和搜索
- 支持显示模式切换

### 3. 图片相关控件
#### ImagePathDelegate
图片路径代理
- 继承自 QStyledItemDelegate
- 处理图片路径的显示
- 支持图片预览
- 优化性能

#### PreviewWidget
图片预览控件
- 继承自 QWidget
- 实现图片预览功能
- 支持缩放和平移
- 显示图片信息

## 技术特点

### 1. PyQt6 新特性
- 使用 Qt6 的新控件基类
- 采用新的信号槽机制
- 支持高 DPI 显示
- 现代化的界面风格

### 2. 性能优化
- 延迟加载机制
- 数据缓存处理
- 异步图片加载
- 内存使用优化

### 3. 用户体验
- 响应式设计
- 流畅的交互
- 直观的操作
- 及时的反馈

### 4. 可扩展性
- 模块化设计
- 接口标准化
- 易于继承和扩展
- 代码复用

## 开发指南

### 1. 创建新控件
```python
from PyQt6.QtWidgets import QWidget

class CustomWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        # UI 初始化代码
        pass
```

### 2. 信号处理
```python
from PyQt6.QtCore import pyqtSignal

class CustomControl(QWidget):
    # 定义信号
    value_changed = pyqtSignal(str)
    
    def on_value_change(self, value):
        # 发送信号
        self.value_changed.emit(value)
```

### 3. 样式设置
```python
def setup_style(self):
    self.setStyleSheet("""
        QWidget {
            background-color: white;
            border: 1px solid #ccc;
        }
        QWidget:hover {
            border-color: #666;
        }
    """)
```

## 使用示例

### 1. 表格控件
```python
table = BatchTable(parent)
table.batch_selected.connect(on_batch_selected)
table.load_data()
```

### 2. 工具栏
```python
toolbar = ProductToolBar(parent)
toolbar.add_clicked.connect(on_add)
toolbar.search_changed.connect(on_search)
```

### 3. 图片控件
```python
preview = PreviewWidget(parent)
preview.load_image("path/to/image.jpg")
preview.set_zoom(1.2)
```

## 注意事项
1. 遵循 Qt 的设计规范
2. 实现必要的信号和槽
3. 注意内存管理
4. 优化性能
5. 处理边界情况
6. 提供错误处理
7. 添加适当的注释
8. 保持代码风格统一 