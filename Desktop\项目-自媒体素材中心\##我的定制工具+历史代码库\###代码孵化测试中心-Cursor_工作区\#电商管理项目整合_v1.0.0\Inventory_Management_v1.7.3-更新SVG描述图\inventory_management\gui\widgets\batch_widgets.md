# 批次管理组件 (batch_widgets.py)

## 功能概述
`batch_widgets.py` 实现了批次管理相关的自定义控件，包括批次表格和工具栏。这些控件提供了批次数据的显示、过滤、编辑等功能，以及相关的用户交互界面。

## 组件结构

### BatchTable 类
```python
class BatchTable(QTableWidget):
    """批次表格控件，继承自 QTableWidget"""
```

#### 信号定义
- `batch_selected(str)`: 批次选中信号，参数为批次ID
- `batch_edited(str)`: 批次编辑信号，参数为批次ID
- `batch_deleted(str)`: 批次删除信号，参数为批次ID

#### 核心方法
1. **setup_table()**
   - 设置表格基本属性
   - 配置列标题（批次ID、名称、创建时间等）
   - 设置选择模式和交互行为
   - 配置右键菜单

2. **load_batches()**
   - 从数据库加载批次数据
   - 清空现有数据
   - 填充新数据
   - 自动调整列宽

3. **show_context_menu(pos)**
   - 显示右键菜单
   - 提供编辑、删除等选项
   - 触发相应的操作信号

4. **filter_batches(text)**
   - 根据搜索文本过滤批次列表
   - 动态显示/隐藏行

### BatchToolBar 类
```python
class BatchToolBar(QWidget):
    """批次工具栏控件，继承自 QWidget"""
```

#### 信号定义
- `add_batch`: 添加批次信号
- `edit_batch`: 编辑批次信号
- `delete_batch`: 删除批次信号
- `refresh_data`: 刷新数据信号
- `search_text_changed(str)`: 搜索文本变更信号

#### 核心功能
1. **工具按钮**
   - 添加批次
   - 编辑批次
   - 删除批次
   - 刷新数据

2. **搜索功能**
   - 搜索框（实时过滤）
   - 搜索提示
   - 清除搜索

## 使用示例
```python
# 创建批次表格
batch_table = BatchTable(parent)
batch_table.batch_selected.connect(on_batch_selected)
batch_table.batch_edited.connect(on_batch_edited)
batch_table.batch_deleted.connect(on_batch_deleted)

# 创建工具栏
toolbar = BatchToolBar(parent)
toolbar.add_batch.connect(show_batch_dialog)
toolbar.edit_batch.connect(edit_batch)
toolbar.delete_batch.connect(delete_batch)
toolbar.refresh_data.connect(refresh_data)
toolbar.search_text_changed.connect(filter_batches)
```

## 依赖关系
- PyQt5 组件
- 数据库工具 (database.db_utils)
- 错误处理 (utils.error_handler)
- 图标资源 (:/icons/)

## 注意事项
1. 数据一致性
   - 批次操作后需要及时刷新数据
   - 保持与主窗口状态的同步
   - 确保过滤条件的正确应用

2. 错误处理
   - 所有数据库操作都有异常处理
   - 用户操作有适当的提示和反馈
   - 输入验证和数据检查

3. 性能优化
   - 批量数据加载时使用事务
   - 避免频繁刷新整个表格
   - 优化过滤算法

4. 用户体验
   - 表格列宽自动调整
   - 清晰的操作反馈
   - 直观的过滤机制 