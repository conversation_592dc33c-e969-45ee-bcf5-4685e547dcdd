from PyQt6.QtWidgets import (
    QTableWidget,
    QTableWidgetItem,
    QAbstractItemView,
    QMenu,
    QMessageBox,
    QToolBar,
    QLineEdit,
    QPushButton,
    QHBoxLayout,
    QWidget,
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QIcon, QAction
from database.db_utils import get_connection
from utils.error_handler import <PERSON>rrorHandler
from datetime import datetime
from icons import Icons


class BatchTable(QTableWidget):
    """批次表格控件"""

    # 定义信号
    batch_selected = pyqtSignal(str)  # 批次选中信号
    batch_deleted = pyqtSignal(str)  # 批次删除信号
    batch_edited = pyqtSignal(str)  # 批次编辑信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent = parent
        self.setup_table()

    def setup_table(self):
        """设置表格基本属性"""
        self.setColumnCount(6)
        self.setHorizontalHeaderLabels(
            ["批次ID", "批次名称", "商品数量", "备注", "创建时间", "更新时间"]
        )
        self.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

    def load_batches(self):
        """加载批次数据"""
        try:
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT b.batch_id, b.batch_name, b.remarks,
                           b.created_at, b.updated_at,
                           COUNT(pb.product_id) as product_count
                    FROM batches b
                    LEFT JOIN product_batches pb ON b.batch_id = pb.batch_id
                    GROUP BY b.batch_id
                    ORDER BY b.created_at DESC
                    """
                )
                batches = cursor.fetchall()

                self.setRowCount(len(batches))
                for row, batch in enumerate(batches):
                    # 批次ID
                    item = QTableWidgetItem(str(batch[0]))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.setItem(row, 0, item)

                    # 批次名称
                    item = QTableWidgetItem(str(batch[1]))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.setItem(row, 1, item)

                    # 商品数量
                    item = QTableWidgetItem(str(batch[5]))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.setItem(row, 2, item)

                    # 备注
                    item = QTableWidgetItem(str(batch[2]) if batch[2] else "")
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.setItem(row, 3, item)

                    # 创建时间
                    created_at = datetime.fromisoformat(batch[3]).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    item = QTableWidgetItem(created_at)
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.setItem(row, 4, item)

                    # 更新时间
                    updated_at = datetime.fromisoformat(batch[4]).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    item = QTableWidgetItem(updated_at)
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.setItem(row, 5, item)

                # 调整列宽
                self.resizeColumnsToContents()

            ErrorHandler.log_info(f"加载了 {len(batches)} 个批次")
        except Exception as e:
            ErrorHandler.log_error(f"加载批次列表失败: {str(e)}")
            raise

    def show_context_menu(self, pos):
        """显示右键菜单"""
        try:
            current_row = self.currentRow()
            if current_row < 0:
                return

            menu = QMenu(self)

            edit_action = menu.addAction("编辑批次")
            edit_action.triggered.connect(
                lambda: self.batch_edited.emit(self.item(current_row, 0).text())
            )

            delete_action = menu.addAction("删除批次")
            delete_action.triggered.connect(
                lambda: self.batch_deleted.emit(self.item(current_row, 0).text())
            )

            menu.addSeparator()

            view_products_action = menu.addAction("查看相关商品")
            view_products_action.triggered.connect(
                lambda: self.batch_selected.emit(self.item(current_row, 1).text())
            )

            menu.exec(self.viewport().mapToGlobal(pos))
        except Exception as e:
            ErrorHandler.log_error(f"显示批次右键菜单失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"显示批次右键菜单失败: {str(e)}")

    def filter_batches(self, search_text):
        """过滤批次列表"""
        try:
            search_text = search_text.strip().lower()
            for row in range(self.rowCount()):
                show_row = False
                for col in [0, 1]:  # 只搜索批次ID和名称列
                    item = self.item(row, col)
                    if item and search_text in item.text().lower():
                        show_row = True
                        break
                self.setRowHidden(row, not show_row if search_text else False)
        except Exception as e:
            ErrorHandler.log_error(f"过滤批次失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"过滤批次失败: {str(e)}")


class BatchToolBar(QWidget):
    """批次工具栏控件"""

    # 定义信号
    add_batch = pyqtSignal()  # 添加批次信号
    edit_batch = pyqtSignal()  # 编辑批次信号
    delete_batch = pyqtSignal()  # 删除批次信号
    refresh_data = pyqtSignal()  # 刷新数据信号
    search_text_changed = pyqtSignal(str)  # 搜索文本变更信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置界面"""
        layout = QHBoxLayout()
        layout.setSpacing(10)

        # 添加批次按钮
        add_batch_btn = QPushButton("添加批次")
        add_batch_btn.setIcon(Icons.instance().get_icon("add_batch"))
        add_batch_btn.clicked.connect(self.add_batch.emit)
        layout.addWidget(add_batch_btn)

        # 编辑批次按钮
        edit_batch_btn = QPushButton("编辑批次")
        edit_batch_btn.setIcon(Icons.instance().get_icon("edit"))
        edit_batch_btn.clicked.connect(self.edit_batch.emit)
        layout.addWidget(edit_batch_btn)

        # 删除批次按钮
        delete_batch_btn = QPushButton("删除批次")
        delete_batch_btn.setIcon(Icons.instance().get_icon("delete"))
        delete_batch_btn.clicked.connect(self.delete_batch.emit)
        layout.addWidget(delete_batch_btn)

        layout.addStretch()

        # 搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索批次...")
        self.search_input.textChanged.connect(self.search_text_changed.emit)
        layout.addWidget(self.search_input)

        # 刷新按钮
        refresh_btn = QPushButton()
        refresh_btn.setIcon(Icons.instance().get_icon("refresh"))
        refresh_btn.setToolTip("刷新")
        refresh_btn.clicked.connect(self.refresh_data.emit)
        layout.addWidget(refresh_btn)

        self.setLayout(layout)
