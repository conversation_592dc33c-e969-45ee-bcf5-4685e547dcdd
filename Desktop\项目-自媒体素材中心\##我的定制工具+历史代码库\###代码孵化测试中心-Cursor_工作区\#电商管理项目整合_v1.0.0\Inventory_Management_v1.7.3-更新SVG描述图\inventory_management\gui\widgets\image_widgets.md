# 图片处理组件 (image_widgets.py)

## 功能概述
`image_widgets.py` 实现了图片处理相关的自定义控件，包括图片路径代理和预览对话框。这些控件提供了在表格单元格中显示图片预览、处理图片路径、显示大图预览等功能。

## 组件结构

### ImagePathDelegate 类
```python
class ImagePathDelegate(QStyledItemDelegate):
    """图片路径代理控件，继承自 QStyledItemDelegate"""
```

#### 核心方法
1. **paint(painter, option, index)**
   - 在表格单元格中绘制图片预览
   - 处理图片缩放和对齐
   - 显示图片路径文本
   - 优化绘制性能

2. **createEditor(parent, option, index)**
   - 创建路径编辑器
   - 提供文件选择功能
   - 验证图片格式

3. **setEditorData(editor, index)**
   - 设置当前图片路径
   - 更新编辑器状态

4. **setModelData(editor, model, index)**
   - 保存修改后的图片路径
   - 触发数据更新

### PreviewDialog 类
```python
class PreviewDialog(QDialog):
    """图片预览对话框，继承自 QDialog"""
```

#### 核心功能
1. **界面设置**
   - 图片显示区域
   - 缩放控制按钮
   - 关闭按钮

2. **图片操作**
   - 加载图片
   - 缩放控制
   - 适应窗口大小
   - 原始大小显示

3. **快捷键支持**
   - ESC关闭预览
   - 方向键浏览
   - 缩放快捷键

## 使用示例
```python
# 在表格中使用图片路径代理
table = QTableWidget()
delegate = ImagePathDelegate(table)
table.setItemDelegateForColumn(image_column, delegate)

# 显示图片预览对话框
preview = PreviewDialog(parent)
preview.load_image(image_path)
preview.exec_()
```

## 依赖关系
- PyQt5 组件
  - QtWidgets
  - QtGui
  - QtCore
- PIL (Python Imaging Library)
- 错误处理 (utils.error_handler)

## 性能优化
1. **图片缓存**
   - 缓存预览图片
   - 延迟加载机制
   - 内存使用优化

2. **绘制优化**
   - 使用QPixmapCache
   - 避免频繁重绘
   - 图片尺寸限制

3. **资源管理**
   - 及时释放图片资源
   - 控制内存使用
   - 异步加载大图

## 注意事项
1. **图片处理**
   - 支持的图片格式
   - 图片大小限制
   - 路径验证

2. **内存管理**
   - 大图片处理
   - 缓存清理
   - 资源释放

3. **用户体验**
   - 加载进度提示
   - 错误信息反馈
   - 操作响应及时

## 最佳实践
1. **图片加载**
   ```python
   # 推荐的图片加载方式
   try:
       preview.load_image(image_path)
       preview.show()
   except Exception as e:
       ErrorHandler.handle_error(e)
   ```

2. **代理设置**
   ```python
   # 设置图片列的代理
   delegate = ImagePathDelegate(table)
   delegate.set_preview_size(100, 100)  # 设置预览大小
   table.setItemDelegateForColumn(image_column, delegate)
   ```

3. **性能优化**
   ```python
   # 启用缓存
   delegate.enable_cache(True)
   delegate.set_cache_limit(100)  # 设置缓存数量限制
   ``` 