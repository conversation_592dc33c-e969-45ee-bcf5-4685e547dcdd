from PyQt6.QtWidgets import (
    QStyledItemDelegate,
    QDialog,
    QVBoxLayout,
    QLabel,
    QPushButton,
    QStyle,
    QMessageBox,
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QPixmap, QPainter
from database.db_utils import get_connection
from database.image_utils import get_image_path
import logging
import os


class ImagePathDelegate(QStyledItemDelegate):
    """图片路径单元格代理，用于显示图片预览"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.thumbnail_size = 64  # 缩略图大小
        self.parent_table = parent
        self.show_thumbnails = True  # 默认显示缩略图

    def set_show_thumbnails(self, show):
        """设置是否显示缩略图"""
        self.show_thumbnails = show

    def createEditor(self, parent, option, index):
        """禁用编辑"""
        return None

    def paint(self, painter, option, index):
        if not index.isValid():
            return super().paint(painter, option, index)

        # 获取商品ID和图片路径
        try:
            product_id = index.model().index(index.row(), 0).data()  # 获取商品ID
            if not product_id:
                self.paint_no_image(painter, option)
                return

            # 获取主图ID和路径
            with get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT image_id, image_path 
                    FROM product_images 
                    WHERE product_id = ? AND is_primary = 1
                    """,
                    (product_id,),
                )
                result = cursor.fetchone()

            if not result:
                self.paint_no_image(painter, option)
                return

            image_id, image_path = result

            # 绘制选中状态背景
            if option.state & QStyle.StateFlag.State_Selected:
                painter.fillRect(option.rect, option.palette.highlight())

            if self.show_thumbnails:
                # 显示缩略图模式
                if image_path and os.path.exists(image_path):
                    try:
                        # 加载并缩放图片
                        pixmap = QPixmap(image_path)
                        if not pixmap.isNull():
                            scaled_pixmap = pixmap.scaled(
                                self.thumbnail_size,
                                self.thumbnail_size,
                                Qt.AspectRatioMode.KeepAspectRatio,
                                Qt.TransformationMode.SmoothTransformation,
                            )

                            # 计算绘制位置（居中）
                            x = (
                                option.rect.x()
                                + (option.rect.width() - scaled_pixmap.width()) // 2
                            )
                            y = (
                                option.rect.y()
                                + (option.rect.height() - scaled_pixmap.height()) // 2
                            )

                            # 绘制缩略图
                            painter.drawPixmap(x, y, scaled_pixmap)
                        else:
                            self.paint_no_image(painter, option)
                    except:
                        self.paint_no_image(painter, option)
                else:
                    self.paint_no_image(painter, option)
            else:
                # 不显示缩略图模式，只显示是否有图片
                if image_path and os.path.exists(image_path):
                    text = "有图片"
                else:
                    text = "无图片"
                painter.drawText(option.rect, Qt.AlignmentFlag.AlignCenter, text)

        except Exception as e:
            logging.exception("绘制图片预览失败")
            self.paint_no_image(painter, option)

    def paint_no_image(self, painter, option):
        """绘制无图片状态"""
        painter.drawText(option.rect, Qt.AlignmentFlag.AlignCenter, "无图片")

    def editorEvent(self, event, model, option, index):
        """处理鼠标事件"""
        if (
            event.type() == event.Type.MouseButtonPress
            and event.button() == Qt.MouseButton.LeftButton
        ):
            try:
                product_id = model.index(index.row(), 0).data()
                if not product_id:
                    return True

                # 获取主图ID
                with get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        """
                        SELECT image_id, image_path 
                        FROM product_images 
                        WHERE product_id = ? AND is_primary = 1
                        """,
                        (product_id,),
                    )
                    result = cursor.fetchone()

                if not result:
                    QMessageBox.warning(None, "错误", "该商品没有图片")
                    return True

                image_id, image_path = result
                preview_path = get_image_path(product_id, image_id, preview=True)

                if preview_path and os.path.exists(preview_path):
                    preview = PreviewDialog(preview_path, self.parent_table)
                    preview.exec()
                else:
                    QMessageBox.warning(None, "错误", "无法加载预览图")
            except Exception as e:
                logging.exception("显示图片预览失败")
                QMessageBox.warning(None, "错误", f"显示图片预览失败: {str(e)}")
            return True
        return super().editorEvent(event, model, option, index)

    def sizeHint(self, option, index):
        """返回单元格建议大小"""
        if self.show_thumbnails:
            return QSize(self.thumbnail_size + 10, self.thumbnail_size + 25)
        else:
            return QSize(80, 30)  # 不显示缩略图时使用较小的单元格


class PreviewDialog(QDialog):
    """图片预览对话框"""

    def __init__(self, image_path, parent=None):
        super().__init__(parent)
        self.image_path = image_path
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("图片预览")
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.WindowStaysOnTopHint)

        layout = QVBoxLayout()

        # 图片标签
        self.image_label = QLabel()
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 加载预览图
        pixmap = QPixmap(self.image_path)
        if not pixmap.isNull():
            self.image_label.setPixmap(pixmap)
            self.resize(pixmap.width() + 40, pixmap.height() + 80)
        else:
            self.image_label.setText("无法加载预览图")

        # 关闭按钮
        close_btn = QPushButton("关闭预览")
        close_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        close_btn.clicked.connect(self.close)

        layout.addWidget(self.image_label)
        layout.addWidget(close_btn, alignment=Qt.AlignmentFlag.AlignCenter)
        self.setLayout(layout)
        self.center()

    def center(self):
        """窗口居中显示"""
        screen = self.screen().geometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2, (screen.height() - size.height()) // 2
        )

    def keyPressEvent(self, event):
        """按ESC键关闭预览"""
        if event.key() == Qt.Key.Key_Escape:
            self.close()
        else:
            super().keyPressEvent(event)
