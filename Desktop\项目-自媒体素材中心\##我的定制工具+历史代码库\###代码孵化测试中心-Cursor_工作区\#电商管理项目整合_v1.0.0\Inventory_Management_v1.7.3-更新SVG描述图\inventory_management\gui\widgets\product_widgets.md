# 商品管理组件 (product_widgets.py)

## 功能概述
`product_widgets.py` 实现了商品管理相关的自定义控件，包括商品表格和工具栏。这些控件提供了商品数据的显示、过滤、编辑等功能，以及相关的用户交互界面。

## 组件结构

### ProductTable 类
```python
class ProductTable(QTableWidget):
    """商品表格控件，继承自 QTableWidget"""
```

#### 信号定义
- `product_selected(str)`: 商品选中信号，参数为商品ID
- `product_double_clicked(str)`: 商品双击信号，参数为商品ID

#### 核心方法
1. **setup_table()**
   - 设置表格基本属性（21列）
   - 配置列标题（商品ID、名称、数量等）
   - 设置选择模式和交互行为
   - 配置右键菜单

2. **load_products(products)**
   - 加载商品数据到表格
   - 清空现有数据
   - 填充新数据
   - 自动调整列宽

3. **show_context_menu(pos)**
   - 显示右键菜单
   - 提供编辑、删除、图片管理等选项
   - 触发相应的操作信号

4. **filter_products(text, category=None, batch=None)**
   - 根据搜索文本过滤商品列表
   - 支持分类过滤
   - 支持批次过滤
   - 动态显示/隐藏行

### ProductToolBar 类
```python
class ProductToolBar(QWidget):
    """商品工具栏控件，继承自 QWidget"""
```

#### 信号定义
- `add_clicked()`: 添加商品信号
- `edit_clicked()`: 编辑商品信号
- `delete_clicked()`: 删除商品信号
- `image_clicked()`: 图片管理信号
- `search_changed(str)`: 搜索文本变更信号
- `category_changed(str)`: 分类选择变更信号
- `batch_changed(str)`: 批次选择变更信号
- `refresh_clicked()`: 刷新数据信号
- `merge_changed(bool)`: 合并显示状态变更信号

#### 核心功能
1. **工具按钮**
   - 添加商品
   - 编辑商品
   - 删除商品
   - 图片管理
   - 刷新数据

2. **过滤功能**
   - 搜索框（实时过滤）
   - 分类过滤（下拉选择）
   - 批次过滤（下拉选择）

3. **显示控制**
   - 合并显示复选框
   - 默认选中状态
   - 状态变更处理

4. **数据更新**
   - `update_categories()`: 更新分类列表
   - `update_batches()`: 更新批次列表
   - `is_merged()`: 获取当前合并状态

## 使用示例
```python
# 创建商品表格
product_table = ProductTable(parent)
product_table.product_selected.connect(on_product_selected)
product_table.product_double_clicked.connect(on_product_double_clicked)

# 创建工具栏
toolbar = ProductToolBar(parent)
toolbar.add_clicked.connect(show_add_dialog)
toolbar.search_changed.connect(filter_products)
toolbar.category_changed.connect(on_category_changed)
toolbar.batch_changed.connect(on_batch_changed)
toolbar.merge_changed.connect(on_merge_state_changed)
```

## 依赖关系
- PyQt5 组件
- 数据库工具 (database.db_utils)
- 错误处理 (utils.error_handler)
- 图标资源 (:/icons/)

## 注意事项
1. 数据一致性
   - 商品操作后需要及时刷新数据
   - 保持与主窗口状态的同步
   - 确保过滤条件的正确应用

2. 错误处理
   - 所有数据库操作都有异常处理
   - 用户操作有适当的提示和反馈
   - 输入验证和数据检查

3. 性能优化
   - 批量数据加载时使用事务
   - 避免频繁刷新整个表格
   - 优化过滤算法

4. 用户体验
   - 表格列宽自动调整
   - 清晰的操作反馈
   - 直观的过滤机制
   - 合并显示功能的状态保持 