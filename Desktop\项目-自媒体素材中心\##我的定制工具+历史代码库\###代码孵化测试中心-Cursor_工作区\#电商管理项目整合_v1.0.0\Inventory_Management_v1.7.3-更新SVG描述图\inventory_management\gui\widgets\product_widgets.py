from PyQt6.QtWidgets import (
    QTableWidget,
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QLineEdit,
    QComboBox,
    QHeaderView,
    QAbstractItemView,
    QTableWidgetItem,
    QMenu,
    QCheckBox,
    QToolBar,
    QToolButton,
)
from PyQt6.QtCore import pyqtSignal, Qt, QSize
from PyQt6.QtGui import QIcon
from utils.error_handler import ErrorHandler
from database.db_utils import get_connection
from icons import Icons
import logging


class ProductTable(QTableWidget):
    """商品表格控件"""

    # 定义信号
    product_selected = pyqtSignal(str)  # 商品选中信号
    product_double_clicked = pyqtSignal(str)  # 商品双击信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.thumbnail_size = 64  # 缩略图大小
        self.default_row_height = self.thumbnail_size + 16  # 默认行高（缩略图模式）
        self.setup_table()

    def setup_table(self):
        """设置表格基本属性"""
        # 设置列数和表头
        self.setColumnCount(21)
        self.setHorizontalHeaderLabels(
            [
                "商品ID",
                "商品名称",
                "数量",
                "分类",
                "所属批次",
                "位置",
                "供应商",
                "供应商链接",
                "采购链接",
                "进货价",
                "运费",
                "其他成本",
                "总成本",
                "零售价",
                "折扣率",
                "预估利润",
                "采购人员",
                "销售链接",
                "状态",
                "单位",
                "图片路径",  # 移到最后一列
            ]
        )

        # 设置表格属性
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # 设置默认行高
        self.verticalHeader().setDefaultSectionSize(self.default_row_height)

        # 设置列宽
        header = self.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Interactive)

    def adjust_row_heights(self, show_thumbnails):
        """调整所有行的高度"""
        row_height = self.thumbnail_size + 16 if show_thumbnails else 30
        self.verticalHeader().setDefaultSectionSize(row_height)
        for row in range(self.rowCount()):
            self.setRowHeight(row, row_height)

    def load_products(self, products):
        """加载商品数据"""
        try:
            # 清空表格
            self.setRowCount(0)

            # 获取缩略图显示状态
            show_thumbnails = True  # 默认显示缩略图
            if hasattr(self.parent(), "product_toolbar"):
                show_thumbnails = self.parent().product_toolbar.is_showing_thumbnails()

            # 设置行高
            row_height = self.thumbnail_size + 16 if show_thumbnails else 30
            self.verticalHeader().setDefaultSectionSize(row_height)

            # 显示数据
            for product in products:
                row = self.rowCount()
                self.insertRow(row)

                # 重新排列数据，将图片路径放到最后
                data = list(product)
                if len(data) >= 19:  # 确保有足够的数据
                    image_path = data[18]  # 保存图片路径
                    data[18] = data[19]  # 状态
                    data[19] = data[20]  # 单位
                    data[20] = image_path  # 图片路径放到最后

                for col, value in enumerate(data):
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    self.setItem(row, col, item)

            # 调整列宽
            self.resizeColumnsToContents()

        except Exception as e:
            ErrorHandler.handle_error(e)

    def show_context_menu(self, pos):
        """显示右键菜单"""
        menu = QMenu(self)

        edit_action = menu.addAction("编辑商品")
        delete_action = menu.addAction("删除商品")
        menu.addSeparator()
        image_action = menu.addAction("管理图片")

        action = menu.exec(self.mapToGlobal(pos))

        if not action:
            return

        row = self.currentRow()
        if row < 0:
            return

        product_id = self.item(row, 0).text()

        if action == edit_action:
            self.product_double_clicked.emit(product_id)
        elif action == delete_action:
            self.delete_product(product_id)
        elif action == image_action:
            self.manage_images(product_id)

    def filter_products(self, text, category=None, batch=None):
        """过滤商品列表"""
        for row in range(self.rowCount()):
            show_row = True

            # 分类过滤
            if category and category != "所有分类":
                item = self.item(row, 3)  # 分类列
                if not item or item.text() != category:
                    show_row = False

            # 批次过滤
            if batch and batch != "所有批次" and show_row:
                item = self.item(row, 4)  # 批次列
                if not item or batch not in item.text().split(", "):
                    show_row = False

            # 搜索文本过滤
            if text and show_row:
                found = False
                # 在ID、名称、分类、批次、位置、供应商列中搜索
                for col in [0, 1, 3, 4, 5, 6]:
                    item = self.item(row, col)
                    if item and text.lower() in item.text().lower():
                        found = True
                        break
                if not found:
                    show_row = False

            self.setRowHidden(row, not show_row)


class ProductToolBar(QToolBar):
    """商品工具栏控件"""

    # 定义信号
    add_clicked = pyqtSignal()
    edit_clicked = pyqtSignal()
    delete_clicked = pyqtSignal()
    image_clicked = pyqtSignal()
    search_changed = pyqtSignal(str)
    category_changed = pyqtSignal(str)
    batch_changed = pyqtSignal(str)
    refresh_clicked = pyqtSignal()
    merge_changed = pyqtSignal(bool)  # 合并状态变更信号
    thumbnail_changed = pyqtSignal(bool)  # 缩略图显示状态变更信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMovable(False)
        self.setFloatable(False)
        self.setIconSize(QSize(24, 24))
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)

        # 获取图标实例
        icons = Icons.instance()

        # 添加商品按钮
        self.add_button = QToolButton()
        self.add_button.setIcon(icons.get_icon("add_product"))
        self.add_button.setText("添加商品")
        self.add_button.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        self.addWidget(self.add_button)

        # 编辑商品按钮
        self.edit_button = QToolButton()
        self.edit_button.setIcon(icons.get_icon("edit"))
        self.edit_button.setText("编辑商品")
        self.edit_button.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        self.addWidget(self.edit_button)

        # 删除商品按钮
        self.delete_button = QToolButton()
        self.delete_button.setIcon(icons.get_icon("delete"))
        self.delete_button.setText("删除商品")
        self.delete_button.setToolButtonStyle(
            Qt.ToolButtonStyle.ToolButtonTextUnderIcon
        )
        self.addWidget(self.delete_button)

        # 图片管理按钮
        self.image_button = QToolButton()
        self.image_button.setIcon(icons.get_icon("image"))
        self.image_button.setText("图片管理")
        self.image_button.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        self.addWidget(self.image_button)

        self.addSeparator()

        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索商品...")
        self.search_edit.setFixedWidth(200)
        self.addWidget(self.search_edit)

        # 分类过滤
        self.category_combo = QComboBox()
        self.category_combo.addItem("所有分类")
        self.category_combo.setFixedWidth(120)
        self.addWidget(self.category_combo)

        # 批次过滤
        self.batch_combo = QComboBox()
        self.batch_combo.addItem("所有批次")
        self.batch_combo.setFixedWidth(120)
        self.addWidget(self.batch_combo)

        self.addSeparator()

        # 刷新按钮
        self.refresh_button = QToolButton()
        self.refresh_button.setIcon(icons.get_icon("refresh"))
        self.refresh_button.setText("刷新")
        self.refresh_button.setToolButtonStyle(
            Qt.ToolButtonStyle.ToolButtonTextUnderIcon
        )
        self.addWidget(self.refresh_button)

        # 缩略图开关
        self.thumbnail_button = QToolButton()
        self.thumbnail_button.setIcon(icons.get_icon("image"))
        self.thumbnail_button.setText("显示缩略图")
        self.thumbnail_button.setCheckable(True)
        self.thumbnail_button.setChecked(True)  # 默认显示缩略图
        self.thumbnail_button.setToolButtonStyle(
            Qt.ToolButtonStyle.ToolButtonTextUnderIcon
        )
        self.addWidget(self.thumbnail_button)

        # 合并显示开关
        self.merge_check = QCheckBox("合并显示")
        self.addWidget(self.merge_check)

        # 连接信号
        self.add_button.clicked.connect(lambda: self.add_clicked.emit())
        self.edit_button.clicked.connect(lambda: self.edit_clicked.emit())
        self.delete_button.clicked.connect(lambda: self.delete_clicked.emit())
        self.image_button.clicked.connect(lambda: self.image_clicked.emit())
        self.search_edit.textChanged.connect(
            lambda text: self.search_changed.emit(text)
        )
        self.category_combo.currentTextChanged.connect(
            lambda text: self.category_changed.emit(text)
        )
        self.batch_combo.currentTextChanged.connect(
            lambda text: self.batch_changed.emit(text)
        )
        self.refresh_button.clicked.connect(lambda: self.refresh_clicked.emit())
        self.merge_check.stateChanged.connect(
            lambda state: self.merge_changed.emit(state == Qt.CheckState.Checked)
        )
        self.thumbnail_button.clicked.connect(
            lambda checked: self.thumbnail_changed.emit(checked)
        )

    def _on_merge_state_changed(self, state):
        """处理合并状态变化"""
        self.merge_changed.emit(state == Qt.CheckState.Checked)

    def is_merged(self):
        """获取当前合并状态"""
        return self.merge_check.isChecked()

    def update_categories(self, categories):
        """更新分类列表"""
        self.category_combo.clear()
        self.category_combo.addItem("所有分类")
        self.category_combo.addItems(categories)

    def update_batches(self, batches):
        """更新批次列表"""
        self.batch_combo.clear()
        self.batch_combo.addItem("所有批次")
        self.batch_combo.addItems(batches)

    def is_showing_thumbnails(self):
        """获取当前缩略图显示状态"""
        return self.thumbnail_button.isChecked()
