# 图标资源管理模块 (icons.py)

## 功能概述
`icons.py` 实现了一个单例模式的图标资源管理器,用于统一管理和提供应用程序中使用的各种图标资源。该模块基于 PyQt5 的图标系统,支持系统标准图标和自定义图标。

## 类定义

### Icons 类
```python
class Icons:
    """图标资源管理类,使用单例模式"""
    
    _instance = None  # 单例实例
    
    @classmethod
    def instance(cls):
        """
        获取单例实例
        :return: Icons实例
        """
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
```

## 初始化

### 1. 构造函数
```python
def __init__(self):
    """初始化图标管理器"""
    if Icons._instance is not None:
        raise Exception("Icons is a singleton class")
        
    # 图标目录
    self.ICON_DIR = os.path.join(os.path.dirname(__file__), "icons")
    
    # 确保图标目录存在
    if not os.path.exists(self.ICON_DIR):
        os.makedirs(self.ICON_DIR)
        
    # 获取系统样式
    self._style = QApplication.style()
```

## 图标管理

### 1. 图标映射
```python
def get_icon(self, name):
    """
    获取图标
    :param name: 图标名称
    :return: QIcon对象
    """
    icon_map = {
        "add_product": QStyle.SP_FileIcon,
        "add_batch": QStyle.SP_DirIcon,
        "import": QStyle.SP_ArrowDown,
        "export": QStyle.SP_ArrowUp,
        "database": QStyle.SP_DriveHDIcon,
        "finance": QStyle.SP_FileDialogInfoView,
        "refresh": QStyle.SP_BrowserReload,
        "image": QStyle.SP_FileDialogDetailedView,
        "edit": QStyle.SP_FileDialogContentsView,
        "delete": QStyle.SP_TrashIcon
    }
    
    if name in icon_map:
        return self._style.standardIcon(icon_map[name])
    return QIcon()
```

### 2. 支持的图标
- add_product: 添加商品图标
- add_batch: 添加批次图标
- import: 导入图标
- export: 导出图标
- database: 数据库图标
- finance: 财务图标
- refresh: 刷新图标
- image: 图片图标
- edit: 编辑图标
- delete: 删除图标

## 使用方法

### 1. 获取图标实例
```python
# 获取图标管理器实例
icons = Icons.instance()

# 获取特定图标
add_icon = icons.get_icon("add_product")
edit_icon = icons.get_icon("edit")
delete_icon = icons.get_icon("delete")
```

### 2. 在界面中使用
```python
# 在按钮中使用
button = QPushButton()
button.setIcon(Icons.instance().get_icon("add_product"))

# 在菜单项中使用
action = QAction("添加商品")
action.setIcon(Icons.instance().get_icon("add_product"))

# 在工具栏中使用
toolbar = QToolBar()
toolbar.addAction(QAction(Icons.instance().get_icon("refresh"), "刷新"))
```

## 依赖关系

### 1. PyQt5 组件
- QIcon
- QStyle
- QApplication

### 2. 系统模块
- os (路径处理)

## 扩展功能

### 1. 自定义图标支持
```python
def load_custom_icon(self, name, path):
    """
    加载自定义图标
    :param name: 图标名称
    :param path: 图标文件路径
    :return: 是否加载成功
    """
    if os.path.exists(path):
        self._custom_icons[name] = QIcon(path)
        return True
    return False
```

### 2. 图标缓存
```python
def clear_cache(self):
    """清除图标缓存"""
    self._icon_cache.clear()
```

## 注意事项
1. 单例模式的正确使用
2. 图标资源的可用性
3. 系统图标的兼容性
4. 自定义图标的加载
5. 资源释放的及时性 