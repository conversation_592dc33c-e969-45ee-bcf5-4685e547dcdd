import os
from PyQt6.QtGui import QIcon
from PyQt6.QtWidgets import QStyle, QApplication


class Icons:
    """图标资源类"""

    _instance = None

    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        if Icons._instance is not None:
            raise Exception("Icons is a singleton class")

        # 图标目录
        self.ICON_DIR = os.path.join(os.path.dirname(__file__), "icons")

        # 确保图标目录存在
        if not os.path.exists(self.ICON_DIR):
            os.makedirs(self.ICON_DIR)

        # 获取系统样式
        self._style = QApplication.style()

    def get_icon(self, name):
        """获取图标"""
        icon_map = {
            "add_product": QStyle.StandardPixmap.SP_FileIcon,
            "add_batch": QStyle.StandardPixmap.SP_DirIcon,
            "import": QStyle.StandardPixmap.SP_ArrowDown,
            "export": QStyle.StandardPixmap.SP_ArrowUp,
            "database": QStyle.StandardPixmap.SP_DriveHDIcon,
            "finance": QStyle.StandardPixmap.SP_FileDialogInfoView,
            "refresh": QStyle.StandardPixmap.SP_BrowserReload,
            "image": QStyle.StandardPixmap.SP_FileDialogDetailedView,
            "edit": QStyle.StandardPixmap.SP_FileDialogContentsView,
            "delete": QStyle.StandardPixmap.SP_TrashIcon,
        }

        if name in icon_map:
            return self._style.standardIcon(icon_map[name])
        return QIcon()
