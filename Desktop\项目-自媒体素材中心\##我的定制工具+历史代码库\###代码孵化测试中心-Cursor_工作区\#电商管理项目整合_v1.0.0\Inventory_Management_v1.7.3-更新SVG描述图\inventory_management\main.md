# 主程序入口模块 (main.py)

## 功能概述
`main.py` 是系统的主程序入口文件,负责启动应用程序并处理命令行参数。该模块提供了一个简单的命令行界面,用于直接启动应用程序或执行特定的管理任务。

## 功能实现

### 1. 命令行参数处理
```python
def parse_args():
    """处理命令行参数"""
    parser = argparse.ArgumentParser(description="库存管理系统")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--db", help="指定数据库文件路径")
    parser.add_argument("--log", help="指定日志文件路径")
    return parser.parse_args()
```

### 2. 日志配置
```python
def setup_logging(log_file=None):
    """
    配置日志系统
    :param log_file: 日志文件路径
    """
    if not os.path.exists("logs"):
        os.makedirs("logs")
        
    log_file = log_file or "logs/app.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(log_file, encoding="utf-8"),
            logging.StreamHandler(sys.stdout)
        ]
    )
```

### 3. 环境检查
```python
def check_environment():
    """检查运行环境"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 6):
            raise RuntimeError("需要Python 3.6或更高版本")
            
        # 检查必要的目录
        required_dirs = ["logs", "database", "images"]
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                os.makedirs(dir_name)
                
        # 检查数据库
        if not os.path.exists(DATABASE_PATH):
            raise RuntimeError("数据库文件不存在")
            
        return True
        
    except Exception as e:
        logging.error(f"环境检查失败: {str(e)}")
        return False
```

### 4. 主函数
```python
def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_args()
        
        # 配置日志
        setup_logging(args.log)
        
        # 检查环境
        if not check_environment():
            sys.exit(1)
            
        # 设置数据库
        if args.db:
            os.environ["DATABASE_PATH"] = args.db
            
        # 设置调试模式
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            
        # 启动应用程序
        from app import run_app
        sys.exit(run_app())
        
    except Exception as e:
        logging.exception("程序启动失败")
        print(f"错误: {str(e)}")
        sys.exit(1)
```

## 使用方法

### 1. 直接启动
```bash
python main.py
```

### 2. 使用命令行参数
```bash
# 启用调试模式
python main.py --debug

# 指定数据库文件
python main.py --db path/to/database.db

# 指定日志文件
python main.py --log path/to/logfile.log
```

## 依赖关系

### 1. 系统模块
- os
- sys
- logging
- argparse

### 2. 自定义模块
- app (run_app 函数)
- database.db_utils (DATABASE_PATH 常量)

## 注意事项
1. 环境检查的完整性
2. 命令行参数的正确性
3. 日志配置的有效性
4. 错误处理的可靠性
5. 数据库路径的正确性 