import sys
import os
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from app import run_app

if __name__ == "__main__":
    try:
        print("=" * 50)
        print("程序开始启动...")
        print("=" * 50)

        # 运行应用程序
        ret = run_app()

        print("=" * 50)
        print(f"程序正常退出，返回值: {ret}")
        print("=" * 50)

    except Exception as e:
        print("=" * 50)
        print("程序发生错误:")
        print("-" * 30)
        print(f"错误类型: {type(e).__name__}")
        print(f"错误信息: {str(e)}")
        print("-" * 30)
        print("详细错误信息:")
        traceback.print_exc()
        print("=" * 50)

    finally:
        print("\n按回车键退出...")
        input()
