# 数据模型目录 (models)

## 目录概述
`models` 目录包含了系统的所有数据模型定义，这些模型定义了系统中各种实体的数据结构和业务逻辑。

## 文件列表

### product.py
商品模型，定义商品的属性和相关操作。

### batch.py
批次模型，管理商品批次信息。

### transaction.py
交易模型，处理商品交易记录。

### __init__.py
包初始化文件，定义包的公共接口。

## 模型说明

### 1. 商品模型 (Product)
- 基本信息：ID、名称、类别
- 库存信息：数量、单位
- 价格信息：成本、售价
- 供应商信息
- 图片管理
- 状态管理

### 2. 批次模型 (Batch)
- 批次信息：ID、名称
- 商品关联
- 时间管理
- 备注信息

### 3. 交易模型 (Transaction)
- 交易记录
- 价格变动
- 库存变化
- 财务影响

## 数据关系
1. 商品-批次：一对多关系
2. 商品-交易：一对多关系
3. 批次-交易：一对多关系

## 依赖关系
- 数据库模块：`database.db_utils`
- 工具模块：`utils.error_handler`
- Python 标准库：
  - datetime：时间处理
  - decimal：精确数值计算
  - typing：类型注解

## 使用说明
1. 创建实例：
   ```python
   product = Product(name="示例商品", category="电子产品")
   batch = Batch(name="2024年第一批")
   transaction = Transaction(product_id="PROD001", type="IN")
   ```

2. 数据验证：
   - 所有模型都包含数据验证
   - 使用类型注解确保类型安全
   - 业务规则验证

3. 关系处理：
   - 使用外键保持数据完整性
   - 级联操作处理相关数据

## 开发指南
1. 新增模型规范：
   - 继承基础模型类
   - 实现必要的接口
   - 添加完整的文档
   - 包含数据验证

2. 代码风格：
   - 使用类型注解
   - 遵循 PEP 8
   - 添加详细注释

3. 测试要求：
   - 单元测试覆盖
   - 集成测试验证
   - 边界条件测试

# 模型模块

## 概述
模型模块定义了系统中的核心数据模型，包括产品、批次、交易等实体类，以及它们之间的关系。

## 模型定义

### 1. 产品模型
```python
class Product:
    def __init__(self, id=None, name=None, code=None):
        self.id = id
        self.name = name
        self.code = code
        self.category = None
        self.description = None
        self.image_path = None
        self.created_at = None
        self.updated_at = None
        
    def validate(self):
        if not self.name:
            raise ValueError("产品名称不能为空")
        if not self.code:
            raise ValueError("产品编码不能为空")
```

### 2. 批次模型
```python
class Batch:
    def __init__(self, id=None, code=None):
        self.id = id
        self.code = code
        self.name = None
        self.status = 'active'
        self.created_at = None
        self.updated_at = None
        self.products = []
        
    def add_product(self, product, quantity, price):
        self.products.append({
            'product': product,
            'quantity': quantity,
            'price': price
        })
```

### 3. 交易模型
```python
class Transaction:
    def __init__(self, id=None):
        self.id = id
        self.product_id = None
        self.batch_id = None
        self.type = None
        self.quantity = 0
        self.price = 0.0
        self.created_at = None
        
    def calculate_amount(self):
        return self.quantity * self.price
```

## 数据访问

### 1. 产品操作
```python
class ProductDAO:
    @staticmethod
    def get_by_id(id):
        query = "SELECT * FROM products WHERE id = ?"
        row = db.execute_query(query, (id,))
        return Product(**row) if row else None
        
    @staticmethod
    def save(product):
        if product.id:
            query = """
                UPDATE products 
                SET name = ?, code = ?, category = ?
                WHERE id = ?
            """
            params = (product.name, product.code, 
                     product.category, product.id)
        else:
            query = """
                INSERT INTO products (name, code, category)
                VALUES (?, ?, ?)
            """
            params = (product.name, product.code, 
                     product.category)
        return db.execute_update(query, params)
```

### 2. 批次操作
```python
class BatchDAO:
    @staticmethod
    def get_with_products(id):
        query = """
            SELECT b.*, pb.quantity, pb.price, p.*
            FROM batches b
            LEFT JOIN product_batch pb ON b.id = pb.batch_id
            LEFT JOIN products p ON pb.product_id = p.id
            WHERE b.id = ?
        """
        rows = db.execute_query(query, (id,))
        if not rows:
            return None
            
        batch = Batch()
        for row in rows:
            if row['product_id']:
                product = Product(**row)
                batch.add_product(product, 
                                row['quantity'],
                                row['price'])
        return batch
```

### 3. 交易操作
```python
class TransactionDAO:
    @staticmethod
    def create(transaction):
        query = """
            INSERT INTO transactions 
            (product_id, batch_id, type, quantity, price)
            VALUES (?, ?, ?, ?, ?)
        """
        params = (transaction.product_id,
                 transaction.batch_id,
                 transaction.type,
                 transaction.quantity,
                 transaction.price)
        return db.execute_update(query, params)
```

## 业务逻辑

### 1. 产品管理
```python
class ProductService:
    @staticmethod
    def create_product(data):
        product = Product()
        product.name = data['name']
        product.code = data['code']
        product.validate()
        return ProductDAO.save(product)
        
    @staticmethod
    def update_stock(product_id, quantity):
        product = ProductDAO.get_by_id(product_id)
        if not product:
            raise ValueError("产品不存在")
        # 更新库存
        transaction = Transaction()
        transaction.product_id = product_id
        transaction.quantity = quantity
        TransactionDAO.create(transaction)
```

### 2. 批次管理
```python
class BatchService:
    @staticmethod
    def create_batch(data):
        batch = Batch()
        batch.code = data['code']
        batch.name = data['name']
        return BatchDAO.save(batch)
        
    @staticmethod
    def add_product(batch_id, product_id, quantity, price):
        batch = BatchDAO.get_by_id(batch_id)
        product = ProductDAO.get_by_id(product_id)
        if not batch or not product:
            raise ValueError("批次或产品不存在")
        batch.add_product(product, quantity, price)
        BatchDAO.update_products(batch)
```

## 注意事项
1. 数据验证完整性
2. 事务处理正确性
3. 关联关系维护
4. 性能优化考虑
5. 错误处理规范
6. 代码复用原则
7. 类型注解使用
8. 文档注释完整 