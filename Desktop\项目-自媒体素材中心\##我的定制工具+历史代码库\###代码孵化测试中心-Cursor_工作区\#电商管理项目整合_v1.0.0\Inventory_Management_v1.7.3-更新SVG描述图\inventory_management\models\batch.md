# 批次模型 (batch.py)

## 功能概述
`batch.py` 定义了系统中的批次模型，用于管理商品批次信息。该模型提供了批次的基本属性和数据转换方法，支持批次的创建、管理和查询功能。

## 类定义

### Batch 类

#### 属性
```python
def __init__(self, data=None):
    self.batch_id = data.get("batch_id")       # 批次ID
    self.batch_name = data.get("batch_name")   # 批次名称
    self.remarks = data.get("remarks")         # 备注信息
    self.created_at = data.get("created_at")   # 创建时间
    self.updated_at = data.get("updated_at")   # 更新时间
    self.products = data.get("products", [])   # 关联商品列表
```

#### 核心方法

1. **字典转换**
```python
def to_dict(self):
```
- 功能：将批次对象转换为字典格式
- 返回：包含批次信息的字典
- 字段：
  - batch_id
  - batch_name
  - remarks
  - created_at
  - updated_at

2. **从数据库创建**
```python
@staticmethod
def from_db_row(row, columns):
```
- 功能：从数据库查询结果创建批次对象
- 参数：
  - row: 数据库行数据
  - columns: 列名列表
- 返回：Batch 对象或 None

3. **字符串表示**
```python
def __str__(self):
```
- 功能：返回批次的字符串表示
- 格式：`{batch_id} - {batch_name}`

## 数据结构

### 1. 基本信息
- batch_id：批次唯一标识符
- batch_name：批次名称
- remarks：备注信息

### 2. 时间信息
- created_at：创建时间
- updated_at：更新时间

### 3. 关联信息
- products：关联的商品列表

## 使用示例

### 1. 创建批次
```python
# 创建新批次
batch_data = {
    "batch_name": "2024年第一批",
    "remarks": "春季新品"
}
batch = Batch(batch_data)
```

### 2. 数据转换
```python
# 转换为字典
batch_dict = batch.to_dict()

# 从数据库行创建
db_batch = Batch.from_db_row(row_data, columns)
```

### 3. 批次管理
```python
# 添加商品到批次
batch.products.append(product_id)

# 获取批次信息
print(batch)  # 输出：batch_id - batch_name
```

## 数据规范

### 1. ID 生成
- 使用 UUID 生成批次ID
- 确保唯一性
- 格式统一

### 2. 命名规则
- 批次名称：年份+批次号
- 示例：2024年第一批

### 3. 时间格式
- 使用数据库时间戳
- ISO 格式字符串
- UTC 时间存储

## 关联关系

### 1. 商品关联
- 一个批次可以包含多个商品
- 通过 products 列表管理关联
- 支持批量操作

### 2. 数据库关系
- 批次表：存储批次基本信息
- 关联表：存储批次-商品关系
- 外键约束保证数据完整性

## 最佳实践

### 1. 批次创建
- 生成唯一ID
- 设置必要属性
- 初始化关联列表

### 2. 数据管理
- 及时更新时间戳
- 维护关联关系
- 保持数据一致性

### 3. 批次操作
- 批量添加商品
- 批量更新状态
- 事务处理

## 注意事项

1. 数据验证
   - 批次名称唯一性
   - 必填字段检查
   - 关联数据验证

2. 性能优化
   - 批量操作优化
   - 关联查询优化
   - 索引使用

3. 数据安全
   - 事务完整性
   - 并发处理
   - 数据备份

## 扩展建议

1. 功能扩展
   - 批次状态管理
   - 批次分类功能
   - 批次标签系统

2. 数据分析
   - 批次统计报表
   - 性能分析
   - 趋势分析

3. 接口优化
   - 批量操作接口
   - 查询优化
   - API 设计 