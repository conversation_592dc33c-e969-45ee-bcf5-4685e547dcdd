import uuid
from datetime import datetime


class Batch:
    def __init__(self, data=None):
        self.batch_id = data.get("batch_id") if data else None
        self.batch_name = data.get("batch_name") if data else None
        self.remarks = data.get("remarks") if data else None
        self.created_at = data.get("created_at") if data else None
        self.updated_at = data.get("updated_at") if data else None
        self.products = data.get("products", []) if data else []

    def to_dict(self):
        """转换为字典"""
        return {
            "batch_id": self.batch_id,
            "batch_name": self.batch_name,
            "remarks": self.remarks,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }

    @staticmethod
    def from_db_row(row, columns):
        """从数据库行创建批次对象"""
        if not row:
            return None
        data = dict(zip(columns, row))
        return Batch(data)

    def __str__(self):
        return f"{self.batch_id} - {self.batch_name}"
