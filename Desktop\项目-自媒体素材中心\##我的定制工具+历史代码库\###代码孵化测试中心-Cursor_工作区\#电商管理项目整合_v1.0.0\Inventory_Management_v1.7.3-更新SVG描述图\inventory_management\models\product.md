# 商品模型 (product.py)

## 功能概述
`product.py` 定义了系统中的商品模型，包含商品的所有属性和相关操作方法。该模型处理商品的基本信息、价格计算、数据验证等功能。

## 类定义

### Product 类

#### 属性
```python
def __init__(self,
    product_id=None,    # 商品ID
    name=None,          # 商品名称
    quantity=1,         # 数量
    category=None,      # 类别
    unit="个",          # 单位
    status="在库",      # 状态
    location=None,      # 位置
    supplier=None,      # 供应商
    supplier_link=None, # 供应商链接
    purchase_link=None, # 采购链接
    purchase_price=0,   # 采购价
    shipping_cost=0,    # 运费
    other_cost=0,       # 其他成本
    total_cost=0,       # 总成本
    selling_price=0,    # 售价
    discount_rate=100,  # 折扣率
    total_profit=0,     # 总利润
    purchaser=None,     # 采购人
    selling_link=None,  # 销售链接
    image_path=None,    # 图片路径
    remarks=None,       # 备注
    created_at=None,    # 创建时间
    updated_at=None     # 更新时间
):
```

#### 核心方法

1. **成本计算**
```python
def calculate_total_cost(self):
```
- 功能：计算商品总成本
- 计算公式：总成本 = 采购价 + 运费 + 其他成本
- 返回：总成本金额

2. **折扣价计算**
```python
def calculate_discounted_price(self):
```
- 功能：计算折后价格
- 计算公式：折后价 = 售价 × (折扣率 / 100)
- 返回：折后价格

3. **利润计算**
```python
def calculate_profit(self):
```
- 功能：计算预估利润
- 计算公式：利润 = 折后价 - 总成本
- 返回：预估利润金额

4. **利润率计算**
```python
def calculate_profit_margin(self):
```
- 功能：计算利润率
- 计算公式：利润率 = (利润 / 折后价) × 100%
- 返回：利润率百分比

#### 数据转换

1. **转换为字典**
```python
def to_dict(self):
```
- 功能：将对象转换为字典格式
- 返回：包含所有属性的字典

2. **从字典创建**
```python
@staticmethod
def from_dict(data):
```
- 功能：从字典创建商品对象
- 参数：data - 包含商品信息的字典
- 返回：Product 对象

3. **从数据库行创建**
```python
@staticmethod
def from_db_row(row, columns):
```
- 功能：从数据库查询结果创建对象
- 参数：
  - row: 数据库行数据
  - columns: 列名列表
- 返回：Product 对象或 None

#### 数据验证
```python
def validate(self):
```
- 功能：验证商品数据有效性
- 验证项目：
  - 名称不能为空
  - 类别不能为空
  - 数量不能为负
  - 价格不能为负
  - 折扣率范围 0-100
- 异常：验证失败时抛出 ValueError

## 使用示例

### 1. 创建商品
```python
# 创建新商品
product = Product(
    name="测试商品",
    category="电子产品",
    purchase_price=100,
    selling_price=150
)

# 验证数据
product.validate()
```

### 2. 价格计算
```python
# 计算各项金额
total_cost = product.calculate_total_cost()
discounted_price = product.calculate_discounted_price()
profit = product.calculate_profit()
margin = product.calculate_profit_margin()
```

### 3. 数据转换
```python
# 转换为字典
product_dict = product.to_dict()

# 从字典创建对象
new_product = Product.from_dict(product_dict)

# 从数据库行创建对象
db_product = Product.from_db_row(row_data, columns)
```

## 数据规范

### 1. 价格字段
- 使用浮点数表示
- 默认值为0
- 不允许负数

### 2. 状态字段
- 默认值："在库"
- 可选值：在库、在售、已售、缺货等

### 3. 时间字段
- created_at：创建时间
- updated_at：最后更新时间
- 使用数据库时间戳

## 注意事项

1. 数值计算
   - 使用 float 类型
   - 注意精度问题
   - 处理零值情况

2. 数据验证
   - 必填字段检查
   - 数值范围验证
   - 格式验证

3. 数据转换
   - 类型转换安全
   - 空值处理
   - 格式统一

## 最佳实践

1. 创建商品
   - 设置必要属性
   - 进行数据验证
   - 计算相关金额

2. 更新商品
   - 更新相关字段
   - 重新计算金额
   - 更新时间戳

3. 价格管理
   - 统一价格计算
   - 保持数据一致
   - 记录价格变动 