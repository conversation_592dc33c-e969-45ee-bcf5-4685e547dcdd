class Product:
    """商品类"""

    def __init__(
        self,
        product_id=None,
        name=None,
        quantity=1,
        category=None,
        unit="个",
        status="在库",
        location=None,
        supplier=None,
        supplier_link=None,
        purchase_link=None,
        purchase_price=0,
        shipping_cost=0,
        other_cost=0,
        total_cost=0,
        selling_price=0,
        discount_rate=100,
        total_profit=0,
        purchaser=None,
        selling_link=None,
        image_path=None,
        remarks=None,
        created_at=None,
        updated_at=None,
    ):
        self.product_id = product_id
        self.name = name
        self.quantity = quantity
        self.category = category
        self.unit = unit
        self.status = status
        self.location = location
        self.supplier = supplier
        self.supplier_link = supplier_link
        self.purchase_link = purchase_link
        self.purchase_price = purchase_price
        self.shipping_cost = shipping_cost
        self.other_cost = other_cost
        self.total_cost = total_cost
        self.selling_price = selling_price
        self.discount_rate = discount_rate
        self.total_profit = total_profit
        self.purchaser = purchaser
        self.selling_link = selling_link
        self.image_path = image_path
        self.remarks = remarks
        self.created_at = created_at
        self.updated_at = updated_at

    def calculate_total_cost(self):
        """计算总成本"""
        self.total_cost = (
            float(self.purchase_price or 0)
            + float(self.shipping_cost or 0)
            + float(self.other_cost or 0)
        )
        return self.total_cost

    def calculate_discounted_price(self):
        """计算折后价"""
        selling_price = float(self.selling_price or 0)
        discount_rate = float(self.discount_rate or 100) / 100
        return selling_price * discount_rate

    def calculate_profit(self):
        """计算预估利润"""
        discounted_price = self.calculate_discounted_price()
        total_cost = self.calculate_total_cost()
        self.total_profit = discounted_price - total_cost
        return self.total_profit

    def calculate_profit_margin(self):
        """计算利润率"""
        discounted_price = self.calculate_discounted_price()
        if discounted_price > 0:
            return (self.calculate_profit() / discounted_price) * 100
        return 0

    def to_dict(self):
        """转换为字典"""
        return {
            "product_id": self.product_id,
            "name": self.name,
            "quantity": self.quantity,
            "category": self.category,
            "unit": self.unit,
            "status": self.status,
            "location": self.location,
            "supplier": self.supplier,
            "supplier_link": self.supplier_link,
            "purchase_link": self.purchase_link,
            "purchase_price": self.purchase_price,
            "shipping_cost": self.shipping_cost,
            "other_cost": self.other_cost,
            "total_cost": self.total_cost,
            "selling_price": self.selling_price,
            "discount_rate": self.discount_rate,
            "total_profit": self.total_profit,
            "purchaser": self.purchaser,
            "selling_link": self.selling_link,
            "image_path": self.image_path,
            "remarks": self.remarks,
        }

    @staticmethod
    def from_dict(data):
        """从字典创建对象"""
        return Product(**data)

    @staticmethod
    def from_db_row(row, columns):
        """从数据库行创建对象"""
        if not row:
            return None
        data = dict(zip(columns, row))
        return Product.from_dict(data)

    def validate(self):
        """验证数据有效性"""
        if not self.name:
            raise ValueError("商品名称不能为空")
        if not self.category:
            raise ValueError("商品分类不能为空")
        if self.quantity < 0:
            raise ValueError("库存数量不能为负数")
        if self.purchase_price < 0:
            raise ValueError("采购价不能为负数")
        if self.shipping_cost < 0:
            raise ValueError("运费不能为负数")
        if self.other_cost < 0:
            raise ValueError("其他成本不能为负数")
        if self.selling_price < 0:
            raise ValueError("销售价不能为负数")
        if not 0 <= self.discount_rate <= 100:
            raise ValueError("折扣率必须在0-100之间")
        return True

    def __str__(self):
        return f"{self.product_id} - {self.name} ({self.category})"
