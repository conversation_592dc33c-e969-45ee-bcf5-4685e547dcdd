# 交易模型 (transaction.py)

## 功能概述
`transaction.py` 定义了系统中的交易模型，用于记录和管理商品的交易信息。该模型包含交易的基本信息、金额计算、数据转换等功能，支持完整的交易流程管理。

## 类定义

### Transaction 类

#### 属性
```python
def __init__(self, data=None):
    self.transaction_id = data.get("transaction_id")     # 交易ID
    self.product_id = data.get("product_id")            # 商品ID
    self.transaction_type = data.get("transaction_type") # 交易类型
    self.quantity = data.get("quantity", 0)             # 数量
    self.unit = data.get("unit")                        # 单位
    self.unit_price = data.get("unit_price", 0)         # 单价
    self.total_price = data.get("total_price", 0)       # 总价
    self.shipping_cost = data.get("shipping_cost", 0)   # 运费
    self.transaction_time = data.get(                   # 交易时间
        "transaction_time", datetime.now()
    )
    self.profit = data.get("profit", 0)                 # 利润
    self.payment_method = data.get("payment_method")    # 支付方式
    self.order_id = data.get("order_id")               # 订单ID
    self.remarks = data.get("remarks")                 # 备注
    self.created_at = data.get("created_at")          # 创建时间
```

#### 核心方法

1. **总价计算**
```python
def calculate_total_price(self):
```
- 功能：计算交易总价
- 计算公式：总价 = 数量 × 单价
- 返回：总价金额

2. **利润计算**
```python
def calculate_profit(self, product_cost):
```
- 功能：计算交易利润
- 参数：product_cost - 商品成本
- 计算公式：利润 = 总价 - (成本 × 数量) - 运费
- 返回：利润金额

3. **数据转换**
```python
def to_dict(self):
```
- 功能：将交易对象转换为字典
- 返回：包含所有交易信息的字典

4. **从数据库创建**
```python
@staticmethod
def from_db_row(row, columns):
```
- 功能：从数据库行创建交易对象
- 参数：
  - row: 数据库行数据
  - columns: 列名列表
- 返回：Transaction 对象或 None

5. **字符串表示**
```python
def __str__(self):
```
- 功能：返回交易的字符串表示
- 格式：`{transaction_id} - {transaction_type} ({quantity} {unit})`

## 数据结构

### 1. 基本信息
- transaction_id：交易唯一标识
- product_id：关联商品ID
- transaction_type：交易类型
- quantity：交易数量
- unit：计量单位

### 2. 金额信息
- unit_price：单价
- total_price：总价
- shipping_cost：运费
- profit：利润

### 3. 其他信息
- transaction_time：交易时间
- payment_method：支付方式
- order_id：订单编号
- remarks：备注信息
- created_at：创建时间

## 使用示例

### 1. 创建交易记录
```python
# 创建新交易
transaction_data = {
    "product_id": "PROD001",
    "transaction_type": "SALE",
    "quantity": 2,
    "unit_price": 100,
    "payment_method": "CASH"
}
transaction = Transaction(transaction_data)
```

### 2. 金额计算
```python
# 计算总价
total = transaction.calculate_total_price()

# 计算利润
profit = transaction.calculate_profit(product_cost=80)
```

### 3. 数据转换
```python
# 转换为字典
transaction_dict = transaction.to_dict()

# 从数据库行创建
db_transaction = Transaction.from_db_row(row_data, columns)
```

## 交易类型

### 1. 销售类型
- SALE：普通销售
- WHOLESALE：批发销售
- RETURN：退货

### 2. 库存类型
- PURCHASE：采购入库
- INVENTORY：库存调整
- LOSS：库存损耗

### 3. 其他类型
- TRANSFER：库存转移
- SAMPLE：样品
- OTHER：其他

## 支付方式

### 1. 线上支付
- ALIPAY：支付宝
- WECHAT：微信支付
- BANK：银行转账

### 2. 线下支付
- CASH：现金
- POS：刷卡
- OTHER：其他方式

## 最佳实践

### 1. 交易创建
- 生成唯一交易ID
- 设置必要属性
- 进行金额计算

### 2. 数据验证
- 检查必填字段
- 验证数值合法性
- 确保关联有效

### 3. 金额处理
- 使用浮点数计算
- 处理精度问题
- 验证计算结果

## 注意事项

1. 数据完整性
   - 必填字段验证
   - 关联数据检查
   - 数值范围验证

2. 金额计算
   - 精度控制
   - 舍入规则
   - 负数处理

3. 时间处理
   - 时区处理
   - 格式统一
   - 有效期验证

## 扩展建议

1. 功能扩展
   - 交易状态管理
   - 批量交易处理
   - 交易撤销功能

2. 数据分析
   - 交易统计
   - 趋势分析
   - 利润报表

3. 接口优化
   - 批量操作
   - 查询优化
   - 导出功能 