from datetime import datetime


class Transaction:
    def __init__(self, data=None):
        self.transaction_id = data.get("transaction_id") if data else None
        self.product_id = data.get("product_id") if data else None
        self.transaction_type = data.get("transaction_type") if data else None
        self.quantity = data.get("quantity", 0)
        self.unit = data.get("unit") if data else None
        self.unit_price = data.get("unit_price", 0)
        self.total_price = data.get("total_price", 0)
        self.shipping_cost = data.get("shipping_cost", 0)
        self.transaction_time = data.get("transaction_time", datetime.now())
        self.profit = data.get("profit", 0)
        self.payment_method = data.get("payment_method") if data else None
        self.order_id = data.get("order_id") if data else None
        self.remarks = data.get("remarks") if data else None
        self.created_at = data.get("created_at") if data else None

    def to_dict(self):
        """转换为字典"""
        return {
            "transaction_id": self.transaction_id,
            "product_id": self.product_id,
            "transaction_type": self.transaction_type,
            "quantity": self.quantity,
            "unit": self.unit,
            "unit_price": self.unit_price,
            "total_price": self.total_price,
            "shipping_cost": self.shipping_cost,
            "transaction_time": self.transaction_time,
            "profit": self.profit,
            "payment_method": self.payment_method,
            "order_id": self.order_id,
            "remarks": self.remarks,
            "created_at": self.created_at,
        }

    def calculate_total_price(self):
        """计算总价"""
        self.total_price = float(self.quantity or 0) * float(self.unit_price or 0)
        return self.total_price

    def calculate_profit(self, product_cost):
        """计算利润"""
        total_cost = float(product_cost or 0) * float(self.quantity or 0)
        self.profit = (
            self.calculate_total_price() - total_cost - float(self.shipping_cost or 0)
        )
        return self.profit

    @staticmethod
    def from_db_row(row, columns):
        """从数据库行创建交易对象"""
        if not row:
            return None
        data = dict(zip(columns, row))
        return Transaction(data)

    def __str__(self):
        return f"{self.transaction_id} - {self.transaction_type} ({self.quantity} {self.unit})"
