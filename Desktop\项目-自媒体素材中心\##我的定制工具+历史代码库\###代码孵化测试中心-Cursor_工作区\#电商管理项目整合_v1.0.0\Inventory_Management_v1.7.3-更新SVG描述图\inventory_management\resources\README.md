# 资源模块

## 概述
资源模块包含了系统使用的所有静态资源，包括图标、样式表、配置文件等。

## 目录结构

### 1. 图标资源
```
icons/
├── app.ico          # 应用程序图标
├── toolbar/         # 工具栏图标
│   ├── add.png     # 添加
│   ├── delete.png  # 删除
│   ├── edit.png    # 编辑
│   └── refresh.png # 刷新
├── status/         # 状态图标
│   ├── error.png   # 错误
│   ├── info.png    # 信息
│   └── warning.png # 警告
└── menu/           # 菜单图标
    ├── import.png  # 导入
    ├── export.png  # 导出
    └── settings.png # 设置
```

### 2. 样式表
```
styles/
├── main.qss        # 主样式表
├── dark.qss        # 暗色主题
└── light.qss       # 亮色主题
```

### 3. 配置文件
```
config/
├── app.ini         # 应用配置
├── database.ini    # 数据库配置
└── logging.ini     # 日志配置
```

## 资源使用

### 1. 图标加载
```python
from PyQt6.QtGui import QIcon
from pathlib import Path

def load_icon(name):
    icon_path = Path(__file__).parent / 'icons' / name
    return QIcon(str(icon_path))
```

### 2. 样式应用
```python
def apply_style(theme='light'):
    style_path = Path(__file__).parent / 'styles' / f'{theme}.qss'
    with open(style_path) as f:
        return f.read()
```

### 3. 配置读取
```python
from configparser import ConfigParser

def load_config(name):
    config = ConfigParser()
    config_path = Path(__file__).parent / 'config' / f'{name}.ini'
    config.read(str(config_path))
    return config
```

## 样式定义

### 1. 主窗口
```qss
QMainWindow {
    background-color: #f0f0f0;
}

QToolBar {
    border: none;
    background: #ffffff;
}

QStatusBar {
    background: #e0e0e0;
}
```

### 2. 对话框
```qss
QDialog {
    background-color: #ffffff;
}

QDialog QLabel {
    color: #333333;
}

QDialog QPushButton {
    background: #2196F3;
    color: white;
    border: none;
    padding: 5px 15px;
}
```

### 3. 表格
```qss
QTableWidget {
    border: 1px solid #d0d0d0;
    background: white;
}

QTableWidget::item {
    padding: 5px;
}

QHeaderView::section {
    background: #f5f5f5;
    padding: 5px;
    border: none;
}
```

## 配置示例

### 1. 应用配置
```ini
[General]
theme = light
language = zh_CN
auto_save = true

[Window]
width = 1024
height = 768
maximized = false
```

### 2. 数据库配置
```ini
[Database]
type = sqlite
path = data/inventory.db
backup_path = backup/
max_connections = 10
```

### 3. 日志配置
```ini
[Logger]
level = INFO
format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
file = logs/app.log
max_size = 1MB
backup_count = 5
```

## 注意事项
1. 资源文件命名规范
2. 样式表模块化
3. 配置分类管理
4. 资源路径处理
5. 主题切换支持
6. 国际化支持
7. 资源文件压缩
8. 版本控制管理 