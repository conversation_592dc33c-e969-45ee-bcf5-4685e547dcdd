/* 暗色主题 */

/* 主窗口 */
QMainWindow {
    background-color: #1e1e1e;
    color: #ffffff;
}

/* 菜单栏 */
QMenuBar {
    background-color: #2d2d2d;
    color: #ffffff;
    border-bottom: 1px solid #3d3d3d;
}

QMenuBar::item {
    padding: 4px 8px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #3d3d3d;
}

/* 工具栏 */
QToolBar {
    background-color: #2d2d2d;
    border: none;
    spacing: 3px;
    padding: 3px;
}

QToolButton {
    border: 1px solid transparent;
    border-radius: 2px;
    padding: 3px;
    color: #ffffff;
}

QToolButton:hover {
    background-color: #3d3d3d;
}

/* 状态栏 */
QStatusBar {
    background-color: #2d2d2d;
    color: #ffffff;
    border-top: 1px solid #3d3d3d;
}

/* 表格 */
QTableView {
    background-color: #2d2d2d;
    alternate-background-color: #252525;
    border: 1px solid #3d3d3d;
    color: #ffffff;
}

QTableView::item {
    padding: 5px;
}

QTableView::item:selected {
    background-color: #0d47a1;
    color: white;
}

QHeaderView::section {
    background-color: #3d3d3d;
    color: #ffffff;
    padding: 5px;
    border: none;
    border-right: 1px solid #4d4d4d;
    border-bottom: 1px solid #4d4d4d;
}

/* 按钮 */
QPushButton {
    background-color: #0d47a1;
    color: white;
    border: none;
    border-radius: 2px;
    padding: 5px 15px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #1565c0;
}

QPushButton:pressed {
    background-color: #0a3880;
}

QPushButton:disabled {
    background-color: #4d4d4d;
    color: #8d8d8d;
}

/* 输入框 */
QLineEdit {
    background-color: #3d3d3d;
    color: #ffffff;
    border: 1px solid #4d4d4d;
    border-radius: 2px;
    padding: 5px;
}

QLineEdit:focus {
    border: 1px solid #0d47a1;
}

/* 下拉框 */
QComboBox {
    background-color: #3d3d3d;
    color: #ffffff;
    border: 1px solid #4d4d4d;
    border-radius: 2px;
    padding: 5px;
}

QComboBox::drop-down {
    border: none;
}

QComboBox::down-arrow {
    image: url(resources/icons/down-arrow-white.png);
    width: 12px;
    height: 12px;
}

/* 标签页 */
QTabWidget::pane {
    border: 1px solid #3d3d3d;
    background-color: #2d2d2d;
}

QTabBar::tab {
    background-color: #252525;
    color: #ffffff;
    border: 1px solid #3d3d3d;
    padding: 8px 12px;
}

QTabBar::tab:selected {
    background-color: #2d2d2d;
    border-bottom-color: #2d2d2d;
}

/* 滚动条 */
QScrollBar:vertical {
    border: none;
    background-color: #2d2d2d;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #4d4d4d;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background-color: #5d5d5d;
}

QScrollBar:horizontal {
    border: none;
    background-color: #2d2d2d;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #4d4d4d;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #5d5d5d;
} 