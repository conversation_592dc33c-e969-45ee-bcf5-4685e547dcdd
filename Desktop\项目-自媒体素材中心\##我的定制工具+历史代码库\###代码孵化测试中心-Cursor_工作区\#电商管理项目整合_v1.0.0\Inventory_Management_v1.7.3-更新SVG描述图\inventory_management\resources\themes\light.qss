/* 亮色主题 */

/* 主窗口 */
QMainWindow {
    background-color: #f5f5f5;
}

/* 菜单栏 */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
}

QMenuBar::item {
    padding: 4px 8px;
    background-color: transparent;
}

QMenuBar::item:selected {
    background-color: #e0e0e0;
}

/* 工具栏 */
QToolBar {
    background-color: #ffffff;
    border: none;
    spacing: 3px;
    padding: 3px;
}

QToolButton {
    border: 1px solid transparent;
    border-radius: 2px;
    padding: 3px;
}

QToolButton:hover {
    background-color: #e0e0e0;
}

/* 状态栏 */
QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #e0e0e0;
}

/* 表格 */
QTableView {
    background-color: #ffffff;
    alternate-background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
}

QTableView::item {
    padding: 5px;
}

QTableView::item:selected {
    background-color: #2196F3;
    color: white;
}

QHeaderView::section {
    background-color: #f0f0f0;
    padding: 5px;
    border: none;
    border-right: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
}

/* 按钮 */
QPushButton {
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 2px;
    padding: 5px 15px;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #1976D2;
}

QPushButton:pressed {
    background-color: #0D47A1;
}

QPushButton:disabled {
    background-color: #BDBDBD;
}

/* 输入框 */
QLineEdit {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    padding: 5px;
}

QLineEdit:focus {
    border: 1px solid #2196F3;
}

/* 下拉框 */
QComboBox {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    padding: 5px;
}

QComboBox::drop-down {
    border: none;
}

QComboBox::down-arrow {
    image: url(resources/icons/down-arrow.png);
    width: 12px;
    height: 12px;
}

/* 标签页 */
QTabWidget::pane {
    border: 1px solid #e0e0e0;
    background-color: white;
}

QTabBar::tab {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    padding: 8px 12px;
}

QTabBar::tab:selected {
    background-color: white;
    border-bottom-color: white;
}

/* 滚动条 */
QScrollBar:vertical {
    border: none;
    background-color: #f5f5f5;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar:horizontal {
    border: none;
    background-color: #f5f5f5;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #c0c0c0;
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #a0a0a0;
} 