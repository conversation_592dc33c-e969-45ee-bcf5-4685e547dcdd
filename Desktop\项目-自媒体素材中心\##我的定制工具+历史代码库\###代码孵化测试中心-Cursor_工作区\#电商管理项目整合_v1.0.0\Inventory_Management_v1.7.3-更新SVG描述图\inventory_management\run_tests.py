#!/usr/bin/env python3
"""
运行基本功能测试
"""
import sys
import os
import unittest
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    try:
        # 测试核心模块导入
        from models.product import Product
        from database.image_utils import get_image_type, validate_image
        from utils.config import Config
        from utils.error_handler import ErrorHandler
        from utils.theme_manager import ThemeManager
        
        print("✅ 所有核心模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_product_model():
    """测试Product模型"""
    print("\n🔍 测试Product模型...")
    
    try:
        from models.product import Product
        
        # 创建产品实例
        product = Product(
            name="测试商品",
            category="测试分类",
            quantity=10,
            purchase_price=100,
            selling_price=150
        )
        
        # 测试计算功能
        total_cost = product.calculate_total_cost()
        profit = product.calculate_profit()
        margin = product.calculate_profit_margin()
        
        print(f"✅ 产品创建成功: {product.name}")
        print(f"✅ 总成本计算: {total_cost}")
        print(f"✅ 利润计算: {profit}")
        print(f"✅ 利润率计算: {margin}%")
        
        # 测试验证
        product.validate()
        print("✅ 数据验证通过")
        
        # 测试字典转换
        product_dict = product.to_dict()
        product_from_dict = Product.from_dict(product_dict)
        print("✅ 字典转换成功")
        
        return True
    except Exception as e:
        print(f"❌ Product模型测试失败: {e}")
        traceback.print_exc()
        return False

def test_image_utils():
    """测试图片工具"""
    print("\n🔍 测试图片工具...")
    
    try:
        from database.image_utils import get_image_type, init_image_dir
        
        # 测试目录初始化
        init_image_dir()
        print("✅ 图片目录初始化成功")
        
        # 测试图片类型检测（使用一个假的路径）
        image_type = get_image_type("test.jpg")
        print(f"✅ 图片类型检测: {image_type}")
        
        return True
    except Exception as e:
        print(f"❌ 图片工具测试失败: {e}")
        traceback.print_exc()
        return False

def test_config():
    """测试配置管理"""
    print("\n🔍 测试配置管理...")
    
    try:
        from utils.config import Config
        
        # 获取配置实例
        config = Config.instance()
        
        # 测试配置读取
        theme = config.get("ui.theme", "dark")
        print(f"✅ 配置读取成功: theme = {theme}")
        
        # 测试配置设置
        config.set("test.value", "test_data")
        test_value = config.get("test.value")
        print(f"✅ 配置设置成功: test_value = {test_value}")
        
        return True
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    try:
        from database.db_utils import get_connection, create_tables
        
        # 测试数据库连接
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            print(f"✅ 数据库连接成功: {result}")
        
        # 测试表创建
        create_tables()
        print("✅ 数据库表创建/验证成功")
        
        return True
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        traceback.print_exc()
        return False

def run_unit_tests():
    """运行单元测试"""
    print("\n🔍 运行单元测试...")
    
    try:
        # 查找测试文件
        test_dir = os.path.join(current_dir, "tests")
        if os.path.exists(test_dir):
            # 运行测试
            loader = unittest.TestLoader()
            suite = loader.discover(test_dir, pattern="test_*.py")
            runner = unittest.TextTestRunner(verbosity=2)
            result = runner.run(suite)
            
            if result.wasSuccessful():
                print("✅ 所有单元测试通过")
                return True
            else:
                print(f"❌ 单元测试失败: {len(result.failures)} 失败, {len(result.errors)} 错误")
                return False
        else:
            print("⚠️ 未找到测试目录，跳过单元测试")
            return True
    except Exception as e:
        print(f"❌ 单元测试运行失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 库存管理系统 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("Product模型", test_product_model),
        ("图片工具", test_image_utils),
        ("配置管理", test_config),
        ("数据库连接", test_database_connection),
        ("单元测试", run_unit_tests),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
    
    print("\n" + "="*50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查上述错误。")
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit_code = 0 if success else 1
    except Exception as e:
        print(f"\n💥 测试过程中发生严重错误: {e}")
        traceback.print_exc()
        exit_code = 2
    
    print(f"\n程序退出码: {exit_code}")
    input("按回车键退出...")
    sys.exit(exit_code)
