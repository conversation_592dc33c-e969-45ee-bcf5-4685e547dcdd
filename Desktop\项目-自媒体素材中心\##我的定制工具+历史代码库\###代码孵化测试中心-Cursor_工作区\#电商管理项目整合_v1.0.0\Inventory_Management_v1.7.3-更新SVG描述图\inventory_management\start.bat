@echo off
chcp 65001
title 库存管理系统
echo 正在启动库存管理系统...
echo.

:: 检查Python环境
python --version > nul 2>&1
if errorlevel 1 (
    echo Python未安装！请先安装Python 3.8或更高版本。
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

:: 检查依赖包
echo 检查依赖包...
pip install -r requirements.txt > nul 2>&1
if errorlevel 1 (
    echo 安装依赖包失败！请检查网络连接或手动运行: pip install -r requirements.txt
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

:: 启动程序
echo 启动程序...
python start.py

:: 如果程序异常退出
if errorlevel 1 (
    echo.
    echo 程序异常退出！请检查logs目录下的日志文件。
    echo 按任意键退出...
    pause > nul
    exit /b 1
)

exit /b 0 