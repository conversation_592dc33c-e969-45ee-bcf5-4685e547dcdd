import os
import sys
import logging
from database.db_manager import DatabaseManager
from database.db_utils import DATABASE_DIR


def setup_logging():
    """设置日志配置"""
    if not os.path.exists("logs"):
        os.makedirs("logs")

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("logs/app.log", encoding="utf-8"),
            logging.StreamHandler(sys.stdout),
        ],
    )


def print_menu():
    """打印主菜单"""
    print("\n=== 库存管理系统 - 数据库管理 ===")
    print("1. 查看所有数据库")
    print("2. 创建新数据库")
    print("3. 删除数据库")
    print("4. 备份数据库")
    print("5. 选择并启动")
    print("0. 退出")
    print("============================")


def list_databases(db_manager):
    """列出所有数据库"""
    print("\n=== 可用数据库列表 ===")
    databases = db_manager.list_databases()
    if not databases:
        print("暂无可用数据库")
        return

    current_db = os.path.basename(db_manager.get_current_db())
    for i, db in enumerate(databases, 1):
        size = db_manager.get_database_size(db)
        current = " (当前使用)" if db == current_db else ""
        print(f"{i}. {db} - {size}{current}")


def create_database(db_manager):
    """创建新数据库"""
    name = input("\n请输入新数据库名称（不需要.db后缀）: ").strip()
    if not name:
        print("数据库名称不能为空！")
        return
    try:
        db_path = db_manager.create_database(name)
        print(f"成功创建数据库: {db_path}")
    except Exception as e:
        print(f"创建数据库失败: {str(e)}")


def delete_database(db_manager):
    """删除数据库"""
    list_databases(db_manager)
    databases = db_manager.list_databases()
    if not databases:
        return

    try:
        choice = int(input("\n请选择要删除的数据库编号: "))
        if 1 <= choice <= len(databases):
            db_name = databases[choice - 1]
            confirm = input(f"确定要删除数据库 {db_name} 吗？(y/N): ")
            if confirm.lower() == "y":
                db_manager.remove_database(db_name)
                print(f"成功删除数据库: {db_name}")
        else:
            print("无效的选择！")
    except ValueError:
        print("请输入有效的数字！")
    except Exception as e:
        print(f"删除数据库失败: {str(e)}")


def backup_database(db_manager):
    """备份数据库"""
    list_databases(db_manager)
    databases = db_manager.list_databases()
    if not databases:
        return

    try:
        choice = int(input("\n请选择要备份的数据库编号: "))
        if 1 <= choice <= len(databases):
            db_name = databases[choice - 1]
            backup_name = input(
                "请输入备份文件名（可选，直接回车使用自动生成的名称）: "
            ).strip()
            backup_name = backup_name if backup_name else None
            backup_file = db_manager.backup_database(db_name, backup_name)
            print(f"成功创建备份: {backup_file}")
        else:
            print("无效的选择！")
    except ValueError:
        print("请输入有效的数字！")
    except Exception as e:
        print(f"备份数据库失败: {str(e)}")


def select_and_start(db_manager):
    """选择数据库并启动程序"""
    list_databases(db_manager)
    databases = db_manager.list_databases()
    if not databases:
        return False

    try:
        choice = int(input("\n请选择要使用的数据库编号: "))
        if 1 <= choice <= len(databases):
            db_name = databases[choice - 1]
            db_path = os.path.join(DATABASE_DIR, db_name)
            if db_manager.switch_to_database(db_path):
                print(f"已切换到数据库: {db_name}")
                return True
        else:
            print("无效的选择！")
    except ValueError:
        print("请输入有效的数字！")
    except Exception as e:
        print(f"切换数据库失败: {str(e)}")
    return False


def start_main_program():
    """启动主程序"""
    try:
        # 添加当前目录到Python路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.append(current_dir)

        from app import run_app

        print("\n正在启动主程序...")
        run_app()
    except ImportError as e:
        logging.error(f"导入主程序模块失败: {str(e)}")
        print("错误：无法导入主程序模块，请确保app.py文件存在且包含run_app函数")
    except Exception as e:
        logging.error(f"启动主程序失败: {str(e)}")
        print(f"启动主程序时发生错误: {str(e)}")
        print("请检查日志文件获取详细信息")


def main():
    """主函数"""
    setup_logging()
    db_manager = DatabaseManager()

    while True:
        print_menu()
        choice = input("请选择操作: ").strip()

        if choice == "1":
            list_databases(db_manager)
        elif choice == "2":
            create_database(db_manager)
        elif choice == "3":
            delete_database(db_manager)
        elif choice == "4":
            backup_database(db_manager)
        elif choice == "5":
            if select_and_start(db_manager):
                start_main_program()
                break
        elif choice == "0":
            print("感谢使用！再见！")
            break
        else:
            print("无效的选择，请重试！")


if __name__ == "__main__":
    main()
