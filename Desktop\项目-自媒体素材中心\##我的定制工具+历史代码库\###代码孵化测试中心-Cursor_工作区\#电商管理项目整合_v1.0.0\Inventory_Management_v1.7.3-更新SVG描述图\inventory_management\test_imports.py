#!/usr/bin/env python3
"""
测试导入模块，诊断启动问题
"""
import sys
import traceback


def test_import(module_name, description=""):
    """测试导入模块"""
    try:
        if module_name == "PyQt6":
            import PyQt6
            from PyQt6.QtCore import qVersion

            print(f"✅ {description or module_name} - 版本: {qVersion()}")
        elif module_name == "PyQt6.QtWidgets":
            from PyQt6.QtWidgets import QApplication

            print(f"✅ {description or module_name} - QApplication 可用")
        elif module_name == "pandas":
            import pandas as pd

            print(f"✅ {description or module_name} - 版本: {pd.__version__}")
        elif module_name == "PIL":
            from PIL import Image

            print(f"✅ {description or module_name} - Pillow 可用")
        elif module_name == "cv2":
            import cv2

            print(f"✅ {description or module_name} - OpenCV 版本: {cv2.__version__}")
        elif module_name == "pyzbar":
            import pyzbar

            print(f"✅ {description or module_name} - 可用")
        else:
            exec(f"import {module_name}")
            print(f"✅ {description or module_name} - 导入成功")
        return True
    except ImportError as e:
        print(f"❌ {description or module_name} - 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ {description or module_name} - 其他错误: {e}")
        return False


def test_project_modules():
    """测试项目模块"""
    print("\n=== 测试项目模块 ===")

    # 添加当前目录到路径
    import os

    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    modules_to_test = [
        ("utils.config", "配置模块"),
        ("utils.error_handler", "错误处理模块"),
        ("utils.theme_manager", "主题管理模块"),
        ("database.image_utils", "图片工具模块"),
        ("models.product", "产品模型"),
    ]

    for module, desc in modules_to_test:
        test_import(module, desc)


def main():
    print("🔍 库存管理系统 - 导入测试")
    print("=" * 50)

    print(f"Python 版本: {sys.version}")
    print(f"Python 路径: {sys.executable}")
    print()

    print("=== 测试基础依赖 ===")
    dependencies = [
        ("PyQt6", "PyQt6 核心"),
        ("PyQt6.QtWidgets", "PyQt6 控件"),
        ("pandas", "数据处理"),
        ("PIL", "图片处理"),
        ("cv2", "OpenCV"),
        ("pyzbar", "二维码解析"),
    ]

    failed_deps = []
    for dep, desc in dependencies:
        if not test_import(dep, desc):
            failed_deps.append(dep)

    if failed_deps:
        print(f"\n❌ 缺少依赖: {', '.join(failed_deps)}")
        print("请运行以下命令安装:")
        print(
            "pip install PyQt6 pandas openpyxl pillow python-dateutil opencv-python pyzbar"
        )
        return False

    # 测试项目模块
    test_project_modules()

    print("\n=== 测试应用启动 ===")
    try:
        from app import run_app

        print("✅ 应用模块导入成功")

        # 测试创建QApplication
        from PyQt6.QtWidgets import QApplication

        app = QApplication([])
        print("✅ QApplication 创建成功")
        app.quit()

        return True
    except Exception as e:
        print(f"❌ 应用启动测试失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 所有测试通过！应用应该可以正常启动。")
        else:
            print("\n💥 测试失败，请检查上述错误。")
    except Exception as e:
        print(f"\n💥 测试过程中发生错误: {e}")
        traceback.print_exc()

    input("\n按回车键退出...")
