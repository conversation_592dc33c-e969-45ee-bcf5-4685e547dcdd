# 测试模块

## 概述
测试模块包含了系统的所有测试用例，基于 PyQt6 的测试框架，确保系统功能的正确性和稳定性。

## 测试类型

### 1. 单元测试
- 模块功能测试
- 类方法测试
- 工具函数测试
- 边界条件测试

### 2. 集成测试
- 模块间交互
- 数据流测试
- 界面集成
- 功能组合

### 3. GUI测试
- 界面元素测试
- 事件处理测试
- 用户交互测试
- 布局验证

### 4. 数据库测试
- 连接测试
- CRUD操作
- 事务处理
- 并发访问

## 测试框架

### 1. PyTest
- 测试用例组织
- 断言机制
- 夹具使用
- 参数化测试

### 2. QtTest
- 界面测试
- 事件模拟
- 信号槽测试
- 定时器测试

### 3. Mock工具
- 对象模拟
- 行为验证
- 状态检查
- 依赖隔离

## 测试目录

### 1. 单元测试
```
tests/unit/
├── test_error_handler.py   # 错误处理测试
├── test_database.py       # 数据库操作测试
├── test_config.py        # 配置管理测试
└── test_validator.py     # 数据验证测试
```

### 2. 集成测试
```
tests/integration/
├── test_product_management.py  # 产品管理测试
├── test_batch_operations.py   # 批次操作测试
├── test_finance_stats.py     # 财务统计测试
└── test_image_processing.py  # 图片处理测试
```

### 3. GUI测试
```
tests/gui/
├── test_main_window.py    # 主窗口测试
├── test_dialogs.py       # 对话框测试
├── test_widgets.py       # 控件测试
└── test_events.py       # 事件处理测试
```

## 测试示例

### 1. 单元测试
```python
def test_error_handler():
    with pytest.raises(ValueError):
        @ErrorHandler.exception_handler
        def raise_error():
            raise ValueError("测试错误")
        raise_error()
```

### 2. GUI测试
```python
def test_button_click(qtbot):
    window = MainWindow()
    qtbot.addWidget(window)
    qtbot.mouseClick(window.button, Qt.LeftButton)
    assert window.result_label.text() == "已点击"
```

### 3. 数据库测试
```python
def test_database_connection():
    with DatabaseUtils.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        assert cursor.fetchone()[0] == 1
```

## 运行测试

### 1. 全部测试
```bash
pytest tests/
```

### 2. 指定模块
```bash
pytest tests/unit/test_error_handler.py
```

### 3. 指定函数
```bash
pytest tests/gui/test_main_window.py::test_button_click
```

## 测试配置

### 1. pytest.ini
```ini
[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
```

### 2. conftest.py
```python
@pytest.fixture
def app(qtbot):
    window = MainWindow()
    qtbot.addWidget(window)
    return window
```

## 注意事项
1. 保持测试独立性
2. 清理测试数据
3. 模拟外部依赖
4. 验证边界条件
5. 检查异常处理
6. 测试代码覆盖
7. 维护测试文档
8. 定期运行测试 