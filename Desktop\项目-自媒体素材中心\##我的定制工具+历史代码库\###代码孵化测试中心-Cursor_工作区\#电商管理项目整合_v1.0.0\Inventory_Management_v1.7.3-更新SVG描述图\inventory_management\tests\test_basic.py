import conftest  # 导入配置
import sys
import unittest
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from utils.error_handler import ErrorHandler
from icons import Icons


class TestBasicFunctions(unittest.TestCase):
    """基础功能测试"""

    @classmethod
    def setUpClass(cls):
        """测试前创建应用实例"""
        cls.app = QApplication(sys.argv)

    def test_error_handler(self):
        """测试错误处理"""
        # 测试日志初始化
        ErrorHandler.init_logger()
        ErrorHandler.log_info("测试信息")
        ErrorHandler.log_warning("测试警告")
        ErrorHandler.log_error("测试错误")

        # 测试装饰器
        @ErrorHandler.exception_handler
        def test_func():
            raise ValueError("测试异常")

        with self.assertRaises(ValueError):
            test_func()

    def test_icons(self):
        """测试图标加载"""
        icons = Icons.instance()
        # 测试所有图标
        icon_names = [
            "add_product",
            "add_batch",
            "import",
            "export",
            "database",
            "finance",
            "refresh",
            "image",
            "edit",
            "delete",
        ]
        for name in icon_names:
            icon = icons.get_icon(name)
            self.assertFalse(icon.isNull())

    def test_high_dpi(self):
        """测试高DPI支持"""
        policy = QApplication.highDpiScaleFactorRoundingPolicy()
        self.assertEqual(policy, Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)


if __name__ == "__main__":
    unittest.main()
