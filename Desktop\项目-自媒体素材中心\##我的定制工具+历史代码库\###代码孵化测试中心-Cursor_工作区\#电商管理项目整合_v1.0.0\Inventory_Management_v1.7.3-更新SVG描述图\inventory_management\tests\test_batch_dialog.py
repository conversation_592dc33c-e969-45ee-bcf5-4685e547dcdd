import unittest
import logging
from PyQt6.QtWidgets import QApplication, QDialog
from PyQt6.QtCore import Qt
from gui.dialogs.batch_dialog import BatchDialog


class TestBatchDialog(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        try:
            cls.app = QApplication([])
            logging.info("成功创建 QApplication 实例")
        except Exception as e:
            logging.error(f"创建 QApplication 实例失败: {e}")
            raise

    def setUp(self):
        """每个测试用例开始前的设置"""
        try:
            self.dialog = BatchDialog()  # 创建新批次
            logging.info("成功创建 BatchDialog 实例")
        except Exception as e:
            logging.error(f"创建 BatchDialog 实例失败: {e}")
            raise

    def test_init(self):
        """测试对话框初始化"""
        try:
            # 测试窗口标题
            self.assertEqual(self.dialog.windowTitle(), "添加批次")

            # 测试基本控件
            self.assertIsNotNone(self.dialog.batch_name_input)
            self.assertIsNotNone(self.dialog.remarks_input)
            self.assertIsNotNone(self.dialog.product_table)
            self.assertIsNotNone(self.dialog.search_input)

            logging.info("对话框初始化测试通过")
        except Exception as e:
            logging.error(f"对话框初始化测试失败: {e}")
            raise

    def test_product_table(self):
        """测试商品表格功能"""
        try:
            # 测试表格列数
            self.assertEqual(self.dialog.product_table.columnCount(), 5)

            # 测试表格标题
            headers = [
                self.dialog.product_table.horizontalHeaderItem(i).text()
                for i in range(5)
            ]
            expected_headers = ["商品ID", "名称", "类别", "状态", "所属批次"]
            self.assertEqual(headers, expected_headers)

            # 测试表格属性
            self.assertTrue(self.dialog.product_table.alternatingRowColors())
            self.assertTrue(self.dialog.product_table.isSortingEnabled())

            logging.info("商品表格测试通过")
        except Exception as e:
            logging.error(f"商品表格测试失败: {e}")
            raise

    def test_search_function(self):
        """测试搜索功能"""
        try:
            # 设置搜索文本
            self.dialog.search_input.setText("test")

            # 触发搜索
            self.dialog.search_products()

            logging.info("搜索功能测试通过")
        except Exception as e:
            logging.error(f"搜索功能测试失败: {e}")
            raise

    def test_save_validation(self):
        """测试保存验证功能"""
        try:
            # 测试空批次名称
            self.dialog.batch_name_input.clear()
            self.dialog.save_batch()

            # 测试未选择商品
            self.dialog.batch_name_input.setText("测试批次")
            self.dialog.save_batch()

            logging.info("保存验证测试通过")
        except Exception as e:
            logging.error(f"保存验证测试失败: {e}")
            raise

    def test_edit_mode(self):
        """测试编辑模式"""
        try:
            edit_dialog = BatchDialog(batch_id="1")
            self.assertEqual(edit_dialog.windowTitle(), "编辑批次")

            logging.info("编辑模式测试通过")
        except Exception as e:
            logging.error(f"编辑模式测试失败: {e}")
            raise

    def tearDown(self):
        """每个测试用例结束后的清理"""
        try:
            self.dialog.close()
            logging.info("成功关闭对话框")
        except Exception as e:
            logging.error(f"关闭对话框失败: {e}")
            raise

    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        try:
            cls.app.quit()
            logging.info("成功退出应用")
        except Exception as e:
            logging.error(f"退出应用失败: {e}")
            raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    unittest.main()
