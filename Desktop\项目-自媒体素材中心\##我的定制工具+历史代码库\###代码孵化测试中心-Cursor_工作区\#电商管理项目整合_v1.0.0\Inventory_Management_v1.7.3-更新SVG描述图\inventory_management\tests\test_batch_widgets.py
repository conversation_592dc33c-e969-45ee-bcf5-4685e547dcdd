import unittest
import logging
from PyQt6.QtWidgets import QApplication, QPushButton
from PyQt6.QtCore import Qt
from gui.widgets.batch_widgets import BatchTable, BatchToolBar
import sys

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class TestBatchWidgets(unittest.TestCase):
    """批次控件测试"""

    @classmethod
    def setUpClass(cls):
        """测试前创建应用实例"""
        try:
            cls.app = QApplication(sys.argv)
            logger.info("QApplication created successfully")
        except Exception as e:
            logger.error(f"Failed to create QApplication: {e}")
            raise

    def test_batch_table(self):
        """测试批次表格"""
        try:
            logger.info("Starting batch table test")
            table = BatchTable()

            # 测试表格初始化
            self.assertEqual(table.columnCount(), 6)
            self.assertEqual(table.rowCount(), 0)
            logger.info("Table initialization test passed")

            # 测试过滤功能
            table.filter_batches("test")
            table.filter_batches("")  # 清除过滤
            logger.info("Table filter test passed")

        except Exception as e:
            logger.error(f"Error in batch table test: {e}")
            raise

    def test_batch_toolbar(self):
        """测试批次工具栏"""
        try:
            logger.info("Starting batch toolbar test")
            toolbar = BatchToolBar()

            # 测试信号发送
            signals_received = []

            def on_add():
                logger.debug("Add button clicked")
                signals_received.append("add")

            def on_edit():
                logger.debug("Edit button clicked")
                signals_received.append("edit")

            def on_delete():
                logger.debug("Delete button clicked")
                signals_received.append("delete")

            def on_refresh():
                logger.debug("Refresh button clicked")
                signals_received.append("refresh")

            def on_search(text):
                logger.debug(f"Search text changed: {text}")
                signals_received.append(f"search:{text}")

            # 连接信号
            toolbar.add_batch.connect(on_add)
            toolbar.edit_batch.connect(on_edit)
            toolbar.delete_batch.connect(on_delete)
            toolbar.refresh_data.connect(on_refresh)
            toolbar.search_text_changed.connect(on_search)
            logger.info("Signals connected successfully")

            # 获取所有按钮
            buttons = toolbar.findChildren(QPushButton)
            logger.debug(f"Found {len(buttons)} buttons")

            # 触发信号
            for i, button in enumerate(buttons[:3]):
                logger.debug(f"Clicking button {i}: {button.text()}")
                button.click()

            # 测试搜索
            logger.debug("Setting search text")
            toolbar.search_input.setText("test")

            # 测试刷新按钮
            logger.debug("Clicking refresh button")
            buttons[-1].click()

            # 验证信号接收
            expected_signals = [
                "add",
                "edit",
                "delete",
                "search:test",
                "refresh",
            ]
            logger.info(f"Expected signals: {expected_signals}")
            logger.info(f"Received signals: {signals_received}")
            self.assertEqual(signals_received, expected_signals)
            logger.info("Toolbar test passed")

        except Exception as e:
            logger.error(f"Error in batch toolbar test: {e}")
            raise


if __name__ == "__main__":
    unittest.main()
