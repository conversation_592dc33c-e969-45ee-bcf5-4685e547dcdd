import unittest
import logging
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from gui.dialogs.database_dialog import DatabaseDialog
import sys
import os
import shutil
import tempfile

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class TestDatabaseDialog(unittest.TestCase):
    """数据库对话框测试"""

    @classmethod
    def setUpClass(cls):
        """测试前创建应用实例"""
        try:
            cls.app = QApplication(sys.argv)
            logger.info("QApplication created successfully")

            # 创建临时测试目录
            cls.test_dir = tempfile.mkdtemp()
            logger.info(f"Created test directory: {cls.test_dir}")

            # 创建测试数据库文件
            cls.test_db = os.path.join(cls.test_dir, "test.db")
            with open(cls.test_db, "w") as f:
                f.write("")
            logger.info(f"Created test database: {cls.test_db}")

        except Exception as e:
            logger.error(f"Failed to setup test environment: {e}")
            raise

    @classmethod
    def tearDownClass(cls):
        """测试后清理"""
        try:
            # 删除临时测试目录
            shutil.rmtree(cls.test_dir)
            logger.info(f"Removed test directory: {cls.test_dir}")
        except Exception as e:
            logger.error(f"Failed to cleanup test environment: {e}")

    def test_dialog_init(self):
        """测试对话框初始化"""
        try:
            logger.info("Starting dialog initialization test")
            dialog = DatabaseDialog()

            # 测试窗口标题
            self.assertEqual(dialog.windowTitle(), "数据库管理")

            # 测试窗口大小
            self.assertEqual(dialog.size().width(), 500)
            self.assertEqual(dialog.size().height(), 400)

            # 测试UI组件
            self.assertIsNotNone(dialog.db_list)
            self.assertIsNotNone(dialog.add_btn)
            self.assertIsNotNone(dialog.switch_btn)
            self.assertIsNotNone(dialog.delete_btn)
            self.assertIsNotNone(dialog.close_btn)

            logger.info("Dialog initialization test passed")

        except Exception as e:
            logger.error(f"Error in dialog initialization test: {e}")
            raise

    def test_database_operations(self):
        """测试数据库操作"""
        try:
            logger.info("Starting database operations test")
            dialog = DatabaseDialog()

            # 测试数据库列表加载
            initial_count = dialog.db_list.count()
            logger.info(f"Initial database count: {initial_count}")

            # 测试信号发送
            signals_received = []

            def on_database_changed():
                logger.debug("Database changed signal received")
                signals_received.append("changed")

            dialog.database_changed.connect(on_database_changed)
            logger.info("Signal connected successfully")

            # 模拟数据库操作
            dialog.load_databases()
            logger.info("Database list reloaded")

            self.assertEqual(len(signals_received), 0)
            logger.info("Database operations test passed")

        except Exception as e:
            logger.error(f"Error in database operations test: {e}")
            raise


if __name__ == "__main__":
    unittest.main()
