import unittest
import logging
from datetime import datetime, timedelta
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt, QDate
from gui.dialogs.finance_dialog import FinanceDialog


class TestFinanceDialog(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        try:
            cls.app = QApplication([])
            logging.info("成功创建 QApplication 实例")
        except Exception as e:
            logging.error(f"创建 QApplication 实例失败: {e}")
            raise

    def setUp(self):
        """每个测试用例开始前的设置"""
        try:
            self.dialog = FinanceDialog()
            logging.info("成功创建 FinanceDialog 实例")
        except Exception as e:
            logging.error(f"创建 FinanceDialog 实例失败: {e}")
            raise

    def test_init(self):
        """测试对话框初始化"""
        try:
            # 测试窗口标题和大小
            self.assertEqual(self.dialog.windowTitle(), "财务统计")
            self.assertEqual(self.dialog.size().width(), 800)
            self.assertEqual(self.dialog.size().height(), 600)

            # 测试选项卡数量
            self.assertEqual(self.dialog.tab_widget.count(), 4)
            self.assertEqual(self.dialog.tab_widget.tabText(0), "总体统计")
            self.assertEqual(self.dialog.tab_widget.tabText(1), "每日统计")
            self.assertEqual(self.dialog.tab_widget.tabText(2), "商品业绩")
            self.assertEqual(self.dialog.tab_widget.tabText(3), "批次统计")

            logging.info("对话框初始化测试通过")
        except Exception as e:
            logging.error(f"对话框初始化测试失败: {e}")
            raise

    def test_overall_tab(self):
        """测试总体统计选项卡"""
        try:
            # 测试日期选择器
            self.assertIsNotNone(self.dialog.start_date)
            self.assertIsNotNone(self.dialog.end_date)

            # 测试分类下拉框
            self.assertIsNotNone(self.dialog.category_combo)
            self.assertEqual(self.dialog.category_combo.currentText(), "全部分类")

            # 测试统计表格
            self.assertEqual(self.dialog.overall_table.columnCount(), 7)
            self.assertEqual(
                self.dialog.overall_table.horizontalHeaderItem(0).text(), "统计项目"
            )

            logging.info("总体统计选项卡测试通过")
        except Exception as e:
            logging.error(f"总体统计选项卡测试失败: {e}")
            raise

    def test_daily_tab(self):
        """测试每日统计选项卡"""
        try:
            # 测试天数选择器
            self.assertIsNotNone(self.dialog.days_spin)
            self.assertEqual(self.dialog.days_spin.value(), 30)
            self.assertEqual(self.dialog.days_spin.minimum(), 1)
            self.assertEqual(self.dialog.days_spin.maximum(), 365)

            # 测试统计表格
            self.assertEqual(self.dialog.daily_table.columnCount(), 6)
            self.assertEqual(
                self.dialog.daily_table.horizontalHeaderItem(0).text(), "日期"
            )

            logging.info("每日统计选项卡测试通过")
        except Exception as e:
            logging.error(f"每日统计选项卡测试失败: {e}")
            raise

    def test_product_tab(self):
        """测试商品业绩选项卡"""
        try:
            # 测试商品选择器
            self.assertIsNotNone(self.dialog.product_combo)

            # 测试日期选择器
            self.assertIsNotNone(self.dialog.product_start_date)
            self.assertIsNotNone(self.dialog.product_end_date)

            # 测试统计表格
            self.assertEqual(self.dialog.product_table.columnCount(), 8)
            self.assertEqual(
                self.dialog.product_table.horizontalHeaderItem(0).text(), "日期"
            )

            logging.info("商品业绩选项卡测试通过")
        except Exception as e:
            logging.error(f"商品业绩选项卡测试失败: {e}")
            raise

    def test_batch_tab(self):
        """测试批次统计选项卡"""
        try:
            # 测试批次选择器
            self.assertIsNotNone(self.dialog.batch_combo)

            # 测试统计表格
            self.assertEqual(self.dialog.batch_table.columnCount(), 7)
            self.assertEqual(
                self.dialog.batch_table.horizontalHeaderItem(0).text(), "统计项目"
            )

            logging.info("批次统计选项卡测试通过")
        except Exception as e:
            logging.error(f"批次统计选项卡测试失败: {e}")
            raise

    def test_data_loading(self):
        """测试数据加载功能"""
        try:
            # 测试分类加载
            self.dialog.load_categories()
            self.assertGreater(self.dialog.category_combo.count(), 1)

            # 测试商品ID加载
            self.dialog.load_product_ids()
            self.assertGreaterEqual(self.dialog.product_combo.count(), 0)

            # 测试批次加载
            self.dialog.load_batches()
            self.assertGreaterEqual(self.dialog.batch_combo.count(), 0)

            logging.info("数据加载功能测试通过")
        except Exception as e:
            logging.error(f"数据加载功能测试失败: {e}")
            raise

    def test_statistics_update(self):
        """测试统计数据更新功能"""
        try:
            # 测试总体统计更新
            self.dialog.update_statistics()
            self.assertGreaterEqual(self.dialog.overall_table.rowCount(), 0)

            # 测试每日统计更新
            self.dialog.update_daily_stats()
            self.assertGreaterEqual(self.dialog.daily_table.rowCount(), 0)

            # 测试批次统计更新
            self.dialog.update_batch_statistics()
            self.assertEqual(self.dialog.batch_table.rowCount(), 6)

            logging.info("统计数据更新功能测试通过")
        except Exception as e:
            logging.error(f"统计数据更新功能测试失败: {e}")
            raise

    def tearDown(self):
        """每个测试用例结束后的清理"""
        try:
            self.dialog.close()
            logging.info("成功关闭对话框")
        except Exception as e:
            logging.error(f"关闭对话框失败: {e}")
            raise

    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        try:
            cls.app.quit()
            logging.info("成功退出应用")
        except Exception as e:
            logging.error(f"退出应用失败: {e}")
            raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    unittest.main()
