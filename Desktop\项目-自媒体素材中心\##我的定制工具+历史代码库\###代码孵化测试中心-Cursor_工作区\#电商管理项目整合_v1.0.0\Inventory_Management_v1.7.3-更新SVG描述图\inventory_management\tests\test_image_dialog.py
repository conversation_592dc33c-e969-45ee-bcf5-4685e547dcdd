import unittest
import logging
import os
from PyQt6.QtWidgets import QApplication, QPushButton, QTabWidget, QDialog
from PyQt6.QtCore import Qt, QSize
from gui.dialogs.image_dialog import ImageDialog, ImagePreviewDialog


class TestImageDialog(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        try:
            cls.app = QApplication([])
            logging.info("成功创建 QApplication 实例")
        except Exception as e:
            logging.error(f"创建 QApplication 实例失败: {e}")
            raise

    def setUp(self):
        """每个测试用例开始前的设置"""
        try:
            self.dialog = ImageDialog(product_id=1)  # 使用测试商品ID
            logging.info("成功创建 ImageDialog 实例")
        except Exception as e:
            logging.error(f"创建 ImageDialog 实例失败: {e}")
            raise

    def test_init(self):
        """测试对话框初始化"""
        try:
            # 测试窗口标题和大小
            self.assertEqual(self.dialog.windowTitle(), "图片管理")
            self.assertEqual(self.dialog.size().width(), 800)
            self.assertEqual(self.dialog.size().height(), 600)

            # 测试标签页
            tab_widget = self.dialog.findChild(QTabWidget)
            self.assertIsNotNone(tab_widget)
            self.assertEqual(tab_widget.count(), 2)
            self.assertEqual(tab_widget.tabText(0), "当前商品图片")
            self.assertEqual(tab_widget.tabText(1), "图片库")

            logging.info("对话框初始化测试通过")
        except Exception as e:
            logging.error(f"对话框初始化测试失败: {e}")
            raise

    def test_image_list(self):
        """测试图片列表控件"""
        try:
            # 测试商品图片列表
            self.assertIsNotNone(self.dialog.image_list)
            self.assertEqual(
                self.dialog.image_list.viewMode(),
                self.dialog.image_list.ViewMode.IconMode,
            )
            self.assertEqual(self.dialog.image_list.iconSize(), QSize(100, 100))

            # 测试图片库列表
            self.assertIsNotNone(self.dialog.library_list)
            self.assertEqual(
                self.dialog.library_list.viewMode(),
                self.dialog.library_list.ViewMode.IconMode,
            )
            self.assertEqual(self.dialog.library_list.iconSize(), QSize(100, 100))

            logging.info("图片列表控件测试通过")
        except Exception as e:
            logging.error(f"图片列表控件测试失败: {e}")
            raise

    def test_buttons(self):
        """测试按钮功能"""
        try:
            # 测试商品图片页面的按钮
            buttons = self.dialog.findChildren(QPushButton)
            button_texts = [btn.text() for btn in buttons]

            self.assertIn("添加图片", button_texts)
            self.assertIn("设为主图", button_texts)
            self.assertIn("删除图片", button_texts)
            self.assertIn("确定", button_texts)
            self.assertIn("取消", button_texts)

            logging.info("按钮功能测试通过")
        except Exception as e:
            logging.error(f"按钮功能测试失败: {e}")
            raise

    def test_search_input(self):
        """测试搜索输入框"""
        try:
            self.assertIsNotNone(self.dialog.search_input)
            self.assertEqual(
                self.dialog.search_input.placeholderText(), "搜索图片（按名称或标签）"
            )

            logging.info("搜索输入框测试通过")
        except Exception as e:
            logging.error(f"搜索输入框测试失败: {e}")
            raise

    def test_preview_dialog(self):
        """测试预览对话框"""
        try:
            preview = ImagePreviewDialog(self.dialog, 1, 1)  # 使用测试ID

            # 测试窗口属性
            self.assertEqual(preview.windowTitle(), "图片预览")
            self.assertEqual(preview.size().width(), 800)
            self.assertEqual(preview.size().height(), 600)

            # 测试图片标签
            self.assertIsNotNone(preview.image_label)
            self.assertEqual(
                preview.image_label.alignment(), Qt.AlignmentFlag.AlignCenter
            )

            logging.info("预览对话框测试通过")
        except Exception as e:
            logging.error(f"预览对话框测试失败: {e}")
            raise

    def test_image_operations(self):
        """测试图片操作功能"""
        try:
            # 测试加载图片
            self.dialog.load_images()
            self.dialog.load_library_images()

            # 测试搜索功能
            self.dialog.search_input.setText("test")
            self.dialog.search_library()

            logging.info("图片操作功能测试通过")
        except Exception as e:
            logging.error(f"图片操作功能测试失败: {e}")
            raise

    def tearDown(self):
        """每个测试用例结束后的清理"""
        try:
            self.dialog.close()
            logging.info("成功关闭对话框")
        except Exception as e:
            logging.error(f"关闭对话框失败: {e}")
            raise

    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        try:
            cls.app.quit()
            logging.info("成功退出应用")
        except Exception as e:
            logging.error(f"退出应用失败: {e}")
            raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    unittest.main()
