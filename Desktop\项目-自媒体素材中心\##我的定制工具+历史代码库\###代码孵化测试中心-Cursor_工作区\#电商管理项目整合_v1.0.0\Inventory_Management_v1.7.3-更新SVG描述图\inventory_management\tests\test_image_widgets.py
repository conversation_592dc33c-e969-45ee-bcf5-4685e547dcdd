import conftest
import unittest
from PyQt6.QtWidgets import QApplication, QTableWidget
from PyQt6.QtCore import Qt, QModelIndex
from PyQt6.QtGui import QPainter, QPixmap
from gui.widgets.image_widgets import ImagePathDelegate, PreviewDialog
import sys
import os


class TestImageWidgets(unittest.TestCase):
    """图片控件测试"""

    @classmethod
    def setUpClass(cls):
        """测试前创建应用实例"""
        cls.app = QApplication(sys.argv)
        cls.table = QTableWidget()

    def test_image_delegate(self):
        """测试图片代理"""
        delegate = ImagePathDelegate(self.table)

        # 测试创建编辑器（应该返回None）
        self.assertIsNone(delegate.createEditor(None, None, None))

        # 测试建议大小
        size = delegate.sizeHint(None, None)
        self.assertEqual(size.width(), 60)
        self.assertEqual(size.height(), 60)

    def test_preview_dialog(self):
        """测试预览对话框"""
        # 创建测试图片
        test_image = QPixmap(100, 100)
        test_image.fill(Qt.GlobalColor.white)
        test_path = "test_image.png"
        test_image.save(test_path)

        try:
            dialog = PreviewDialog(test_path)

            # 测试窗口标题
            self.assertEqual(dialog.windowTitle(), "图片预览")

            # 测试窗口标志
            self.assertTrue(dialog.windowFlags() & Qt.WindowType.Window)
            self.assertTrue(dialog.windowFlags() & Qt.WindowType.WindowStaysOnTopHint)

            # 测试图片加载
            self.assertFalse(dialog.image_label.pixmap().isNull())

        finally:
            # 清理测试图片
            if os.path.exists(test_path):
                os.remove(test_path)


if __name__ == "__main__":
    unittest.main()
