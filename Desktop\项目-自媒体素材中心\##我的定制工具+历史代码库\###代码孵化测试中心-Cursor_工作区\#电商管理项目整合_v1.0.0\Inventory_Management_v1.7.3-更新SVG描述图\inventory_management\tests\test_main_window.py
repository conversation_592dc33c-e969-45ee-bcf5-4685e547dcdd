import unittest
import logging
from PyQt6.QtWidgets import QApplication, QMenu
from PyQt6.QtCore import Qt
from gui.main_window import MainWindow


class TestMainWindow(unittest.TestCase):
    """测试主窗口功能"""

    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        try:
            cls.app = QApplication([])
            logging.info("QApplication initialized successfully.")
        except Exception as e:
            logging.error(f"Failed to initialize QApplication: {str(e)}")
            raise

    def setUp(self):
        """每个测试用例的初始化"""
        try:
            self.window = MainWindow()
            logging.info("MainWindow initialized successfully.")
        except Exception as e:
            logging.error(f"Failed to initialize MainWindow: {str(e)}")
            raise

    def test_init(self):
        """测试窗口初始化"""
        try:
            # 检查窗口标题
            self.assertEqual(self.window.windowTitle(), "库存管理系统")

            # 检查标签页数量
            self.assertEqual(self.window.tab_widget.count(), 2)

            # 检查标签页标题
            self.assertEqual(self.window.tab_widget.tabText(0), "商品管理")
            self.assertEqual(self.window.tab_widget.tabText(1), "批次管理")

            logging.info("Window initialization test passed.")
        except Exception as e:
            logging.error(f"Window initialization test failed: {str(e)}")
            raise

    def test_product_tab(self):
        """测试商品管理标签页"""
        try:
            # 检查工具栏是否存在
            self.assertIsNotNone(self.window.product_toolbar)

            # 检查商品表格是否存在
            self.assertIsNotNone(self.window.product_table)

            # 检查过滤器是否存在
            self.assertIsNotNone(self.window.product_toolbar.category_combo)
            self.assertIsNotNone(self.window.product_toolbar.batch_combo)

            logging.info("Product tab test passed.")
        except Exception as e:
            logging.error(f"Product tab test failed: {str(e)}")
            raise

    def test_batch_tab(self):
        """测试批次管理标签页"""
        try:
            # 检查工具栏是否存在
            self.assertIsNotNone(self.window.batch_toolbar)

            # 检查批次表格是否存在
            self.assertIsNotNone(self.window.batch_table)

            logging.info("Batch tab test passed.")
        except Exception as e:
            logging.error(f"Batch tab test failed: {str(e)}")
            raise

    def test_status_bar(self):
        """测试状态栏"""
        try:
            # 检查状态栏标签是否存在
            self.assertIsNotNone(self.window.db_label)
            self.assertIsNotNone(self.window.count_label)
            self.assertIsNotNone(self.window.time_label)

            logging.info("Status bar test passed.")
        except Exception as e:
            logging.error(f"Status bar test failed: {str(e)}")
            raise

    def test_menu_bar(self):
        """测试菜单栏"""
        try:
            # 检查菜单栏是否存在
            self.assertIsNotNone(self.window.menuBar())

            # 检查必要的菜单项
            menus = [menu.title() for menu in self.window.menuBar().findChildren(QMenu)]
            self.assertIn("文件", menus)
            self.assertIn("数据", menus)
            self.assertIn("统计", menus)

            logging.info("Menu bar test passed.")
        except Exception as e:
            logging.error(f"Menu bar test failed: {str(e)}")
            raise

    def test_auto_refresh(self):
        """测试自动刷新功能"""
        try:
            # 检查自动刷新动作是否存在
            self.assertIsNotNone(self.window.auto_refresh_action)

            # 检查刷新间隔设置
            self.assertTrue(isinstance(self.window.refresh_interval, int))
            self.assertGreater(self.window.refresh_interval, 0)

            # 测试自动刷新开关
            self.window.auto_refresh_action.setChecked(True)
            self.assertTrue(self.window.refresh_timer.isActive())

            self.window.auto_refresh_action.setChecked(False)
            self.assertFalse(self.window.refresh_timer.isActive())

            logging.info("Auto refresh test passed.")
        except Exception as e:
            logging.error(f"Auto refresh test failed: {str(e)}")
            raise

    def test_filter_products(self):
        """测试商品过滤功能"""
        try:
            # 测试搜索过滤
            self.window.product_toolbar.search_edit.setText("test")
            self.window.filter_products()

            # 测试分类过滤
            self.window.product_toolbar.category_combo.setCurrentText("所有分类")
            self.window.filter_products()

            # 测试批次过滤
            self.window.product_toolbar.batch_combo.setCurrentText("所有批次")
            self.window.filter_products()

            logging.info("Product filter test passed.")
        except Exception as e:
            logging.error(f"Product filter test failed: {str(e)}")
            raise

    def tearDown(self):
        """每个测试用例的清理"""
        try:
            self.window.close()
            logging.info("MainWindow closed successfully.")
        except Exception as e:
            logging.error(f"Failed to close MainWindow: {str(e)}")
            raise

    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        try:
            cls.app.quit()
            logging.info("QApplication quit successfully.")
        except Exception as e:
            logging.error(f"Failed to quit QApplication: {str(e)}")
            raise


if __name__ == "__main__":
    unittest.main()
