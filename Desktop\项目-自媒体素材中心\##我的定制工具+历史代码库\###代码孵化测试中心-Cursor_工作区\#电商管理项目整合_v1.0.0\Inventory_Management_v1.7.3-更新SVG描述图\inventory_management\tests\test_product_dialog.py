import unittest
import logging
from PyQt6.QtWidgets import QApplication, QDialog, QMessageBox
from PyQt6.QtCore import Qt
from gui.dialogs.product_dialog import ProductDialog


class TestProductDialog(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        try:
            cls.app = QApplication([])
            logging.info("成功创建 QApplication 实例")
        except Exception as e:
            logging.error(f"创建 QApplication 实例失败: {e}")
            raise

    def setUp(self):
        """每个测试用例开始前的设置"""
        try:
            self.dialog = ProductDialog()  # 创建新商品
            logging.info("成功创建 ProductDialog 实例")
        except Exception as e:
            logging.error(f"创建 ProductDialog 实例失败: {e}")
            raise

    def test_init(self):
        """测试对话框初始化"""
        try:
            # 测试窗口标题和大小
            self.assertEqual(self.dialog.windowTitle(), "添加商品")
            self.assertEqual(self.dialog.size().width(), 800)
            self.assertEqual(self.dialog.size().height(), 600)

            # 测试基本控件
            self.assertIsNotNone(self.dialog.name_input)
            self.assertIsNotNone(self.dialog.category_input)
            self.assertIsNotNone(self.dialog.quantity_input)
            self.assertIsNotNone(self.dialog.merge_checkbox)

            logging.info("对话框初始化测试通过")
        except Exception as e:
            logging.error(f"对话框初始化测试失败: {e}")
            raise

    def test_price_calculation(self):
        """测试价格计算功能"""
        try:
            # 设置测试数据
            self.dialog.purchase_price_input.setValue(100)
            self.dialog.shipping_cost_input.setValue(10)
            self.dialog.other_cost_input.setValue(5)
            self.dialog.selling_price_input.setValue(150)
            self.dialog.discount_rate_input.setValue(90)

            # 验证计算结果
            self.assertEqual(self.dialog.total_cost_label.text(), "¥115.00")
            self.assertEqual(self.dialog.profit_label.text(), "¥20.00")

            logging.info("价格计算测试通过")
        except Exception as e:
            logging.error(f"价格计算测试失败: {e}")
            raise

    def test_save_validation(self):
        """测试保存验证功能"""
        try:
            # 测试空名称
            self.dialog.name_input.clear()
            self.dialog.save_product()

            # 测试空分类
            self.dialog.name_input.setText("测试商品")
            self.dialog.category_input.setCurrentText("")
            self.dialog.save_product()

            logging.info("保存验证测试通过")
        except Exception as e:
            logging.error(f"保存验证测试失败: {e}")
            raise

    def test_edit_mode(self):
        """测试编辑模式"""
        try:
            edit_dialog = ProductDialog(product_id="TEST-001")
            self.assertEqual(edit_dialog.windowTitle(), "编辑商品")
            self.assertFalse(edit_dialog.product_id_input.isEnabled())

            logging.info("编辑模式测试通过")
        except Exception as e:
            logging.error(f"编辑模式测试失败: {e}")
            raise

    def test_merge_view(self):
        """测试合并视图功能"""
        try:
            merge_dialog = ProductDialog(is_merged_view=True)
            self.assertTrue(merge_dialog.merge_checkbox.isChecked())

            # 切换合并状态
            merge_dialog.merge_checkbox.setChecked(False)
            self.assertFalse(merge_dialog.merge_checkbox.isChecked())

            logging.info("合并视图测试通过")
        except Exception as e:
            logging.error(f"合并视图测试失败: {e}")
            raise

    def tearDown(self):
        """每个测试用例结束后的清理"""
        try:
            self.dialog.close()
            logging.info("成功关闭对话框")
        except Exception as e:
            logging.error(f"关闭对话框失败: {e}")
            raise

    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        try:
            cls.app.quit()
            logging.info("成功退出应用")
        except Exception as e:
            logging.error(f"退出应用失败: {e}")
            raise


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    unittest.main()
