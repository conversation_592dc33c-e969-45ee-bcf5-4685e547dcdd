import conftest
import unittest
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from gui.widgets.product_widgets import ProductTable, ProductToolBar
import sys


class TestProductWidgets(unittest.TestCase):
    """商品控件测试"""

    @classmethod
    def setUpClass(cls):
        """测试前创建应用实例"""
        cls.app = QApplication(sys.argv)

    def test_product_table(self):
        """测试商品表格"""
        table = ProductTable()

        # 测试表格初始化
        self.assertEqual(table.columnCount(), 21)
        self.assertEqual(table.rowCount(), 0)

        # 测试加载数据
        test_data = [
            [
                "P001",
                "测试商品",
                "10",
                "测试分类",
                "批次1",
                "A1",
                "供应商A",
                "http://supplier.com",
                "http://buy.com",
                "100",
                "10",
                "5",
                "115",
                "200",
                "100",
                "85",
                "张三",
                "http://sell.com",
                "",
                "正常",
                "个",
            ]
        ]
        table.load_products(test_data)

        # 验证数据加载
        self.assertEqual(table.rowCount(), 1)
        self.assertEqual(table.item(0, 0).text(), "P001")
        self.assertEqual(table.item(0, 1).text(), "测试商品")

        # 测试过滤功能
        table.filter_products("测试", "测试分类", "批次1")
        self.assertFalse(table.isRowHidden(0))

        table.filter_products("不存在")
        self.assertTrue(table.isRowHidden(0))

    def test_product_toolbar(self):
        """测试商品工具栏"""
        toolbar = ProductToolBar()

        # 测试信号发送
        signals_received = []

        def on_add():
            signals_received.append("add")

        def on_edit():
            signals_received.append("edit")

        def on_delete():
            signals_received.append("delete")

        def on_image():
            signals_received.append("image")

        def on_search(text):
            signals_received.append(f"search:{text}")

        def on_category(category):
            signals_received.append(f"category:{category}")

        def on_batch(batch):
            signals_received.append(f"batch:{batch}")

        def on_refresh():
            signals_received.append("refresh")

        def on_merge(state):
            signals_received.append(f"merge:{state}")

        # 连接信号
        toolbar.add_clicked.connect(on_add)
        toolbar.edit_clicked.connect(on_edit)
        toolbar.delete_clicked.connect(on_delete)
        toolbar.image_clicked.connect(on_image)
        toolbar.search_changed.connect(on_search)
        toolbar.category_changed.connect(on_category)
        toolbar.batch_changed.connect(on_batch)
        toolbar.refresh_clicked.connect(on_refresh)
        toolbar.merge_changed.connect(on_merge)

        # 触发信号
        toolbar.add_btn.click()
        toolbar.edit_btn.click()
        toolbar.delete_btn.click()
        toolbar.image_btn.click()
        toolbar.search_edit.setText("test")
        toolbar.category_combo.addItem("分类1")
        toolbar.category_combo.setCurrentText("分类1")
        toolbar.batch_combo.addItem("批次1")
        toolbar.batch_combo.setCurrentText("批次1")
        toolbar.refresh_btn.click()
        toolbar.merge_check.setChecked(False)

        # 验证信号接收
        expected_signals = [
            "add",
            "edit",
            "delete",
            "image",
            "search:test",
            "category:分类1",
            "batch:批次1",
            "refresh",
            "merge:False",
        ]
        self.assertEqual(signals_received, expected_signals)

        # 测试更新方法
        toolbar.update_categories(["分类A", "分类B"])
        self.assertEqual(toolbar.category_combo.count(), 3)  # 包含"所有分类"

        toolbar.update_batches(["批次A", "批次B"])
        self.assertEqual(toolbar.batch_combo.count(), 3)  # 包含"所有批次"


if __name__ == "__main__":
    unittest.main()
