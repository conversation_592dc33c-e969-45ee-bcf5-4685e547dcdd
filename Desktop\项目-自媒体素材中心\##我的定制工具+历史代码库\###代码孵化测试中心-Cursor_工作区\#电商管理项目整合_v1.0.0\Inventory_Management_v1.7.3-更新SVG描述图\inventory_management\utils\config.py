import os
import json
import logging
from PyQt6.QtCore import QSettings


class Config:
    """配置管理类"""

    _instance = None

    @classmethod
    def instance(cls):
        """获取单例实例"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def __init__(self):
        if Config._instance is not None:
            raise Exception("Config is a singleton class")

        # 配置文件路径
        self.config_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "config"
        )
        self.config_file = os.path.join(self.config_dir, "config.json")

        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)

        # 加载配置
        self.load_config()

    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
            else:
                self.config = self.get_default_config()
                self.save_config()

        except Exception as e:
            logging.exception("加载配置失败")
            self.config = self.get_default_config()

    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)

        except Exception as e:
            logging.exception("保存配置失败")

    def get_default_config(self):
        """获取默认配置"""
        return {
            "database": {
                "path": "data/inventory.db",
                "backup_dir": "backup",
                "auto_backup": True,
                "backup_interval": 24,  # 小时
            },
            "ui": {
                "theme": "dark",
                "language": "zh_CN",
                "font_size": 12,
                "window_size": [1720, 1200],
                "window_position": [100, 100],
            },
            "image": {
                "max_size": 1024,  # KB
                "quality": 85,
                "thumbnail_size": [100, 100],
                "preview_size": [800, 800],
            },
            "product": {"code_prefix": "P", "code_length": 8, "batch_size": 100},
            "batch": {"code_prefix": "B", "code_length": 8, "items_per_page": 50},
            "export": {
                "default_format": "xlsx",
                "encoding": "utf-8",
                "date_format": "%Y-%m-%d %H:%M:%S",
            },
        }

    def get(self, key, default=None):
        """获取配置值"""
        try:
            keys = key.split(".")
            value = self.config
            for k in keys:
                value = value[k]
            return value
        except:
            return default

    def set(self, key, value):
        """设置配置值"""
        try:
            keys = key.split(".")
            config = self.config
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            config[keys[-1]] = value
            self.save_config()
            return True
        except:
            return False

    def reset(self):
        """重置配置"""
        self.config = self.get_default_config()
        self.save_config()
