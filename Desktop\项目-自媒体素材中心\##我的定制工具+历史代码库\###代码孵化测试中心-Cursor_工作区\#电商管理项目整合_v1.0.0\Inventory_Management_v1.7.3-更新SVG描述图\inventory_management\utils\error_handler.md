# 错误处理模块 (error_handler.py)

## 功能概述
`error_handler.py` 提供了统一的错误处理机制，包括异常捕获、日志记录、用户界面提示等功能。该模块设计为静态工具类，可以在整个应用程序中方便地使用。

## 类说明

### ErrorHandler 类

#### 日志管理

1. **初始化日志记录器**
```python
@staticmethod
def init_logger():
```
- 功能：初始化日志系统
- 配置：
  - 日志文件：`logs/inventory_YYYYMMDD.log`
  - 日志级别：INFO
  - 时间格式：YYYY-MM-DD HH:MM:SS
  - 日志格式：时间 [级别] 消息

2. **日志记录方法**
```python
@staticmethod
def log_info(message)    # 信息日志
def log_warning(message) # 警告日志
def log_error(message)   # 错误日志
def log_debug(message)   # 调试日志
```
- 功能：记录不同级别的日志
- 参数：message - 日志消息
- 返回：记录成功返回 True，失败返回 False

#### 错误处理

1. **错误处理器**
```python
@staticmethod
def handle_error(error, parent=None, show_message=True):
```
- 功能：统一处理异常
- 参数：
  - error: 异常对象
  - parent: 父窗口（用于显示消息框）
  - show_message: 是否显示错误消息框
- 操作：
  - 记录错误类型
  - 记录错误信息
  - 记录堆栈跟踪
  - 显示错误消息框（可选）

2. **异常处理装饰器**
```python
@staticmethod
def exception_handler(show_message=True):
```
- 功能：用于装饰可能抛出异常的函数
- 参数：show_message - 是否显示错误消息
- 使用示例：
  ```python
  @ErrorHandler.exception_handler()
  def some_function():
      # 可能抛出异常的代码
      pass
  ```

#### 用户界面交互

1. **确认对话框**
```python
@staticmethod
def confirm_action(parent, title, message):
```
- 功能：显示确认对话框
- 参数：
  - parent: 父窗口
  - title: 对话框标题
  - message: 确认消息
- 返回：用户选择 Yes 返回 True，否则返回 False

2. **信息提示**
```python
@staticmethod
def show_info(parent, title, message):
```
- 功能：显示信息消息框
- 参数：
  - parent: 父窗口
  - title: 消息框标题
  - message: 提示信息

3. **警告提示**
```python
@staticmethod
def show_warning(parent, title, message):
```
- 功能：显示警告消息框
- 参数：
  - parent: 父窗口
  - title: 消息框标题
  - message: 警告信息

## 使用示例

### 1. 基本错误处理
```python
try:
    # 可能出错的代码
    raise ValueError("示例错误")
except Exception as e:
    ErrorHandler.handle_error(e, self)
```

### 2. 使用装饰器
```python
@ErrorHandler.exception_handler(show_message=True)
def process_data():
    # 处理数据的代码
    pass
```

### 3. 日志记录
```python
# 记录不同级别的日志
ErrorHandler.log_info("操作成功完成")
ErrorHandler.log_warning("发现潜在问题")
ErrorHandler.log_error("操作失败")
ErrorHandler.log_debug("调试信息")
```

### 4. 用户交互
```python
# 确认操作
if ErrorHandler.confirm_action(self, "确认", "是否继续？"):
    # 用户确认后的操作
    pass

# 显示信息
ErrorHandler.show_info(self, "成功", "操作已完成")

# 显示警告
ErrorHandler.show_warning(self, "警告", "请注意...")
```

## 依赖关系
- Python 标准库：
  - logging：日志记录
  - traceback：异常堆栈
  - datetime：时间处理
  - functools：装饰器工具
- PyQt5：
  - QMessageBox：消息框组件

## 错误处理流程
1. 异常捕获
2. 日志记录
3. 用户界面提示
4. 错误恢复（如果可能）

## 最佳实践
1. 在应用程序启动时调用 `init_logger()`
2. 对关键函数使用 `@exception_handler` 装饰器
3. 适当使用不同级别的日志记录
4. 为用户提供清晰的错误信息
5. 在出错时提供适当的恢复机制

## 注意事项
1. 确保日志目录存在且可写
2. 避免在日志记录中包含敏感信息
3. 合理使用日志级别
4. 保持错误消息简洁明了
5. 处理嵌套异常的情况 