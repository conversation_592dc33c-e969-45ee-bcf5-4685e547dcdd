import logging
import traceback
from datetime import datetime
from PyQt6.QtWidgets import QMessageBox
from functools import wraps


class ErrorHandler:
    """错误处理类"""

    @staticmethod
    def init_logger():
        """初始化日志记录器"""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s",
            handlers=[
                logging.FileHandler("error.log", encoding="utf-8"),
                logging.StreamHandler(),
            ],
        )

    @staticmethod
    def handle_error(error, parent=None):
        """处理错误并显示错误消息"""
        error_msg = str(error)
        error_detail = traceback.format_exc()

        # 记录错误日志
        logging.error(f"错误: {error_msg}")
        logging.error(f"详细信息:\n{error_detail}")

        # 显示错误消息框
        if parent:
            QMessageBox.critical(parent, "错误", error_msg)

    @staticmethod
    def log_info(message):
        """记录信息日志"""
        logging.info(message)

    @staticmethod
    def log_warning(message):
        """记录警告日志"""
        logging.warning(message)

    @staticmethod
    def log_error(message):
        """记录错误日志"""
        logging.error(message)

    @staticmethod
    def log_debug(message):
        """记录调试日志"""
        logging.debug(message)

    @staticmethod
    def exception_handler(func):
        """异常处理装饰器"""

        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                ErrorHandler.handle_error(e)
                raise

        return wrapper

    @staticmethod
    def confirm(parent, title, message):
        """显示确认对话框"""
        reply = QMessageBox.question(
            parent,
            title,
            message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No,
        )
        return reply == QMessageBox.StandardButton.Yes

    @staticmethod
    def info(parent, title, message):
        """显示信息对话框"""
        QMessageBox.information(parent, title, message)

    @staticmethod
    def warning(parent, title, message):
        """显示警告对话框"""
        QMessageBox.warning(parent, title, message)
