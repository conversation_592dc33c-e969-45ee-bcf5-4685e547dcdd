# 扫描器模块 (scanner.py)

## 功能概述
`scanner.py` 提供了二维码扫描和解码功能，支持从图片文件或摄像头输入中识别二维码。该模块基于 OpenCV 和 pyzbar 库实现，提供了灵活的扫描选项和清晰的结果输出。

## 核心功能

### 1. 二维码扫描
```python
def scan_qr_code(image_path=None, camera_index=0):
```
- 功能：扫描二维码
- 参数：
  - image_path: 图片路径（可选）
  - camera_index: 摄像头索引（默认0）
- 返回：扫描到的二维码内容列表
- 特点：
  - 支持图片文件输入
  - 支持摄像头实时扫描
  - 自动选择输入源

### 2. 图片解码
```python
def decode_image(image):
```
- 功能：解码图片中的二维码
- 参数：
  - image: OpenCV图片对象
- 返回：解码结果列表，每个结果包含：
  - type: 二维码类型
  - data: 解码内容
  - points: 二维码位置坐标
- 处理流程：
  1. 转换为灰度图
  2. 二维码检测
  3. 位置标记
  4. 内容解码

### 3. 摄像头扫描
```python
def scan_from_camera(camera_index=0):
```
- 功能：从摄像头实时扫描二维码
- 参数：
  - camera_index: 摄像头索引
- 返回：扫描到的二维码内容列表
- 特点：
  - 实时预览
  - 自动检测
  - ESC键退出
  - 资源自动释放

## 依赖关系
- 外部库：
  - OpenCV (cv2)：图像处理
  - pyzbar：二维码解码
  - numpy：数组处理
- Python 标准库：
  - logging：日志记录

## 使用示例

### 1. 扫描图片文件
```python
# 从图片文件扫描二维码
results = scan_qr_code(image_path="path/to/image.jpg")
for result in results:
    print(f"类型: {result['type']}")
    print(f"内容: {result['data']}")
```

### 2. 使用摄像头扫描
```python
# 使用默认摄像头扫描
results = scan_qr_code()  # 不指定 image_path 则使用摄像头

# 使用指定摄像头
results = scan_qr_code(camera_index=1)  # 使用第二个摄像头
```

### 3. 直接解码图片
```python
# 读取并解码图片
image = cv2.imread("path/to/image.jpg")
results = decode_image(image)
```

## 图像处理流程
1. 输入处理：
   - 图片文件读取或摄像头捕获
   - 格式转换（BGR到灰度）

2. 二维码检测：
   - 边缘检测
   - 轮廓识别
   - 位置确定

3. 内容解码：
   - 二维码类型识别
   - 数据提取
   - 字符串解码

4. 结果处理：
   - 位置标记
   - 数据整理
   - 结果返回

## 错误处理
- 图片读取失败处理
- 摄像头访问异常处理
- 解码错误处理
- 完整的日志记录

## 性能优化
1. 图像处理优化：
   - 使用灰度图像
   - 适当的图像尺寸
   - 高效的算法选择

2. 实时扫描优化：
   - 帧率控制
   - 资源及时释放
   - 内存管理

## 使用注意事项
1. 图片要求：
   - 清晰的二维码图像
   - 适当的光照条件
   - 合适的拍摄角度

2. 摄像头使用：
   - 确保摄像头可用
   - 正确的设备索引
   - 适当的扫描距离

3. 资源管理：
   - 及时释放摄像头
   - 关闭窗口
   - 异常情况处理

## 扩展建议
1. 支持更多条码类型
2. 添加图像预处理选项
3. 提供批量处理功能
4. 增加扫描结果缓存
5. 添加自定义识别区域 