import cv2
import logging
from pyzbar.pyzbar import decode
import numpy as np


def scan_qr_code(image_path=None, camera_index=0):
    """扫描二维码

    Args:
        image_path: 图片路径,如果为None则使用摄像头
        camera_index: 摄像头索引,默认为0

    Returns:
        扫描到的二维码内容列表
    """
    try:
        if image_path:
            # 从图片读取
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")
            return decode_image(image)
        else:
            # 从摄像头读取
            return scan_from_camera(camera_index)
    except Exception as e:
        logging.exception("扫描二维码失败")
        raise e


def decode_image(image):
    """解码图片中的二维码

    Args:
        image: OpenCV图片对象

    Returns:
        解码结果列表
    """
    try:
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # 解码
        decoded_objects = decode(gray)

        results = []
        for obj in decoded_objects:
            # 获取二维码类型和内容
            qr_type = obj.type
            data = obj.data.decode("utf-8")

            # 获取二维码位置
            points = obj.polygon
            if len(points) > 4:
                hull = cv2.convexHull(
                    np.array([point for point in points], dtype=np.float32)
                )
                points = hull

            # 在图片上标记二维码位置
            n = len(points)
            for j in range(n):
                cv2.line(image, points[j], points[(j + 1) % n], (255, 0, 0), 3)

            results.append({"type": qr_type, "data": data, "points": points})

        return results

    except Exception as e:
        logging.exception("解码二维码失败")
        raise e


def scan_from_camera(camera_index=0):
    """从摄像头扫描二维码

    Args:
        camera_index: 摄像头索引,默认为0

    Returns:
        扫描到的二维码内容列表
    """
    try:
        # 打开摄像头
        cap = cv2.VideoCapture(camera_index)
        if not cap.isOpened():
            raise ValueError(f"无法打开摄像头 {camera_index}")

        results = []
        while True:
            # 读取一帧
            ret, frame = cap.read()
            if not ret:
                break

            # 解码当前帧
            frame_results = decode_image(frame)
            if frame_results:
                results.extend(frame_results)
                break

            # 显示预览
            cv2.imshow("QR Code Scanner", frame)

            # 按ESC键退出
            if cv2.waitKey(1) & 0xFF == 27:
                break

        # 释放资源
        cap.release()
        cv2.destroyAllWindows()

        return results

    except Exception as e:
        logging.exception("从摄像头扫描二维码失败")
        raise e
