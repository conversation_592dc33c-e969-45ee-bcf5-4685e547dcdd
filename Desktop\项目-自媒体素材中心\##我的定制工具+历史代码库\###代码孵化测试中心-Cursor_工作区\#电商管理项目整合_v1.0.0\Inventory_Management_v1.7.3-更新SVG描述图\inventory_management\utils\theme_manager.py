import os
import logging
from PyQt6.QtWidgets import QApplication
from PyQt6.QtGui import QPalette, QColor
from PyQt6.QtCore import Qt


class ThemeManager:
    """主题管理类"""

    @staticmethod
    def apply_theme(app: QApplication, theme_name: str):
        """应用主题"""
        try:
            if theme_name == "dark":
                ThemeManager._apply_dark_theme(app)
            elif theme_name == "light":
                ThemeManager._apply_light_theme(app)
            else:
                # 默认使用暗色主题
                ThemeManager._apply_dark_theme(app)

        except Exception as e:
            logging.exception("应用主题失败")
            # 使用默认主题
            ThemeManager._apply_dark_theme(app)

    @staticmethod
    def _apply_dark_theme(app: QApplication):
        """应用暗色主题"""
        app.setStyle("Fusion")

        # 创建暗色调色板
        palette = QPalette()
        palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.Base, QColor(25, 25, 25))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.ToolTipBase, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
        palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
        palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.black)

        app.setPalette(palette)

        # 设置样式表
        app.setStyleSheet(
            """
            QToolTip {
                color: #ffffff;
                background-color: #2a82da;
                border: 1px solid white;
            }
            
            QTableWidget {
                gridline-color: #353535;
                background-color: #252525;
                border: 1px solid #353535;
                border-radius: 4px;
                padding: 2px;
            }
            
            QTableWidget::item {
                padding: 4px;
            }
            
            QTableWidget::item:selected {
                background-color: #2a82da;
            }
            
            QHeaderView::section {
                background-color: #353535;
                padding: 4px;
                border: 1px solid #252525;
                font-size: 14px;
            }
            
            QLineEdit {
                border: 1px solid #353535;
                border-radius: 4px;
                padding: 4px;
                background-color: #252525;
                color: white;
                selection-background-color: #2a82da;
            }
            
            QPushButton {
                border: 1px solid #353535;
                border-radius: 4px;
                background-color: #353535;
                min-width: 80px;
                padding: 6px;
            }
            
            QPushButton:hover {
                background-color: #404040;
            }
            
            QPushButton:pressed {
                background-color: #252525;
            }
            
            QComboBox {
                border: 1px solid #353535;
                border-radius: 4px;
                padding: 4px;
                min-width: 100px;
            }
            
            QComboBox:hover {
                border: 1px solid #2a82da;
            }
            
            QComboBox::drop-down {
                border: 0px;
            }
            
            QComboBox::down-arrow {
                image: url(resources/icons/down_arrow.png);
                width: 12px;
                height: 12px;
            }
            
            QComboBox::down-arrow:on {
                top: 1px;
                left: 1px;
            }
            
            QComboBox QAbstractItemView {
                border: 1px solid #353535;
                selection-background-color: #2a82da;
            }
            
            QScrollBar:vertical {
                border: 1px solid #353535;
                background: #252525;
                width: 12px;
                margin: 0px;
            }
            
            QScrollBar::handle:vertical {
                background: #353535;
                min-height: 20px;
            }
            
            QScrollBar::add-line:vertical {
                border: 1px solid #353535;
                background: #353535;
                height: 15px;
                subcontrol-position: bottom;
                subcontrol-origin: margin;
            }
            
            QScrollBar::sub-line:vertical {
                border: 1px solid #353535;
                background: #353535;
                height: 15px;
                subcontrol-position: top;
                subcontrol-origin: margin;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                border: 1px solid #353535;
                width: 3px;
                height: 3px;
                background: white;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """
        )

    @staticmethod
    def _apply_light_theme(app: QApplication):
        """应用亮色主题"""
        app.setStyle("Fusion")

        # 创建亮色调色板
        palette = QPalette()
        palette.setColor(QPalette.ColorRole.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.Base, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(233, 233, 233))
        palette.setColor(QPalette.ColorRole.ToolTipBase, Qt.GlobalColor.white)
        palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.black)
        palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
        palette.setColor(QPalette.ColorRole.Link, QColor(0, 0, 255))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(0, 120, 215))
        palette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.white)

        app.setPalette(palette)

        # 设置样式表
        app.setStyleSheet(
            """
            QToolTip {
                color: #000000;
                background-color: #ffffff;
                border: 1px solid #c0c0c0;
            }
            
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: #ffffff;
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                padding: 2px;
            }
            
            QTableWidget::item {
                padding: 4px;
            }
            
            QTableWidget::item:selected {
                background-color: #0078d7;
                color: #ffffff;
            }
            
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: 1px solid #c0c0c0;
                font-size: 14px;
            }
            
            QLineEdit {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                padding: 4px;
                background-color: #ffffff;
                selection-background-color: #0078d7;
            }
            
            QPushButton {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                background-color: #f0f0f0;
                min-width: 80px;
                padding: 6px;
            }
            
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            
            QComboBox {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                padding: 4px;
                min-width: 100px;
            }
            
            QComboBox:hover {
                border: 1px solid #0078d7;
            }
            
            QComboBox::drop-down {
                border: 0px;
            }
            
            QComboBox::down-arrow {
                image: url(resources/icons/down_arrow.png);
                width: 12px;
                height: 12px;
            }
            
            QComboBox::down-arrow:on {
                top: 1px;
                left: 1px;
            }
            
            QComboBox QAbstractItemView {
                border: 1px solid #c0c0c0;
                selection-background-color: #0078d7;
            }
            
            QScrollBar:vertical {
                border: 1px solid #c0c0c0;
                background: #f0f0f0;
                width: 12px;
                margin: 0px;
            }
            
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                min-height: 20px;
            }
            
            QScrollBar::add-line:vertical {
                border: 1px solid #c0c0c0;
                background: #f0f0f0;
                height: 15px;
                subcontrol-position: bottom;
                subcontrol-origin: margin;
            }
            
            QScrollBar::sub-line:vertical {
                border: 1px solid #c0c0c0;
                background: #f0f0f0;
                height: 15px;
                subcontrol-position: top;
                subcontrol-origin: margin;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                border: 1px solid #c0c0c0;
                width: 3px;
                height: 3px;
                background: black;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: none;
            }
        """
        )
