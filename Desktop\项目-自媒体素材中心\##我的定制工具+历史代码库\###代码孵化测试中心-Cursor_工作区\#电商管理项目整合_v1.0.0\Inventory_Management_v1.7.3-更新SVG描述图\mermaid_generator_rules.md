# 🧜‍♀️ Mermaid流程图生成规则

## 概述
作为SVG流程图的补充，提供基于Mermaid语法的快速流程图生成规则，可以在Markdown文档中直接使用。

## 通用Mermaid模板

### 1. 系统架构图 (C4 Model)
```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        UI[用户界面]
        Theme[主题管理]
        Dialog[对话框组件]
    end
    
    subgraph "业务逻辑层 (Business Layer)"
        Product[商品管理]
        Batch[批次管理]
        Finance[财务管理]
    end
    
    subgraph "数据访问层 (Data Layer)"
        DB[(数据库)]
        File[文件存储]
        Image[图片管理]
    end
    
    subgraph "基础设施层 (Infrastructure)"
        Config[配置管理]
        Log[日志系统]
        Error[错误处理]
    end
    
    UI --> Product
    UI --> Batch
    UI --> Finance
    Product --> DB
    Batch --> DB
    Finance --> DB
    Image --> File
    Product --> Image
    
    classDef presentation fill:#3498db,stroke:#2980b9,color:#fff
    classDef business fill:#27ae60,stroke:#1e8449,color:#fff
    classDef data fill:#e74c3c,stroke:#c0392b,color:#fff
    classDef infrastructure fill:#9b59b6,stroke:#8e44ad,color:#fff
    
    class UI,Theme,Dialog presentation
    class Product,Batch,Finance business
    class DB,File,Image data
    class Config,Log,Error infrastructure
```

### 2. 业务流程图 (Flowchart)
```mermaid
flowchart TD
    Start([开始]) --> Input[用户输入]
    Input --> Validate{数据验证}
    Validate -->|通过| Process[业务处理]
    Validate -->|失败| Error[显示错误]
    Error --> Input
    Process --> Save[保存数据]
    Save --> Success{操作成功?}
    Success -->|是| Notify[成功通知]
    Success -->|否| ErrorHandle[错误处理]
    ErrorHandle --> Log[记录日志]
    Log --> Error
    Notify --> End([结束])
    
    classDef startEnd fill:#27ae60,stroke:#1e8449,color:#fff
    classDef process fill:#3498db,stroke:#2980b9,color:#fff
    classDef decision fill:#f39c12,stroke:#e67e22,color:#fff
    classDef error fill:#e74c3c,stroke:#c0392b,color:#fff
    
    class Start,End startEnd
    class Input,Process,Save,Notify process
    class Validate,Success decision
    class Error,ErrorHandle,Log error
```

### 3. 数据流程图 (Sequence Diagram)
```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 用户界面
    participant BL as 业务逻辑
    participant DB as 数据库
    participant FS as 文件系统
    
    U->>UI: 创建商品
    UI->>UI: 验证输入
    alt 验证通过
        UI->>BL: 处理商品数据
        BL->>DB: 保存商品信息
        DB-->>BL: 返回商品ID
        opt 有图片
            BL->>FS: 保存图片文件
            FS-->>BL: 返回文件路径
            BL->>DB: 更新图片路径
        end
        BL-->>UI: 返回成功结果
        UI-->>U: 显示成功消息
    else 验证失败
        UI-->>U: 显示错误信息
    end
```

### 4. 状态图 (State Diagram)
```mermaid
stateDiagram-v2
    [*] --> 待处理
    待处理 --> 处理中 : 开始处理
    处理中 --> 已完成 : 处理成功
    处理中 --> 处理失败 : 处理异常
    处理失败 --> 待处理 : 重新处理
    处理失败 --> 已取消 : 取消处理
    已完成 --> [*]
    已取消 --> [*]
    
    待处理 : 等待用户操作
    处理中 : 系统正在处理
    已完成 : 操作成功完成
    处理失败 : 出现错误
    已取消 : 用户取消操作
```

### 5. 实体关系图 (ER Diagram)
```mermaid
erDiagram
    PRODUCT {
        string product_id PK
        string name
        string category
        int quantity
        decimal purchase_price
        decimal selling_price
        string image_path
        datetime created_at
        datetime updated_at
    }
    
    BATCH {
        string batch_id PK
        string code UK
        string name
        string status
        datetime created_at
    }
    
    PRODUCT_BATCH {
        string product_id FK
        string batch_id FK
        int quantity
        datetime created_at
    }
    
    PRODUCT_IMAGE {
        string image_id PK
        string product_id FK
        string image_path
        boolean is_primary
        datetime created_at
    }
    
    TRANSACTION {
        string transaction_id PK
        string product_id FK
        string transaction_type
        int quantity
        decimal unit_price
        decimal total_amount
        datetime transaction_date
    }
    
    PRODUCT ||--o{ PRODUCT_BATCH : "belongs to"
    BATCH ||--o{ PRODUCT_BATCH : "contains"
    PRODUCT ||--o{ PRODUCT_IMAGE : "has"
    PRODUCT ||--o{ TRANSACTION : "involves"
```

### 6. 甘特图 (Gantt Chart)
```mermaid
gantt
    title 项目开发时间线
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求收集           :done,    req1, 2024-01-01, 2024-01-07
    需求分析           :done,    req2, 2024-01-08, 2024-01-14
    section 系统设计
    架构设计           :done,    design1, 2024-01-15, 2024-01-21
    数据库设计         :done,    design2, 2024-01-22, 2024-01-28
    section 开发阶段
    核心功能开发       :active,  dev1, 2024-01-29, 2024-02-25
    界面开发           :         dev2, 2024-02-12, 2024-03-10
    测试阶段           :         test1, 2024-03-11, 2024-03-24
    section 部署上线
    部署准备           :         deploy1, 2024-03-25, 2024-03-31
    正式上线           :         deploy2, 2024-04-01, 2024-04-07
```

## 项目类型映射

### Web应用项目
```mermaid
graph LR
    subgraph "前端"
        React[React/Vue/Angular]
        Router[路由管理]
        State[状态管理]
    end
    
    subgraph "后端"
        API[REST API]
        Auth[身份认证]
        Business[业务逻辑]
    end
    
    subgraph "数据层"
        DB[(数据库)]
        Cache[(缓存)]
        File[文件存储]
    end
    
    React --> API
    Router --> React
    State --> React
    API --> Auth
    API --> Business
    Business --> DB
    Business --> Cache
    Business --> File
```

### 微服务架构
```mermaid
graph TB
    subgraph "API网关"
        Gateway[API Gateway]
    end
    
    subgraph "用户服务"
        UserAPI[User API]
        UserDB[(User DB)]
    end
    
    subgraph "订单服务"
        OrderAPI[Order API]
        OrderDB[(Order DB)]
    end
    
    subgraph "支付服务"
        PaymentAPI[Payment API]
        PaymentDB[(Payment DB)]
    end
    
    subgraph "消息队列"
        MQ[Message Queue]
    end
    
    Gateway --> UserAPI
    Gateway --> OrderAPI
    Gateway --> PaymentAPI
    
    UserAPI --> UserDB
    OrderAPI --> OrderDB
    PaymentAPI --> PaymentDB
    
    OrderAPI --> MQ
    PaymentAPI --> MQ
    UserAPI --> MQ
```

### 数据处理流水线
```mermaid
graph LR
    subgraph "数据源"
        API1[API数据]
        File1[文件数据]
        DB1[(数据库)]
    end
    
    subgraph "数据处理"
        Extract[数据提取]
        Transform[数据转换]
        Load[数据加载]
    end
    
    subgraph "数据存储"
        DW[(数据仓库)]
        Lake[(数据湖)]
        Cache[(缓存)]
    end
    
    subgraph "数据应用"
        BI[商业智能]
        ML[机器学习]
        Report[报表系统]
    end
    
    API1 --> Extract
    File1 --> Extract
    DB1 --> Extract
    
    Extract --> Transform
    Transform --> Load
    
    Load --> DW
    Load --> Lake
    Load --> Cache
    
    DW --> BI
    Lake --> ML
    Cache --> Report
```

## 使用指南

### 在Markdown中使用
```markdown
# 项目架构图

```mermaid
graph TB
    A[开始] --> B[处理]
    B --> C[结束]
```
```

### 在Cursor中生成
1. 输入: `@mermaid-generator "生成系统架构图"`
2. 选择图表类型
3. 自动生成Mermaid代码
4. 可以直接复制到Markdown文档

### 导出为图片
```bash
# 安装mermaid-cli
npm install -g @mermaid-js/mermaid-cli

# 导出为PNG
mmdc -i diagram.mmd -o diagram.png

# 导出为SVG
mmdc -i diagram.mmd -o diagram.svg
```

## 样式定制

### 主题配置
```mermaid
%%{init: {'theme':'dark', 'themeVariables': { 'primaryColor': '#ff0000'}}}%%
graph TD
    A[开始] --> B[处理]
```

### 自定义CSS
```css
.node rect {
    fill: #3498db;
    stroke: #2980b9;
    stroke-width: 2px;
}

.node text {
    fill: #ffffff;
    font-family: Arial, sans-serif;
    font-size: 14px;
}
```

## 最佳实践

### 1. 保持简洁
- 避免过于复杂的图表
- 使用清晰的标签和描述
- 合理使用颜色和样式

### 2. 逻辑清晰
- 确保流程方向一致
- 使用适当的连接线类型
- 添加必要的注释说明

### 3. 可维护性
- 使用版本控制跟踪变更
- 定期更新图表内容
- 保持与代码的同步

---

**创建日期**: 2025-06-23  
**版本**: v1.0.0  
**兼容性**: Mermaid.js, GitHub, GitLab, Notion等支持Mermaid的平台
