<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 库存管理系统 - 架构图总览 v1.7.3 (v1.1.6缩放功能全面增强版)</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: rgba(52, 152, 219, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .info-card h3 {
            color: #3498db;
            margin-bottom: 5px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 40px;
            margin-bottom: 30px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .chart-header {
            margin-bottom: 20px;
        }

        .chart-header h2 {
            color: #2c3e50;
            font-size: 1.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .chart-icon {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            border-radius: 6px;
        }

        .tech-icon {
            background: #3498db;
        }

        .system-icon {
            background: #f39c12;
        }

        .business-icon {
            background: #27ae60;
        }

        .data-icon {
            background: #9b59b6;
        }

        .chart-description {
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        .chart-preview {
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chart-preview:hover {
            border-color: #3498db;
            background: #f1f5f9;
        }

        .chart-preview svg {
            width: 100%;
            height: auto;
            max-height: 280px;
        }

        .chart-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .features-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .feature-card {
            padding: 20px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 5px 0;
            color: #7f8c8d;
        }

        .feature-list li:before {
            content: "✓";
            color: #27ae60;
            font-weight: bold;
            margin-right: 8px;
        }

        .footer {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .footer p {
            color: #95a5a6;
            margin-bottom: 5px;
        }

        /* v1.1.6 缩放预览功能样式 */
        .btn-zoom {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-zoom:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* v1.1.6 大尺寸缩放弹窗样式 */
        .zoom-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(5px);
        }

        .zoom-modal-content {
            background: white;
            border-radius: 12px;
            padding: 15px;
            width: 1300px;
            /* 固定大尺寸 */
            height: 1200px;
            /* 固定大尺寸 */
            max-width: 95vw;
            /* 响应式后备 */
            max-height: 95vh;
            /* 响应式后备 */
            overflow: auto;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .zoom-modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #64748b;
            z-index: 1001;
        }

        .zoom-modal-close:hover {
            color: #1e293b;
        }

        /* 响应式缩放适配 */
        @media (max-width: 1400px) {
            .zoom-modal-content {
                width: 90vw;
                height: 90vh;
            }
        }

        @media (max-width: 1000px) {
            .zoom-modal-content {
                width: 95vw;
                height: 95vh;
            }
        }

        @media (max-width: 768px) {
            .zoom-modal-content {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
                padding: 10px;
            }
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <!-- 头部信息 -->
        <div class="header">
            <h1>📊 库存管理系统 - 架构图总览</h1>
            <p>基于 PyQt6 的现代化桌面应用程序 - v1.7.3 (v1.1.6缩放功能全面增强版)</p>

            <div class="info-grid">
                <div class="info-card">
                    <h3>项目类型</h3>
                    <p>桌面应用</p>
                </div>
                <div class="info-card">
                    <h3>技术栈</h3>
                    <p>Python + PyQt6</p>
                </div>
                <div class="info-card">
                    <h3>架构模式</h3>
                    <p>分层架构</p>
                </div>
                <div class="info-card">
                    <h3>图表数量</h3>
                    <p>4个专业图表</p>
                </div>
            </div>
        </div>

        <!-- 架构图表展示 -->
        <div class="charts-grid">
            <!-- 技术栈架构图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h2>
                        <div class="chart-icon tech-icon"></div>
                        技术栈架构图
                    </h2>
                    <div class="chart-description">
                        展示系统的分层技术架构，包括前端UI层、Python核心层、数据存储层和工具支持层的完整技术组件。
                    </div>
                </div>
                <div class="chart-preview">
                    <embed src="技术栈架构图-库存管理系统-v1.7.3.svg" type="image/svg+xml" width="100%" height="100%">
                </div>
                <div class="chart-actions">
                    <button class="btn-zoom" onclick="showZoomPreview('技术栈架构图-库存管理系统-v1.7.3.svg', '技术栈架构图')">
                        🔍 缩放预览
                    </button>
                    <a href="技术栈架构图-库存管理系统-v1.7.3.svg" target="_blank" class="btn btn-primary">
                        📄 查看详细
                    </a>
                    <a href="技术栈架构图-库存管理系统-v1.7.3.svg" download class="btn btn-secondary">
                        📥 下载
                    </a>
                </div>
            </div>

            <!-- 系统架构概览图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h2>
                        <div class="chart-icon system-icon"></div>
                        系统架构概览图
                    </h2>
                    <div class="chart-description">
                        展示系统的整体架构设计，包括用户交互层、应用逻辑层、服务接口层和数据持久层的职责分工。
                    </div>
                </div>
                <div class="chart-preview">
                    <embed src="系统架构概览图-库存管理系统-v1.7.3.svg" type="image/svg+xml" width="100%" height="100%">
                </div>
                <div class="chart-actions">
                    <button class="btn-zoom" onclick="showZoomPreview('系统架构概览图-库存管理系统-v1.7.3.svg', '系统架构概览图')">
                        🔍 缩放预览
                    </button>
                    <a href="系统架构概览图-库存管理系统-v1.7.3.svg" target="_blank" class="btn btn-primary">
                        📄 查看详细
                    </a>
                    <a href="系统架构概览图-库存管理系统-v1.7.3.svg" download class="btn btn-secondary">
                        📥 下载
                    </a>
                </div>
            </div>

            <!-- 业务处理流程图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h2>
                        <div class="chart-icon business-icon"></div>
                        业务处理流程图
                    </h2>
                    <div class="chart-description">
                        展示核心业务处理流程，包括产品管理、批次管理、财务统计和数据同步备份的完整业务逻辑。
                    </div>
                </div>
                <div class="chart-preview">
                    <embed src="业务处理流程图-库存管理系统-v1.7.3.svg" type="image/svg+xml" width="100%" height="100%">
                </div>
                <div class="chart-actions">
                    <button class="btn-zoom" onclick="showZoomPreview('业务处理流程图-库存管理系统-v1.7.3.svg', '业务处理流程图')">
                        🔍 缩放预览
                    </button>
                    <a href="业务处理流程图-库存管理系统-v1.7.3.svg" target="_blank" class="btn btn-primary">
                        📄 查看详细
                    </a>
                    <a href="业务处理流程图-库存管理系统-v1.7.3.svg" download class="btn btn-secondary">
                        📥 下载
                    </a>
                </div>
            </div>

            <!-- 数据流向图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h2>
                        <div class="chart-icon data-icon"></div>
                        数据流向图
                    </h2>
                    <div class="chart-description">
                        展示数据实体关系和处理流程，包括核心实体、关联实体、数据处理节点和外部系统接口。
                    </div>
                </div>
                <div class="chart-preview">
                    <embed src="数据流向图-库存管理系统-v1.7.3.svg" type="image/svg+xml" width="100%" height="100%">
                </div>
                <div class="chart-actions">
                    <button class="btn-zoom" onclick="showZoomPreview('数据流向图-库存管理系统-v1.7.3.svg', '数据流向图')">
                        🔍 缩放预览
                    </button>
                    <a href="数据流向图-库存管理系统-v1.7.3.svg" target="_blank" class="btn btn-primary">
                        📄 查看详细
                    </a>
                    <a href="数据流向图-库存管理系统-v1.7.3.svg" download class="btn btn-secondary">
                        📥 下载
                    </a>
                </div>
            </div>
        </div>

        <!-- 系统特性 -->
        <div class="features-section">
            <h2>系统特性与架构优势</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <h3>技术特点</h3>
                    <ul class="feature-list">
                        <li>现代化界面设计</li>
                        <li>高性能数据处理</li>
                        <li>完整的数据验证</li>
                        <li>模块化组件设计</li>
                        <li>丰富的功能集成</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>架构优势</h3>
                    <ul class="feature-list">
                        <li>清晰的分层架构</li>
                        <li>数据一致性保证</li>
                        <li>健壮的错误处理</li>
                        <li>智能缓存机制</li>
                        <li>易于维护扩展</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>核心功能</h3>
                    <ul class="feature-list">
                        <li>产品信息管理</li>
                        <li>批次库存控制</li>
                        <li>财务数据分析</li>
                        <li>图像处理支持</li>
                        <li>条码扫描集成</li>
                    </ul>
                </div>
                <div class="feature-card">
                    <h3>技术栈</h3>
                    <ul class="feature-list">
                        <li>Python 3.8+ 运行时</li>
                        <li>PyQt6 界面框架</li>
                        <li>SQLite 数据存储</li>
                        <li>pandas 数据分析</li>
                        <li>PIL/OpenCV 图像处理</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p><strong>🎨 生成器版本:</strong> SVG专业流程图生成器 v1.1.6 (缩放功能全面增强版)</p>
            <p><strong>📅 生成日期:</strong> 2024-01-02</p>
            <p><strong>🚀 项目版本:</strong> 库存管理系统 v1.7.3</p>
            <p><strong>✨ 新特性:</strong> 🔍 1300×1200大尺寸缩放预览 | 📱 响应式适配 | 🎯 1行1列完整展示</p>
            <p>本页面采用v1.1.6标准，提供专业级架构图展示和增强的缩放预览体验。</p>
        </div>
    </div>

    <!-- v1.1.6 缩放预览模态框 -->
    <div id="zoomModal" class="zoom-modal">
        <div class="zoom-modal-content">
            <button class="zoom-modal-close" onclick="closeZoomModal()">&times;</button>
            <div id="zoomChart"></div>
        </div>
    </div>

    <script>
        // v1.1.6 增强缩放预览功能
        function showZoomPreview(svgPath, chartTitle) {
            const modal = document.getElementById('zoomModal');
            const zoomChart = document.getElementById('zoomChart');

            // 设置弹窗内容 - 使用object标签优先，iframe作为后备
            zoomChart.innerHTML = `
                <div style="text-align: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: #1e293b; font-size: 1.5em;">${chartTitle}</h3>
                    <p style="margin: 5px 0 0 0; color: #64748b; font-size: 0.9em;">🔍 1300×1200 大尺寸缩放预览 - 库存管理系统 v1.7.3</p>
                </div>
                <div style="width: 100%; height: calc(100% - 80px); border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                    <object data="${svgPath}" type="image/svg+xml" style="width: 100%; height: 100%;">
                        <iframe src="${svgPath}" style="width: 100%; height: 100%; border: none;">
                            <p>您的浏览器不支持SVG格式。<a href="${svgPath}" target="_blank">点击这里直接查看</a></p>
                        </iframe>
                    </object>
                </div>
            `;

            // 显示弹窗
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // 添加加载提示
            setTimeout(() => {
                const objectElement = zoomChart.querySelector('object');
                if (objectElement) {
                    objectElement.addEventListener('load', function () {
                        console.log('SVG加载成功:', chartTitle);
                    });
                }
            }, 100);
        }

        function closeZoomModal() {
            const modal = document.getElementById('zoomModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 增强的事件监听
        document.addEventListener('DOMContentLoaded', function () {
            const zoomModal = document.getElementById('zoomModal');

            // 点击背景关闭弹窗
            zoomModal.addEventListener('click', function (e) {
                if (e.target === this) {
                    closeZoomModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape') {
                    closeZoomModal();
                }
            });

            // 防止弹窗内容区域点击关闭
            const zoomContent = document.querySelector('.zoom-modal-content');
            if (zoomContent) {
                zoomContent.addEventListener('click', function (e) {
                    e.stopPropagation();
                });
            }

            // 为图表预览区域添加点击缩放功能
            const chartPreviews = document.querySelectorAll('.chart-preview');
            chartPreviews.forEach((preview, index) => {
                const svgPaths = [
                    '技术栈架构图-库存管理系统-v1.7.3.svg',
                    '系统架构概览图-库存管理系统-v1.7.3.svg',
                    '业务处理流程图-库存管理系统-v1.7.3.svg',
                    '数据流向图-库存管理系统-v1.7.3.svg'
                ];
                const chartTitles = [
                    '技术栈架构图',
                    '系统架构概览图',
                    '业务处理流程图',
                    '数据流向图'
                ];

                preview.addEventListener('click', function () {
                    showZoomPreview(svgPaths[index], chartTitles[index]);
                });
            });
        });
    </script>
</body>

</html>