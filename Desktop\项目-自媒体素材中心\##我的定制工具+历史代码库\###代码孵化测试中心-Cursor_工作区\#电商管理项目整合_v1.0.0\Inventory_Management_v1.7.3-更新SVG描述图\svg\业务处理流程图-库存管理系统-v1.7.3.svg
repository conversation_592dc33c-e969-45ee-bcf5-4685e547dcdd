<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1600 1000" width="1600" height="1000">
  <!-- 业务处理流程图 - 库存管理系统 v1.7.3 -->
  
  <!-- 渐变定义 -->
  <defs>
    <!-- 背景渐变 -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1"/>
    </linearGradient>

    <!-- 开始结束节点渐变 -->
    <linearGradient id="startEndGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#059669;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1"/>
    </linearGradient>

    <!-- 处理节点渐变 -->
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1"/>
    </linearGradient>

    <!-- 决策节点渐变 -->
    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ea580c;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1"/>
    </linearGradient>

    <!-- 数据节点渐变 -->
    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1"/>
      <stop offset="100%" style="stop-color:#6b21b6;stop-opacity:1"/>
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.2"/>
    </filter>

    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280"/>
    </marker>

    <!-- 决策箭头 -->
    <marker id="decisionArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ea580c"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1600" height="1000" fill="url(#bgGradient)"/>

  <!-- 标题区域 -->
  <rect x="50" y="30" width="1500" height="60" fill="white" rx="15" filter="url(#shadow)"/>
  <text x="800" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="#1e293b">
    🔄 库存管理系统 - 业务处理流程图 v1.7.3
  </text>
  <text x="800" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#64748b">
    产品管理 | 批次处理 | 财务统计 | 数据同步
  </text>

  <!-- 流程1: 产品管理流程 -->
  <g id="productFlow">
    <!-- 开始 -->
    <rect x="80" y="120" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="140" y="135" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">开始</text>
    <text x="140" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">产品管理</text>

    <!-- 选择操作类型 -->
    <polygon points="270,120 370,145 270,170 170,145" fill="url(#decisionGradient)" filter="url(#shadow)"/>
    <text x="270" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">选择操作</text>
    <text x="270" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">类型？</text>

    <!-- 添加产品 -->
    <rect x="420" y="80" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="480" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">添加产品</text>
    <text x="480" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">输入产品信息</text>
    <text x="480" y="122" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">上传产品图片</text>

    <!-- 编辑产品 -->
    <rect x="420" y="145" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="480" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">编辑产品</text>
    <text x="480" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">修改产品信息</text>
    <text x="480" y="187" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">更新图片资源</text>

    <!-- 删除产品 -->
    <rect x="420" y="210" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="480" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">删除产品</text>
    <text x="480" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">确认删除操作</text>
    <text x="480" y="252" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">清理关联数据</text>

    <!-- 数据验证 -->
    <rect x="600" y="145" width="120" height="50" fill="url(#dataGradient)" rx="15" filter="url(#shadow)"/>
    <text x="660" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">数据验证</text>
    <text x="660" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">格式检查</text>
    <text x="660" y="187" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">完整性验证</text>

    <!-- 更新数据库 -->
    <rect x="780" y="145" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="840" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">更新数据库</text>
    <text x="840" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">SQLite操作</text>
    <text x="840" y="187" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">事务提交</text>

    <!-- 操作成功 -->
    <rect x="960" y="145" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="1020" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">操作完成</text>
    <text x="1020" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">更新界面</text>
    <text x="1020" y="187" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">记录日志</text>
  </g>

  <!-- 流程2: 批次管理流程 -->
  <g id="batchFlow">
    <!-- 开始 -->
    <rect x="80" y="320" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="140" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">开始</text>
    <text x="140" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">批次管理</text>

    <!-- 扫码识别 -->
    <rect x="240" y="320" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="300" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">扫码识别</text>
    <text x="300" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">条码扫描</text>
    <text x="300" y="362" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">产品匹配</text>

    <!-- 批次信息验证 -->
    <polygon points="420,320 520,345 420,370 320,345" fill="url(#decisionGradient)" filter="url(#shadow)"/>
    <text x="420" y="340" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">批次信息</text>
    <text x="420" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">是否存在？</text>

    <!-- 创建新批次 -->
    <rect x="580" y="280" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="640" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">创建新批次</text>
    <text x="640" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">生成批次号</text>
    <text x="640" y="322" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">设置初始库存</text>

    <!-- 更新现有批次 -->
    <rect x="580" y="360" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="640" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">更新现有批次</text>
    <text x="640" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">库存变动</text>
    <text x="640" y="402" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">状态更新</text>

    <!-- 库存计算 -->
    <rect x="750" y="320" width="120" height="50" fill="url(#dataGradient)" rx="15" filter="url(#shadow)"/>
    <text x="810" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">库存计算</text>
    <text x="810" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">自动统计</text>
    <text x="810" y="362" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">预警检查</text>

    <!-- 记录事务 -->
    <rect x="920" y="320" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="980" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">记录事务</text>
    <text x="980" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">事务日志</text>
    <text x="980" y="362" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">审计跟踪</text>

    <!-- 完成 -->
    <rect x="1100" y="320" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="1160" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">完成</text>
    <text x="1160" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">批次处理</text>
  </g>

  <!-- 流程3: 财务统计流程 -->
  <g id="financeFlow">
    <!-- 开始 -->
    <rect x="80" y="520" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="140" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">开始</text>
    <text x="140" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">财务分析</text>

    <!-- 数据收集 -->
    <rect x="240" y="520" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="300" y="535" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">数据收集</text>
    <text x="300" y="550" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">销售记录</text>
    <text x="300" y="562" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">成本信息</text>

    <!-- 选择统计类型 -->
    <polygon points="420,520 520,545 420,570 320,545" fill="url(#decisionGradient)" filter="url(#shadow)"/>
    <text x="420" y="540" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">统计类型</text>
    <text x="420" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">选择？</text>

    <!-- 销售统计 -->
    <rect x="580" y="480" width="120" height="50" fill="url(#dataGradient)" rx="15" filter="url(#shadow)"/>
    <text x="640" y="495" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">销售统计</text>
    <text x="640" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">pandas分析</text>
    <text x="640" y="522" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">图表生成</text>

    <!-- 利润分析 -->
    <rect x="580" y="540" width="120" height="50" fill="url(#dataGradient)" rx="15" filter="url(#shadow)"/>
    <text x="640" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">利润分析</text>
    <text x="640" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">成本计算</text>
    <text x="640" y="582" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">利润率统计</text>

    <!-- 趋势分析 -->
    <rect x="580" y="600" width="120" height="50" fill="url(#dataGradient)" rx="15" filter="url(#shadow)"/>
    <text x="640" y="615" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">趋势分析</text>
    <text x="640" y="630" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">时间序列</text>
    <text x="640" y="642" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">预测模型</text>

    <!-- 生成报表 -->
    <rect x="750" y="540" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="810" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">生成报表</text>
    <text x="810" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">Excel导出</text>
    <text x="810" y="582" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">图表展示</text>

    <!-- 保存结果 -->
    <rect x="920" y="540" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="980" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">保存结果</text>
    <text x="980" y="570" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">文件存储</text>
    <text x="980" y="582" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">历史记录</text>

    <!-- 完成 -->
    <rect x="1100" y="540" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="1160" y="555" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">完成</text>
    <text x="1160" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">财务分析</text>
  </g>

  <!-- 流程4: 数据同步与备份 -->
  <g id="syncFlow">
    <!-- 自动备份触发 -->
    <rect x="80" y="720" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="140" y="735" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">定时触发</text>
    <text x="140" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">自动备份</text>

    <!-- 检查数据变更 -->
    <polygon points="270,720 370,745 270,770 170,745" fill="url(#decisionGradient)" filter="url(#shadow)"/>
    <text x="270" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="white">数据变更</text>
    <text x="270" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">检查？</text>

    <!-- 创建备份 -->
    <rect x="420" y="720" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="480" y="735" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">创建备份</text>
    <text x="480" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">数据库备份</text>
    <text x="480" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">文件备份</text>

    <!-- 压缩存储 -->
    <rect x="580" y="720" width="120" height="50" fill="url(#dataGradient)" rx="15" filter="url(#shadow)"/>
    <text x="640" y="735" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">压缩存储</text>
    <text x="640" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">ZIP压缩</text>
    <text x="640" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">版本标记</text>

    <!-- 清理旧备份 -->
    <rect x="740" y="720" width="120" height="50" fill="url(#processGradient)" rx="15" filter="url(#shadow)"/>
    <text x="800" y="735" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white">清理旧备份</text>
    <text x="800" y="750" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">保留策略</text>
    <text x="800" y="762" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="white">空间管理</text>

    <!-- 等待下次 -->
    <rect x="900" y="720" width="120" height="50" fill="url(#startEndGradient)" rx="25" filter="url(#shadow)"/>
    <text x="960" y="735" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" fill="white">等待下次</text>
    <text x="960" y="755" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">定时循环</text>
  </g>

  <!-- 连接线 -->
  <g id="flowConnections" stroke="#6b7280" stroke-width="2" fill="none">
    <!-- 产品管理流程连接 -->
    <line x1="200" y1="145" x2="220" y2="145" marker-end="url(#arrowhead)"/>
    <line x1="320" y1="125" x2="420" y2="105" marker-end="url(#decisionArrow)"/>
    <line x1="320" y1="145" x2="420" y2="170" marker-end="url(#decisionArrow)"/>
    <line x1="320" y1="165" x2="420" y2="235" marker-end="url(#decisionArrow)"/>
    <line x1="540" y1="105" x2="600" y2="160" marker-end="url(#arrowhead)"/>
    <line x1="540" y1="170" x2="600" y2="170" marker-end="url(#arrowhead)"/>
    <line x1="540" y1="235" x2="600" y2="180" marker-end="url(#arrowhead)"/>
    <line x1="720" y1="170" x2="780" y2="170" marker-end="url(#arrowhead)"/>
    <line x1="900" y1="170" x2="960" y2="170" marker-end="url(#arrowhead)"/>

    <!-- 批次管理流程连接 -->
    <line x1="200" y1="345" x2="240" y2="345" marker-end="url(#arrowhead)"/>
    <line x1="360" y1="345" x2="380" y2="345" marker-end="url(#arrowhead)"/>
    <line x1="470" y1="325" x2="580" y2="305" marker-end="url(#decisionArrow)"/>
    <line x1="470" y1="365" x2="580" y2="385" marker-end="url(#decisionArrow)"/>
    <line x1="700" y1="305" x2="750" y2="335" marker-end="url(#arrowhead)"/>
    <line x1="700" y1="385" x2="750" y2="355" marker-end="url(#arrowhead)"/>
    <line x1="870" y1="345" x2="920" y2="345" marker-end="url(#arrowhead)"/>
    <line x1="1040" y1="345" x2="1100" y2="345" marker-end="url(#arrowhead)"/>

    <!-- 财务统计流程连接 -->
    <line x1="200" y1="545" x2="240" y2="545" marker-end="url(#arrowhead)"/>
    <line x1="360" y1="545" x2="380" y2="545" marker-end="url(#arrowhead)"/>
    <line x1="470" y1="525" x2="580" y2="505" marker-end="url(#decisionArrow)"/>
    <line x1="470" y1="545" x2="580" y2="565" marker-end="url(#decisionArrow)"/>
    <line x1="470" y1="565" x2="580" y2="625" marker-end="url(#decisionArrow)"/>
    <line x1="700" y1="505" x2="750" y2="555" marker-end="url(#arrowhead)"/>
    <line x1="700" y1="565" x2="750" y2="565" marker-end="url(#arrowhead)"/>
    <line x1="700" y1="625" x2="750" y2="575" marker-end="url(#arrowhead)"/>
    <line x1="870" y1="565" x2="920" y2="565" marker-end="url(#arrowhead)"/>
    <line x1="1040" y1="565" x2="1100" y2="565" marker-end="url(#arrowhead)"/>

    <!-- 数据同步流程连接 -->
    <line x1="200" y1="745" x2="220" y2="745" marker-end="url(#arrowhead)"/>
    <line x1="320" y1="745" x2="420" y2="745" marker-end="url(#decisionArrow)"/>
    <line x1="540" y1="745" x2="580" y2="745" marker-end="url(#arrowhead)"/>
    <line x1="700" y1="745" x2="740" y2="745" marker-end="url(#arrowhead)"/>
    <line x1="860" y1="745" x2="900" y2="745" marker-end="url(#arrowhead)"/>

    <!-- 流程间连接 -->
    <path d="M 1020 195 Q 1150 250 1160 320" stroke="#4ade80" stroke-width="2" stroke-dasharray="5,5" fill="none" marker-end="url(#arrowhead)"/>
    <path d="M 1160 370 Q 1180 450 1160 520" stroke="#4ade80" stroke-width="2" stroke-dasharray="5,5" fill="none" marker-end="url(#arrowhead)"/>
    <path d="M 1160 590 Q 1100 650 960 720" stroke="#4ade80" stroke-width="2" stroke-dasharray="5,5" fill="none" marker-end="url(#arrowhead)"/>
  </g>

  <!-- 标签说明 -->
  <g id="labels" font-family="Arial, sans-serif" font-size="8" fill="#6b7280">
    <text x="385" y="100">添加</text>
    <text x="385" y="140">编辑</text>
    <text x="385" y="180">删除</text>
    <text x="480" y="300">新建</text>
    <text x="480" y="340">更新</text>
    <text x="530" y="485">销售</text>
    <text x="530" y="550">利润</text>
    <text x="530" y="610">趋势</text>
    <text x="385" y="735">有变更</text>
    <text x="1150" y="270" font-size="9" fill="#4ade80">触发批次</text>
    <text x="1190" y="460" font-size="9" fill="#4ade80">触发财务</text>
    <text x="1070" y="680" font-size="9" fill="#4ade80">触发备份</text>
  </g>

  <!-- 图例说明 -->
  <rect x="1250" y="120" width="300" height="200" fill="white" rx="10" filter="url(#shadow)"/>
  <text x="1270" y="140" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1e293b">🔍 图例说明</text>
  
  <g id="legend">
    <rect x="1270" y="155" width="30" height="15" fill="url(#startEndGradient)" rx="7"/>
    <text x="1310" y="165" font-family="Arial, sans-serif" font-size="9" fill="#374151">开始/结束</text>
    
    <rect x="1270" y="175" width="30" height="15" fill="url(#processGradient)" rx="5"/>
    <text x="1310" y="185" font-family="Arial, sans-serif" font-size="9" fill="#374151">处理过程</text>
    
    <polygon points="1270,195 1300,202 1270,210 1240,202" fill="url(#decisionGradient)"/>
    <text x="1310" y="205" font-family="Arial, sans-serif" font-size="9" fill="#374151">决策判断</text>
    
    <rect x="1270" y="215" width="30" height="15" fill="url(#dataGradient)" rx="5"/>
    <text x="1310" y="225" font-family="Arial, sans-serif" font-size="9" fill="#374151">数据处理</text>

    <line x1="1270" y1="240" x2="1300" y2="240" stroke="#6b7280" stroke-width="2" marker-end="url(#arrowhead)"/>
    <text x="1310" y="245" font-family="Arial, sans-serif" font-size="9" fill="#374151">数据流向</text>

    <line x1="1270" y1="255" x2="1300" y2="255" stroke="#4ade80" stroke-width="2" stroke-dasharray="3,3" marker-end="url(#arrowhead)"/>
    <text x="1310" y="260" font-family="Arial, sans-serif" font-size="9" fill="#374151">流程触发</text>

    <text x="1270" y="285" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="#059669">✅ 流程特点:</text>
    <text x="1270" y="298" font-family="Arial, sans-serif" font-size="8" fill="#374151">• 自动化处理</text>
    <text x="1270" y="310" font-family="Arial, sans-serif" font-size="8" fill="#374151">• 智能决策分支</text>
  </g>

  <!-- 版本信息 -->
  <text x="1550" y="980" text-anchor="end" font-family="Arial, sans-serif" font-size="10" fill="#94a3b8">
    Generated by SVG Generator v1.1.6 | 2024-12-24
  </text>
</svg> 