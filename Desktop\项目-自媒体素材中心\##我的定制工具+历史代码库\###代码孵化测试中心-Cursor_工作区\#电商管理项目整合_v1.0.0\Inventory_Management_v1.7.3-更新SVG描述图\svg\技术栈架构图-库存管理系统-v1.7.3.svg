<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .component-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #ffffff; }
      .description { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .tech-stack { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 11px; fill: #ffffff; }
      .version { font-family: Arial, sans-serif; font-size: 10px; fill: #95a5a6; }

      .layer-ui { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .layer-core { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .layer-data { fill: #27ae60; stroke: #1e8449; stroke-width: 2; }
      .layer-tools { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      
      .component-bg { fill: rgba(255,255,255,0.2); stroke: rgba(255,255,255,0.4); stroke-width: 1; rx: 8; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" class="title">库存管理系统 - 技术栈架构图 v1.7.3</text>
  <text x="600" y="65" text-anchor="middle" class="description">基于 PyQt6 的现代化桌面应用程序技术架构</text>
  
  <!-- 前端UI层 -->
  <rect x="50" y="100" width="1100" height="140" class="layer-ui" rx="10"/>
  <text x="70" y="125" class="layer-title">前端UI层 (Presentation Layer)</text>
  <text x="70" y="145" class="description">基于 PyQt6 的现代化用户界面</text>
  
  <rect x="80" y="160" width="150" height="65" class="component-bg"/>
  <text x="155" y="185" text-anchor="middle" class="component-title">主窗口</text>
  <text x="155" y="200" text-anchor="middle" class="tech-stack">PyQt6 MainWindow</text>
  
  <rect x="250" y="160" width="150" height="65" class="component-bg"/>
  <text x="325" y="185" text-anchor="middle" class="component-title">对话框</text>
  <text x="325" y="200" text-anchor="middle" class="tech-stack">QDialog</text>
  
  <rect x="420" y="160" width="150" height="65" class="component-bg"/>
  <text x="495" y="185" text-anchor="middle" class="component-title">自定义控件</text>
  <text x="495" y="200" text-anchor="middle" class="tech-stack">Custom Widgets</text>
  
  <rect x="590" y="160" width="150" height="65" class="component-bg"/>
  <text x="665" y="185" text-anchor="middle" class="component-title">主题系统</text>
  <text x="665" y="200" text-anchor="middle" class="tech-stack">QSS Themes</text>
  
  <rect x="760" y="160" width="150" height="65" class="component-bg"/>
  <text x="835" y="185" text-anchor="middle" class="component-title">图像界面</text>
  <text x="835" y="200" text-anchor="middle" class="tech-stack">PIL &amp; OpenCV</text>
  
  <rect x="930" y="160" width="150" height="65" class="component-bg"/>
  <text x="1005" y="185" text-anchor="middle" class="component-title">扫码界面</text>
  <text x="1005" y="200" text-anchor="middle" class="tech-stack">pyzbar</text>
  
  <!-- Python核心层 -->
  <rect x="50" y="270" width="1100" height="140" class="layer-core" rx="10"/>
  <text x="70" y="295" class="layer-title">Python核心层 (Business Logic Layer)</text>
  <text x="70" y="315" class="description">业务逻辑处理、数据模型管理</text>
  
  <rect x="80" y="330" width="150" height="65" class="component-bg"/>
  <text x="155" y="355" text-anchor="middle" class="component-title">产品管理</text>
  <text x="155" y="370" text-anchor="middle" class="tech-stack">Product Models</text>
  
  <rect x="250" y="330" width="150" height="65" class="component-bg"/>
  <text x="325" y="355" text-anchor="middle" class="component-title">批次管理</text>
  <text x="325" y="370" text-anchor="middle" class="tech-stack">Batch Models</text>
  
  <rect x="420" y="330" width="150" height="65" class="component-bg"/>
  <text x="495" y="355" text-anchor="middle" class="component-title">财务分析</text>
  <text x="495" y="370" text-anchor="middle" class="tech-stack">pandas</text>
  
  <rect x="590" y="330" width="150" height="65" class="component-bg"/>
  <text x="665" y="355" text-anchor="middle" class="component-title">配置管理</text>
  <text x="665" y="370" text-anchor="middle" class="tech-stack">Config System</text>
  
  <rect x="760" y="330" width="150" height="65" class="component-bg"/>
  <text x="835" y="355" text-anchor="middle" class="component-title">错误处理</text>
  <text x="835" y="370" text-anchor="middle" class="tech-stack">Error Handler</text>
  
  <rect x="930" y="330" width="150" height="65" class="component-bg"/>
  <text x="1005" y="355" text-anchor="middle" class="component-title">事务管理</text>
  <text x="1005" y="370" text-anchor="middle" class="tech-stack">Transaction</text>
  
  <!-- 数据存储层 -->
  <rect x="50" y="440" width="1100" height="140" class="layer-data" rx="10"/>
  <text x="70" y="465" class="layer-title">数据存储层 (Data Persistence Layer)</text>
  <text x="70" y="485" class="description">数据库操作、文件管理</text>
  
  <rect x="80" y="500" width="180" height="65" class="component-bg"/>
  <text x="170" y="525" text-anchor="middle" class="component-title">SQLite数据库</text>
  <text x="170" y="540" text-anchor="middle" class="tech-stack">db_utils.py</text>
  
  <rect x="280" y="500" width="180" height="65" class="component-bg"/>
  <text x="370" y="525" text-anchor="middle" class="component-title">图像存储</text>
  <text x="370" y="540" text-anchor="middle" class="tech-stack">Image Utils</text>
  
  <rect x="480" y="500" width="180" height="65" class="component-bg"/>
  <text x="570" y="525" text-anchor="middle" class="component-title">Excel导出</text>
  <text x="570" y="540" text-anchor="middle" class="tech-stack">openpyxl</text>
  
  <rect x="680" y="500" width="180" height="65" class="component-bg"/>
  <text x="770" y="525" text-anchor="middle" class="component-title">数据模型</text>
  <text x="770" y="540" text-anchor="middle" class="tech-stack">ORM Models</text>
  
  <rect x="880" y="500" width="180" height="65" class="component-bg"/>
  <text x="970" y="525" text-anchor="middle" class="component-title">数据管理</text>
  <text x="970" y="540" text-anchor="middle" class="tech-stack">DB Manager</text>
  
  <!-- 工具支持层 -->
  <rect x="50" y="610" width="1100" height="140" class="layer-tools" rx="10"/>
  <text x="70" y="635" class="layer-title">工具支持层 (Tools &amp; Testing Layer)</text>
  <text x="70" y="655" class="description">测试框架、开发工具</text>
  
  <rect x="80" y="670" width="150" height="65" class="component-bg"/>
  <text x="155" y="695" text-anchor="middle" class="component-title">单元测试</text>
  <text x="155" y="710" text-anchor="middle" class="tech-stack">pytest</text>
  
  <rect x="250" y="670" width="150" height="65" class="component-bg"/>
  <text x="325" y="695" text-anchor="middle" class="component-title">日志系统</text>
  <text x="325" y="710" text-anchor="middle" class="tech-stack">logging</text>
  
  <rect x="420" y="670" width="150" height="65" class="component-bg"/>
  <text x="495" y="695" text-anchor="middle" class="component-title">依赖管理</text>
  <text x="495" y="710" text-anchor="middle" class="tech-stack">requirements.txt</text>
  
  <rect x="590" y="670" width="150" height="65" class="component-bg"/>
  <text x="665" y="695" text-anchor="middle" class="component-title">构建工具</text>
  <text x="665" y="710" text-anchor="middle" class="tech-stack">pyproject.toml</text>
  
  <rect x="760" y="670" width="150" height="65" class="component-bg"/>
  <text x="835" y="695" text-anchor="middle" class="component-title">启动脚本</text>
  <text x="835" y="710" text-anchor="middle" class="tech-stack">start.bat/py</text>
  
  <rect x="930" y="670" width="150" height="65" class="component-bg"/>
  <text x="1005" y="695" text-anchor="middle" class="component-title">运行环境</text>
  <text x="1005" y="710" text-anchor="middle" class="tech-stack">Python 3.8+</text>
  
  <!-- 版本信息 -->
  <text x="50" y="785" class="version">Generated by SVG架构图生成器 v1.1.6</text>
  <text x="1150" y="785" text-anchor="end" class="version">2024-01-02</text>
</svg> 