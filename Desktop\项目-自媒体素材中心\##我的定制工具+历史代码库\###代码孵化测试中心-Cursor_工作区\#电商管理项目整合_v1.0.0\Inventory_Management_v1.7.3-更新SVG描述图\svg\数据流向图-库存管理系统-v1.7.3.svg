<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .section-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #34495e; }
      .entity-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 13px; font-weight: bold; fill: #ffffff; }
      .description { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 11px; fill: #7f8c8d; }
      .detail { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 10px; fill: #95a5a6; }
      .version { font-family: Arial, sans-serif; font-size: 10px; fill: #95a5a6; }

      .core-entity { fill: #3498db; stroke: #2980b9; stroke-width: 3; filter: url(#glow); }
      .related-entity { fill: #27ae60; stroke: #1e8449; stroke-width: 2; }
      .process-node { fill: #f39c12; stroke: #e67e22; stroke-width: 2; rx: 8; }
      .external-system { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; rx: 8; }
      .attribute-node { fill: #9b59b6; stroke: #8e44ad; stroke-width: 1; rx: 4; }
      
      .primary-flow { stroke: #2c3e50; stroke-width: 3; fill: none; marker-end: url(#arrowhead-primary); }
      .relation-flow { stroke: #27ae60; stroke-width: 2; fill: none; marker-end: url(#arrowhead-green); }
      .process-flow { stroke: #f39c12; stroke-width: 2; fill: none; marker-end: url(#arrowhead-orange); stroke-dasharray: 5,5; }
      .external-flow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead-red); stroke-dasharray: 8,4; }
    </style>
    
    <marker id="arrowhead-primary" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto">
      <polygon points="0 0, 12 4, 0 8" fill="#2c3e50" />
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#27ae60" />
    </marker>
    <marker id="arrowhead-orange" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#f39c12" />
    </marker>
    <marker id="arrowhead-red" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 分组区域 -->
    <linearGradient id="coreArea" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f4fd;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#d4edda;stop-opacity:0.3" />
    </linearGradient>
    
    <linearGradient id="configArea" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3cd;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#f8d7da;stop-opacity:0.3" />
    </linearGradient>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" class="title">库存管理系统 - 数据流向图 v1.7.3</text>
  <text x="700" y="65" text-anchor="middle" class="description">展示核心实体关系、数据处理流程和外部系统接口</text>
  
  <!-- 核心业务实体区域 -->
  <rect x="50" y="100" width="900" height="350" fill="url(#coreArea)" stroke="#3498db" stroke-width="1" stroke-dasharray="10,5" rx="15"/>
  <text x="70" y="125" class="section-title">核心业务实体区</text>
  
  <!-- 产品实体 -->
  <circle cx="200" cy="200" r="45" class="core-entity"/>
  <text x="200" y="195" text-anchor="middle" class="entity-title">产品实体</text>
  <text x="200" y="210" text-anchor="middle" class="entity-title" style="font-size: 11px;">Product</text>
  
  <!-- 批次实体 -->
  <circle cx="450" cy="200" r="45" class="core-entity"/>
  <text x="450" y="195" text-anchor="middle" class="entity-title">批次实体</text>
  <text x="450" y="210" text-anchor="middle" class="entity-title" style="font-size: 11px;">Batch</text>
  
  <!-- 事务实体 -->
  <circle cx="700" cy="200" r="45" class="core-entity"/>
  <text x="700" y="195" text-anchor="middle" class="entity-title">事务实体</text>
  <text x="700" y="210" text-anchor="middle" class="entity-title" style="font-size: 11px;">Transaction</text>
  
  <!-- 关联实体 -->
  <ellipse cx="200" cy="350" rx="50" ry="30" class="related-entity"/>
  <text x="200" y="345" text-anchor="middle" class="entity-title">图像数据</text>
  <text x="200" y="360" text-anchor="middle" class="entity-title" style="font-size: 11px;">Images</text>
  
  <ellipse cx="450" cy="350" rx="50" ry="30" class="related-entity"/>
  <text x="450" y="345" text-anchor="middle" class="entity-title">分类数据</text>
  <text x="450" y="360" text-anchor="middle" class="entity-title" style="font-size: 11px;">Categories</text>
  
  <ellipse cx="700" cy="350" rx="50" ry="30" class="related-entity"/>
  <text x="700" y="345" text-anchor="middle" class="entity-title">财务数据</text>
  <text x="700" y="360" text-anchor="middle" class="entity-title" style="font-size: 11px;">Finance</text>
  
  <!-- 系统配置区 -->
  <rect x="980" y="100" width="370" height="350" fill="url(#configArea)" stroke="#f39c12" stroke-width="1" stroke-dasharray="10,5" rx="15"/>
  <text x="1000" y="125" class="section-title">系统配置区</text>
  
  <!-- 用户配置 -->
  <ellipse cx="1100" cy="180" rx="50" ry="30" class="related-entity"/>
  <text x="1100" y="175" text-anchor="middle" class="entity-title">用户配置</text>
  <text x="1100" y="190" text-anchor="middle" class="entity-title" style="font-size: 11px;">Config</text>
  
  <!-- 主题设置 -->
  <ellipse cx="1250" cy="180" rx="50" ry="30" class="related-entity"/>
  <text x="1250" y="175" text-anchor="middle" class="entity-title">主题设置</text>
  <text x="1250" y="190" text-anchor="middle" class="entity-title" style="font-size: 11px;">Themes</text>
  
  <!-- 统计数据 -->
  <ellipse cx="1100" cy="280" rx="50" ry="30" class="related-entity"/>
  <text x="1100" y="275" text-anchor="middle" class="entity-title">统计数据</text>
  <text x="1100" y="290" text-anchor="middle" class="entity-title" style="font-size: 11px;">Statistics</text>
  
  <!-- 日志数据 -->
  <ellipse cx="1250" cy="280" rx="50" ry="30" class="related-entity"/>
  <text x="1250" y="275" text-anchor="middle" class="entity-title">日志数据</text>
  <text x="1250" y="290" text-anchor="middle" class="entity-title" style="font-size: 11px;">Logs</text>
  
  <!-- 数据处理区 -->
  <rect x="50" y="500" width="1300" height="120" fill="#f8f9fa" stroke="#6c757d" stroke-width="1" rx="10"/>
  <text x="70" y="525" class="section-title">数据处理区</text>
  
  <!-- 数据验证 -->
  <rect x="100" y="540" width="120" height="60" class="process-node"/>
  <text x="160" y="565" text-anchor="middle" class="entity-title">数据验证</text>
  <text x="160" y="580" text-anchor="middle" class="entity-title" style="font-size: 11px;">Validation</text>
  
  <!-- 数据转换 -->
  <rect x="280" y="540" width="120" height="60" class="process-node"/>
  <text x="340" y="565" text-anchor="middle" class="entity-title">数据转换</text>
  <text x="340" y="580" text-anchor="middle" class="entity-title" style="font-size: 11px;">Transform</text>
  
  <!-- 数据聚合 -->
  <rect x="460" y="540" width="120" height="60" class="process-node"/>
  <text x="520" y="565" text-anchor="middle" class="entity-title">数据聚合</text>
  <text x="520" y="580" text-anchor="middle" class="entity-title" style="font-size: 11px;">Aggregation</text>
  
  <!-- 数据缓存 -->
  <rect x="640" y="540" width="120" height="60" class="process-node"/>
  <text x="700" y="565" text-anchor="middle" class="entity-title">数据缓存</text>
  <text x="700" y="580" text-anchor="middle" class="entity-title" style="font-size: 11px;">Cache</text>
  
  <!-- 数据同步 -->
  <rect x="820" y="540" width="120" height="60" class="process-node"/>
  <text x="880" y="565" text-anchor="middle" class="entity-title">数据同步</text>
  <text x="880" y="580" text-anchor="middle" class="entity-title" style="font-size: 11px;">Sync</text>
  
  <!-- 错误处理 -->
  <rect x="1000" y="540" width="120" height="60" class="external-system"/>
  <text x="1060" y="565" text-anchor="middle" class="entity-title">错误处理</text>
  <text x="1060" y="580" text-anchor="middle" class="entity-title" style="font-size: 11px;">Error Handler</text>
  
  <!-- 外部系统 -->
  <rect x="50" y="680" width="1300" height="120" fill="#fff5f5" stroke="#e74c3c" stroke-width="1" rx="10"/>
  <text x="70" y="705" class="section-title">外部系统接口</text>
  
  <!-- 文件系统 -->
  <rect x="100" y="720" width="120" height="60" class="external-system"/>
  <text x="160" y="745" text-anchor="middle" class="entity-title">文件系统</text>
  <text x="160" y="760" text-anchor="middle" class="entity-title" style="font-size: 11px;">File System</text>
  
  <!-- SQLite数据库 -->
  <rect x="280" y="720" width="120" height="60" class="external-system"/>
  <text x="340" y="745" text-anchor="middle" class="entity-title">SQLite数据库</text>
  <text x="340" y="760" text-anchor="middle" class="entity-title" style="font-size: 11px;">Database</text>
  
  <!-- Excel导入导出 -->
  <rect x="460" y="720" width="120" height="60" class="external-system"/>
  <text x="520" y="745" text-anchor="middle" class="entity-title">Excel导入导出</text>
  <text x="520" y="760" text-anchor="middle" class="entity-title" style="font-size: 11px;">openpyxl</text>
  
  <!-- 图像处理 -->
  <rect x="640" y="720" width="120" height="60" class="external-system"/>
  <text x="700" y="745" text-anchor="middle" class="entity-title">图像处理</text>
  <text x="700" y="760" text-anchor="middle" class="entity-title" style="font-size: 11px;">PIL/OpenCV</text>
  
  <!-- 扫码设备 -->
  <rect x="820" y="720" width="120" height="60" class="external-system"/>
  <text x="880" y="745" text-anchor="middle" class="entity-title">扫码设备</text>
  <text x="880" y="760" text-anchor="middle" class="entity-title" style="font-size: 11px;">pyzbar</text>
  
  <!-- 打印系统 -->
  <rect x="1000" y="720" width="120" height="60" class="external-system"/>
  <text x="1060" y="745" text-anchor="middle" class="entity-title">打印系统</text>
  <text x="1060" y="760" text-anchor="middle" class="entity-title" style="font-size: 11px;">Print Service</text>
  
  <!-- 属性节点 -->
  <!-- 产品属性 -->
  <rect x="80" y="150" width="60" height="20" class="attribute-node"/>
  <text x="110" y="163" text-anchor="middle" class="entity-title" style="font-size: 10px;">产品编号</text>
  
  <rect x="80" y="175" width="60" height="20" class="attribute-node"/>
  <text x="110" y="188" text-anchor="middle" class="entity-title" style="font-size: 10px;">产品名称</text>
  
  <rect x="80" y="225" width="60" height="20" class="attribute-node"/>
  <text x="110" y="238" text-anchor="middle" class="entity-title" style="font-size: 10px;">销售价格</text>
  
  <rect x="80" y="250" width="60" height="20" class="attribute-node"/>
  <text x="110" y="263" text-anchor="middle" class="entity-title" style="font-size: 10px;">库存数量</text>
  
  <!-- 批次属性 -->
  <rect x="330" y="150" width="60" height="20" class="attribute-node"/>
  <text x="360" y="163" text-anchor="middle" class="entity-title" style="font-size: 10px;">批次号</text>
  
  <rect x="330" y="175" width="60" height="20" class="attribute-node"/>
  <text x="360" y="188" text-anchor="middle" class="entity-title" style="font-size: 10px;">生产日期</text>
  
  <rect x="510" y="150" width="60" height="20" class="attribute-node"/>
  <text x="540" y="163" text-anchor="middle" class="entity-title" style="font-size: 10px;">过期日期</text>
  
  <rect x="510" y="175" width="60" height="20" class="attribute-node"/>
  <text x="540" y="188" text-anchor="middle" class="entity-title" style="font-size: 10px;">批次状态</text>
  
  <!-- 事务属性 -->
  <rect x="580" y="150" width="60" height="20" class="attribute-node"/>
  <text x="610" y="163" text-anchor="middle" class="entity-title" style="font-size: 10px;">事务ID</text>
  
  <rect x="580" y="175" width="60" height="20" class="attribute-node"/>
  <text x="610" y="188" text-anchor="middle" class="entity-title" style="font-size: 10px;">事务类型</text>
  
  <rect x="760" y="150" width="60" height="20" class="attribute-node"/>
  <text x="790" y="163" text-anchor="middle" class="entity-title" style="font-size: 10px;">事务时间</text>
  
  <rect x="760" y="175" width="60" height="20" class="attribute-node"/>
  <text x="790" y="188" text-anchor="middle" class="entity-title" style="font-size: 10px;">操作用户</text>
  
  <!-- 连接关系 -->
  <!-- 核心实体间的主要关系 -->
  <line x1="245" y1="200" x2="405" y2="200" class="primary-flow"/>
  <line x1="495" y1="200" x2="655" y2="200" class="primary-flow"/>
  
  <!-- 实体与关联数据的关系 -->
  <line x1="200" y1="245" x2="200" y2="320" class="relation-flow"/>
  <line x1="450" y1="245" x2="450" y2="320" class="relation-flow"/>
  <line x1="700" y1="245" x2="700" y2="320" class="relation-flow"/>
  
  <!-- 属性连接 -->
  <line x1="140" y1="160" x2="155" y2="180" class="relation-flow"/>
  <line x1="140" y1="185" x2="155" y2="195" class="relation-flow"/>
  <line x1="140" y1="235" x2="155" y2="215" class="relation-flow"/>
  <line x1="140" y1="260" x2="155" y2="220" class="relation-flow"/>
  
  <line x1="390" y1="160" x2="405" y2="180" class="relation-flow"/>
  <line x1="390" y1="185" x2="405" y2="190" class="relation-flow"/>
  <line x1="510" y1="160" x2="495" y2="180" class="relation-flow"/>
  <line x1="510" y1="185" x2="495" y2="190" class="relation-flow"/>
  
  <line x1="640" y1="160" x2="655" y2="180" class="relation-flow"/>
  <line x1="640" y1="185" x2="655" y2="190" class="relation-flow"/>
  <line x1="760" y1="160" x2="745" y2="180" class="relation-flow"/>
  <line x1="760" y1="185" x2="745" y2="190" class="relation-flow"/>
  
  <!-- 数据处理流程 -->
  <line x1="220" y1="570" x2="280" y2="570" class="process-flow"/>
  <line x1="400" y1="570" x2="460" y2="570" class="process-flow"/>
  <line x1="580" y1="570" x2="640" y2="570" class="process-flow"/>
  <line x1="760" y1="570" x2="820" y2="570" class="process-flow"/>
  <line x1="940" y1="570" x2="1000" y2="570" class="external-flow"/>
  
  <!-- 外部系统连接 -->
  <line x1="160" y1="600" x2="160" y2="720" class="external-flow"/>
  <line x1="340" y1="600" x2="340" y2="720" class="external-flow"/>
  <line x1="520" y1="600" x2="520" y2="720" class="external-flow"/>
  <line x1="700" y1="600" x2="700" y2="720" class="external-flow"/>
  <line x1="880" y1="600" x2="880" y2="720" class="external-flow"/>
  
  <!-- 配置区连接 -->
  <line x1="950" y1="200" x2="1050" y2="180" class="relation-flow"/>
  <line x1="950" y1="220" x2="1200" y2="180" class="relation-flow"/>
  <line x1="950" y1="300" x2="1050" y2="280" class="process-flow"/>
  <line x1="950" y1="320" x2="1200" y2="280" class="external-flow"/>
  
  <!-- 图例说明 -->
  <rect x="50" y="840" width="1300" height="100" fill="none" stroke="#bdc3c7" stroke-width="1"/>
  <text x="60" y="860" class="section-title">数据流向图例</text>
  
  <!-- 实体类型 -->
  <circle cx="100" cy="890" r="15" class="core-entity"/>
  <text x="125" y="895" class="description">核心实体</text>
  
  <ellipse cx="200" cy="890" rx="20" ry="12" class="related-entity"/>
  <text x="230" y="895" class="description">关联实体</text>
  
  <rect x="300" y="878" width="30" height="24" class="process-node"/>
  <text x="340" y="895" class="description">处理节点</text>
  
  <rect x="420" y="878" width="30" height="24" class="external-system"/>
  <text x="460" y="895" class="description">外部系统</text>
  
  <rect x="540" y="883" width="25" height="14" class="attribute-node"/>
  <text x="575" y="895" class="description">属性节点</text>
  
  <!-- 关系类型 -->
  <line x1="650" y1="880" x2="700" y2="880" class="primary-flow"/>
  <text x="710" y="885" class="description">主要数据流</text>
  
  <line x1="650" y1="900" x2="700" y2="900" class="relation-flow"/>
  <text x="710" y="905" class="description">关联关系</text>
  
  <line x1="790" y1="880" x2="840" y2="880" class="process-flow"/>
  <text x="850" y="885" class="description">处理流程</text>
  
  <line x1="790" y1="900" x2="840" y2="900" class="external-flow"/>
  <text x="850" y="905" class="description">外部接口</text>
  
  <!-- 数据量级 -->
  <text x="980" y="875" class="description">数据量级：</text>
  <text x="980" y="890" class="description">• 产品记录：10K+ 条</text>
  <text x="980" y="905" class="description">• 批次记录：50K+ 条</text>
  <text x="980" y="920" class="description">• 事务记录：100K+ 条</text>
  
  <!-- 性能特点 -->
  <text x="1150" y="875" class="description">性能特点：</text>
  <text x="1150" y="890" class="description">• 实时数据同步</text>
  <text x="1150" y="905" class="description">• 异步数据处理</text>
  <text x="1150" y="920" class="description">• 智能缓存机制</text>
  
  <!-- 版本信息 -->
  <text x="50" y="965" class="version">Generated by SVG架构图生成器 v1.1.6</text>
  <text x="1350" y="965" text-anchor="end" class="version">2024-01-02</text>
</svg> 