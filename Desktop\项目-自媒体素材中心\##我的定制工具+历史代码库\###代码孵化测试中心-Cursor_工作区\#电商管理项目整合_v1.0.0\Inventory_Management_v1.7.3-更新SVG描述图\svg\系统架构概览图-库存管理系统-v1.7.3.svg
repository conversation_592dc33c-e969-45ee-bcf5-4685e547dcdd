<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 26px; font-weight: bold; fill: #2c3e50; }
      .layer-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #34495e; }
      .component-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #2c3e50; }
      .description { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #7f8c8d; }
      .tech-stack { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 11px; fill: #3498db; font-weight: bold; }
      .version { font-family: Arial, sans-serif; font-size: 10px; fill: #95a5a6; }

      .layer-ui { fill: #e8f4f8; stroke: #3498db; stroke-width: 2; }
      .layer-logic { fill: #fff2e8; stroke: #f39c12; stroke-width: 2; }
      .layer-service { fill: #e8f8e8; stroke: #27ae60; stroke-width: 2; }
      .layer-data { fill: #f8e8e8; stroke: #e74c3c; stroke-width: 2; }
      
      .component-bg { fill: #ffffff; stroke: #bdc3c7; stroke-width: 1; rx: 8; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" class="title">库存管理系统 - 系统架构概览图 v1.7.3</text>
  <text x="600" y="65" text-anchor="middle" class="description">分层架构设计，展示各层职责和数据流向</text>
  
  <!-- 用户交互层 -->
  <rect x="50" y="100" width="1100" height="120" class="layer-ui" rx="10"/>
  <text x="70" y="125" class="layer-title">用户交互层 (User Interface Layer)</text>
  <text x="70" y="145" class="description">基于PyQt6的现代化桌面界面，提供友好的用户交互体验</text>
  
  <rect x="80" y="160" width="200" height="50" class="component-bg"/>
  <text x="180" y="180" text-anchor="middle" class="component-title">主窗口</text>
  <text x="180" y="195" text-anchor="middle" class="tech-stack">PyQt6 MainWindow</text>
  
  <rect x="300" y="160" width="200" height="50" class="component-bg"/>
  <text x="400" y="180" text-anchor="middle" class="component-title">对话框集合</text>
  <text x="400" y="195" text-anchor="middle" class="tech-stack">QDialog Components</text>
  
  <rect x="520" y="160" width="200" height="50" class="component-bg"/>
  <text x="620" y="180" text-anchor="middle" class="component-title">自定义控件</text>
  <text x="620" y="195" text-anchor="middle" class="tech-stack">Custom Widgets</text>
  
  <rect x="740" y="160" width="200" height="50" class="component-bg"/>
  <text x="840" y="180" text-anchor="middle" class="component-title">主题系统</text>
  <text x="840" y="195" text-anchor="middle" class="tech-stack">QSS Theme Manager</text>
  
  <rect x="960" y="160" width="160" height="50" class="component-bg"/>
  <text x="1040" y="180" text-anchor="middle" class="component-title">资源管理</text>
  <text x="1040" y="195" text-anchor="middle" class="tech-stack">Resources</text>
  
  <!-- 应用逻辑层 -->
  <rect x="50" y="250" width="1100" height="120" class="layer-logic" rx="10"/>
  <text x="70" y="275" class="layer-title">应用逻辑层 (Application Logic Layer)</text>
  <text x="70" y="295" class="description">业务逻辑处理、数据验证和应用程序控制流程</text>
  
  <rect x="80" y="310" width="160" height="50" class="component-bg"/>
  <text x="160" y="330" text-anchor="middle" class="component-title">产品管理</text>
  <text x="160" y="345" text-anchor="middle" class="tech-stack">Product Logic</text>
  
  <rect x="260" y="310" width="160" height="50" class="component-bg"/>
  <text x="340" y="330" text-anchor="middle" class="component-title">批次管理</text>
  <text x="340" y="345" text-anchor="middle" class="tech-stack">Batch Logic</text>
  
  <rect x="440" y="310" width="160" height="50" class="component-bg"/>
  <text x="520" y="330" text-anchor="middle" class="component-title">财务分析</text>
  <text x="520" y="345" text-anchor="middle" class="tech-stack">Finance Logic</text>
  
  <rect x="620" y="310" width="160" height="50" class="component-bg"/>
  <text x="700" y="330" text-anchor="middle" class="component-title">配置管理</text>
  <text x="700" y="345" text-anchor="middle" class="tech-stack">Config Handler</text>
  
  <rect x="800" y="310" width="160" height="50" class="component-bg"/>
  <text x="880" y="330" text-anchor="middle" class="component-title">错误处理</text>
  <text x="880" y="345" text-anchor="middle" class="tech-stack">Error Handler</text>
  
  <rect x="980" y="310" width="140" height="50" class="component-bg"/>
  <text x="1050" y="330" text-anchor="middle" class="component-title">工具集</text>
  <text x="1050" y="345" text-anchor="middle" class="tech-stack">Utils</text>
  
  <!-- 服务接口层 -->
  <rect x="50" y="400" width="1100" height="120" class="layer-service" rx="10"/>
  <text x="70" y="425" class="layer-title">服务接口层 (Service Interface Layer)</text>
  <text x="70" y="445" class="description">数据访问接口、文件操作和外部服务集成</text>
  
  <rect x="80" y="460" width="180" height="50" class="component-bg"/>
  <text x="170" y="480" text-anchor="middle" class="component-title">数据库服务</text>
  <text x="170" y="495" text-anchor="middle" class="tech-stack">DB Utils &amp; ORM</text>
  
  <rect x="280" y="460" width="180" height="50" class="component-bg"/>
  <text x="370" y="480" text-anchor="middle" class="component-title">图像处理服务</text>
  <text x="370" y="495" text-anchor="middle" class="tech-stack">PIL &amp; OpenCV</text>
  
  <rect x="480" y="460" width="180" height="50" class="component-bg"/>
  <text x="570" y="480" text-anchor="middle" class="component-title">Excel服务</text>
  <text x="570" y="495" text-anchor="middle" class="tech-stack">openpyxl</text>
  
  <rect x="680" y="460" width="180" height="50" class="component-bg"/>
  <text x="770" y="480" text-anchor="middle" class="component-title">扫码服务</text>
  <text x="770" y="495" text-anchor="middle" class="tech-stack">pyzbar Scanner</text>
  
  <rect x="880" y="460" width="180" height="50" class="component-bg"/>
  <text x="970" y="480" text-anchor="middle" class="component-title">日志服务</text>
  <text x="970" y="495" text-anchor="middle" class="tech-stack">Logging System</text>
  
  <!-- 数据持久层 -->
  <rect x="50" y="550" width="1100" height="120" class="layer-data" rx="10"/>
  <text x="70" y="575" class="layer-title">数据持久层 (Data Persistence Layer)</text>
  <text x="70" y="595" class="description">数据存储、缓存管理和数据持久化操作</text>
  
  <rect x="80" y="610" width="200" height="50" class="component-bg"/>
  <text x="180" y="630" text-anchor="middle" class="component-title">SQLite数据库</text>
  <text x="180" y="645" text-anchor="middle" class="tech-stack">inventory.db</text>
  
  <rect x="300" y="610" width="200" height="50" class="component-bg"/>
  <text x="400" y="630" text-anchor="middle" class="component-title">图像文件系统</text>
  <text x="400" y="645" text-anchor="middle" class="tech-stack">images/ directory</text>
  
  <rect x="520" y="610" width="200" height="50" class="component-bg"/>
  <text x="620" y="630" text-anchor="middle" class="component-title">配置文件</text>
  <text x="620" y="645" text-anchor="middle" class="tech-stack">config.json</text>
  
  <rect x="740" y="610" width="200" height="50" class="component-bg"/>
  <text x="840" y="630" text-anchor="middle" class="component-title">日志文件</text>
  <text x="840" y="645" text-anchor="middle" class="tech-stack">logs/app.log</text>
  
  <rect x="960" y="610" width="160" height="50" class="component-bg"/>
  <text x="1040" y="630" text-anchor="middle" class="component-title">备份数据</text>
  <text x="1040" y="645" text-anchor="middle" class="tech-stack">Backup Files</text>
  
  <!-- 箭头连接 -->
  <line x1="600" y1="220" x2="600" y2="250" class="arrow"/>
  <line x1="600" y1="370" x2="600" y2="400" class="arrow"/>
  <line x1="600" y1="520" x2="600" y2="550" class="arrow"/>
  
  <!-- 系统特性 -->
  <rect x="50" y="700" width="1100" height="60" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1" rx="8"/>
  <text x="70" y="720" class="layer-title">系统特性</text>
  <text x="70" y="740" class="description">• 分层架构设计 • 模块化组件 • 数据一致性 • 高性能优化 • 安全可靠 • 易于维护</text>
  <text x="70" y="755" class="tech-stack">前端UI: PyQt6 | 业务逻辑: Python/pandas | 数据存储: SQLite | 工具: PIL/OpenCV/pyzbar</text>
  
  <!-- 版本信息 -->
  <text x="50" y="785" class="version">Generated by SVG架构图生成器 v1.1.6</text>
  <text x="1150" y="785" text-anchor="end" class="version">2024-01-02</text>
</svg> 